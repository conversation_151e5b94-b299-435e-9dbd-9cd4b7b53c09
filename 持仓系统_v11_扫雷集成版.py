#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持仓系统 V11 扫雷集成版
基于V10版本，新增扫雷风险评估功能
集成通达信扫雷模块，为每只股票提供风险评估分数
保持完全向后兼容性，集成年内最低价缓存系统
"""

from flask import Flask, render_template_string, jsonify, request
import requests
import time
import json
import pandas as pd
import os
from datetime import datetime, timedelta
import threading
from urllib.parse import urlencode
from werkzeug.utils import secure_filename
from wechat_alert import WeChatAlert
from A股交易时间监测_简化版 import AStockTradingTimeMonitor
from yearly_low_cache_reader import get_yearly_low_from_cache, check_cache_status, is_cache_available
from tdx_scan_module import TdxScanClient

app = Flask(__name__)

# 配置文件上传
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
ALLOWED_EXTENSIONS = {'xlsx', 'xls'}

# 确保上传目录存在
if not os.path.exists(app.config['UPLOAD_FOLDER']):
    os.makedirs(app.config['UPLOAD_FOLDER'])

# 全局变量存储股票数据
stock_data = {}
last_update_time = None
imported_stock_list = []  # 存储导入的股票列表

# 配置
CONFIG = {
    'stock_interval': 0.1,      # 每只股票间隔时间（秒）
    'round_interval': 600,      # 每轮更新间隔时间（秒）
    'request_timeout': 15,      # 请求超时时间（秒）
    'alert_threshold': 70.0,    # 企业微信提醒阈值（距离最低点涨幅%）
}

# 企业微信提醒配置
WECHAT_WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7bddd706-ef0f-4d6c-9e1f-3cc8a0a18d9e"

# 初始化企业微信提醒
wechat_alert = WeChatAlert(WECHAT_WEBHOOK_URL)

# 初始化A股交易时间监测器
trading_monitor = AStockTradingTimeMonitor()

# 初始化扫雷客户端
scan_client = TdxScanClient(cache_duration=1800)  # 缓存30分钟

def get_security_type(code):
    """根据代码判断证券类型"""
    code_str = str(code)
    if code_str.startswith('1'):
        return 'ETF'
    elif code_str.startswith('5'):
        return '基金'
    else:
        return '股票'

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def gen_secid(rawcode: str) -> str:
    """生成东方财富专用的secid"""
    if rawcode[0] == '6':
        return f'1.{rawcode}'  # 沪市
    return f'0.{rawcode}'      # 深市

def get_yearly_low_price_original(code: str) -> dict:
    """获取股票一年内的最低价（前复权）- 原始实现"""
    
    # 计算一年前的日期
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365)
    
    start_date_str = start_date.strftime('%Y%m%d')
    end_date_str = end_date.strftime('%Y%m%d')
    
    secid = gen_secid(code)
    
    params = {
        'fields1': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13',
        'fields2': 'f51,f52,f53,f54,f55',
        'beg': start_date_str,
        'end': end_date_str,
        'rtntype': '6',
        'secid': secid,
        'klt': '101',  # 日K
        'fqt': '1',    # 前复权
    }
    
    base_url = 'https://push2his.eastmoney.com/api/qt/stock/kline/get'
    url = base_url + '?' + urlencode(params)
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; WOW64; Trident/7.0; Touch; rv:11.0) like Gecko',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.8',
        'Referer': 'http://quote.eastmoney.com/',
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=CONFIG['request_timeout'])
        
        if response.status_code == 200:
            json_data = response.json()
            
            if 'data' in json_data and json_data['data'] and 'klines' in json_data['data']:
                klines = json_data['data']['klines']
                
                if klines:
                    # 找到最低价
                    min_low = float('inf')
                    low_date = None
                    
                    for kline in klines:
                        kline_data = kline.split(',')
                        if len(kline_data) >= 5:
                            low_price = float(kline_data[4])  # 最低价
                            if low_price < min_low:
                                min_low = low_price
                                low_date = kline_data[0]  # 日期
                    
                    return {
                        'yearly_low': min_low,
                        'low_date': low_date,
                        'data_points': len(klines)
                    }
        
        return {'yearly_low': None, 'low_date': None, 'data_points': 0}
        
    except Exception as e:
        print(f"获取股票 {code} 历史数据失败: {e}")
        return {'yearly_low': None, 'low_date': None, 'data_points': 0}

def get_yearly_low_price(code: str) -> dict:
    """获取股票一年内的最低价（前复权）- 优先使用缓存"""
    
    # 首先尝试从缓存获取
    try:
        cache_result = get_yearly_low_from_cache(code)
        if cache_result['success']:
            return {
                'yearly_low': cache_result['yearly_low'],
                'low_date': cache_result['low_date'],
                'data_points': cache_result['data_points']
            }
    except Exception as e:
        print(f"从缓存获取 {code} 年内最低价失败: {e}")
    
    # 缓存获取失败，使用原始方法
    print(f"⚠️ 缓存获取失败，使用实时API获取 {code} 年内最低价...")
    return get_yearly_low_price_original(code)

def get_stock_info(code: str) -> dict:
    """获取股票基本信息"""
    
    secid = gen_secid(code)
    
    params = {
        'fields': 'f57,f58,f162,f163,f164,f165,f166,f167,f168,f169,f170,f46,f44,f51,f52,f50,f48,f47,f164,f116,f60,f45,f49,f171,f177,f78,f81,f84,f85,f183,f184,f185,f186,f187,f188,f189,f190,f191,f192,f107,f111,f86,f177,f78,f110,f262,f263,f264,f267,f268,f250,f251,f252,f253,f254,f255,f256,f257,f258,f266',
        'secid': secid
    }
    
    url = 'https://push2.eastmoney.com/api/qt/stock/get?' + urlencode(params)
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; WOW64; Trident/7.0; Touch; rv:11.0) like Gecko',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.8',
        'Referer': 'http://quote.eastmoney.com/',
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=CONFIG['request_timeout'])
        
        if response.status_code == 200:
            json_data = response.json()
            
            if 'data' in json_data and json_data['data']:
                data = json_data['data']
                
                # 获取年内最低价
                yearly_low_info = get_yearly_low_price(code)
                
                # 计算距离最低点的涨幅
                current_price = data.get('f43', 0) / 100 if data.get('f43') else 0
                yearly_low = yearly_low_info.get('yearly_low')
                distance_from_low_pct = None
                
                if yearly_low and yearly_low > 0 and current_price > 0:
                    distance_from_low_pct = ((current_price - yearly_low) / yearly_low) * 100
                
                return {
                    'code': code,
                    'name': data.get('f58', ''),
                    'price': current_price,
                    'change': data.get('f169', 0) / 100 if data.get('f169') else 0,
                    'change_pct': data.get('f170', 0) / 100 if data.get('f170') else 0,
                    'volume': data.get('f47', 0),
                    'turnover': data.get('f48', 0) / 100 if data.get('f48') else 0,
                    'pe_ratio': data.get('f162', 0) / 100 if data.get('f162') else 0,
                    'pb_ratio': data.get('f167', 0) / 100 if data.get('f167') else 0,
                    'dividend_yield': data.get('f187', 0) / 100 if data.get('f187') else 0,
                    'market_cap': data.get('f116', 0),
                    'industry': data.get('f127', ''),
                    'yearly_low': yearly_low,
                    'low_date': yearly_low_info.get('low_date'),
                    'distance_from_low_pct': distance_from_low_pct,
                    'security_type': get_security_type(code),
                    'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
        
        return None
        
    except Exception as e:
        print(f"获取股票 {code} 信息失败: {e}")
        return None

def load_stock_list():
    """加载股票列表"""
    global imported_stock_list

    # 优先使用导入的股票列表
    if imported_stock_list:
        return imported_stock_list

    # 否则尝试从CSV文件加载
    csv_file = 'stocks_list.csv'
    if os.path.exists(csv_file):
        try:
            df = pd.read_csv(csv_file, encoding='utf-8-sig')
            stock_list = []
            for _, row in df.iterrows():
                stock_list.append({
                    '代码': str(row['代码']).zfill(6),
                    '名称': row.get('名称', ''),
                    '数量': row.get('数量', 0)
                })
            return stock_list
        except Exception as e:
            print(f"加载股票列表失败: {e}")

    return []

def update_single_stock(stock_info):
    """更新单只股票数据"""
    global stock_data

    code = stock_info['代码']
    name = stock_info.get('名称', '')
    quantity = stock_info.get('数量', 0)

    print(f"正在处理 {code} {name}...")

    # 获取股票信息
    info = get_stock_info(code)

    if info:
        # 添加持仓数量信息
        info['quantity'] = quantity
        info['name'] = name if name else info.get('name', '')

        # 获取扫雷数据
        try:
            scan_result = scan_client.get_stock_score(code)
            if scan_result:
                info['scan_score'] = scan_result['score']
                info['scan_risk_level'] = scan_result['risk_level']
                info['scan_risk_color'] = scan_result['risk_color']
                info['scan_triggered_risks'] = scan_result['triggered_risks']
                info['scan_display_text'] = f"{scan_result['score']}分"
                print(f"🔍 扫雷: {scan_result['score']}分 ({scan_result['risk_level']})")
            else:
                # 添加默认扫雷数据
                info['scan_score'] = 0
                info['scan_risk_level'] = '数据异常'
                info['scan_risk_color'] = '#cccccc'
                info['scan_triggered_risks'] = 0
                info['scan_display_text'] = '获取失败'
        except Exception as e:
            print(f"获取扫雷数据失败: {e}")
            # 添加默认扫雷数据
            info['scan_score'] = 0
            info['scan_risk_level'] = '数据异常'
            info['scan_risk_color'] = '#cccccc'
            info['scan_triggered_risks'] = 0
            info['scan_display_text'] = '获取失败'

        # 存储到全局数据
        stock_data[code] = info

        print(f"✅ {code} {info['name']} 价格: {info['price']:.2f} 涨跌: {info['change_pct']:.2f}%")

        # 检查是否需要发送企业微信提醒
        if info.get('distance_from_low_pct') and info['distance_from_low_pct'] >= CONFIG['alert_threshold']:
            try:
                message = f"📈 高涨幅提醒\n股票: {info['name']} ({code})\n当前价格: {info['price']:.2f}元\n距最低点涨幅: {info['distance_from_low_pct']:.1f}%\n年内最低价: {info['yearly_low']:.2f}元 ({info['low_date']})"
                wechat_alert.send_message(message)
                print(f"📱 已发送企业微信提醒: {info['name']} 涨幅 {info['distance_from_low_pct']:.1f}%")
            except Exception as e:
                print(f"发送企业微信提醒失败: {e}")
    else:
        print(f"❌ {code} {name} 获取数据失败")

def update_all_stocks():
    """更新所有股票数据"""
    global last_update_time

    stock_list = load_stock_list()
    if not stock_list:
        print("❌ 没有找到股票列表")
        return

    print(f"🚀 开始自动获取 {len(stock_list)} 只股票的数据...")

    start_time = time.time()

    for i, stock_info in enumerate(stock_list):
        print(f"[{i+1:3d}/{len(stock_list)}] ", end="")
        update_single_stock(stock_info)

        # 股票间延时
        if i < len(stock_list) - 1:
            time.sleep(CONFIG['stock_interval'])

    end_time = time.time()
    duration = end_time - start_time
    last_update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    print(f"\n✅ 数据更新完成！")
    print(f"📊 成功获取: {len(stock_data)}/{len(stock_list)} 只股票")
    print(f"⏱️ 耗时: {duration:.1f} 秒")
    print(f"🕐 更新时间: {last_update_time}")

def continuous_update():
    """持续更新数据"""
    while True:
        try:
            # 检查是否在交易时间
            if trading_monitor.is_trading_time():
                print(f"\n📈 交易时间内，开始更新数据...")
                update_all_stocks()
            else:
                print(f"\n💤 非交易时间，跳过更新")
                print(f"📅 下次交易时间: {trading_monitor.get_next_trading_time()}")

        except Exception as e:
            print(f"❌ 更新过程中发生错误: {e}")

        # 计算下次更新时间
        update_interval = CONFIG['round_interval']
        next_update = datetime.now() + timedelta(seconds=update_interval)
        print(f"\n⏰ 下次更新时间: {next_update.strftime('%H:%M:%S')}")
        if update_interval >= 60:
            print(f"   (约 {update_interval/60:.1f} 分钟后)")

        time.sleep(update_interval)

# ==================== API 路由 ====================

@app.route('/')
def index():
    """主页 - 使用内嵌HTML模板"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/stocks')
def get_stocks():
    """获取所有股票数据API"""
    return jsonify({
        'stocks': list(stock_data.values()),
        'last_update': last_update_time,
        'total_count': len(stock_data),
        'config': CONFIG
    })

@app.route('/api/stock/<stock_code>')
def get_stock(stock_code):
    """获取单只股票数据API"""
    if stock_code in stock_data:
        return jsonify(stock_data[stock_code])
    else:
        return jsonify({'error': '股票代码不存在'}), 404

@app.route('/api/trading-status')
def get_trading_status():
    """获取A股交易状态API"""
    try:
        status = trading_monitor.get_trading_status()
        return jsonify({
            'success': True,
            'trading_status': status,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500

@app.route('/api/refresh-holidays')
def refresh_holidays():
    """刷新节假日数据API"""
    try:
        success = trading_monitor.refresh_current_year_holidays()
        if success:
            return jsonify({
                'success': True,
                'message': '节假日数据刷新成功',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
        else:
            return jsonify({
                'success': False,
                'message': '节假日数据刷新失败',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500

@app.route('/api/refresh')
def refresh_data():
    """手动刷新数据"""
    try:
        threading.Thread(target=update_all_stocks, daemon=True).start()
        return jsonify({
            'success': True,
            'message': '数据刷新已启动'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'刷新失败: {str(e)}'
        }), 500

@app.route('/api/scan_score/<stock_code>')
def get_scan_score_api(stock_code):
    """获取单只股票的扫雷分数API"""
    try:
        result = scan_client.get_stock_score(stock_code)
        if result:
            return jsonify(result)
        else:
            return jsonify({'error': f'无法获取 {stock_code} 的扫雷数据'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/scan_batch', methods=['POST'])
def get_scan_batch_api():
    """批量获取扫雷分数API"""
    try:
        data = request.get_json()
        stock_codes = data.get('codes', [])

        if not stock_codes:
            return jsonify({'error': '股票代码列表不能为空'}), 400

        results = scan_client.get_batch_scores(stock_codes)
        return jsonify(results)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/wechat/test')
def test_wechat():
    """测试企业微信连接"""
    try:
        success = wechat_alert.test_connection()
        return jsonify({
            'success': success,
            'message': '企业微信连接正常' if success else '企业微信连接失败'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'测试失败: {str(e)}'
        }), 500

@app.route('/api/cache/status')
def get_cache_status_api():
    """获取年内最低价缓存状态"""
    try:
        status = check_cache_status()
        return jsonify({
            'success': True,
            'cache_status': status,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '获取缓存状态失败',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500

@app.route('/api/cache/check')
def check_cache_availability():
    """检查缓存是否可用"""
    try:
        available = is_cache_available()
        status = check_cache_status()

        return jsonify({
            'success': True,
            'cache_available': available,
            'cache_status': status,
            'message': '缓存可用' if available else '缓存不可用，建议运行 yearly_low_cache_system.py 生成缓存',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '检查缓存失败',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500

@app.route('/api/clear-data', methods=['POST'])
def clear_data():
    """清空数据API"""
    global stock_data, last_update_time

    try:
        stock_count = len(stock_data)
        stock_data.clear()
        last_update_time = None

        return jsonify({
            'success': True,
            'message': f'已清空 {stock_count} 只股票的数据',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500

@app.route('/api/upload-holdings', methods=['POST'])
def upload_holdings():
    """上传持仓表格API"""
    global imported_stock_list

    try:
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'message': '没有选择文件'
            }), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'success': False,
                'message': '没有选择文件'
            }), 400

        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{timestamp}_{filename}"
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)

            # 读取Excel文件
            try:
                df = pd.read_excel(filepath)

                # 检查必要的列
                required_columns = ['代码', '名称']
                missing_columns = [col for col in required_columns if col not in df.columns]

                if missing_columns:
                    return jsonify({
                        'success': False,
                        'message': f'缺少必要的列: {", ".join(missing_columns)}'
                    }), 400

                # 处理数据
                stock_list = []
                for index, row in df.iterrows():
                    try:
                        code = str(row['代码']).zfill(6)
                        name = str(row['名称'])
                        quantity = float(row.get('数量', 0)) if pd.notna(row.get('数量')) else 0

                        stock_list.append({
                            '代码': code,
                            '名称': name,
                            '数量': quantity
                        })
                    except Exception as e:
                        print(f"处理第 {index+1} 行数据时出错: {e}")
                        continue

                if not stock_list:
                    return jsonify({
                        'success': False,
                        'message': '没有找到有效的股票数据'
                    }), 400

                # 更新全局股票列表
                imported_stock_list = stock_list

                # 保存为CSV文件
                csv_filename = f'stocks_list_{timestamp}.csv'
                df_output = pd.DataFrame(stock_list)
                df_output.to_csv(csv_filename, index=False, encoding='utf-8-sig')

                return jsonify({
                    'success': True,
                    'message': f'成功导入 {len(stock_list)} 只股票',
                    'stock_count': len(stock_list),
                    'stocks': stock_list[:10],  # 只返回前10只股票作为预览
                    'csv_file': csv_filename,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })

            except Exception as e:
                return jsonify({
                    'success': False,
                    'message': f'读取Excel文件失败: {str(e)}'
                }), 500

        else:
            return jsonify({
                'success': False,
                'message': '不支持的文件格式，请上传 .xlsx 或 .xls 文件'
            }), 400

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500

@app.route('/api/refresh-data', methods=['POST'])
def refresh_all_data():
    """重新获取数据API"""
    global stock_data, last_update_time, imported_stock_list

    try:
        # 清空现有数据
        stock_data.clear()
        last_update_time = None

        # 获取股票列表
        stock_list = load_stock_list()

        if not stock_list:
            return jsonify({
                'success': False,
                'message': '没有找到股票列表，请先上传持仓表格'
            }), 400

        # 启动后台更新任务
        def background_update():
            try:
                update_all_stocks()
            except Exception as e:
                print(f"后台更新失败: {e}")

        threading.Thread(target=background_update, daemon=True).start()

        return jsonify({
            'success': True,
            'message': f'开始获取 {len(stock_list)} 只股票的数据，请稍候...',
            'stock_count': len(stock_list),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500

@app.route('/api/wechat/summary')
def send_wechat_summary():
    """发送每日摘要到企业微信"""
    try:
        valid_stocks = [stock for stock in stock_data.values() if stock.get('distance_from_low_pct') is not None]
        if valid_stocks:
            # 按距最低点涨幅排序
            sorted_stocks = sorted(valid_stocks, key=lambda x: x['distance_from_low_pct'], reverse=True)
            top_5 = sorted_stocks[:5]

            message = f"📊 每日持仓摘要 ({len(valid_stocks)}只)\n\n🔝 距最低点涨幅前5:\n"
            for i, stock in enumerate(top_5, 1):
                message += f"{i}. {stock['name']} {stock['distance_from_low_pct']:.1f}%\n"

            wechat_alert.send_message(message)
            return jsonify({'success': True, 'message': '每日摘要已发送'})
        else:
            return jsonify({'success': False, 'message': '没有有效数据'})
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'发送失败: {str(e)}'
        }), 500

@app.route('/api/wechat/alert/<float:threshold>')
def manual_alert_check(threshold):
    """手动检查高涨幅股票并发送提醒"""
    try:
        valid_stocks = [stock for stock in stock_data.values() if stock.get('distance_from_low_pct') is not None]
        if valid_stocks:
            high_stocks = [stock for stock in valid_stocks if stock['distance_from_low_pct'] >= threshold]

            if high_stocks:
                message = f"📈 高涨幅股票提醒 (>{threshold}%)\n\n"
                for stock in sorted(high_stocks, key=lambda x: x['distance_from_low_pct'], reverse=True):
                    message += f"• {stock['name']} {stock['distance_from_low_pct']:.1f}%\n"

                wechat_alert.send_message(message)
                return jsonify({
                    'success': True,
                    'message': f'已发送 {len(high_stocks)} 只高涨幅股票提醒',
                    'count': len(high_stocks)
                })
            else:
                return jsonify({
                    'success': True,
                    'message': f'没有涨幅超过 {threshold}% 的股票',
                    'count': 0
                })
        else:
            return jsonify({'success': False, 'message': '没有有效数据'})
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'检查失败: {str(e)}'
        }), 500

# ==================== HTML 模板 ====================

HTML_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>持仓股票综合数据实时监控 V10 表格增强版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
        }

        .header .subtitle {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .version-badge {
            background: rgba(255,255,255,0.2);
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
            margin-top: 10px;
            display: inline-block;
        }

        .update-info {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 5px;
            margin-top: 15px;
        }

        .controls {
            background: white;
            padding: 20px;
            margin: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            align-items: center;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
        }

        .table-controls {
            background: white;
            padding: 15px 20px;
            margin: 0 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .table-controls .left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .table-controls .right {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .search-box {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            width: 200px;
        }

        .table-container {
            background: white;
            margin: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-wrapper {
            overflow-x: auto;
            max-height: 70vh;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
            cursor: pointer;
            user-select: none;
            transition: background-color 0.3s ease;
            white-space: nowrap;
        }

        th:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }

        th.sortable {
            position: relative;
        }

        th.sortable::after {
            content: '';
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            opacity: 0.5;
        }

        th.sort-asc::after {
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-bottom: 6px solid white;
            opacity: 1;
        }

        th.sort-desc::after {
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 6px solid white;
            opacity: 1;
        }

        th.dragging {
            opacity: 0.5;
            background: #999;
        }

        th.drag-over {
            border-left: 3px solid #ff6b6b;
        }

        td {
            padding: 10px 8px;
            text-align: center;
            border-bottom: 1px solid #eee;
            white-space: nowrap;
        }

        tr:hover {
            background-color: #f8f9ff;
        }

        .positive {
            color: #e74c3c;
            font-weight: bold;
        }

        .negative {
            color: #27ae60;
            font-weight: bold;
        }

        .neutral {
            color: #7f8c8d;
        }

        .high-gain {
            background-color: #ffe6e6;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .status-trading {
            background-color: #27ae60;
        }

        .status-closed {
            background-color: #e74c3c;
        }

        .status-unknown {
            background-color: #f39c12;
        }

        /* 列设置弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 10px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .modal-header h3 {
            margin: 0;
            color: #333;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            color: #000;
        }

        .column-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            cursor: move;
        }

        .column-item:last-child {
            border-bottom: none;
        }

        .column-item.dragging {
            opacity: 0.5;
        }

        .column-item input[type="checkbox"] {
            margin-right: 10px;
        }

        .column-item .drag-handle {
            margin-right: 10px;
            color: #999;
            cursor: move;
        }

        .column-item .column-name {
            flex: 1;
        }

        .modal-footer {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            gap: 10px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 1.8em;
            }

            .controls {
                margin: 10px;
                padding: 15px;
            }

            .table-controls {
                margin: 0 10px;
                flex-direction: column;
                align-items: stretch;
            }

            .table-controls .left,
            .table-controls .right {
                justify-content: center;
            }

            .search-box {
                width: 100%;
            }

            .table-container {
                margin: 10px;
            }

            table {
                font-size: 12px;
            }

            th, td {
                padding: 8px 4px;
            }

            .modal-content {
                margin: 10% auto;
                width: 95%;
            }
        }

        @media (max-width: 480px) {
            .header {
                padding: 15px;
            }

            .header h1 {
                font-size: 1.5em;
            }

            .btn {
                padding: 8px 15px;
                font-size: 12px;
            }

            table {
                font-size: 11px;
            }

            th, td {
                padding: 6px 3px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>持仓股票综合数据实时监控</h1>
        <div class="subtitle">实时跟踪股票价格、涨跌幅、年内最低价等关键指标</div>
        <div class="version-badge">V10 表格增强版 - 支持排序和列管理</div>
        <div class="update-info">
            <div>📊 股票数量: <span id="stockCount">0</span> |
                 🕐 最后更新: <span id="lastUpdate">未更新</span> |
                 <span class="status-indicator" id="tradingStatus"></span>
                 <span id="tradingStatusText">检查中...</span>
            </div>
        </div>
    </div>

    <div class="controls">
        <button class="btn" onclick="refreshData()">🔄 刷新数据</button>
        <button class="btn btn-secondary" onclick="clearData()">🗑️ 清空数据</button>
        <button class="btn btn-success" onclick="sendSummary()">📱 发送摘要</button>
        <button class="btn btn-warning" onclick="testWechat()">🧪 测试微信</button>

        <input type="file" id="fileInput" accept=".xlsx,.xls" style="display: none;" onchange="uploadFile()">
        <button class="btn" onclick="document.getElementById('fileInput').click()">📁 上传持仓表格</button>

        <div style="margin-left: auto;">
            <span>提醒阈值: </span>
            <input type="number" id="alertThreshold" value="70" min="0" max="1000" style="width: 60px; padding: 5px;">
            <span>%</span>
            <button class="btn" onclick="checkAlert()">📢 检查提醒</button>
        </div>
    </div>

    <div class="table-controls">
        <div class="left">
            <input type="text" class="search-box" id="searchBox" placeholder="搜索股票代码或名称..." onkeyup="filterTable()">
            <span id="filterInfo">显示 0 / 0 只股票</span>
        </div>
        <div class="right">
            <button class="btn" onclick="openColumnSettings()">⚙️ 列设置</button>
            <button class="btn" onclick="resetTable()">🔄 重置表格</button>
            <button class="btn" onclick="exportData()">📊 导出数据</button>
        </div>
    </div>

    <div class="table-container">
        <div class="table-wrapper">
            <table id="stockTable">
                <thead>
                    <tr id="tableHeader">
                        <th data-column="code" class="sortable" draggable="true">股票代码</th>
                        <th data-column="name" class="sortable" draggable="true">股票名称</th>
                        <th data-column="price" class="sortable" draggable="true">最新价格</th>
                        <th data-column="change" class="sortable" draggable="true">涨跌额</th>
                        <th data-column="change_pct" class="sortable" draggable="true">涨跌幅%</th>
                        <th data-column="yearly_low" class="sortable" draggable="true">年内最低价</th>
                        <th data-column="low_date" class="sortable" draggable="true">最低价日期</th>
                        <th data-column="distance_from_low_pct" class="sortable" draggable="true">距最低点涨幅%</th>
                        <th data-column="pb_ratio" class="sortable" draggable="true">市净率</th>
                        <th data-column="pe_ratio" class="sortable" draggable="true">市盈率</th>
                        <th data-column="dividend_yield" class="sortable" draggable="true">股息率%</th>
                        <th data-column="industry" class="sortable" draggable="true">行业</th>
                        <th data-column="scan_score" class="sortable" draggable="true">扫雷</th>
                        <th data-column="quantity" class="sortable" draggable="true">持仓数量</th>
                        <th data-column="security_type" class="sortable" draggable="true">类型</th>
                        <th data-column="update_time" class="sortable" draggable="true">更新时间</th>
                    </tr>
                </thead>
                <tbody id="stockTableBody">
                    <tr>
                        <td colspan="16" class="loading">正在加载数据...</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 列设置弹窗 -->
    <div id="columnModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>列设置</h3>
                <span class="close" onclick="closeColumnSettings()">&times;</span>
            </div>
            <div id="columnList">
                <!-- 动态生成列设置项 -->
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="resetColumnSettings()">恢复默认</button>
                <button class="btn btn-success" onclick="applyColumnSettings()">应用设置</button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script>
        // 全局变量
        let stockData = [];
        let filteredData = [];
        let currentSort = { column: null, direction: null };
        let columnSettings = {
            order: ['code', 'name', 'price', 'change', 'change_pct', 'yearly_low', 'low_date', 'distance_from_low_pct', 'pb_ratio', 'pe_ratio', 'dividend_yield', 'industry', 'scan_score', 'quantity', 'security_type', 'update_time'],
            hidden: []
        };

        // 列定义
        const columnDefinitions = {
            code: { name: '股票代码', sortable: true },
            name: { name: '股票名称', sortable: true },
            price: { name: '最新价格', sortable: true, type: 'number' },
            change: { name: '涨跌额', sortable: true, type: 'number' },
            change_pct: { name: '涨跌幅%', sortable: true, type: 'number' },
            yearly_low: { name: '年内最低价', sortable: true, type: 'number' },
            low_date: { name: '最低价日期', sortable: true },
            distance_from_low_pct: { name: '距最低点涨幅%', sortable: true, type: 'number' },
            pb_ratio: { name: '市净率', sortable: true, type: 'number' },
            pe_ratio: { name: '市盈率', sortable: true, type: 'number' },
            dividend_yield: { name: '股息率%', sortable: true, type: 'number' },
            industry: { name: '行业', sortable: true },
            scan_score: { name: '扫雷', sortable: true, type: 'number' },
            quantity: { name: '持仓数量', sortable: true, type: 'number' },
            security_type: { name: '类型', sortable: true },
            update_time: { name: '更新时间', sortable: true }
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadColumnSettings();
            initializeDragAndDrop();
            loadData();
            checkTradingStatus();

            // 定时刷新数据
            setInterval(loadData, 30000); // 30秒刷新一次
            setInterval(checkTradingStatus, 60000); // 1分钟检查一次交易状态
        });

        // 加载列设置
        function loadColumnSettings() {
            const saved = localStorage.getItem('stockTableColumnSettings');
            if (saved) {
                try {
                    columnSettings = JSON.parse(saved);
                } catch (e) {
                    console.error('加载列设置失败:', e);
                }
            }
            applyColumnOrder();
        }

        // 保存列设置
        function saveColumnSettings() {
            localStorage.setItem('stockTableColumnSettings', JSON.stringify(columnSettings));
        }

        // 应用列顺序
        function applyColumnOrder() {
            const header = document.getElementById('tableHeader');
            const newHeader = document.createElement('tr');
            newHeader.id = 'tableHeader';

            // 按设置的顺序重新排列列
            columnSettings.order.forEach(columnKey => {
                if (!columnSettings.hidden.includes(columnKey)) {
                    const th = document.createElement('th');
                    th.setAttribute('data-column', columnKey);
                    th.className = 'sortable';
                    th.draggable = true;
                    th.textContent = columnDefinitions[columnKey].name;
                    th.onclick = () => sortTable(columnKey);
                    newHeader.appendChild(th);
                }
            });

            header.parentNode.replaceChild(newHeader, header);
            initializeDragAndDrop();
        }

        // 初始化拖拽功能
        function initializeDragAndDrop() {
            const header = document.getElementById('tableHeader');

            new Sortable(header, {
                animation: 150,
                ghostClass: 'dragging',
                chosenClass: 'dragging',
                dragClass: 'dragging',
                onEnd: function(evt) {
                    // 更新列顺序
                    const newOrder = [];
                    const headers = header.querySelectorAll('th[data-column]');
                    headers.forEach(th => {
                        newOrder.push(th.getAttribute('data-column'));
                    });

                    // 添加隐藏的列到末尾
                    columnSettings.hidden.forEach(col => {
                        if (!newOrder.includes(col)) {
                            newOrder.push(col);
                        }
                    });

                    columnSettings.order = newOrder;
                    saveColumnSettings();
                    renderTable();
                }
            });
        }

        // 加载数据
        async function loadData() {
            try {
                const response = await fetch('/api/stocks');
                const data = await response.json();

                stockData = data.stocks || [];
                filteredData = [...stockData];

                document.getElementById('stockCount').textContent = stockData.length;
                document.getElementById('lastUpdate').textContent = data.last_update || '未更新';

                renderTable();
                updateFilterInfo();
            } catch (error) {
                console.error('加载数据失败:', error);
                showMessage('加载数据失败: ' + error.message, 'error');
            }
        }

        // 渲染表格
        function renderTable() {
            const tbody = document.getElementById('stockTableBody');

            if (filteredData.length === 0) {
                tbody.innerHTML = '<tr><td colspan="16" class="loading">暂无数据</td></tr>';
                return;
            }

            let html = '';
            filteredData.forEach(stock => {
                const row = createTableRow(stock);
                html += row;
            });

            tbody.innerHTML = html;
        }

        // 创建表格行
        function createTableRow(stock) {
            const changeClass = stock.change_pct > 0 ? 'positive' : stock.change_pct < 0 ? 'negative' : 'neutral';
            const highGainClass = stock.distance_from_low_pct && stock.distance_from_low_pct >= 70 ? 'high-gain' : '';

            let html = `<tr class="${highGainClass}">`;

            // 按列设置的顺序渲染列
            columnSettings.order.forEach(columnKey => {
                if (!columnSettings.hidden.includes(columnKey)) {
                    html += createTableCell(stock, columnKey, changeClass);
                }
            });

            html += '</tr>';
            return html;
        }

        // 创建表格单元格
        function createTableCell(stock, columnKey, changeClass) {
            let value = stock[columnKey];
            let cellClass = '';

            switch (columnKey) {
                case 'code':
                    return `<td><strong>${value || ''}</strong></td>`;
                case 'name':
                    return `<td><strong>${value || ''}</strong></td>`;
                case 'price':
                    return `<td class="${changeClass}">¥${formatNumber(value, 2)}</td>`;
                case 'change':
                    return `<td class="${changeClass}">${formatNumber(value, 2)}</td>`;
                case 'change_pct':
                    return `<td class="${changeClass}"><strong>${formatNumber(value, 2)}%</strong></td>`;
                case 'yearly_low':
                    return `<td>¥${formatNumber(value, 2)}</td>`;
                case 'low_date':
                    return `<td>${value || ''}</td>`;
                case 'distance_from_low_pct':
                    const distanceClass = value >= 70 ? 'positive' : value >= 30 ? 'neutral' : 'negative';
                    return `<td class="${distanceClass}"><strong>${formatNumber(value, 1)}%</strong></td>`;
                case 'pb_ratio':
                    return `<td>${formatNumber(value, 2)}</td>`;
                case 'pe_ratio':
                    return `<td>${formatNumber(value, 2)}</td>`;
                case 'dividend_yield':
                    return `<td>${formatNumber(value, 2)}%</td>`;
                case 'industry':
                    return `<td>${value || ''}</td>`;
                case 'scan_score':
                    const scanColor = stock.scan_risk_color || '#cccccc';
                    const scanText = stock.scan_display_text || (value ? `${value}分` : '未知');
                    return `<td><span style="color: ${scanColor}; font-weight: bold;">${scanText}</span></td>`;
                case 'quantity':
                    return `<td>${formatNumber(value, 0)}</td>`;
                case 'security_type':
                    return `<td>${value || ''}</td>`;
                case 'update_time':
                    return `<td><small>${value || ''}</small></td>`;
                default:
                    return `<td>${value || ''}</td>`;
            }
        }

        // 格式化数字
        function formatNumber(num, decimals = 2) {
            if (num === null || num === undefined || isNaN(num)) {
                return '--';
            }
            return Number(num).toFixed(decimals);
        }

        // 排序表格
        function sortTable(column) {
            // 确定排序方向
            if (currentSort.column === column) {
                if (currentSort.direction === 'asc') {
                    currentSort.direction = 'desc';
                } else if (currentSort.direction === 'desc') {
                    currentSort.direction = null;
                    currentSort.column = null;
                } else {
                    currentSort.direction = 'asc';
                }
            } else {
                currentSort.column = column;
                currentSort.direction = 'desc'; // 默认降序
            }

            // 更新表头样式
            updateSortHeaders();

            // 执行排序
            if (currentSort.column && currentSort.direction) {
                filteredData.sort((a, b) => {
                    let aVal = a[column];
                    let bVal = b[column];

                    // 处理空值
                    if (aVal === null || aVal === undefined) aVal = '';
                    if (bVal === null || bVal === undefined) bVal = '';

                    // 数字类型排序
                    if (columnDefinitions[column].type === 'number') {
                        aVal = parseFloat(aVal) || 0;
                        bVal = parseFloat(bVal) || 0;
                        return currentSort.direction === 'asc' ? aVal - bVal : bVal - aVal;
                    }

                    // 字符串排序
                    aVal = String(aVal).toLowerCase();
                    bVal = String(bVal).toLowerCase();

                    if (currentSort.direction === 'asc') {
                        return aVal.localeCompare(bVal);
                    } else {
                        return bVal.localeCompare(aVal);
                    }
                });
            } else {
                // 恢复原始顺序
                filteredData = stockData.filter(stock => {
                    const searchTerm = document.getElementById('searchBox').value.toLowerCase();
                    return searchTerm === '' ||
                           stock.code.toLowerCase().includes(searchTerm) ||
                           stock.name.toLowerCase().includes(searchTerm);
                });
            }

            renderTable();
        }

        // 更新排序表头样式
        function updateSortHeaders() {
            const headers = document.querySelectorAll('th.sortable');
            headers.forEach(th => {
                th.classList.remove('sort-asc', 'sort-desc');
                if (th.getAttribute('data-column') === currentSort.column) {
                    if (currentSort.direction === 'asc') {
                        th.classList.add('sort-asc');
                    } else if (currentSort.direction === 'desc') {
                        th.classList.add('sort-desc');
                    }
                }
            });
        }

        // 过滤表格
        function filterTable() {
            const searchTerm = document.getElementById('searchBox').value.toLowerCase();

            filteredData = stockData.filter(stock => {
                return searchTerm === '' ||
                       stock.code.toLowerCase().includes(searchTerm) ||
                       stock.name.toLowerCase().includes(searchTerm);
            });

            // 重新应用排序
            if (currentSort.column && currentSort.direction) {
                sortTable(currentSort.column);
            } else {
                renderTable();
            }

            updateFilterInfo();
        }

        // 更新过滤信息
        function updateFilterInfo() {
            const info = document.getElementById('filterInfo');
            info.textContent = `显示 ${filteredData.length} / ${stockData.length} 只股票`;
        }

        // 打开列设置弹窗
        function openColumnSettings() {
            const modal = document.getElementById('columnModal');
            const columnList = document.getElementById('columnList');

            let html = '';
            columnSettings.order.forEach(columnKey => {
                const isHidden = columnSettings.hidden.includes(columnKey);
                const columnDef = columnDefinitions[columnKey];

                html += `
                    <div class="column-item" data-column="${columnKey}">
                        <span class="drag-handle">⋮⋮</span>
                        <input type="checkbox" id="col_${columnKey}" ${!isHidden ? 'checked' : ''}>
                        <label for="col_${columnKey}" class="column-name">${columnDef.name}</label>
                    </div>
                `;
            });

            columnList.innerHTML = html;

            // 初始化拖拽排序
            new Sortable(columnList, {
                handle: '.drag-handle',
                animation: 150,
                ghostClass: 'dragging'
            });

            modal.style.display = 'block';
        }

        // 关闭列设置弹窗
        function closeColumnSettings() {
            document.getElementById('columnModal').style.display = 'none';
        }

        // 应用列设置
        function applyColumnSettings() {
            const columnList = document.getElementById('columnList');
            const items = columnList.querySelectorAll('.column-item');

            const newOrder = [];
            const newHidden = [];

            items.forEach(item => {
                const columnKey = item.getAttribute('data-column');
                const checkbox = item.querySelector('input[type="checkbox"]');

                newOrder.push(columnKey);
                if (!checkbox.checked) {
                    newHidden.push(columnKey);
                }
            });

            columnSettings.order = newOrder;
            columnSettings.hidden = newHidden;

            saveColumnSettings();
            applyColumnOrder();
            renderTable();
            closeColumnSettings();

            showMessage('列设置已应用', 'success');
        }

        // 重置列设置
        function resetColumnSettings() {
            columnSettings = {
                order: ['code', 'name', 'price', 'change', 'change_pct', 'yearly_low', 'low_date', 'distance_from_low_pct', 'pb_ratio', 'pe_ratio', 'dividend_yield', 'industry', 'scan_score', 'quantity', 'security_type', 'update_time'],
                hidden: []
            };

            saveColumnSettings();
            applyColumnOrder();
            renderTable();
            closeColumnSettings();

            showMessage('列设置已重置为默认', 'success');
        }

        // 重置表格
        function resetTable() {
            // 清除搜索
            document.getElementById('searchBox').value = '';

            // 清除排序
            currentSort = { column: null, direction: null };
            updateSortHeaders();

            // 重新加载数据
            filteredData = [...stockData];
            renderTable();
            updateFilterInfo();

            showMessage('表格已重置', 'success');
        }

        // 导出数据
        function exportData() {
            if (filteredData.length === 0) {
                showMessage('没有数据可导出', 'warning');
                return;
            }

            // 准备导出数据
            const exportData = filteredData.map(stock => {
                const row = {};
                columnSettings.order.forEach(columnKey => {
                    if (!columnSettings.hidden.includes(columnKey)) {
                        row[columnDefinitions[columnKey].name] = stock[columnKey] || '';
                    }
                });
                return row;
            });

            // 转换为CSV
            const headers = Object.keys(exportData[0]);
            const csvContent = [
                headers.join(','),
                ...exportData.map(row => headers.map(header => `"${row[header]}"`).join(','))
            ].join('\\n');

            // 下载文件
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `股票数据_${new Date().toISOString().slice(0, 10)}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showMessage('数据导出成功', 'success');
        }

        // 检查交易状态
        async function checkTradingStatus() {
            try {
                const response = await fetch('/api/trading-status');
                const data = await response.json();

                const indicator = document.getElementById('tradingStatus');
                const text = document.getElementById('tradingStatusText');

                if (data.success && data.trading_status) {
                    const status = data.trading_status;
                    if (status.is_trading_time) {
                        indicator.className = 'status-indicator status-trading';
                        text.textContent = '交易中';
                    } else {
                        indicator.className = 'status-indicator status-closed';
                        text.textContent = '休市';
                    }
                } else {
                    indicator.className = 'status-indicator status-unknown';
                    text.textContent = '状态未知';
                }
            } catch (error) {
                console.error('检查交易状态失败:', error);
                document.getElementById('tradingStatus').className = 'status-indicator status-unknown';
                document.getElementById('tradingStatusText').textContent = '检查失败';
            }
        }

        // 刷新数据
        async function refreshData() {
            const btn = event.target;
            btn.disabled = true;
            btn.textContent = '🔄 刷新中...';

            try {
                const response = await fetch('/api/refresh');
                const data = await response.json();

                if (data.success) {
                    showMessage('数据刷新已启动，请稍候...', 'success');
                    // 延迟重新加载数据
                    setTimeout(loadData, 3000);
                } else {
                    showMessage('刷新失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('刷新失败: ' + error.message, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '🔄 刷新数据';
            }
        }

        // 清空数据
        async function clearData() {
            if (!confirm('确定要清空所有数据吗？')) {
                return;
            }

            try {
                const response = await fetch('/api/clear-data', { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    showMessage(data.message, 'success');
                    stockData = [];
                    filteredData = [];
                    renderTable();
                    updateFilterInfo();
                    document.getElementById('stockCount').textContent = '0';
                    document.getElementById('lastUpdate').textContent = '未更新';
                } else {
                    showMessage('清空失败: ' + data.error, 'error');
                }
            } catch (error) {
                showMessage('清空失败: ' + error.message, 'error');
            }
        }

        // 上传文件
        async function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];

            if (!file) {
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            try {
                showMessage('正在上传文件...', 'info');

                const response = await fetch('/api/upload-holdings', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    showMessage(`${data.message}，开始获取数据...`, 'success');

                    // 自动刷新数据
                    setTimeout(async () => {
                        const refreshResponse = await fetch('/api/refresh-data', { method: 'POST' });
                        const refreshData = await refreshResponse.json();

                        if (refreshData.success) {
                            showMessage('数据获取已启动，请稍候...', 'success');
                            setTimeout(loadData, 5000);
                        }
                    }, 1000);

                } else {
                    showMessage('上传失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('上传失败: ' + error.message, 'error');
            } finally {
                fileInput.value = '';
            }
        }

        // 发送摘要
        async function sendSummary() {
            try {
                const response = await fetch('/api/wechat/summary');
                const data = await response.json();

                if (data.success) {
                    showMessage('每日摘要已发送到企业微信', 'success');
                } else {
                    showMessage('发送失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('发送失败: ' + error.message, 'error');
            }
        }

        // 测试微信
        async function testWechat() {
            try {
                const response = await fetch('/api/wechat/test');
                const data = await response.json();

                if (data.success) {
                    showMessage('企业微信连接正常', 'success');
                } else {
                    showMessage('企业微信连接失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('测试失败: ' + error.message, 'error');
            }
        }

        // 检查提醒
        async function checkAlert() {
            const threshold = document.getElementById('alertThreshold').value;

            if (!threshold || threshold < 0) {
                showMessage('请输入有效的阈值', 'warning');
                return;
            }

            try {
                const response = await fetch(`/api/wechat/alert/${threshold}`);
                const data = await response.json();

                if (data.success) {
                    showMessage(data.message, 'success');
                } else {
                    showMessage('检查失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('检查失败: ' + error.message, 'error');
            }
        }

        // 显示消息
        function showMessage(message, type = 'info') {
            // 创建消息元素
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 5px;
                color: white;
                font-weight: bold;
                z-index: 10000;
                max-width: 300px;
                word-wrap: break-word;
                animation: slideIn 0.3s ease-out;
            `;

            // 设置颜色
            switch (type) {
                case 'success':
                    messageDiv.style.backgroundColor = '#27ae60';
                    break;
                case 'error':
                    messageDiv.style.backgroundColor = '#e74c3c';
                    break;
                case 'warning':
                    messageDiv.style.backgroundColor = '#f39c12';
                    break;
                default:
                    messageDiv.style.backgroundColor = '#3498db';
            }

            messageDiv.textContent = message;
            document.body.appendChild(messageDiv);

            // 3秒后自动移除
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.style.animation = 'slideOut 0.3s ease-in';
                    setTimeout(() => {
                        if (messageDiv.parentNode) {
                            document.body.removeChild(messageDiv);
                        }
                    }, 300);
                }
            }, 3000);
        }

        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('columnModal');
            if (event.target === modal) {
                closeColumnSettings();
            }
        }
    </script>
</body>
</html>
'''

# ==================== 主程序启动 ====================

if __name__ == '__main__':
    print("=== 📊 持仓系统 V10 表格增强版 ===")
    print("🚀 新功能:")
    print("   • 涨幅排序功能 - 支持多列排序")
    print("   • 列顺序调整 - 拖拽重排和设置面板")
    print("   • 用户偏好保存 - localStorage持久化")
    print("   • 响应式设计 - 移动端和桌面端适配")
    print("   • 年内最低价缓存系统集成")
    print()

    # 检查缓存状态
    try:
        cache_status = check_cache_status()
        if cache_status['exists'] and cache_status['valid']:
            print("✅ 年内最低价缓存可用")
            if 'success_rate' in cache_status:
                print(f"   📊 缓存统计: {cache_status['success_count']}/{cache_status['total_count']} ({cache_status['success_rate']:.1f}%)")
        else:
            print("⚠️ 年内最低价缓存不可用")
            print("   建议运行: python yearly_low_cache_system.py")
    except Exception as e:
        print(f"⚠️ 缓存检查失败: {e}")

    print()
    print("🌐 启动Web服务器...")
    print("📱 访问地址: http://localhost:5000")
    print("🔄 系统将自动开始数据更新...")
    print()

    # 启动后台数据更新线程
    if load_stock_list():
        print("📈 启动后台数据更新线程...")
        update_thread = threading.Thread(target=continuous_update, daemon=True)
        update_thread.start()
    else:
        print("⚠️ 未找到股票列表，请上传持仓表格后系统将自动开始更新")

    # 启动Flask应用
    try:
        app.run(host='0.0.0.0', port=5000, debug=False, threaded=True)
    except KeyboardInterrupt:
        print("\n👋 系统已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
