"""
自动化获取扫雷宝真实评分
使用Selenium自动化浏览器获取真实分数
"""
import time
import re
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service

class AutoSLBScore:
    def __init__(self):
        self.driver = None
        self.setup_driver()
    
    def setup_driver(self):
        """设置Chrome浏览器"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # 无头模式
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
            
            self.driver = webdriver.Chrome(options=chrome_options)
            print("✅ Chrome浏览器初始化成功")
            return True
        except Exception as e:
            print(f"❌ Chrome浏览器初始化失败: {e}")
            print("💡 请确保已安装Chrome浏览器和ChromeDriver")
            return False
    
    def get_real_score(self, code):
        """自动获取扫雷宝真实评分"""
        if not self.driver:
            print("❌ 浏览器未初始化")
            return None
        
        try:
            # 扫雷宝URL
            url = f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code={code}&color=0"
            print(f"🌐 正在访问: {url}")
            
            self.driver.get(url)
            
            # 等待页面加载
            print("⏳ 等待页面加载...")
            time.sleep(5)
            
            # 方法1: 查找页面中的分数文本
            score_selectors = [
                "//div[contains(text(), '分')]",
                "//span[contains(text(), '分')]",
                "//*[contains(@class, 'score')]",
                "//*[contains(@class, 'rating')]",
                "//*[contains(@id, 'score')]",
                "//canvas/../div",  # 雷达图旁边的分数
            ]
            
            for selector in score_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        text = element.text.strip()
                        print(f"🔍 找到文本: {text}")
                        
                        # 提取分数
                        score_match = re.search(r'(\d+)分', text)
                        if score_match:
                            score = int(score_match.group(1))
                            if 0 <= score <= 100:
                                print(f"✅ 提取到评分: {score}分")
                                return float(score)
                except:
                    continue
            
            # 方法2: 执行JavaScript获取页面变量
            print("🔍 尝试JavaScript方式...")
            js_commands = [
                "return window.realvalue;",
                "return window.showvalue;",
                "return document.querySelector('canvas').nextElementSibling?.textContent;",
                "return Array.from(document.querySelectorAll('*')).find(el => el.textContent.includes('分'))?.textContent;",
            ]
            
            for js_cmd in js_commands:
                try:
                    result = self.driver.execute_script(js_cmd)
                    if result:
                        print(f"🔍 JS结果: {result}")
                        
                        # 如果是数字
                        if isinstance(result, (int, float)):
                            if 0 <= result <= 100:
                                print(f"✅ JS获取评分: {result}分")
                                return float(result)
                        
                        # 如果是字符串，提取分数
                        if isinstance(result, str):
                            score_match = re.search(r'(\d+)', result)
                            if score_match:
                                score = int(score_match.group(1))
                                if 0 <= score <= 100:
                                    print(f"✅ JS提取评分: {score}分")
                                    return float(score)
                except Exception as e:
                    print(f"JS执行失败: {e}")
                    continue
            
            # 方法3: 获取页面源码分析
            print("🔍 分析页面源码...")
            page_source = self.driver.page_source
            
            # 查找JavaScript变量
            js_patterns = [
                r'realvalue\s*=\s*(\d+)',
                r'showvalue\s*=\s*(\d+)',
                r'"score"\s*:\s*(\d+)',
                r'"rating"\s*:\s*(\d+)',
            ]
            
            for pattern in js_patterns:
                matches = re.findall(pattern, page_source)
                if matches:
                    for match in matches:
                        score = int(match)
                        if 0 <= score <= 100:
                            print(f"✅ 源码提取评分: {score}分")
                            return float(score)
            
            print("❌ 未能获取到有效评分")
            return None
            
        except Exception as e:
            print(f"❌ 获取评分失败: {e}")
            return None
    
    def batch_get_scores(self, codes):
        """批量获取评分"""
        results = {}
        for code in codes:
            print(f"\n📊 正在获取 {code} 的评分...")
            score = self.get_real_score(code)
            if score is not None:
                results[code] = score
            time.sleep(2)  # 避免请求过快
        return results
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            print("🔒 浏览器已关闭")

def install_selenium():
    """安装Selenium依赖"""
    import subprocess
    import sys
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "selenium"])
        print("✅ Selenium安装成功")
        
        # 安装ChromeDriver管理器
        subprocess.check_call([sys.executable, "-m", "pip", "install", "webdriver-manager"])
        print("✅ WebDriver管理器安装成功")
        
        return True
    except Exception as e:
        print(f"❌ 安装失败: {e}")
        return False

def test_auto_slb():
    """测试自动获取功能"""
    print("🧪 测试自动获取扫雷宝评分")
    print("=" * 50)
    
    # 检查依赖
    try:
        from selenium import webdriver
        print("✅ Selenium已安装")
    except ImportError:
        print("❌ Selenium未安装，正在安装...")
        if not install_selenium():
            return
    
    # 测试获取评分
    auto_slb = AutoSLBScore()
    
    if auto_slb.driver:
        # 测试神思电子
        test_codes = ['300479', '000001']  # 神思电子、平安银行
        
        for code in test_codes:
            print(f"\n🎯 测试 {code}...")
            score = auto_slb.get_real_score(code)
            if score:
                print(f"🎉 {code} 评分: {score}分")
            else:
                print(f"❌ {code} 获取失败")
    
    auto_slb.close()

if __name__ == '__main__':
    test_auto_slb()
