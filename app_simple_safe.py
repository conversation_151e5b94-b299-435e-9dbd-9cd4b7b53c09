from flask import Flask, render_template, jsonify
import requests
import time
import json
import pandas as pd
from datetime import datetime
import threading
import random

app = Flask(__name__)

# 全局变量
stock_data = {}
last_update_time = None

# 防限制配置
SAFE_CONFIG = {
    'request_delay': (0.5, 1.2),      # 请求间延时范围（秒）
    'batch_size': 6,                   # 每批处理股票数
    'batch_delay': (3, 6),             # 批次间延时范围（秒）
    'update_interval': (180, 300),     # 更新间隔范围（秒）
    'timeout': 15,                     # 请求超时时间
}

# User-Agent列表
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59',
]

def get_safe_headers():
    """获取安全的请求头"""
    return {
        'User-Agent': random.choice(USER_AGENTS),
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Referer': 'http://quote.eastmoney.com/',
        'Cache-Control': 'no-cache',
    }

def safe_request(url, params=None):
    """安全的HTTP请求"""
    try:
        headers = get_safe_headers()
        response = requests.get(
            url, 
            params=params, 
            headers=headers, 
            timeout=SAFE_CONFIG['timeout']
        )
        
        if response.status_code == 200:
            return response
        elif response.status_code == 429:
            print("⚠️  请求频率限制，等待...")
            time.sleep(random.uniform(10, 20))
            return None
        else:
            print(f"⚠️  HTTP状态码: {response.status_code}")
            return None
            
    except requests.exceptions.Timeout:
        print("⚠️  请求超时")
        return None
    except Exception as e:
        print(f"⚠️  请求异常: {e}")
        return None

def get_stock_data_safe(stock_code):
    """安全获取股票数据"""
    
    # 确定市场
    if stock_code.startswith('6'):
        secid = f'1.{stock_code}'
    else:
        secid = f'0.{stock_code}'
    
    url = 'http://push2.eastmoney.com/api/qt/stock/get'
    params = {
        'fltt': '2',
        'invt': '2',
        'fields': 'f43,f44,f45,f46,f47,f48,f57,f58,f60,f169,f170',
        'secid': secid,
        '_': str(int(time.time() * 1000))
    }
    
    response = safe_request(url, params)
    
    if response:
        try:
            data = json.loads(response.text)['data']
            if data:
                return {
                    'code': data['f57'],
                    'name': data['f58'],
                    'price': data['f43'],
                    'open': data['f46'],
                    'high': data['f44'],
                    'low': data['f45'],
                    'yesterday_close': data['f60'],
                    'volume': data['f47'],
                    'amount': data['f48'],
                    'change': data['f169'],
                    'change_pct': data['f170'],
                    'update_time': datetime.now().strftime('%H:%M:%S')
                }
        except Exception as e:
            print(f"解析数据失败: {e}")
    
    return None

def load_stock_list():
    """加载股票列表"""
    try:
        df = pd.read_csv('stocks_list.csv', encoding='utf-8-sig')
        return df.to_dict('records')
    except Exception as e:
        print(f"加载股票列表失败: {e}")
        return []

def update_stocks_safe():
    """安全更新股票数据"""
    global stock_data, last_update_time
    
    stock_list = load_stock_list()
    print(f"🚀 开始安全更新 {len(stock_list)} 只股票...")
    
    stock_data.clear()
    success_count = 0
    
    # 分批处理
    batch_size = SAFE_CONFIG['batch_size']
    total_batches = (len(stock_list) + batch_size - 1) // batch_size
    
    for batch_idx in range(total_batches):
        start_idx = batch_idx * batch_size
        end_idx = min(start_idx + batch_size, len(stock_list))
        batch_stocks = stock_list[start_idx:end_idx]
        
        print(f"📦 第 {batch_idx + 1}/{total_batches} 批 ({len(batch_stocks)} 只)")
        
        for stock in batch_stocks:
            code = str(stock['代码']).zfill(6)
            
            # 获取数据
            real_data = get_stock_data_safe(code)
            
            if real_data:
                stock_data[code] = real_data
                success_count += 1
                print(f"✅ {code} {real_data['name']} {real_data['price']}")
            else:
                # 失败时使用基本信息
                stock_data[code] = {
                    'code': code,
                    'name': stock['名称'],
                    'price': 0, 'change': 0, 'change_pct': 0,
                    'volume': 0, 'amount': 0, 'high': 0, 'low': 0,
                    'open': 0, 'yesterday_close': 0,
                    'update_time': datetime.now().strftime('%H:%M:%S'),
                    'error': '暂无数据'
                }
                print(f"❌ {code} 获取失败")
            
            # 请求间延时
            delay = random.uniform(*SAFE_CONFIG['request_delay'])
            time.sleep(delay)
        
        # 批次间延时
        if batch_idx < total_batches - 1:
            batch_delay = random.uniform(*SAFE_CONFIG['batch_delay'])
            print(f"⏱️  等待 {batch_delay:.1f} 秒...")
            time.sleep(batch_delay)
    
    last_update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    success_rate = success_count / len(stock_list) * 100
    print(f"✅ 更新完成！成功率: {success_rate:.1f}% ({success_count}/{len(stock_list)})")

def background_update_safe():
    """安全的后台更新"""
    while True:
        try:
            update_stocks_safe()
            
            # 随机更新间隔
            interval = random.uniform(*SAFE_CONFIG['update_interval'])
            print(f"⏰ 下次更新: {interval/60:.1f} 分钟后")
            time.sleep(interval)
            
        except Exception as e:
            print(f"❌ 后台更新错误: {e}")
            time.sleep(300)  # 出错后等5分钟

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/stocks')
def get_stocks():
    return jsonify({
        'stocks': list(stock_data.values()),
        'last_update': last_update_time,
        'total_count': len(stock_data)
    })

@app.route('/api/stock/<stock_code>')
def get_stock(stock_code):
    if stock_code in stock_data:
        return jsonify(stock_data[stock_code])
    else:
        return jsonify({'error': '股票代码不存在'}), 404

@app.route('/api/refresh')
def refresh_data():
    try:
        threading.Thread(target=update_stocks_safe, daemon=True).start()
        return jsonify({
            'success': True,
            'message': '数据刷新已启动'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'刷新失败: {str(e)}'
        }), 500

if __name__ == '__main__':
    print("=== 🚀 启动安全防限制股票行情系统 ===")
    print("🛡️  安全策略:")
    print(f"   - 请求延时: {SAFE_CONFIG['request_delay'][0]}-{SAFE_CONFIG['request_delay'][1]}秒")
    print(f"   - 批次大小: {SAFE_CONFIG['batch_size']}只股票")
    print(f"   - 批次延时: {SAFE_CONFIG['batch_delay'][0]}-{SAFE_CONFIG['batch_delay'][1]}秒")
    print(f"   - 更新间隔: {SAFE_CONFIG['update_interval'][0]/60:.1f}-{SAFE_CONFIG['update_interval'][1]/60:.1f}分钟")
    
    # 初始化数据
    print("\n📊 正在初始化数据...")
    update_stocks_safe()
    
    # 启动后台更新
    print("\n🔄 启动后台更新...")
    update_thread = threading.Thread(target=background_update_safe, daemon=True)
    update_thread.start()
    
    print("\n🌐 启动Web服务...")
    print("🔗 访问 http://localhost:5002 查看行情")
    
    app.run(debug=False, host='0.0.0.0', port=5002)
