# 自定义卖出信号取消功能使用说明

## 功能简介

持仓系统现在支持取消自定义卖出信号，让股票恢复到系统自动生成的正常状态。这个功能解决了以下需求：

- 🔄 **可取消性**: 撤销已设置的自定义卖出信号
- 📊 **状态恢复**: 恢复股票的正常交易信号生成
- 📱 **提醒恢复**: 重新启用企业微信提醒功能
- 🎯 **批量操作**: 支持一次性取消多只股票的自定义信号

## 使用方法

### 方法一：单只股票取消

1. **找到目标股票**
   - 在股票列表中找到已设置自定义状态的股票
   - 这些股票在"卖出信号"列会显示自定义状态（如"已减半"、"已清仓"等）

2. **打开状态菜单**
   - 点击股票的卖出信号标签
   - 系统会弹出状态选择菜单

3. **选择取消选项**
   - 在菜单顶部会看到红色的"🔄 取消自定义信号"按钮
   - 点击此按钮

4. **确认操作**
   - 系统会显示确认对话框，说明将要取消的状态
   - 点击"确定"完成操作

5. **查看结果**
   - 股票状态会立即更新为系统自动生成的信号
   - 企业微信提醒功能重新启用

### 方法二：批量取消

1. **检查批量按钮**
   - 在页面顶部的管理按钮区域
   - 如果有自定义状态的股票，会显示"🔄 批量取消自定义信号 (数量)"按钮

2. **点击批量按钮**
   - 点击批量取消按钮
   - 系统会显示所有有自定义状态的股票列表

3. **确认批量操作**
   - 仔细查看要取消的股票列表
   - 点击"确定"执行批量取消

4. **查看操作结果**
   - 系统会显示详细的操作结果
   - 包括成功数量、失败数量和具体信息

## 操作示例

### 示例1：取消"已减半"状态

```
股票：平安银行 (000001)
当前状态：已减半
操作：点击"已减半" → 选择"🔄 取消自定义信号" → 确认
结果：恢复为"📉 减半信号"（基于当前涨幅70%）
```

### 示例2：取消"已清仓"状态

```
股票：招商银行 (600036)
当前状态：已清仓
操作：点击"已清仓" → 选择"🔄 取消自定义信号" → 确认
结果：恢复为"🔥 清仓信号"（基于当前涨幅150%）
```

### 示例3：批量取消多只股票

```
当前自定义状态股票：
• 平安银行 (000001) - 已减半
• 招商银行 (600036) - 已清仓  
• 中国平安 (601318) - 观察中

操作：点击"🔄 批量取消自定义信号 (3)" → 确认
结果：3只股票全部恢复为系统自动信号
```

## 注意事项

### ⚠️ 重要提醒

1. **不可逆操作**
   - 取消自定义信号后，原来的自定义状态将完全丢失
   - 如需保留记录，请在操作前手动记录

2. **信号重新计算**
   - 取消后系统会根据当前股票数据重新计算卖出信号
   - 新信号可能与取消前的预期不同

3. **企业微信提醒**
   - 取消后会重新启用企业微信提醒
   - 如果股票仍触发卖出条件，可能立即收到提醒

### 💡 使用建议

1. **操作前确认**
   - 确认当前股票的价格、涨幅等关键数据
   - 预估取消后可能生成的新信号

2. **分批操作**
   - 对于大量股票，建议分批进行取消操作
   - 每次操作后检查结果再继续

3. **及时关注**
   - 取消后密切关注股票的新信号变化
   - 根据新信号及时调整投资策略

## 常见问题

### Q: 取消后的新信号是如何计算的？
A: 系统会根据当前股票的实时数据（价格、涨幅、PE、PB等）和策略配置重新计算卖出信号，与其他未设置自定义状态的股票使用相同的计算逻辑。

### Q: 可以只取消部分自定义状态吗？
A: 不可以。取消操作会清除该股票的所有自定义状态标记，包括自定义状态、已减半、已清仓等所有手动设置。

### Q: 批量操作失败了怎么办？
A: 批量操作支持部分成功，即使某些股票操作失败，成功的股票仍会被正确处理。系统会提供详细的成功/失败报告。

### Q: 取消后还能重新设置自定义状态吗？
A: 可以。取消操作不影响重新设置自定义状态的功能，您可以随时为股票设置新的自定义状态。

### Q: 如何知道哪些股票设置了自定义状态？
A: 在股票列表的"卖出信号"列中，设置了自定义状态的股票会显示相应的状态标签，如"已减半"、"已清仓"或其他自定义文本。

## 技术支持

如果在使用过程中遇到问题，请：

1. 检查浏览器控制台是否有错误信息
2. 确认网络连接正常
3. 尝试刷新页面后重新操作
4. 记录具体的操作步骤和错误信息以便排查
