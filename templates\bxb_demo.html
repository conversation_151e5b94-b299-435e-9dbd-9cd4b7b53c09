<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通达信爆雷宝分数演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        
        .header h1 {
            color: #333;
            margin: 0;
        }
        
        .header p {
            color: #666;
            margin: 10px 0 0 0;
        }
        
        .input-section {
            margin-bottom: 30px;
            text-align: center;
        }
        
        .input-group {
            display: inline-block;
            margin: 0 10px;
        }
        
        .input-group input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            width: 120px;
        }
        
        .btn {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 5px;
        }
        
        .btn:hover {
            background-color: #0056b3;
        }
        
        .btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .results {
            margin-top: 30px;
        }
        
        .stock-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        
        .stock-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .stock-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        
        .stock-code {
            color: #666;
            font-size: 14px;
        }
        
        .score-display {
            text-align: center;
            padding: 15px;
            border-radius: 50%;
            width: 80px;
            height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .score-number {
            font-size: 24px;
            line-height: 1;
        }
        
        .score-text {
            font-size: 12px;
            margin-top: 2px;
        }
        
        .risk-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .risk-item {
            background: white;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #eee;
        }
        
        .risk-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .risk-value {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        
        .risk-details {
            margin-top: 15px;
            padding: 15px;
            background: white;
            border-radius: 4px;
            border: 1px solid #eee;
        }
        
        .risk-category {
            margin-bottom: 10px;
        }
        
        .risk-category-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .risk-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .risk-list li {
            padding: 3px 0;
            color: #666;
            font-size: 14px;
        }
        
        .risk-list li::before {
            content: "⚠️ ";
            margin-right: 5px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        /* 风险等级颜色 */
        .risk-极低风险 { background-color: #10b068; }
        .risk-低风险 { background-color: #52c41a; }
        .risk-中低风险 { background-color: #faad14; }
        .risk-中等风险 { background-color: #fa8c16; }
        .risk-中高风险 { background-color: #fa541c; }
        .risk-高风险 { background-color: #f5222d; }
        .risk-极高风险 { background-color: #a8071a; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>通达信爆雷宝分数查询</h1>
            <p>基于通达信大数据的股票风险评估系统</p>
        </div>
        
        <div class="input-section">
            <div class="input-group">
                <input type="text" id="stockCode" placeholder="股票代码" value="000001">
            </div>
            <button class="btn" onclick="queryStock()">查询单只股票</button>
            <button class="btn" onclick="queryBatch()">批量查询示例</button>
        </div>
        
        <div id="results" class="results"></div>
    </div>

    <script>
        async function queryStock() {
            const stockCode = document.getElementById('stockCode').value.trim();
            if (!stockCode) {
                alert('请输入股票代码');
                return;
            }
            
            showLoading();
            
            try {
                const response = await fetch(`/api/bxb_score/${stockCode}`);
                const data = await response.json();
                
                if (data.error) {
                    showError(data.error);
                } else {
                    showResults([{code: stockCode, data: data}]);
                }
            } catch (error) {
                showError('查询失败: ' + error.message);
            }
        }
        
        async function queryBatch() {
            const testCodes = ['000001', '000002', '600000', '600036'];
            showLoading();
            
            try {
                const response = await fetch('/api/bxb_batch', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({codes: testCodes})
                });
                
                const data = await response.json();
                
                if (data.error) {
                    showError(data.error);
                } else {
                    const results = Object.entries(data).map(([code, stockData]) => ({
                        code: code,
                        data: stockData
                    }));
                    showResults(results);
                }
            } catch (error) {
                showError('批量查询失败: ' + error.message);
            }
        }
        
        function showLoading() {
            document.getElementById('results').innerHTML = '<div class="loading">正在查询中...</div>';
        }
        
        function showError(message) {
            document.getElementById('results').innerHTML = `<div class="error">${message}</div>`;
        }
        
        function showResults(results) {
            const resultsDiv = document.getElementById('results');
            
            if (results.length === 0) {
                resultsDiv.innerHTML = '<div class="error">没有查询到数据</div>';
                return;
            }
            
            let html = '';
            
            results.forEach(result => {
                const { code, data } = result;
                
                if (data.error) {
                    html += `
                        <div class="stock-card">
                            <div class="stock-header">
                                <div>
                                    <div class="stock-name">${code}</div>
                                </div>
                            </div>
                            <div class="error">${data.error}</div>
                        </div>
                    `;
                    return;
                }
                
                const riskClass = `risk-${data.risk_level}`;
                
                html += `
                    <div class="stock-card">
                        <div class="stock-header">
                            <div>
                                <div class="stock-name">${data.stock_name}</div>
                                <div class="stock-code">${code}</div>
                            </div>
                            <div class="score-display ${riskClass}">
                                <div class="score-number">${data.score}</div>
                                <div class="score-text">分</div>
                            </div>
                        </div>
                        
                        <div class="risk-info">
                            <div class="risk-item">
                                <div class="risk-label">风险等级</div>
                                <div class="risk-value">${data.risk_level}</div>
                            </div>
                            <div class="risk-item">
                                <div class="risk-label">触发风险</div>
                                <div class="risk-value">${data.triggered_risks} / ${data.total_risks}</div>
                            </div>
                            <div class="risk-item">
                                <div class="risk-label">触发率</div>
                                <div class="risk-value">${data.trigger_rate}%</div>
                            </div>
                        </div>
                        
                        ${data.details && data.details.length > 0 ? `
                            <div class="risk-details">
                                <div class="risk-category-title">触发的风险项:</div>
                                ${groupRisksByCategory(data.details)}
                            </div>
                        ` : ''}
                    </div>
                `;
            });
            
            resultsDiv.innerHTML = html;
        }
        
        function groupRisksByCategory(details) {
            const grouped = {};
            
            details.forEach(item => {
                if (!grouped[item.category]) {
                    grouped[item.category] = [];
                }
                grouped[item.category].push(item);
            });
            
            let html = '';
            Object.entries(grouped).forEach(([category, risks]) => {
                html += `
                    <div class="risk-category">
                        <div class="risk-category-title">${category} (${risks.length}项):</div>
                        <ul class="risk-list">
                            ${risks.map(risk => `<li>${risk.name} (扣分: ${risk.score})</li>`).join('')}
                        </ul>
                    </div>
                `;
            });
            
            return html;
        }
        
        // 页面加载完成后自动查询示例
        window.onload = function() {
            queryStock();
        };
    </script>
</body>
</html>
