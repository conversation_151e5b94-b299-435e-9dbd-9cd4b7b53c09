#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持仓系统主应用
==============

模块化重构的持仓系统主启动文件

基于原V14版本的功能：
- 股票数据获取和管理
- 多策略卖出信号计算
- 交易时间监测
- 年内最低价缓存
- 扫雷功能集成
- 企业微信提醒
- 数据持久化

作者: AI Assistant
版本: 2.0 (模块化版本)
日期: 2025-07-25
"""

import sys
import os
import threading
import time
from datetime import datetime

# 添加模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

from flask import Flask

# 导入自定义模块
from modules.data_fetcher import stock_data_fetcher, stock_info_processor
from modules.strategy_manager import SellStrategyManager
from modules.sell_signal import SellSignalPriorityManager, calculate_sell_signal
from modules.cache_manager import cache_manager
from modules.data_persistence import data_persistence
from modules.trading_time_monitor import AStockTradingTimeMonitor
from modules.tdx_scan_module import TdxScanClient
from modules.api_routes import setup_routes

# Flask应用初始化
app = Flask(__name__)

# 配置文件上传
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# 确保必要目录存在
for directory in ['uploads', 'cache', 'logs', 'data', 'config']:
    os.makedirs(directory, exist_ok=True)

# 全局变量
stock_data = {}
last_update_time = None
imported_stock_list = []
auto_update_enabled = True
update_thread_running = False

# 初始化各个组件
print("=== 🚀 启动持仓系统 V2.0 模块化版本 ===")

# 1. 初始化交易时间监测器
print("📅 初始化交易时间监测器...")
trading_monitor = AStockTradingTimeMonitor()

# 2. 初始化策略管理器
print("🔧 初始化策略管理器...")
strategy_manager = SellStrategyManager()

# 3. 初始化优先级管理器
print("⚖️ 初始化优先级管理器...")
priority_manager = SellSignalPriorityManager()

# 4. 初始化扫雷客户端
print("🔍 初始化扫雷客户端...")
scan_client = TdxScanClient(cache_duration=1800)  # 缓存30分钟

# 5. 初始化企业微信提醒（简化版）
class SimpleWeChatAlert:
    def __init__(self):
        self.sent_alerts = {}
    
    def send_alert(self, message, stock_code=None):
        print(f"📱 微信提醒: {message}")
        return True

wechat_alert = SimpleWeChatAlert()

# 6. 加载持久化数据
print("📂 加载持久化数据...")
stock_data, last_update_time, _ = data_persistence.load_stock_data()
imported_stock_list = data_persistence.load_imported_list()
auto_update_enabled = data_persistence.load_auto_update_config()

print(f"   📊 加载了 {len(stock_data)} 只股票数据")
print(f"   📋 加载了 {len(imported_stock_list)} 只导入股票")
print(f"   🔄 自动更新状态: {'启用' if auto_update_enabled else '禁用'}")


def is_stock_only(code):
    """判断是否为股票（用于筛选）"""
    code_str = str(code).zfill(6)
    
    # 只保留6位数字的A股股票
    if len(code_str) != 6 or not code_str.isdigit():
        return False
    
    # 过滤ETF、基金等
    if code_str.startswith('1'):  # ETF
        return False
    elif code_str.startswith('5'):  # 基金
        return False
    elif code_str.startswith('8'):  # 新三板
        return False
    elif code_str.startswith('4'):  # 其他
        return False
    elif code_str.startswith('6'):  # 沪市A股
        return True
    elif code_str.startswith('0') or code_str.startswith('3'):  # 深市A股和创业板
        return True
    
    return False


def load_stock_list():
    """加载股票列表"""
    global imported_stock_list
    
    # 优先使用导入的股票列表
    if imported_stock_list:
        print(f"📋 使用导入的股票列表，包含 {len(imported_stock_list)} 只股票")
        return imported_stock_list
    
    print("⚠️ 没有导入的股票列表")
    return []


def update_single_stock(stock_info):
    """更新单只股票数据"""
    global stock_data
    
    code = stock_info['代码']
    name = stock_info.get('名称', '')
    holdings = stock_info.get('持仓数量', 0)
    
    print(f"🔄 更新股票: {code} {name}")
    
    # 获取完整股票信息
    complete_info = stock_info_processor.get_complete_stock_info(code)
    
    if complete_info:
        # 保留持仓信息
        complete_info['持仓数量'] = holdings
        complete_info['名称'] = name or complete_info.get('name', '')
        
        # 获取扫雷分数
        try:
            scan_result = scan_client.get_stock_score(code)
            complete_info['scan_score'] = scan_result.get('score', 0) if scan_result else 0
        except Exception as e:
            print(f"获取扫雷分数失败 {code}: {e}")
            complete_info['scan_score'] = 0
        
        # 计算卖出信号
        sell_signal = calculate_sell_signal(complete_info, strategy_manager, priority_manager)
        complete_info.update({
            'sell_signal': sell_signal['signal'],
            'sell_reason': sell_signal['reason'],
            'sell_color': sell_signal['color'],
            'sell_priority': sell_signal['priority'],
            'sell_details': sell_signal['details'],
            'sell_source': sell_signal['source']
        })
        
        # 更新股票数据
        stock_data[code] = complete_info
        
        print(f"✅ {code} {name} 更新完成")
    else:
        print(f"❌ {code} {name} 获取数据失败")


def update_all_stocks():
    """更新所有股票数据"""
    global last_update_time
    
    stock_list = load_stock_list()
    if not stock_list:
        print("⚠️ 没有股票列表需要更新")
        return
    
    print(f"🔄 开始更新 {len(stock_list)} 只股票...")
    
    updated_count = 0
    for stock_info in stock_list:
        if is_stock_only(stock_info['代码']):
            update_single_stock(stock_info)
            updated_count += 1
            time.sleep(0.1)  # 避免请求过快
    
    last_update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    print(f"✅ 股票数据更新完成")
    print(f"   📊 更新股票数量: {updated_count}")
    print(f"   🕐 更新时间: {last_update_time}")
    
    # 保存更新后的数据
    data_persistence.save_stock_data(stock_data, last_update_time)


def background_update():
    """后台智能更新股票数据"""
    global update_thread_running
    update_thread_running = True
    round_count = 0
    
    print("🔄 后台自动更新线程已启动")
    
    while update_thread_running:
        try:
            if auto_update_enabled:
                # 检查是否为交易时间
                trading_status = trading_monitor.get_trading_status()
                
                if trading_status.get('is_trading_time', False):
                    round_count += 1
                    print(f"🔄 第 {round_count} 轮自动更新开始...")
                    update_all_stocks()
                    print(f"✅ 第 {round_count} 轮自动更新完成")
                    
                    # 交易时间内每5分钟更新一次
                    time.sleep(300)
                else:
                    print("⏸️ 非交易时间，暂停自动更新")
                    # 非交易时间每30分钟检查一次
                    time.sleep(1800)
            else:
                print("⏸️ 自动更新已禁用")
                time.sleep(60)
                
        except Exception as e:
            print(f"❌ 后台更新异常: {e}")
            time.sleep(60)
    
    update_thread_running = False
    print("🛑 后台自动更新线程已停止")


# 设置API路由
print("🌐 设置API路由...")
setup_routes(app, stock_data, strategy_manager, priority_manager, trading_monitor,
             scan_client, wechat_alert, cache_manager, data_persistence)

# 启动后台更新线程
if auto_update_enabled:
    print("🚀 启动后台更新线程...")
    update_thread = threading.Thread(target=background_update, daemon=True)
    update_thread.start()

print("✅ 系统初始化完成")
print("🌐 Web服务器准备就绪")


if __name__ == '__main__':
    print("\n=== 🎯 启动参数 ===")
    print("📍 运行模式: 开发模式")
    print("🌐 访问地址: http://localhost:5000")
    print("📊 功能特性:")
    print("   ✅ 股票数据获取")
    print("   ✅ 多策略卖出信号")
    print("   ✅ 交易时间监测")
    print("   ✅ 年内最低价缓存")
    print("   ✅ 扫雷功能集成")
    print("   ✅ 数据持久化")
    print("   ✅ 模块化架构")
    print("\n🚀 启动Flask应用...")
    
    try:
        app.run(host='0.0.0.0', port=5000, debug=False, threaded=True)
    except KeyboardInterrupt:
        print("\n🛑 收到停止信号，正在关闭...")
        update_thread_running = False
        print("✅ 应用已安全关闭")
