import requests
import re
import json
import time

def get_safescore_auto_parse(code: str) -> float:
    """自动解析扫雷宝页面获取评分"""
    try:
        print(f"🔍 自动解析扫雷宝页面: {code}")
        
        # 扫雷宝页面URL
        url = f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code={code}&color=0"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Referer': 'http://page3.tdx.com.cn:7615/',
        }
        
        response = requests.get(url, headers=headers, timeout=15)
        response.encoding = 'utf-8'
        
        if response.status_code == 200:
            content = response.text
            
            # 方法1: 查找JavaScript中的分数变量
            score_patterns = [
                r'var\s+showvalue\s*=\s*(\d{1,3})',      # showvalue变量
                r'showvalue\s*=\s*(\d{1,3})',            # showvalue赋值
                r'var\s+realvalue\s*=\s*(\d{1,3})',      # realvalue变量
                r'realvalue\s*=\s*(\d{1,3})',            # realvalue赋值
                r'fillText\([^,]*?(\d{1,3})[^,]*?"分"',   # Canvas绘制分数
                r'(\d{1,3})分',                          # 直接的分数文本
                r'安全分[：:]\s*(\d{1,3})',               # 安全分标签
                r'评分[：:]\s*(\d{1,3})',                 # 评分标签
            ]
            
            found_scores = []
            for pattern in score_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    for match in matches:
                        try:
                            score = int(match)
                            if 50 <= score <= 100:  # 扫雷宝分数通常在这个范围
                                found_scores.append(score)
                                print(f"  找到分数: {score} (模式: {pattern[:20]}...)")
                        except ValueError:
                            continue
            
            if found_scores:
                # 选择最可能的分数（出现频率最高的）
                final_score = max(set(found_scores), key=found_scores.count)
                print(f"✅ 自动解析获取评分: {final_score}分 (候选: {found_scores})")
                return float(final_score)
            
            # 方法2: 查找所有可能的分数，选择最合理的
            all_numbers = re.findall(r'\b(\d{2,3})\b', content)
            valid_scores = []
            for num_str in all_numbers:
                try:
                    num = int(num_str)
                    if 60 <= num <= 100:  # 扫雷宝分数通常较高
                        valid_scores.append(num)
                except ValueError:
                    continue
            
            if valid_scores:
                # 返回最高分数（扫雷宝分数通常较高）
                final_score = max(valid_scores)
                print(f"✅ 推测评分: {final_score}分 (候选: {valid_scores[:10]})")
                return float(final_score)
            
            print(f"⚠️ 无法从页面解析出评分")
            return None
            
    except Exception as e:
        print(f"❌ 自动解析扫雷宝页面失败: {e}")
        return None

def test_multiple_stocks():
    """测试多个股票的扫雷宝评分"""
    
    # 测试股票列表
    test_stocks = [
        ("300479", "神思电子"),
        ("000001", "平安银行"),
        ("600000", "浦发银行"),
        ("000002", "万科A"),
        ("600036", "招商银行"),
        ("000858", "五粮液"),
        ("600519", "贵州茅台"),
        ("000166", "申万宏源"),
        ("300750", "宁德时代"),
        ("002415", "海康威视"),
    ]
    
    print("=== 测试多个股票的扫雷宝评分 ===\n")
    
    results = []
    
    for i, (code, name) in enumerate(test_stocks):
        print(f"[{i+1:2d}/{len(test_stocks)}] 测试 {code} ({name})")
        print("-" * 50)
        
        score = get_safescore_auto_parse(code)
        
        if score is not None:
            results.append((code, name, score))
            print(f"✅ {name} ({code}): {score}分")
        else:
            results.append((code, name, None))
            print(f"❌ {name} ({code}): 获取失败")
        
        print()
        
        # 添加延迟避免请求过快
        if i < len(test_stocks) - 1:
            time.sleep(1)
    
    # 汇总结果
    print("=" * 60)
    print("汇总结果:")
    print("=" * 60)
    
    success_count = 0
    for code, name, score in results:
        if score is not None:
            print(f"✅ {name:10} ({code}): {score:5.1f}分")
            success_count += 1
        else:
            print(f"❌ {name:10} ({code}): 获取失败")
    
    print(f"\n成功率: {success_count}/{len(test_stocks)} ({success_count/len(test_stocks)*100:.1f}%)")

if __name__ == "__main__":
    test_multiple_stocks()
