"""
测试真实扫雷宝评分获取功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 持仓系统_v10_排雷功能版 import get_mine_clearance_score

def test_slb_scores():
    """测试多只股票的扫雷宝评分"""
    test_stocks = [
        ('300479', '神思电子'),
        ('000001', '平安银行'),
        ('600519', '贵州茅台'),
        ('000002', '万科A'),
        ('600036', '招商银行'),
    ]
    
    print("🧪 测试真实扫雷宝评分获取")
    print("=" * 60)
    
    results = []
    for code, name in test_stocks:
        print(f"\n📊 正在获取 {name}({code}) 的扫雷宝评分...")
        print("-" * 40)
        
        try:
            score = get_mine_clearance_score(code)
            results.append((code, name, score))
            print(f"✅ {name}({code}): {score}分")
        except Exception as e:
            print(f"❌ {name}({code}): 获取失败 - {e}")
            results.append((code, name, None))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📋 测试结果汇总:")
    print("=" * 60)
    print(f"{'股票代码':<8} {'股票名称':<12} {'扫雷宝评分':<10}")
    print("-" * 60)
    
    success_count = 0
    for code, name, score in results:
        if score is not None:
            print(f"{code:<8} {name:<12} {score:<10.1f}")
            success_count += 1
        else:
            print(f"{code:<8} {name:<12} {'获取失败':<10}")
    
    print("-" * 60)
    print(f"成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
    
    if success_count > 0:
        print("\n🎉 真实API接口调用成功！")
        print("💡 现在可以获取通达信扫雷宝的真实评分了")
    else:
        print("\n❌ 所有API调用都失败了")
        print("💡 可能需要检查网络连接或API接口变化")

if __name__ == '__main__':
    test_slb_scores()
