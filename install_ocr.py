"""
安装OCR依赖包
"""
import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ {package} 安装失败")
        return False

def main():
    print("🔧 开始安装OCR相关依赖...")
    print("=" * 50)
    
    # 需要安装的包
    packages = [
        "pillow",  # PIL图像处理
        "pytesseract",  # OCR引擎Python接口
    ]
    
    success_count = 0
    for package in packages:
        print(f"📦 正在安装 {package}...")
        if install_package(package):
            success_count += 1
        print()
    
    print("=" * 50)
    print(f"📊 安装结果: {success_count}/{len(packages)} 个包安装成功")
    
    if success_count == len(packages):
        print("🎉 所有依赖安装完成！")
        
        # 检查Tesseract OCR引擎
        print("\n🔍 检查Tesseract OCR引擎...")
        try:
            import pytesseract
            # 尝试获取版本信息
            version = pytesseract.get_tesseract_version()
            print(f"✅ Tesseract版本: {version}")
        except Exception as e:
            print(f"⚠️ Tesseract OCR引擎可能未安装: {e}")
            print("\n💡 请手动安装Tesseract OCR引擎:")
            print("Windows: 下载 https://github.com/UB-Mannheim/tesseract/wiki")
            print("或使用: winget install UB-Mannheim.TesseractOCR")
            
        print("\n🧪 现在可以运行测试:")
        print("python test_ocr_slb.py")
        
    else:
        print("❌ 部分依赖安装失败，请检查网络连接或手动安装")

if __name__ == '__main__':
    main()
