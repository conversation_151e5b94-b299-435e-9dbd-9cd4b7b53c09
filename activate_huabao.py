#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pygetwindow as gw
import pyautogui
import time

def activate_huabao_window():
    """激活华宝证券窗口"""
    print("=== 激活华宝证券窗口 ===")
    
    # 查找华宝证券窗口
    windows = gw.getAllWindows()
    huabao_window = None
    
    for window in windows:
        if window.title and '华宝证券' in window.title:
            huabao_window = window
            print(f"找到华宝证券窗口: {window.title}")
            break
    
    if not huabao_window:
        print("❌ 未找到华宝证券窗口")
        return None
    
    try:
        # 尝试激活窗口
        print("正在激活窗口...")
        huabao_window.activate()
        time.sleep(2)
        
        # 如果窗口被最小化，尝试恢复
        if huabao_window.isMinimized:
            print("窗口被最小化，正在恢复...")
            huabao_window.restore()
            time.sleep(2)
        
        # 将窗口移到前台
        huabao_window.activate()
        time.sleep(1)
        
        print(f"✅ 窗口已激活")
        print(f"当前位置: ({huabao_window.left}, {huabao_window.top})")
        print(f"当前大小: {huabao_window.width}x{huabao_window.height}")
        
        return huabao_window
        
    except Exception as e:
        print(f"❌ 激活窗口失败: {e}")
        return None

def capture_huabao_window(window):
    """截取华宝证券窗口"""
    print("=== 截取华宝证券窗口 ===")
    
    try:
        # 确保窗口在前台
        window.activate()
        time.sleep(1)
        
        # 获取窗口位置和大小
        left = window.left
        top = window.top
        width = window.width
        height = window.height
        
        print(f"窗口区域: ({left}, {top}, {width}, {height})")
        
        # 如果窗口位置异常，截取整个屏幕
        if left < -10000 or top < -10000:
            print("窗口位置异常，截取整个屏幕...")
            screenshot = pyautogui.screenshot()
        else:
            # 截取窗口区域
            screenshot = pyautogui.screenshot(region=(left, top, width, height))
        
        # 保存截图
        screenshot.save("huabao_window.png")
        print("✅ 已保存华宝证券窗口截图: huabao_window.png")
        
        return screenshot
        
    except Exception as e:
        print(f"❌ 截图失败: {e}")
        return None

def main():
    print("=== 华宝证券窗口操作 ===")
    
    # 1. 激活华宝证券窗口
    window = activate_huabao_window()
    if not window:
        return
    
    # 2. 截取窗口截图
    screenshot = capture_huabao_window(window)
    if not screenshot:
        return
    
    print("\n=== 完成 ===")
    print("请查看生成的截图文件:")
    print("- huabao_window.png")
    print("\n下一步:")
    print("1. 查看截图确认是否显示了股票行情数据")
    print("2. 如果显示正确，我们可以继续解析数据")

if __name__ == "__main__":
    main()
