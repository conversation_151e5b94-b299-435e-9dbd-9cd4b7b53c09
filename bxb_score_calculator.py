#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通达信爆雷宝分数计算器
基于源代码分析的计算公式实现
"""

import requests
import json
import time

class BXBScoreCalculator:
    """通达信爆雷宝分数计算器"""
    
    def __init__(self):
        self.base_url = "http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/json/"
        
    def get_stock_data(self, stock_code):
        """获取股票风险数据"""
        url = f"{self.base_url}{stock_code}.json"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        try:
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"获取数据失败: {e}")
            return None
    
    def calculate_score(self, stock_data):
        """
        计算爆雷宝分数

        根据实际验证，正确的计算逻辑如下：
        1. realvalue 初始值为 100
        2. 只计算主风险项：如果 trig=1 且 fs>0，则 realvalue -= fs
        3. 不计算关联风险项 (commonlxid)
        4. 最终分数 = realvalue (但不能小于1)
        """
        if not stock_data or 'data' not in stock_data:
            return None

        # 初始分数为100分
        realvalue = 100

        # 统计信息
        total_risks = 0
        triggered_risks = 0
        risk_details = []

        # 遍历所有风险类别
        for category in stock_data['data']:
            category_name = category.get('name', '未知类别')
            category_risks = []

            # 遍历该类别下的所有风险项
            for risk_item in category.get('rows', []):
                total_risks += 1

                # 获取风险项信息
                risk_id = risk_item.get('id')
                risk_name = risk_item.get('lx', '未知风险')
                fs = risk_item.get('fs', 0)  # 风险分数
                trig = risk_item.get('trig', 0)  # 是否触发
                trigyy = risk_item.get('trigyy', '')  # 触发原因

                # 只计算主风险项：如果风险被触发且有分数，则扣分
                if trig == 1 and fs > 0:
                    triggered_risks += 1
                    realvalue -= fs
                    category_risks.append({
                        'id': risk_id,
                        'name': risk_name,
                        'score': fs,
                        'triggered': True,
                        'reason': trigyy
                    })
                    print(f"触发风险: {risk_name} (扣分: {fs})")
                else:
                    category_risks.append({
                        'id': risk_id,
                        'name': risk_name,
                        'score': fs,
                        'triggered': False,
                        'reason': trigyy
                    })

                # 注意：不计算关联风险 (commonlxid)，这是关键发现！
                # 关联风险只用于显示，不参与分数计算

            if category_risks:
                risk_details.append({
                    'category': category_name,
                    'risks': category_risks
                })

        # 确保分数不小于1
        if realvalue <= 1:
            realvalue = 1
            
        return {
            'stock_name': stock_data.get('name', '未知股票'),
            'final_score': realvalue,
            'total_risks': total_risks,
            'triggered_risks': triggered_risks,
            'trigger_rate': f"{triggered_risks/total_risks*100:.1f}%" if total_risks > 0 else "0%",
            'risk_level': self.get_risk_level(realvalue),
            'details': risk_details
        }
    
    def get_risk_level(self, score):
        """根据分数确定风险等级"""
        if score >= 90:
            return "极低风险"
        elif score >= 80:
            return "低风险"
        elif score >= 70:
            return "中低风险"
        elif score >= 60:
            return "中等风险"
        elif score >= 50:
            return "中高风险"
        elif score >= 40:
            return "高风险"
        else:
            return "极高风险"
    
    def analyze_stock(self, stock_code):
        """分析单只股票"""
        print(f"\n正在分析股票 {stock_code}...")
        
        # 获取数据
        stock_data = self.get_stock_data(stock_code)
        if not stock_data:
            return None
            
        # 计算分数
        result = self.calculate_score(stock_data)
        return result
    
    def print_result(self, result):
        """打印分析结果"""
        if not result:
            print("无法获取分析结果")
            return
            
        print("\n" + "="*60)
        print(f"股票名称: {result['stock_name']}")
        print(f"爆雷宝分数: {result['final_score']} 分")
        print(f"风险等级: {result['risk_level']}")
        print(f"风险触发情况: {result['triggered_risks']}/{result['total_risks']} ({result['trigger_rate']})")
        print("="*60)
        
        # 显示各类别风险详情
        print("\n风险详情:")
        for category_info in result['details']:
            category_name = category_info['category']
            risks = category_info['risks']
            
            triggered_in_category = sum(1 for r in risks if r['triggered'])
            if triggered_in_category > 0:
                print(f"\n【{category_name}】- 触发 {triggered_in_category} 项:")
                for risk in risks:
                    if risk['triggered']:
                        print(f"  ⚠️  {risk['name']} (扣分: {risk['score']})")

def main():
    """主函数"""
    calculator = BXBScoreCalculator()
    
    # 测试股票代码
    test_stocks = ["000001", "000002", "600000", "600036"]
    
    for stock_code in test_stocks:
        result = calculator.analyze_stock(stock_code)
        if result:
            calculator.print_result(result)
        
        # 避免请求过于频繁
        time.sleep(1)

if __name__ == "__main__":
    main()
