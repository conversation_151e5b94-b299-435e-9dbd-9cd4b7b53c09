from flask import Flask, render_template, jsonify
import akshare as ak
import pandas as pd
from datetime import datetime
import threading
import time

app = Flask(__name__)

# 全局变量存储股票数据
stock_data = {}
last_update_time = None

def load_stock_list():
    """加载股票列表"""
    try:
        df = pd.read_csv('stocks_list.csv', encoding='utf-8-sig')
        return df.to_dict('records')
    except Exception as e:
        print(f"加载股票列表失败: {e}")
        return []

def update_all_stocks():
    """正确的逻辑：先获取所有股票数据，再用持仓代码筛选"""
    global stock_data, last_update_time
    
    try:
        # 第一步：直接调用AKSHARE获取所有股票数据（一次调用获取全部）
        print("🚀 正在调用AKSHARE获取所有A股实时数据...")
        df_all = ak.stock_zh_a_spot()
        print(f"✅ 成功获取 {len(df_all)} 只股票的实时数据")
        print(f"📊 数据列名: {df_all.columns.tolist()}")
        
        # 第二步：加载我们的持仓股票代码
        stock_list = load_stock_list()
        our_stock_codes = set(str(stock['代码']).zfill(6) for stock in stock_list)
        print(f"📋 持仓股票数量: {len(our_stock_codes)}")
        
        # 第三步：格式化所有股票数据的代码，用于匹配
        df_all['代码'] = df_all['代码'].astype(str).str.zfill(6)
        
        # 第四步：筛选出我们持仓的股票
        our_stocks = df_all[df_all['代码'].isin(our_stock_codes)]
        print(f"🎯 匹配到 {len(our_stocks)} 只持仓股票")
        
        # 第五步：清空旧数据并填入新数据
        stock_data.clear()
        
        for _, row in our_stocks.iterrows():
            code = row['代码']
            try:
                stock_data[code] = {
                    'code': code,
                    'name': row['名称'],
                    'price': float(row['最新价']),
                    'change': float(row['涨跌额']),
                    'change_pct': float(row['涨跌幅']),
                    'volume': int(float(row['成交量'])),
                    'amount': float(row['成交额']),
                    'high': float(row['最高']),
                    'low': float(row['最低']),
                    'open': float(row['今开']),
                    'yesterday_close': float(row['昨收']),
                    'update_time': datetime.now().strftime('%H:%M:%S')
                }
            except Exception as e:
                print(f"⚠️  处理股票 {code} 数据时出错: {e}")
                continue
        
        # 第六步：处理未匹配到的股票
        matched_codes = set(our_stocks['代码'])
        missing_codes = our_stock_codes - matched_codes
        
        if missing_codes:
            print(f"⚠️  未找到数据的股票: {len(missing_codes)} 只")
            stock_name_map = {str(stock['代码']).zfill(6): stock['名称'] for stock in stock_list}
            
            for code in missing_codes:
                stock_data[code] = {
                    'code': code,
                    'name': stock_name_map.get(code, '未知'),
                    'price': 0, 'change': 0, 'change_pct': 0,
                    'volume': 0, 'amount': 0, 'high': 0, 'low': 0,
                    'open': 0, 'yesterday_close': 0,
                    'update_time': datetime.now().strftime('%H:%M:%S'),
                    'error': '暂无数据'
                }
        
        last_update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"✅ 数据更新完成！处理了 {len(stock_data)} 只股票，时间: {last_update_time}")
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        import traceback
        traceback.print_exc()

def background_update():
    """后台定时更新股票数据"""
    while True:
        update_all_stocks()
        time.sleep(120)  # 每2分钟更新一次，避免过于频繁调用

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/stocks')
def get_stocks():
    """获取所有股票数据API"""
    return jsonify({
        'stocks': list(stock_data.values()),
        'last_update': last_update_time,
        'total_count': len(stock_data)
    })

@app.route('/api/stock/<stock_code>')
def get_stock(stock_code):
    """获取单只股票数据API"""
    if stock_code in stock_data:
        return jsonify(stock_data[stock_code])
    else:
        return jsonify({'error': '股票代码不存在'}), 404

if __name__ == '__main__':
    print("=== 🚀 启动股票实时行情系统 ===")
    
    # 初始加载数据
    print("📊 正在初始化数据...")
    update_all_stocks()
    
    # 启动后台更新线程
    print("🔄 启动后台更新线程...")
    update_thread = threading.Thread(target=background_update, daemon=True)
    update_thread.start()
    
    print("🌐 启动Flask Web服务...")
    print("🔗 访问 http://localhost:5000 查看股票行情")
    
    # 启动Flask应用
    app.run(debug=True, host='0.0.0.0', port=5000)
