<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>清除排序缓存</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        .button {
            background: #f44336;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            display: inline-block;
        }
        .button:hover {
            background: #d32f2f;
        }
        .success {
            background: #4caf50;
        }
        .success:hover {
            background: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .cache-info {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 清除排序缓存工具</h1>
        
        <div class="info">
            <strong>说明：</strong>如果页面没有按照默认排序显示（已清仓股票在最下面），可能是因为浏览器缓存了之前的排序偏好。点击下面的按钮清除缓存。
        </div>

        <div id="currentCache" class="cache-info">
            正在检查当前缓存...
        </div>

        <button class="button" onclick="clearSortCache()">🗑️ 清除排序缓存</button>
        <button class="button success" onclick="setDefaultSort()">✅ 设置默认排序</button>
        <button class="button" onclick="checkCache()">🔍 检查缓存状态</button>

        <div id="result" class="result"></div>

        <div class="info" style="margin-top: 30px;">
            <strong>默认排序规则：</strong>
            <ol>
                <li>🔥 立即清仓信号（最高优先级）</li>
                <li>📉 立即减半信号</li>
                <li>⚠️ 准备清仓信号</li>
                <li>📊 准备减半信号</li>
                <li>🚨 卖出信号</li>
                <li>⚠️ 预警信号</li>
                <li>🧊 已减半股票（冷却期）</li>
                <li>📈 正常持仓股票</li>
                <li>🛑 已清仓股票（最低优先级，在最下面）</li>
            </ol>
        </div>
    </div>

    <script>
        function showResult(message, type = 'success') {
            const result = document.getElementById('result');
            result.textContent = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
            
            setTimeout(() => {
                result.style.display = 'none';
            }, 5000);
        }

        function updateCacheDisplay() {
            const cacheDiv = document.getElementById('currentCache');
            try {
                const sortPrefs = localStorage.getItem('stockTableSortPreferences');
                if (sortPrefs) {
                    const parsed = JSON.parse(sortPrefs);
                    cacheDiv.innerHTML = `
                        <strong>当前缓存的排序偏好：</strong><br>
                        ${JSON.stringify(parsed, null, 2)}
                    `;
                } else {
                    cacheDiv.innerHTML = '<strong>当前没有缓存的排序偏好</strong>';
                }
            } catch (error) {
                cacheDiv.innerHTML = `<strong>缓存读取错误：</strong>${error.message}`;
            }
        }

        function clearSortCache() {
            try {
                // 清除排序偏好缓存
                localStorage.removeItem('stockTableSortPreferences');
                
                // 清除其他可能的相关缓存
                const keysToRemove = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && (key.includes('sort') || key.includes('Sort'))) {
                        keysToRemove.push(key);
                    }
                }
                
                keysToRemove.forEach(key => localStorage.removeItem(key));
                
                updateCacheDisplay();
                showResult(`✅ 已清除排序缓存！清除了 ${keysToRemove.length + 1} 个缓存项。请刷新持仓系统页面查看效果。`, 'success');
            } catch (error) {
                showResult(`❌ 清除缓存失败：${error.message}`, 'info');
            }
        }

        function setDefaultSort() {
            try {
                // 设置默认排序：按卖出信号优先级升序，市值降序
                const defaultSort = {
                    sortColumns: [
                        { column: 'sell_priority', direction: 'asc' },
                        { column: 'market_value', direction: 'desc' }
                    ]
                };
                
                localStorage.setItem('stockTableSortPreferences', JSON.stringify(defaultSort));
                updateCacheDisplay();
                showResult('✅ 已设置默认排序！请刷新持仓系统页面查看效果。', 'success');
            } catch (error) {
                showResult(`❌ 设置默认排序失败：${error.message}`, 'info');
            }
        }

        function checkCache() {
            updateCacheDisplay();
            showResult('🔍 已更新缓存显示', 'info');
        }

        // 页面加载时检查缓存
        window.onload = function() {
            updateCacheDisplay();
        };
    </script>
</body>
</html>
