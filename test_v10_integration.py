#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持仓系统 V10 表格增强版集成测试
测试新功能与现有年内最低价缓存系统的集成
"""

import requests
import time
import json
import pandas as pd
import os
from datetime import datetime

class V10IntegrationTester:
    def __init__(self, base_url='http://localhost:5000'):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
    
    def log_test(self, test_name, success, message, details=None):
        """记录测试结果"""
        result = {
            'test_name': test_name,
            'success': success,
            'message': message,
            'details': details,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {message}")
        if details:
            print(f"   详情: {details}")
    
    def test_server_startup(self):
        """测试服务器启动"""
        try:
            response = self.session.get(f"{self.base_url}/")
            if response.status_code == 200:
                self.log_test("服务器启动", True, "V10系统正常运行")
                return True
            else:
                self.log_test("服务器启动", False, f"HTTP状态码: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("服务器启动", False, f"连接失败: {e}")
            return False
    
    def test_api_compatibility(self):
        """测试API兼容性"""
        api_endpoints = [
            '/api/stocks',
            '/api/trading-status',
            '/api/cache/status',
            '/api/cache/check',
            '/api/wechat/test'
        ]
        
        all_success = True
        for endpoint in api_endpoints:
            try:
                response = self.session.get(f"{self.base_url}{endpoint}")
                if response.status_code == 200:
                    self.log_test(f"API兼容性 - {endpoint}", True, "接口正常")
                else:
                    self.log_test(f"API兼容性 - {endpoint}", False, f"状态码: {response.status_code}")
                    all_success = False
            except Exception as e:
                self.log_test(f"API兼容性 - {endpoint}", False, f"请求失败: {e}")
                all_success = False
        
        return all_success
    
    def test_cache_integration(self):
        """测试缓存系统集成"""
        try:
            # 检查缓存状态
            response = self.session.get(f"{self.base_url}/api/cache/status")
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    cache_status = data['cache_status']
                    self.log_test("缓存系统集成", True, 
                                f"缓存状态: {'可用' if cache_status.get('exists') else '不可用'}",
                                f"文件: {cache_status.get('file_path', 'N/A')}")
                    return True
                else:
                    self.log_test("缓存系统集成", False, "缓存状态检查失败")
                    return False
            else:
                self.log_test("缓存系统集成", False, f"API调用失败: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("缓存系统集成", False, f"测试异常: {e}")
            return False
    
    def test_data_structure(self):
        """测试数据结构完整性"""
        try:
            response = self.session.get(f"{self.base_url}/api/stocks")
            if response.status_code == 200:
                data = response.json()
                stocks = data.get('stocks', [])
                
                if not stocks:
                    self.log_test("数据结构", True, "暂无股票数据（正常状态）")
                    return True
                
                # 检查第一只股票的数据结构
                first_stock = stocks[0]
                required_fields = [
                    'code', 'name', 'price', 'change', 'change_pct',
                    'yearly_low', 'low_date', 'distance_from_low_pct',
                    'pb_ratio', 'pe_ratio', 'dividend_yield', 'industry',
                    'quantity', 'security_type', 'update_time'
                ]
                
                missing_fields = [field for field in required_fields if field not in first_stock]
                
                if not missing_fields:
                    self.log_test("数据结构", True, 
                                f"数据结构完整，包含 {len(stocks)} 只股票",
                                f"字段数: {len(first_stock)}")
                    return True
                else:
                    self.log_test("数据结构", False, 
                                f"缺少字段: {missing_fields}",
                                f"现有字段: {list(first_stock.keys())}")
                    return False
            else:
                self.log_test("数据结构", False, f"API调用失败: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("数据结构", False, f"测试异常: {e}")
            return False
    
    def test_html_template(self):
        """测试HTML模板功能"""
        try:
            response = self.session.get(f"{self.base_url}/")
            if response.status_code == 200:
                html_content = response.text
                
                # 检查关键元素
                key_elements = [
                    'V10 表格增强版',  # 版本标识
                    'sortable',        # 排序功能
                    'columnModal',     # 列设置弹窗
                    'SortableJS',      # 拖拽库
                    'localStorage'     # 本地存储
                ]
                
                missing_elements = []
                for element in key_elements:
                    if element not in html_content:
                        missing_elements.append(element)
                
                if not missing_elements:
                    self.log_test("HTML模板", True, "V10增强功能已正确集成")
                    return True
                else:
                    self.log_test("HTML模板", False, 
                                f"缺少关键元素: {missing_elements}")
                    return False
            else:
                self.log_test("HTML模板", False, f"页面加载失败: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("HTML模板", False, f"测试异常: {e}")
            return False
    
    def test_upload_functionality(self):
        """测试文件上传功能"""
        # 创建测试Excel文件
        test_data = {
            '代码': ['000001', '000002', '600000'],
            '名称': ['平安银行', '万科A', '浦发银行'],
            '数量': [1000, 2000, 1500]
        }
        
        test_file = 'test_holdings_v10.xlsx'
        try:
            df = pd.DataFrame(test_data)
            df.to_excel(test_file, index=False)
            
            # 上传文件
            with open(test_file, 'rb') as f:
                files = {'file': (test_file, f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
                response = self.session.post(f"{self.base_url}/api/upload-holdings", files=files)
            
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    self.log_test("文件上传功能", True, 
                                f"成功上传 {data['stock_count']} 只股票")
                    return True
                else:
                    self.log_test("文件上传功能", False, f"上传失败: {data['message']}")
                    return False
            else:
                self.log_test("文件上传功能", False, f"HTTP错误: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("文件上传功能", False, f"测试异常: {e}")
            return False
        finally:
            # 清理测试文件
            if os.path.exists(test_file):
                os.remove(test_file)
    
    def test_performance(self):
        """测试性能指标"""
        try:
            # 测试API响应时间
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/api/stocks")
            response_time = (time.time() - start_time) * 1000  # 转换为毫秒
            
            if response.status_code == 200:
                data = response.json()
                stock_count = len(data.get('stocks', []))
                
                # 性能基准：每100只股票响应时间应小于500ms
                expected_time = max(200, stock_count * 5)  # 基准时间
                
                if response_time <= expected_time:
                    self.log_test("性能测试", True, 
                                f"响应时间: {response_time:.1f}ms ({stock_count}只股票)")
                    return True
                else:
                    self.log_test("性能测试", False, 
                                f"响应时间过长: {response_time:.1f}ms (期望<{expected_time}ms)")
                    return False
            else:
                self.log_test("性能测试", False, f"API调用失败: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("性能测试", False, f"测试异常: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("=== 📊 持仓系统 V10 表格增强版集成测试 ===\n")
        
        tests = [
            ("服务器连接", self.test_server_startup),
            ("API兼容性", self.test_api_compatibility),
            ("缓存系统集成", self.test_cache_integration),
            ("数据结构完整性", self.test_data_structure),
            ("HTML模板功能", self.test_html_template),
            ("文件上传功能", self.test_upload_functionality),
            ("性能指标", self.test_performance)
        ]
        
        print("🚀 开始执行测试...\n")
        
        success_count = 0
        total_count = len(tests)
        
        for test_name, test_func in tests:
            print(f"🔍 执行测试: {test_name}")
            try:
                if test_func():
                    success_count += 1
            except Exception as e:
                self.log_test(test_name, False, f"测试执行异常: {e}")
            print()
        
        # 生成测试报告
        self.generate_report(success_count, total_count)
    
    def generate_report(self, success_count, total_count):
        """生成测试报告"""
        print("=" * 60)
        print("📋 测试报告")
        print("=" * 60)
        
        success_rate = (success_count / total_count) * 100
        print(f"✅ 成功: {success_count}/{total_count} ({success_rate:.1f}%)")
        print(f"❌ 失败: {total_count - success_count}")
        print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        print("\n📊 详细结果:")
        for result in self.test_results:
            status = "✅" if result['success'] else "❌"
            print(f"{status} {result['test_name']}: {result['message']}")
            if result['details']:
                print(f"   {result['details']}")
        
        # 保存测试报告
        report_file = f"v10_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                'summary': {
                    'total_tests': total_count,
                    'successful_tests': success_count,
                    'failed_tests': total_count - success_count,
                    'success_rate': success_rate,
                    'test_time': datetime.now().isoformat()
                },
                'results': self.test_results
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细报告已保存: {report_file}")
        
        if success_rate >= 80:
            print("\n🎉 测试通过！V10系统运行正常。")
        else:
            print("\n⚠️ 测试未完全通过，请检查失败的测试项。")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='持仓系统V10集成测试')
    parser.add_argument('--url', default='http://localhost:5000', help='服务器地址')
    parser.add_argument('--wait', type=int, default=5, help='启动等待时间（秒）')
    
    args = parser.parse_args()
    
    print(f"🔗 测试目标: {args.url}")
    print(f"⏱️ 等待服务器启动: {args.wait}秒")
    
    # 等待服务器启动
    time.sleep(args.wait)
    
    # 创建测试器并运行测试
    tester = V10IntegrationTester(args.url)
    tester.run_all_tests()

if __name__ == '__main__':
    main()
