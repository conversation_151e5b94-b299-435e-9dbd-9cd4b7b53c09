#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单数据迁移脚本
================

从原系统迁移数据到新的模块化系统

使用方法:
python simple_migrate.py

作者: AI Assistant
版本: 1.0
"""

import os
import shutil
import json
from datetime import datetime


def migrate_data():
    """迁移数据"""
    
    print("=== 🔄 持仓系统数据迁移工具 ===")
    
    # 获取原系统路径
    print("\n请输入原系统目录的完整路径：")
    print("例如: C:\\Users\\<USER>\\Desktop\\持仓系统 - 副本 (4) - 副本")
    
    source_dir = input("原系统路径: ").strip().strip('"')
    
    if not source_dir:
        print("❌ 必须提供源目录路径")
        return False
    
    if not os.path.exists(source_dir):
        print(f"❌ 源目录不存在: {source_dir}")
        return False
    
    print(f"\n📂 源目录: {source_dir}")
    print(f"📂 目标目录: {os.getcwd()}")
    
    # 确认迁移
    confirm = input("\n是否开始迁移？(y/N): ")
    if confirm.lower() not in ['y', 'yes', '是']:
        print("❌ 迁移已取消")
        return False
    
    # 确保目标目录存在
    os.makedirs('data', exist_ok=True)
    os.makedirs('config', exist_ok=True)
    os.makedirs('cache', exist_ok=True)
    os.makedirs('uploads', exist_ok=True)
    
    migration_count = 0
    
    print(f"\n🔄 开始迁移数据...")
    
    # 定义要迁移的文件映射
    file_mappings = [
        # (源文件名, 目标路径, 描述)
        ('stock_data_cache.json', 'data/stock_data_cache.json', '股票数据缓存'),
        ('imported_stock_list.json', 'data/imported_stock_list.json', '导入股票列表'),
        ('strategy_config.json', 'config/strategy_config.json', '策略配置'),
        ('auto_update_config.json', 'data/auto_update_config.json', '自动更新配置'),
        ('wechat_alert_cache.pkl', 'data/wechat_alert_cache.pkl', '微信提醒缓存'),
        ('节假日缓存.json', 'data/节假日缓存.json', '节假日缓存'),
    ]
    
    # 迁移单个文件
    for source_filename, target_path, description in file_mappings:
        source_file = os.path.join(source_dir, source_filename)
        
        if os.path.exists(source_file):
            try:
                shutil.copy2(source_file, target_path)
                print(f"   ✅ {description}: {source_filename}")
                migration_count += 1
            except Exception as e:
                print(f"   ❌ {description} 迁移失败: {e}")
        else:
            print(f"   ⚠️ {description} 文件不存在: {source_filename}")
    
    # 迁移缓存目录
    cache_source = os.path.join(source_dir, 'cache')
    if os.path.exists(cache_source):
        print(f"\n📈 迁移缓存文件...")
        try:
            for file in os.listdir(cache_source):
                if file.endswith('.csv') and 'yearly_low_cache' in file:
                    source_file = os.path.join(cache_source, file)
                    target_file = os.path.join('cache', file)
                    shutil.copy2(source_file, target_file)
                    print(f"   ✅ 缓存文件: {file}")
                    migration_count += 1
        except Exception as e:
            print(f"   ❌ 缓存文件迁移失败: {e}")
    
    # 迁移上传目录
    uploads_source = os.path.join(source_dir, 'uploads')
    if os.path.exists(uploads_source):
        print(f"\n📁 迁移上传文件...")
        try:
            for file in os.listdir(uploads_source):
                source_file = os.path.join(uploads_source, file)
                target_file = os.path.join('uploads', file)
                if os.path.isfile(source_file):
                    shutil.copy2(source_file, target_file)
                    print(f"   ✅ 上传文件: {file}")
                    migration_count += 1
        except Exception as e:
            print(f"   ❌ 上传文件迁移失败: {e}")
    
    # 创建迁移报告
    report = {
        'migration_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'source_directory': source_dir,
        'target_directory': os.getcwd(),
        'migrated_files_count': migration_count,
        'status': 'completed'
    }
    
    with open('migration_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 数据迁移完成！")
    print(f"   📊 总共迁移了 {migration_count} 个文件")
    print(f"   📋 迁移报告已保存到: migration_report.json")
    
    return True


def check_migrated_data():
    """检查迁移的数据"""
    print(f"\n🔍 检查迁移的数据...")
    
    # 检查关键文件
    key_files = [
        ('data/stock_data_cache.json', '股票数据'),
        ('data/imported_stock_list.json', '导入列表'),
        ('config/strategy_config.json', '策略配置'),
    ]
    
    for file_path, description in key_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if isinstance(data, dict):
                        if 'stock_data' in data:
                            count = len(data['stock_data'])
                            print(f"   ✅ {description}: {count} 条记录")
                        elif 'imported_stock_list' in data:
                            count = len(data['imported_stock_list'])
                            print(f"   ✅ {description}: {count} 只股票")
                        else:
                            print(f"   ✅ {description}: 配置文件正常")
                    else:
                        print(f"   ✅ {description}: 数据文件正常")
            except Exception as e:
                print(f"   ❌ {description} 文件格式错误: {e}")
        else:
            print(f"   ⚠️ {description} 文件不存在")


def main():
    """主函数"""
    try:
        success = migrate_data()
        
        if success:
            check_migrated_data()
            
            print(f"\n🎉 迁移完成！接下来的步骤：")
            print(f"   1. 运行测试: python test_system.py")
            print(f"   2. 启动系统: python app.py")
            print(f"   3. 访问系统: http://localhost:5000")
        
        return success
        
    except KeyboardInterrupt:
        print(f"\n❌ 迁移被用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 迁移过程中发生错误: {e}")
        return False


if __name__ == '__main__':
    success = main()
    
    input(f"\n按回车键退出...")
    exit(0 if success else 1)
