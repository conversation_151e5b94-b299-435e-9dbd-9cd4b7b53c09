"""
扫雷宝评分采集器
专门用于采集和保存扫雷宝评分到本地文件
"""

import os
import json
import time
import re
from datetime import datetime
from PIL import Image, ImageEnhance
import pytesseract
import requests

class SLBScoreCollector:
    """扫雷宝评分采集器"""
    
    def __init__(self, data_file="slb_scores.json"):
        self.data_file = data_file
        self.scores = self.load_scores()
        self.setup_ocr()
    
    def setup_ocr(self):
        """设置OCR环境"""
        try:
            pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
            self.ocr_available = True
            print("✅ OCR环境已配置")
        except:
            self.ocr_available = False
            print("⚠️ OCR环境不可用")
    
    def load_scores(self):
        """加载已保存的评分"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    scores = json.load(f)
                print(f"✅ 加载已有评分: {len(scores)} 条")
                return scores
            except Exception as e:
                print(f"⚠️ 加载评分文件失败: {e}")
        
        # 初始化已知的准确评分
        initial_scores = {
            '300479': {
                'name': '神思电子',
                'score': 75.0,
                'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'method': 'manual_verified',
                'verified': True
            }
        }
        
        print("📝 创建新的评分数据库")
        return initial_scores
    
    def save_scores(self):
        """保存评分到文件"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.scores, f, ensure_ascii=False, indent=2)
            print(f"✅ 评分已保存到: {self.data_file}")
            return True
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False
    
    def add_manual_score(self, code, name, score, verified=False):
        """手动添加评分"""
        self.scores[code] = {
            'name': name,
            'score': float(score),
            'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'method': 'manual',
            'verified': verified
        }
        
        print(f"✅ 已添加: {name}({code}) = {score}分")
        self.save_scores()
    
    def get_score_from_screenshot(self, image_path):
        """从截图中识别评分"""
        if not self.ocr_available:
            print("❌ OCR不可用")
            return None
        
        try:
            image = Image.open(image_path)
            print(f"📸 分析截图: {image.size}")
            
            # 针对橙色圆形背景白色字体的优化配置
            ocr_configs = [
                '--psm 8 -c tessedit_char_whitelist=0123456789',  # 单词模式，只识别数字
                '--psm 10 -c tessedit_char_whitelist=0123456789', # 单字符模式
                '--psm 6 -c tessedit_char_whitelist=0123456789分', # 包含"分"字
                '--psm 7 -c tessedit_char_whitelist=0123456789',  # 单行模式
            ]
            
            all_scores = []
            
            # 原图识别
            for i, config in enumerate(ocr_configs):
                try:
                    text = pytesseract.image_to_string(image, config=config)
                    scores = self.extract_scores_from_text(text)
                    if scores:
                        print(f"  配置{i+1}: {text.strip()} -> {scores}")
                        all_scores.extend(scores)
                except:
                    continue
            
            # 增强对比度后识别
            try:
                enhancer = ImageEnhance.Contrast(image)
                enhanced = enhancer.enhance(3.0)  # 高对比度
                
                text = pytesseract.image_to_string(enhanced, config='--psm 8 -c tessedit_char_whitelist=0123456789')
                scores = self.extract_scores_from_text(text)
                if scores:
                    print(f"  增强对比度: {text.strip()} -> {scores}")
                    all_scores.extend(scores)
            except:
                pass
            
            # 分析结果
            if all_scores:
                valid_scores = [s for s in all_scores if 50 <= s <= 100]
                if valid_scores:
                    from collections import Counter
                    final_score = Counter(valid_scores).most_common(1)[0][0]
                    print(f"🎯 识别评分: {final_score}分")
                    return float(final_score)
            
            return None
            
        except Exception as e:
            print(f"❌ 截图识别失败: {e}")
            return None
    
    def extract_scores_from_text(self, text):
        """从文本中提取分数"""
        scores = []
        patterns = [r'(\d{1,3})分', r'(\d{1,3})\s*分', r'(\d{2,3})']
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                try:
                    score = int(match)
                    if 0 <= score <= 100:
                        scores.append(score)
                except:
                    continue
        
        return scores
    
    def collect_score_with_selenium(self, stock_code):
        """使用Selenium采集评分"""
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            from selenium.webdriver.chrome.service import Service
            from webdriver_manager.chrome import ChromeDriverManager
            
            # 配置浏览器
            options = Options()
            options.add_argument('--headless')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--window-size=1200,800')
            
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=options)
            
            try:
                # 访问扫雷宝页面
                url = f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code={stock_code}&color=0"
                print(f"🌐 访问: {stock_code}")
                driver.get(url)
                
                # 等待页面加载
                time.sleep(3)
                
                # 截图
                screenshot_path = f"temp_slb_{stock_code}.png"
                driver.save_screenshot(screenshot_path)
                
                # OCR识别
                score = self.get_score_from_screenshot(screenshot_path)
                
                # 清理截图
                try:
                    os.remove(screenshot_path)
                except:
                    pass
                
                return score
                
            finally:
                driver.quit()
                
        except Exception as e:
            print(f"❌ Selenium采集失败: {e}")
            return None
    
    def batch_collect(self, stock_codes):
        """批量采集评分"""
        print(f"🚀 开始批量采集 {len(stock_codes)} 只股票的评分")
        
        results = {}
        
        for i, code in enumerate(stock_codes):
            print(f"\n[{i+1}/{len(stock_codes)}] 处理: {code}")
            
            # 检查是否已有评分
            if code in self.scores:
                existing = self.scores[code]
                print(f"  已有评分: {existing['score']}分 ({existing['update_time']})")
                results[code] = existing['score']
                continue
            
            # 采集新评分
            score = self.collect_score_with_selenium(code)
            
            if score is not None:
                # 保存评分
                self.scores[code] = {
                    'name': f'股票{code}',
                    'score': score,
                    'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'method': 'selenium_ocr',
                    'verified': False
                }
                
                results[code] = score
                print(f"✅ {code}: {score}分")
                
                # 实时保存
                self.save_scores()
            else:
                print(f"❌ {code}: 采集失败")
                results[code] = None
            
            # 间隔
            time.sleep(2)
        
        return results
    
    def show_scores(self):
        """显示所有评分"""
        if not self.scores:
            print("📊 暂无评分数据")
            return
        
        print(f"\n📊 扫雷宝评分数据库 ({len(self.scores)} 条)")
        print("=" * 70)
        print(f"{'股票名称':12} {'代码':8} {'评分':6} {'更新时间':20} {'方法':12} {'验证':4}")
        print("-" * 70)
        
        for code, info in sorted(self.scores.items()):
            verified = "✅" if info.get('verified', False) else "⚠️"
            print(f"{info['name']:12} {code:8} {info['score']:6.1f} {info['update_time']:20} {info['method']:12} {verified:4}")
    
    def export_to_csv(self, filename="slb_scores.csv"):
        """导出为CSV文件"""
        try:
            import pandas as pd
            
            data = []
            for code, info in self.scores.items():
                data.append({
                    '股票代码': code,
                    '股票名称': info['name'],
                    '扫雷宝评分': info['score'],
                    '更新时间': info['update_time'],
                    '采集方法': info['method'],
                    '已验证': info.get('verified', False)
                })
            
            df = pd.DataFrame(data)
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"✅ 已导出到: {filename}")
            
        except ImportError:
            print("❌ 需要安装pandas: pip install pandas")
        except Exception as e:
            print(f"❌ 导出失败: {e}")

def main():
    """主函数"""
    collector = SLBScoreCollector()
    
    while True:
        print("\n🎯 扫雷宝评分采集器")
        print("=" * 40)
        print("1. 查看已有评分")
        print("2. 手动添加评分")
        print("3. 批量采集评分")
        print("4. 单个采集评分")
        print("5. 导出CSV文件")
        print("6. 退出")
        
        try:
            choice = input("\n请选择功能 (1-6): ").strip()
            
            if choice == "1":
                collector.show_scores()
                
            elif choice == "2":
                code = input("股票代码 (6位): ").strip()
                if not re.match(r'^\d{6}$', code):
                    print("❌ 请输入6位数字")
                    continue
                
                name = input("股票名称: ").strip()
                score_str = input("扫雷宝评分 (0-100): ").strip()
                
                try:
                    score = float(score_str)
                    if 0 <= score <= 100:
                        verified = input("是否已验证? (y/n): ").strip().lower() == 'y'
                        collector.add_manual_score(code, name, score, verified)
                    else:
                        print("❌ 评分必须在0-100之间")
                except ValueError:
                    print("❌ 请输入有效数字")
                    
            elif choice == "3":
                codes_str = input("股票代码列表 (用逗号分隔): ").strip()
                codes = [c.strip() for c in codes_str.split(',') if re.match(r'^\d{6}$', c.strip())]
                
                if codes:
                    collector.batch_collect(codes)
                else:
                    print("❌ 请输入有效的股票代码")
                    
            elif choice == "4":
                code = input("股票代码 (6位): ").strip()
                if re.match(r'^\d{6}$', code):
                    score = collector.collect_score_with_selenium(code)
                    if score:
                        collector.add_manual_score(code, f'股票{code}', score)
                else:
                    print("❌ 请输入6位数字")
                    
            elif choice == "5":
                filename = input("CSV文件名 (默认slb_scores.csv): ").strip()
                if not filename:
                    filename = "slb_scores.csv"
                collector.export_to_csv(filename)
                
            elif choice == "6":
                print("👋 再见！")
                break
                
            else:
                print("❌ 无效选择")
                
        except KeyboardInterrupt:
            print("\n👋 用户取消操作")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")

if __name__ == "__main__":
    main()
