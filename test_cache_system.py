#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
年内最低价缓存系统测试脚本
"""

import os
import pandas as pd
from datetime import datetime
from yearly_low_cache_system import YearlyLowCacheSystem
from yearly_low_cache_reader import YearlyLow<PERSON>acheReader, get_yearly_low_from_cache, check_cache_status

def test_cache_system():
    """测试完整的缓存系统"""
    
    print("=== 📊 年内最低价缓存系统测试 ===\n")
    
    # 1. 创建测试股票列表
    print("1️⃣ 创建测试股票列表...")
    test_stocks = [
        {'代码': '000001', '名称': '平安银行'},
        {'代码': '000002', '名称': '万科A'},
        {'代码': '600000', '名称': '浦发银行'},
        {'代码': '600036', '名称': '招商银行'},
        {'代码': '000858', '名称': '五粮液'}
    ]
    
    test_file = 'test_stocks.csv'
    df = pd.DataFrame(test_stocks)
    df.to_csv(test_file, index=False, encoding='utf-8-sig')
    print(f"✅ 创建测试股票列表: {test_file} ({len(test_stocks)} 只股票)")
    
    # 2. 测试缓存生成系统
    print("\n2️⃣ 测试缓存生成系统...")
    cache_system = YearlyLowCacheSystem()
    
    # 获取缓存信息
    info = cache_system.get_cache_info()
    print(f"📋 当前缓存状态: {'存在' if info['exists'] else '不存在'}")
    
    # 生成缓存（使用测试股票列表）
    print("🚀 开始生成缓存...")
    success = cache_system.fetch_all_yearly_lows(test_stocks, force_refresh=True)
    
    if success:
        print("✅ 缓存生成成功")
        
        # 显示缓存信息
        info = cache_system.get_cache_info()
        if info['exists'] and 'total_count' in info:
            print(f"📊 缓存统计: 总数 {info['total_count']}, 成功 {info['success_count']}, 失败 {info['failed_count']}")
            print(f"📈 成功率: {info['success_rate']:.1f}%")
    else:
        print("❌ 缓存生成失败")
        return False
    
    # 3. 测试缓存读取系统
    print("\n3️⃣ 测试缓存读取系统...")
    reader = YearlyLowCacheReader()
    
    # 检查缓存状态
    status = reader.get_cache_status()
    print(f"📋 缓存状态: {status['message']}")
    
    if status['exists']:
        print(f"📊 缓存数据: 总数 {status['total_count']}, 成功 {status['success_count']}")
        
        # 测试读取数据
        print("\n🔍 测试读取数据:")
        for stock in test_stocks:
            code = stock['代码']
            name = stock['名称']
            
            result = reader.get_yearly_low(code)
            if result['success']:
                print(f"   ✅ {code} {name}: 最低价 {result['yearly_low']:.2f} ({result['low_date']})")
            else:
                print(f"   ❌ {code} {name}: {result['message']}")
    
    # 4. 测试便捷函数
    print("\n4️⃣ 测试便捷函数...")
    
    # 测试状态检查
    status = check_cache_status()
    print(f"📋 便捷函数状态检查: {'成功' if status['exists'] else '失败'}")
    
    # 测试数据获取
    if status['exists']:
        test_code = '000001'
        result = get_yearly_low_from_cache(test_code)
        if result['success']:
            print(f"✅ 便捷函数获取 {test_code}: 最低价 {result['yearly_low']:.2f}")
        else:
            print(f"❌ 便捷函数获取 {test_code}: {result['message']}")
    
    # 5. 测试主系统集成
    print("\n5️⃣ 测试主系统集成...")
    try:
        # 导入主系统的函数
        from 持仓系统_v9_移动端适配版 import get_yearly_low_price
        
        test_code = '000001'
        result = get_yearly_low_price(test_code)
        
        if result['yearly_low'] is not None:
            print(f"✅ 主系统集成测试成功: {test_code} 最低价 {result['yearly_low']:.2f}")
        else:
            print(f"⚠️ 主系统集成测试: {test_code} 未获取到数据")
            
    except ImportError as e:
        print(f"⚠️ 无法导入主系统模块: {e}")
    except Exception as e:
        print(f"❌ 主系统集成测试失败: {e}")
    
    # 6. 清理测试文件
    print("\n6️⃣ 清理测试文件...")
    try:
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"🗑️ 删除测试文件: {test_file}")
    except Exception as e:
        print(f"⚠️ 清理测试文件失败: {e}")
    
    print("\n✅ 缓存系统测试完成！")
    return True

def test_performance():
    """测试性能"""
    print("\n=== ⚡ 性能测试 ===")
    
    reader = YearlyLowCacheReader()
    
    # 测试加载时间
    start_time = datetime.now()
    success = reader.load_cache()
    load_time = (datetime.now() - start_time).total_seconds()
    
    if success:
        print(f"📊 缓存加载时间: {load_time:.3f} 秒")
        
        # 测试查询时间
        test_codes = ['000001', '000002', '600000', '600036', '000858']
        
        start_time = datetime.now()
        for code in test_codes:
            reader.get_yearly_low(code)
        query_time = (datetime.now() - start_time).total_seconds()
        
        print(f"🔍 查询 {len(test_codes)} 只股票耗时: {query_time:.3f} 秒")
        print(f"⚡ 平均查询时间: {query_time/len(test_codes)*1000:.1f} 毫秒/股票")
    else:
        print("❌ 缓存加载失败，无法进行性能测试")

def main():
    """主函数"""
    try:
        # 基本功能测试
        success = test_cache_system()
        
        if success:
            # 性能测试
            test_performance()
        
        print(f"\n🎉 测试完成！")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
