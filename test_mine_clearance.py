import requests
import re
import json

def analyze_mine_clearance_response(code):
    try:
        api_url = f'http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code={code}&color=0'
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Referer': 'http://page3.tdx.com.cn:7615/',
        }
        
        response = requests.get(api_url, headers=headers, timeout=10)
        content = response.text
        
        print(f'=== 分析股票 {code} 的响应内容 ===')
        
        # 查找可能包含分数的关键词
        keywords = ['分数', '评分', 'score', '排雷', '风险', '安全', '等级']
        for keyword in keywords:
            if keyword in content:
                print(f'找到关键词: {keyword}')
                # 查找关键词周围的内容
                pattern = f'.{{0,50}}{keyword}.{{0,50}}'
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches[:3]:  # 只显示前3个匹配
                    print(f'  上下文: {match}')
        
        # 查找JavaScript变量或数据
        js_patterns = [
            r'var\s+\w*score\w*\s*=\s*([0-9.]+)',
            r'"score"\s*:\s*([0-9.]+)',
            r'score\s*:\s*([0-9.]+)',
            r'data\s*:\s*\{[^}]*score[^}]*\}',
            r'window\.\w+\s*=\s*\{[^}]*\}',
        ]
        
        print('\n查找JavaScript数据:')
        for pattern in js_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                print(f'模式 {pattern}: {matches}')
        
        # 查找所有数字（可能的分数）
        numbers = re.findall(r'\b([0-9]{1,2}\.?[0-9]*)\b', content)
        reasonable_scores = [float(n) for n in numbers if n and 0 <= float(n) <= 100]
        if reasonable_scores:
            print(f'\n可能的分数: {reasonable_scores[:10]}')  # 只显示前10个
            
        # 查找特定的HTML结构
        html_patterns = [
            r'<span[^>]*>([0-9.]+)</span>',
            r'<div[^>]*score[^>]*>([^<]+)</div>',
            r'<td[^>]*>([0-9.]+)</td>',
        ]
        
        print('\n查找HTML结构:')
        for pattern in html_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                print(f'HTML模式: {matches[:5]}')
                
        # 保存完整响应到文件以便进一步分析
        with open(f'mine_response_{code}.html', 'w', encoding='utf-8') as f:
            f.write(content)
        print(f'\n完整响应已保存到 mine_response_{code}.html')
                
        return content
        
    except Exception as e:
        print(f'错误: {e}')
        return None

if __name__ == '__main__':
    # 分析一个股票的响应
    analyze_mine_clearance_response('000001')
