from flask import Flask, render_template, jsonify
import akshare as ak
import pandas as pd
import json
from datetime import datetime
import threading
import time
import traceback

app = Flask(__name__)

# 全局变量存储股票数据
stock_data = {}
last_update_time = None

def load_stock_list():
    """加载股票列表"""
    try:
        df = pd.read_csv('stocks_list.csv', encoding='utf-8-sig')
        return df.to_dict('records')
    except Exception as e:
        print(f"加载股票列表失败: {e}")
        return []

def get_all_stocks_realtime_data():
    """批量获取所有A股实时数据 - 只调用一次接口"""
    try:
        print("正在获取所有A股实时数据...")
        # 使用stock_zh_a_spot接口，一次性获取所有A股数据
        df = ak.stock_zh_a_spot()
        print(f"成功获取 {len(df)} 只股票的实时数据")
        return df
    except Exception as e:
        print(f"获取股票实时数据失败: {e}")
        return None

def update_all_stocks():
    """更新所有股票数据 - 先获取全部数据，再筛选持仓股票"""
    global stock_data, last_update_time

    # 加载我们的持仓股票列表
    stock_list = load_stock_list()
    # 确保代码格式统一（6位数字，前面补0）
    our_stock_codes = set()
    for stock in stock_list:
        code = str(stock['代码']).zfill(6)  # 补齐到6位
        our_stock_codes.add(code)
    print(f"持仓股票数量: {len(our_stock_codes)}")
    print(f"股票代码示例: {list(our_stock_codes)[:5]}")

    try:
        # 一次性批量获取所有A股实时数据
        print("正在获取所有A股实时数据...")
        df = get_all_stocks_realtime_data()
        if df is None:
            print("无法获取股票数据")
            return

        print(f"成功获取 {len(df)} 只股票数据，开始筛选持仓股票...")

        # 将代码转换为字符串类型并统一格式（6位数字）
        df['代码'] = df['代码'].astype(str).str.zfill(6)

        # 筛选出我们持仓的股票
        our_stocks_df = df[df['代码'].isin(our_stock_codes)]
        print(f"匹配到 {len(our_stocks_df)} 只持仓股票")

        # 清空旧数据
        stock_data.clear()

        # 处理匹配到的股票数据
        for _, row in our_stocks_df.iterrows():
            code = str(row['代码'])
            try:
                stock_data[code] = {
                    'code': code,
                    'name': str(row['名称']),
                    'price': float(row['最新价']) if pd.notna(row['最新价']) else 0,
                    'change': float(row['涨跌额']) if pd.notna(row['涨跌额']) else 0,
                    'change_pct': float(row['涨跌幅']) if pd.notna(row['涨跌幅']) else 0,
                    'volume': int(float(row['成交量'])) if pd.notna(row['成交量']) else 0,
                    'amount': float(row['成交额']) if pd.notna(row['成交额']) else 0,
                    'high': float(row['最高']) if pd.notna(row['最高']) else 0,
                    'low': float(row['最低']) if pd.notna(row['最低']) else 0,
                    'open': float(row['今开']) if pd.notna(row['今开']) else 0,
                    'yesterday_close': float(row['昨收']) if pd.notna(row['昨收']) else 0,
                    'update_time': datetime.now().strftime('%H:%M:%S')
                }
            except (ValueError, TypeError) as e:
                print(f"处理股票 {code} 数据时出错: {e}")
                continue

        # 处理没有匹配到的股票（显示为无数据）
        matched_codes = set(our_stocks_df['代码'].astype(str))
        missing_codes = our_stock_codes - matched_codes

        if missing_codes:
            print(f"未找到数据的股票: {len(missing_codes)} 只")
            # 从原始股票列表中获取名称
            stock_name_map = {str(stock['代码']): stock['名称'] for stock in stock_list}

            for code in missing_codes:
                stock_data[code] = {
                    'code': code,
                    'name': stock_name_map.get(code, '未知'),
                    'price': 0,
                    'change': 0,
                    'change_pct': 0,
                    'volume': 0,
                    'amount': 0,
                    'high': 0,
                    'low': 0,
                    'open': 0,
                    'yesterday_close': 0,
                    'update_time': datetime.now().strftime('%H:%M:%S'),
                    'error': '暂无数据'
                }

        last_update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"股票数据更新完成，成功处理 {len(stock_data)} 只股票，更新时间: {last_update_time}")

    except Exception as e:
        print(f"更新股票数据失败: {e}")
        import traceback
        traceback.print_exc()

def background_update():
    """后台定时更新股票数据"""
    while True:
        update_all_stocks()
        time.sleep(60)  # 每60秒更新一次，避免过于频繁调用

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/stocks')
def get_stocks():
    """获取所有股票数据API"""
    return jsonify({
        'stocks': list(stock_data.values()),
        'last_update': last_update_time,
        'total_count': len(stock_data)
    })

@app.route('/api/stock/<stock_code>')
def get_stock(stock_code):
    """获取单只股票数据API"""
    if stock_code in stock_data:
        return jsonify(stock_data[stock_code])
    else:
        return jsonify({'error': '股票代码不存在'}), 404

if __name__ == '__main__':
    print("=== 启动股票实时行情系统 ===")

    # 初始加载数据
    print("正在初始化数据...")
    update_all_stocks()

    # 启动后台更新线程
    print("启动后台更新线程...")
    update_thread = threading.Thread(target=background_update, daemon=True)
    update_thread.start()

    print("启动Flask Web服务...")
    print("访问 http://localhost:5000 查看股票行情")

    # 启动Flask应用
    app.run(debug=True, host='0.0.0.0', port=5000)
