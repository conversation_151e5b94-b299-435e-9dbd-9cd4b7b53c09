#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持仓系统 V10 - 表格增强版
==========================

主要功能:
1. 股票实时数据监控
2. 持仓管理和分析
3. 移动端响应式设计
4. 智能交易时间检测
5. 企业微信提醒功能
6. 数据可视化展示
7. 通达信扫雷风险评估

版本特性:
- 移动端优化界面
- 响应式表格设计
- 触摸友好的操作
- 自适应布局
- 新增扫雷风险评估列

作者: AI Assistant
版本: 10.0
日期: 2025-01-22
"""

from flask import Flask, render_template, jsonify, request
import requests
import time
import json
import pandas as pd
import os
from datetime import datetime, timedelta
import threading
from urllib.parse import urlencode
from werkzeug.utils import secure_filename
from wechat_alert import WeChatAlert
from A股交易时间监测_简化版 import AStockTradingTimeMonitor
from tdx_scan_module import TdxScanClient

app = Flask(__name__)

# 配置文件上传
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
ALLOWED_EXTENSIONS = {'xlsx', 'xls'}

# 确保上传目录存在
if not os.path.exists(app.config['UPLOAD_FOLDER']):
    os.makedirs(app.config['UPLOAD_FOLDER'])

# 全局变量存储股票数据
stock_data = {}
last_update_time = None
imported_stock_list = []  # 存储导入的股票列表

# 初始化扫雷客户端
scan_client = TdxScanClient(cache_duration=1800)  # 缓存30分钟

# 配置
CONFIG = {
    'stock_interval': 0.1,      # 每只股票间隔时间（秒）
    'round_interval': 600,      # 每轮更新间隔时间（秒）
    'request_timeout': 15,      # 请求超时时间（秒）
    'max_retries': 3,           # 最大重试次数
    'enable_wechat': True,      # 是否启用企业微信提醒
    'alert_threshold': 15.0,    # 涨幅提醒阈值（%）
    'scan_update_interval': 1800,  # 扫雷数据更新间隔（秒）
}

# 初始化企业微信提醒
try:
    wechat_alert = WeChatAlert("YOUR_WEBHOOK_URL_HERE")  # 请替换为实际的webhook地址
except Exception as e:
    print(f"企业微信初始化失败: {e}")
    wechat_alert = None

# 初始化交易时间监控
trading_monitor = AStockTradingTimeMonitor()

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_yearly_low_data(stock_code):
    """获取股票一年内最低价数据"""
    try:
        # 根据股票代码确定市场
        if stock_code.startswith('6'):
            market = '1'  # 上海
        elif stock_code.startswith(('0', '3')):
            market = '0'  # 深圳
        else:
            return {'yearly_low': None, 'low_date': None, 'data_points': 0}
        
        # 构建请求URL - 获取日K线数据
        secid = f"{market}.{stock_code}"
        
        # 获取一年的数据（约250个交易日）
        url = "http://push2his.eastmoney.com/api/qt/stock/kline/get"
        params = {
            'secid': secid,
            'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
            'fields1': 'f1,f2,f3,f4,f5,f6',
            'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
            'klt': '101',  # 日K线
            'fqt': '1',    # 前复权
            'end': '20500101',
            'lmt': '250'   # 获取250个交易日数据
        }
        
        response = requests.get(url, params=params, timeout=CONFIG['request_timeout'])
        response.raise_for_status()
        
        data = response.json()
        
        if data.get('rc') == 0 and data.get('data') and data['data'].get('klines'):
            klines = data['data']['klines']
            
            # 解析K线数据，找到最低价
            min_price = float('inf')
            min_date = None
            
            for kline in klines:
                parts = kline.split(',')
                if len(parts) >= 6:
                    date = parts[0]
                    low_price = float(parts[4])  # 最低价
                    
                    if low_price < min_price:
                        min_price = low_price
                        min_date = date
            
            return {
                'yearly_low': min_price if min_price != float('inf') else None,
                'low_date': min_date,
                'data_points': len(klines)
            }
        
        return {'yearly_low': None, 'low_date': None, 'data_points': 0}
        
    except Exception as e:
        print(f"获取 {stock_code} 一年最低价失败: {e}")
        return {'yearly_low': None, 'low_date': None, 'data_points': 0}

def get_eastmoney_stock_data_v2(stock_code):
    """
    获取东方财富股票数据 - 第二版，包含股息率
    """
    # 根据股票代码确定市场
    if stock_code.startswith('6'):
        market = '1'  # 上海
    elif stock_code.startswith(('0', '3')):
        market = '0'  # 深圳
    else:
        return None
    
    try:
        # 构建请求URL
        secid = f"{market}.{stock_code}"
        url = "http://push2.eastmoney.com/api/qt/stock/get"
        params = {
            'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
            'invt': '2',
            'fltt': '2',
            'fields': 'f43,f57,f58,f169,f170,f46,f44,f51,f168,f47,f164,f163,f116,f60,f45,f52,f50,f48,f167,f117,f71,f161,f49,f530,f135,f136,f137,f138,f139,f141,f142,f144,f145,f147,f148,f140,f143,f146,f149,f55,f62,f162,f92,f173,f104,f105,f84,f85,f183,f184,f185,f186,f187,f188,f189,f190,f191,f192,f107,f111,f86,f177,f78,f110,f262,f263,f264,f267,f268,f250,f251,f252,f253,f254,f255,f256,f257,f258,f266,f269,f270,f271,f273,f274,f275,f127,f199,f128,f198,f259,f260,f261,f171,f277,f278,f279,f288,f152,f250,f251,f252,f253,f254,f255,f256,f257,f258',
            'secid': secid
        }
        
        response = requests.get(url, params=params, timeout=CONFIG['request_timeout'])
        response.raise_for_status()
        
        data = response.json()
        
        if data.get('rc') == 0 and data.get('data'):
            stock_info = data['data']
            
            # 提取关键数据
            result = {
                'code': stock_code,
                'name': stock_info.get('f58', ''),
                'price': stock_info.get('f43', 0) / 100 if stock_info.get('f43') else 0,
                'change': stock_info.get('f169', 0) / 100 if stock_info.get('f169') else 0,
                'change_pct': stock_info.get('f170', 0) / 100 if stock_info.get('f170') else 0,
                'volume': stock_info.get('f47', 0),
                'turnover': stock_info.get('f48', 0) / 100 if stock_info.get('f48') else 0,
                'pe_ttm': stock_info.get('f162', 0) / 100 if stock_info.get('f162') else 0,
                'pb_ratio': stock_info.get('f173', 0) / 100 if stock_info.get('f173') else 0,
                'market_cap': stock_info.get('f116', 0) / 100000000 if stock_info.get('f116') else 0,  # 转换为亿
                'dividend_yield': stock_info.get('f187', 0) / 100 if stock_info.get('f187') else 0,  # 股息率
                'update_time': datetime.now().strftime('%H:%M:%S')
            }
            
            return result
            
    except Exception as e:
        print(f"获取 {stock_code} 数据失败: {e}")
        return None

def get_stock_industry_info(stock_code):
    """
    获取股票行业信息 - 使用单独的接口
    """
    # 根据股票代码确定市场
    if stock_code.startswith('6'):
        market = '1'  # 上海
    elif stock_code.startswith(('0', '3')):
        market = '0'  # 深圳
    else:
        return {'industry': '', 'sector': '', 'concept': ''}
    
    try:
        secid = f"{market}.{stock_code}"
        
        # 使用东方财富的股票详情接口
        url = "http://push2.eastmoney.com/api/qt/stock/get"
        params = {
            'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
            'invt': '2',
            'fltt': '2',
            'fields': 'f127,f116,f117',  # 行业相关字段
            'secid': secid
        }
        
        response = requests.get(url, params=params, timeout=CONFIG['request_timeout'])
        response.raise_for_status()
        
        data = response.json()
        
        if data.get('rc') == 0 and data.get('data'):
            stock_info = data['data']
            
            # 尝试从不同字段获取行业信息
            industry = stock_info.get('f127', '') or ''
            
            return {
                'industry': industry,
                'sector': industry,  # 暂时使用同一个值
                'concept': ''
            }
            
    except Exception as e:
        print(f"获取 {stock_code} 行业信息失败: {e}")
    
    return {'industry': '', 'sector': '', 'concept': ''}

def get_stock_realtime_and_valuation(stock_codes):
    """批量获取股票实时价格和估值数据"""
    
    # 构建股票代码列表
    secids = []
    for code in stock_codes:
        if code.startswith('6'):
            secids.append(f"1.{code}")
        elif code.startswith(('0', '3')):
            secids.append(f"0.{code}")
    
    if not secids:
        return {}
    
    try:
        # 批量获取实时数据
        url = "http://push2.eastmoney.com/api/qt/ulist.np/get"
        params = {
            'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
            'fltt': '2',
            'invt': '2',
            'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152,f45,f46,f47,f48,f49,f50,f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f116,f117,f162,f164,f167,f168,f169,f170,f171,f173,f183,f184,f185,f186,f187,f188,f189,f190,f191,f192',
            'secids': ','.join(secids)
        }
        
        response = requests.get(url, params=params, timeout=CONFIG['request_timeout'])
        response.raise_for_status()
        
        data = response.json()
        
        result = {}
        
        if data.get('rc') == 0 and data.get('data') and data['data'].get('diff'):
            for stock_info in data['data']['diff']:
                code = stock_info.get('f12', '')
                if code:
                    result[code] = {
                        'code': code,
                        'name': stock_info.get('f14', ''),
                        'price': stock_info.get('f2', 0) / 100 if stock_info.get('f2') else 0,
                        'change': stock_info.get('f4', 0) / 100 if stock_info.get('f4') else 0,
                        'change_pct': stock_info.get('f3', 0) / 100 if stock_info.get('f3') else 0,
                        'volume': stock_info.get('f5', 0),
                        'turnover': stock_info.get('f6', 0) / 100 if stock_info.get('f6') else 0,
                        'pe_ttm': stock_info.get('f162', 0) / 100 if stock_info.get('f162') else 0,
                        'pb_ratio': stock_info.get('f173', 0) / 100 if stock_info.get('f173') else 0,
                        'market_cap': stock_info.get('f116', 0) / 100000000 if stock_info.get('f116') else 0,
                        'dividend_yield': stock_info.get('f187', 0) / 100 if stock_info.get('f187') else 0,
                        'update_time': datetime.now().strftime('%H:%M:%S')
                    }
        
        return result
        
    except Exception as e:
        print(f"批量获取股票数据失败: {e}")
        return {}

def get_eastmoney_stock_data_v2(stock_code):
    """
    获取东方财富股票数据 - 第二版，包含股息率
    """
    # 根据股票代码确定市场
    if stock_code.startswith('6'):
        secid = f'1.{stock_code}'
    else:
        secid = f'0.{stock_code}'

    # 使用批量接口获取更准确的股息率数据
    url = 'http://push2.eastmoney.com/api/qt/ulist.np/get'

    # 请求参数 - 使用正确的股息率字段 f133
    params = {
        'fltt': '2',
        'invt': '2',
        'fields': 'f12,f14,f2,f3,f4,f23,f115,f114,f9,f133,f162,f173,f116,f127,f128,f129,f47,f48',
        'secids': secid,
        '_': str(int(time.time() * 1000))
    }

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'http://quote.eastmoney.com/',
    }

    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)

        if response.status_code == 200:
            data = json.loads(response.text)

            if 'data' in data and 'diff' in data['data'] and len(data['data']['diff']) > 0:
                stock_info = data['data']['diff'][0]

                # 处理股息率 - f133字段
                dividend_yield = stock_info.get('f133', 0)
                if dividend_yield == '-' or dividend_yield is None:
                    dividend_yield = 0
                else:
                    try:
                        dividend_yield = float(dividend_yield)
                    except:
                        dividend_yield = 0

                return {
                    'code': stock_info.get('f12', ''),
                    'name': stock_info.get('f14', ''),
                    'price': stock_info.get('f2', 0),
                    'change': stock_info.get('f4', 0),
                    'change_pct': stock_info.get('f3', 0),
                    'dividend_yield': dividend_yield,
                    'pb_ratio': stock_info.get('f23', None) if stock_info.get('f23') != '-' else None,
                    'pe_ttm': stock_info.get('f115', None) if stock_info.get('f115') != '-' else None,
                }

    except Exception as e:
        print(f"获取股票 {stock_code} 数据失败: {e}")
        return None

def get_stock_industry_info(stock_code):
    """
    获取股票行业信息 - 使用单独的接口
    """
    # 根据股票代码确定市场
    if stock_code.startswith('6'):
        secid = f'1.{stock_code}'
    else:
        secid = f'0.{stock_code}'

    url = 'http://push2.eastmoney.com/api/qt/stock/get'

    params = {
        'fltt': '2',
        'invt': '2',
        'fields': 'f57,f58,f84,f85,f86,f127,f116,f117',  # 行业相关字段
        'secid': secid,
        '_': str(int(time.time() * 1000))
    }

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'http://quote.eastmoney.com/',
    }

    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)

        if response.status_code == 200:
            data = json.loads(response.text)

            if 'data' in data and data['data']:
                stock_info = data['data']

                # 提取行业信息
                industry = stock_info.get('f127', '') or stock_info.get('f57', '') or ''
                sector = stock_info.get('f116', '') or stock_info.get('f58', '') or ''
                concept = stock_info.get('f117', '') or stock_info.get('f84', '') or ''

                return {
                    'industry': industry,
                    'sector': sector,
                    'concept': concept
                }

    except Exception as e:
        print(f"获取股票 {stock_code} 行业信息失败: {e}")

    return {'industry': '', 'sector': '', 'concept': ''}

def get_stock_realtime_and_valuation(stock_codes):
    """批量获取股票实时价格和估值数据"""

    # 构建股票代码列表
    secids = []
    for code in stock_codes:
        if code.startswith('6'):
            secids.append(f'1.{code}')
        else:
            secids.append(f'0.{code}')

    secids_str = ','.join(secids)

    url = 'http://push2.eastmoney.com/api/qt/ulist.np/get'

    params = {
        'fltt': '2',
        'invt': '2',
        'fields': 'f12,f14,f2,f3,f4,f23,f115,f114,f9,f133,f127',  # 添加f127行业字段
        'secids': secids_str,
        '_': str(int(time.time() * 1000))
    }

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'http://quote.eastmoney.com/',
    }

    try:
        response = requests.get(url, params=params, headers=headers, timeout=CONFIG['request_timeout'])

        if response.status_code == 200:
            data = json.loads(response.text)

            if 'data' in data and 'diff' in data['data']:
                results = {}

                for item in data['data']['diff']:
                    try:
                        code = item.get('f12', '')

                        # 处理数据
                        pb_ratio = item.get('f23', None)
                        if pb_ratio == '-':
                            pb_ratio = None

                        pe_ttm = item.get('f115', None)
                        if pe_ttm == '-':
                            pe_ttm = None

                        # 处理股息率
                        dividend_yield = item.get('f133', None)
                        if dividend_yield == '-' or dividend_yield == 0:
                            dividend_yield = 0

                        # 处理行业
                        industry = item.get('f127', '')
                        if industry == '-':
                            industry = ''

                        results[code] = {
                            'code': code,
                            'name': item.get('f14', ''),
                            'price': item.get('f2', 0),
                            'change': item.get('f4', 0),
                            'change_pct': item.get('f3', 0),
                            'pb_ratio': pb_ratio,
                            'pe_ttm': pe_ttm,
                            'dividend_yield': dividend_yield,  # 新增股息率
                            'industry': industry,              # 新增行业
                            'update_time': datetime.now().strftime('%H:%M:%S')
                        }

                    except Exception as e:
                        print(f"处理股票数据时出错: {e}")
                        continue

                return results

        return {}

    except Exception as e:
        print(f"批量获取股票数据失败: {e}")
        return {}

def get_yearly_low_data(stock_code):
    """获取股票一年内最低价数据"""
    try:
        # 根据股票代码确定市场
        if stock_code.startswith('6'):
            secid = f'1.{stock_code}'
        else:
            secid = f'0.{stock_code}'

        # 计算一年前的日期
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365)

        # 格式化日期
        start_date_str = start_date.strftime('%Y%m%d')
        end_date_str = end_date.strftime('%Y%m%d')

        # 构建请求URL
        url = 'http://push2his.eastmoney.com/api/qt/stock/kline/get'

        params = {
            'secid': secid,
            'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
            'fields1': 'f1,f2,f3,f4,f5,f6',
            'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
            'klt': '101',  # 日K线
            'fqt': '1',    # 前复权
            'beg': start_date_str,
            'end': end_date_str,
            '_': str(int(time.time() * 1000))
        }

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': 'http://quote.eastmoney.com/',
        }

        response = requests.get(url, params=params, headers=headers, timeout=10)

        if response.status_code == 200:
            data = json.loads(response.text)

            if 'data' in data and data['data'] and 'klines' in data['data']:
                klines = data['data']['klines']

                if klines:
                    # 解析K线数据，找到最低价
                    min_price = float('inf')
                    min_date = None

                    for kline in klines:
                        parts = kline.split(',')
                        if len(parts) >= 6:
                            date_str = parts[0]  # 日期
                            low_price = float(parts[4])  # 最低价

                            if low_price < min_price:
                                min_price = low_price
                                min_date = date_str

                    if min_price != float('inf'):
                        return {
                            'yearly_low': min_price,
                            'low_date': min_date,
                            'data_points': len(klines)
                        }

    except Exception as e:
        print(f"获取股票 {stock_code} 一年内最低价失败: {e}")

    return {'yearly_low': None, 'low_date': None, 'data_points': 0}

def load_stock_list():
    """加载股票列表"""
    try:
        df = pd.read_csv('stocks_list.csv', encoding='utf-8-sig')
        return df.to_dict('records')
    except Exception as e:
        print(f"加载股票列表失败: {e}")
        return []

def update_all_stocks():
    """更新所有股票数据"""
    global stock_data, last_update_time

    stock_list = load_stock_list()
    stock_codes = [str(stock['代码']).zfill(6) for stock in stock_list]

    if not stock_codes:
        print("没有找到股票列表")
        return

    print(f"开始更新 {len(stock_codes)} 只股票数据...")

    # 批量获取实时数据
    realtime_data = get_stock_realtime_and_valuation(stock_codes)

    # 更新每只股票的数据
    for i, stock in enumerate(stock_list):
        code = str(stock['代码']).zfill(6)

        try:
            # 获取实时数据
            if code in realtime_data:
                stock_info = realtime_data[code]
            else:
                stock_info = get_eastmoney_stock_data_v2(code)

            if stock_info:
                # 获取一年最低价数据
                yearly_data = get_yearly_low_data(code)

                # 获取行业信息
                industry_info = get_stock_industry_info(code)

                # 获取扫雷分数
                scan_result = scan_client.get_stock_score(code)

                # 计算距离最低价的涨幅
                distance_from_low_pct = None
                if yearly_data['yearly_low'] and stock_info['price'] > 0:
                    distance_from_low_pct = ((stock_info['price'] - yearly_data['yearly_low']) / yearly_data['yearly_low']) * 100

                # 合并所有数据
                combined_data = {
                    **stock_info,
                    **yearly_data,
                    **industry_info,
                    'distance_from_low_pct': distance_from_low_pct,
                    'type': stock.get('类型', '股票'),
                    'holdings': stock.get('持仓', 0),
                    'market_value': stock.get('市值', 0),
                }

                # 添加扫雷数据
                if scan_result:
                    combined_data.update({
                        'scan_score': scan_result['score'],
                        'scan_risk_level': scan_result['risk_level'],
                        'scan_risk_color': scan_result['risk_color'],
                        'scan_triggered_risks': scan_result['triggered_risks'],
                        'scan_display_text': f"{scan_result['score']}分 ({scan_result['risk_level']})",
                        'scan_update_time': scan_result['update_time']
                    })
                else:
                    # 添加默认扫雷数据
                    combined_data.update({
                        'scan_score': 0,
                        'scan_risk_level': '数据异常',
                        'scan_risk_color': '#cccccc',
                        'scan_triggered_risks': 0,
                        'scan_display_text': '数据获取失败',
                        'scan_update_time': ''
                    })

                stock_data[code] = combined_data
                print(f"✓ {code} {stock_info['name']} - 价格: {stock_info['price']:.2f} - 扫雷: {combined_data['scan_display_text']}")
            else:
                print(f"✗ {code} 数据获取失败")

            # 控制请求频率
            if i < len(stock_list) - 1:
                time.sleep(CONFIG['stock_interval'])

        except Exception as e:
            print(f"✗ {code} 处理异常: {e}")
            continue

    last_update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"股票数据更新完成，共处理 {len(stock_data)} 只股票")

def send_wechat_alert(message):
    """发送企业微信提醒"""
    if not CONFIG['enable_wechat'] or not wechat_alert:
        return

    try:
        success = wechat_alert.send_message(message)
        if success:
            print(f"✓ 企业微信提醒发送成功: {message[:50]}...")
        else:
            print(f"✗ 企业微信提醒发送失败")
    except Exception as e:
        print(f"❌ 企业微信提醒发送失败: {e}")

def background_update():
    """后台智能更新股票数据 - 只在交易时间更新"""
    round_count = 0

    while True:
        round_count += 1

        # 检查是否为交易时间
        trading_status = trading_monitor.get_trading_status()

        if trading_status['is_trading']:
            print(f"\n=== 第 {round_count} 轮更新开始 ===")
            print(f"交易状态: {trading_status['status']}")

            # 更新股票数据
            update_all_stocks()

            # 检查高涨幅股票并发送提醒
            if CONFIG['enable_wechat']:
                try:
                    valid_stocks = [stock for stock in stock_data.values() if stock.get('distance_from_low_pct') is not None]
                    high_gain_stocks = [stock for stock in valid_stocks if stock['distance_from_low_pct'] > CONFIG['alert_threshold']]

                    if high_gain_stocks:
                        message = f"🚀 发现 {len(high_gain_stocks)} 只高涨幅股票:\n"
                        for stock in high_gain_stocks[:5]:  # 只显示前5只
                            message += f"• {stock['name']}({stock['code']}): +{stock['distance_from_low_pct']:.1f}%\n"
                        send_wechat_alert(message)
                except Exception as e:
                    print(f"检查高涨幅股票失败: {e}")

            print(f"=== 第 {round_count} 轮更新完成 ===\n")
        else:
            print(f"非交易时间 ({trading_status['status']})，跳过更新")

        # 等待下一轮更新
        update_interval = CONFIG['round_interval']
        print(f"等待 {update_interval} 秒后进行下一轮更新...")
        time.sleep(update_interval)

@app.route('/')
def index():
    """主页"""
    return render_template('持仓页面_v10.html')

@app.route('/api/stocks')
def get_stocks():
    """获取所有股票数据API"""
    return jsonify({
        'stocks': list(stock_data.values()),
        'last_update': last_update_time,
        'total_count': len(stock_data),
        'scan_cache_info': scan_client.get_cache_info()
    })

@app.route('/api/stock/<stock_code>')
def get_stock(stock_code):
    """获取单只股票数据API"""
    if stock_code in stock_data:
        return jsonify(stock_data[stock_code])
    else:
        return jsonify({'error': '股票代码不存在'}), 404

@app.route('/api/scan_score/<stock_code>')
def get_scan_score_api(stock_code):
    """获取股票扫雷分数API"""
    try:
        result = scan_client.get_stock_score(stock_code)
        if result:
            return jsonify(result)
        else:
            return jsonify({'error': f'无法获取 {stock_code} 的扫雷数据'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/scan_batch', methods=['POST'])
def get_scan_batch_api():
    """批量获取扫雷分数API"""
    try:
        data = request.get_json()
        stock_codes = data.get('codes', [])

        if not stock_codes:
            return jsonify({'error': '股票代码列表不能为空'}), 400

        results = scan_client.get_batch_scores(stock_codes)
        return jsonify(results)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/trading-status')
def get_trading_status():
    """获取A股交易状态API"""
    try:
        status = trading_monitor.get_trading_status()
        return jsonify({
            'success': True,
            'data': status,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/refresh-holidays')
def refresh_holidays():
    """刷新节假日数据API"""
    try:
        success = trading_monitor.refresh_current_year_holidays()
        if success:
            return jsonify({
                'success': True,
                'message': '节假日数据刷新成功',
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'message': '节假日数据刷新失败',
                'timestamp': datetime.now().isoformat()
            }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/refresh')
def refresh_data():
    """手动刷新数据"""
    try:
        threading.Thread(target=update_all_stocks, daemon=True).start()
        return jsonify({
            'success': True,
            'message': '数据刷新已启动',
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/wechat/test')
def test_wechat():
    """测试企业微信连接"""
    try:
        if not wechat_alert:
            return jsonify({
                'success': False,
                'message': '企业微信未正确初始化',
                'timestamp': datetime.now().isoformat()
            }), 500

        success = wechat_alert.test_connection()
        return jsonify({
            'success': success,
            'message': '企业微信连接测试成功' if success else '企业微信连接测试失败',
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/clear-data', methods=['POST'])
def clear_data():
    """清空数据API"""
    global stock_data, last_update_time

    try:
        stock_data.clear()
        last_update_time = None

        # 清空扫雷缓存
        scan_client.clear_cache()

        return jsonify({
            'success': True,
            'message': '数据已清空',
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/upload-holdings', methods=['POST'])
def upload_holdings():
    """上传持仓表格API"""
    global imported_stock_list

    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': '没有选择文件'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': '没有选择文件'}), 400

        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{timestamp}_{filename}"
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)

            try:
                # 读取Excel文件
                df = pd.read_excel(filepath)

                # 检查必要的列
                required_columns = ['代码', '名称']
                missing_columns = [col for col in required_columns if col not in df.columns]

                if missing_columns:
                    return jsonify({
                        'success': False,
                        'error': f'缺少必要的列: {", ".join(missing_columns)}',
                        'available_columns': list(df.columns)
                    }), 400

                # 处理数据
                processed_stocks = []
                for _, row in df.iterrows():
                    try:
                        stock_code = str(row['代码']).zfill(6)
                        stock_name = str(row['名称'])

                        # 基本股票信息
                        stock_info = {
                            '代码': stock_code,
                            '名称': stock_name,
                            '类型': row.get('类型', '股票'),
                            '持仓': float(row.get('持仓', 0)) if pd.notna(row.get('持仓', 0)) else 0,
                            '市值': float(row.get('市值', 0)) if pd.notna(row.get('市值', 0)) else 0,
                        }

                        processed_stocks.append(stock_info)

                    except Exception as e:
                        print(f"处理股票 {row.get('代码', 'Unknown')} 时出错: {e}")
                        continue

                if not processed_stocks:
                    return jsonify({'success': False, 'error': '没有有效的股票数据'}), 400

                # 保存到CSV文件
                df_processed = pd.DataFrame(processed_stocks)
                df_processed.to_csv('stocks_list.csv', index=False, encoding='utf-8-sig')

                # 更新全局变量
                imported_stock_list = processed_stocks

                # 删除临时文件
                try:
                    os.remove(filepath)
                except:
                    pass

                return jsonify({
                    'success': True,
                    'message': f'成功导入 {len(processed_stocks)} 只股票',
                    'data': {
                        'total_count': len(processed_stocks),
                        'stock_count': len([s for s in processed_stocks if s['类型'] == '股票']),
                        'bond_count': len([s for s in processed_stocks if s['类型'] == '可转债']),
                        'etf_count': len([s for s in processed_stocks if s['类型'] == 'ETF']),
                        'stocks': processed_stocks[:10]  # 返回前10只作为预览
                    },
                    'timestamp': datetime.now().isoformat()
                })

            except Exception as e:
                # 删除临时文件
                try:
                    os.remove(filepath)
                except:
                    pass

                return jsonify({
                    'success': False,
                    'error': f'文件处理失败: {str(e)}',
                    'timestamp': datetime.now().isoformat()
                }), 500

        else:
            return jsonify({'success': False, 'error': '不支持的文件格式，请上传 .xlsx 或 .xls 文件'}), 400

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/refresh-data', methods=['POST'])
def refresh_all_data():
    """重新获取数据API"""
    global stock_data, last_update_time, imported_stock_list

    try:
        # 清空现有数据
        stock_data.clear()
        last_update_time = None

        # 清空扫雷缓存
        scan_client.clear_cache()

        # 检查是否有导入的股票列表
        if not imported_stock_list:
            # 尝试从CSV文件加载
            imported_stock_list = load_stock_list()

        if not imported_stock_list:
            return jsonify({
                'success': False,
                'error': '没有股票列表，请先上传持仓表格',
                'timestamp': datetime.now().isoformat()
            }), 400

        # 启动后台更新
        threading.Thread(target=update_all_stocks, daemon=True).start()

        return jsonify({
            'success': True,
            'message': f'开始重新获取 {len(imported_stock_list)} 只股票的数据',
            'data': {
                'total_count': len(imported_stock_list),
                'stock_types': {
                    '股票': len([s for s in imported_stock_list if s.get('类型') == '股票']),
                    '可转债': len([s for s in imported_stock_list if s.get('类型') == '可转债']),
                    'ETF': len([s for s in imported_stock_list if s.get('类型') == 'ETF'])
                }
            },
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/wechat/summary')
def send_wechat_summary():
    """发送每日摘要到企业微信"""
    try:
        valid_stocks = [stock for stock in stock_data.values() if stock.get('distance_from_low_pct') is not None]
        if valid_stocks:
            # 计算统计数据
            total_count = len(valid_stocks)
            near_low_count = len([s for s in valid_stocks if s['distance_from_low_pct'] < 20])
            high_gain_count = len([s for s in valid_stocks if s['distance_from_low_pct'] > 50])

            # 构建摘要消息
            message = f"📊 每日持仓摘要 ({datetime.now().strftime('%Y-%m-%d')})\n"
            message += f"总股票数: {total_count}\n"
            message += f"接近最低价(<20%): {near_low_count}\n"
            message += f"高涨幅(>50%): {high_gain_count}\n"

            send_wechat_alert(message)

            return jsonify({'success': True, 'message': '每日摘要发送成功'})
        else:
            return jsonify({'success': False, 'message': '没有有效的股票数据'})
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/wechat/alert/<float:threshold>')
def manual_alert_check(threshold):
    """手动检查高涨幅股票并发送提醒"""
    try:
        valid_stocks = [stock for stock in stock_data.values() if stock.get('distance_from_low_pct') is not None]
        if valid_stocks:
            high_gain_stocks = [stock for stock in valid_stocks if stock['distance_from_low_pct'] > threshold]

            if high_gain_stocks:
                message = f"🚀 发现 {len(high_gain_stocks)} 只高涨幅股票(>{threshold}%):\n"
                for stock in high_gain_stocks[:10]:  # 最多显示10只
                    message += f"• {stock['name']}({stock['code']}): +{stock['distance_from_low_pct']:.1f}%\n"

                send_wechat_alert(message)

                return jsonify({
                    'success': True,
                    'message': f'发现 {len(high_gain_stocks)} 只高涨幅股票，提醒已发送',
                    'data': high_gain_stocks
                })
            else:
                return jsonify({
                    'success': True,
                    'message': f'没有发现涨幅超过 {threshold}% 的股票',
                    'data': []
                })
        else:
            return jsonify({'success': False, 'message': '没有有效的股票数据'})
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

if __name__ == '__main__':
    print("=" * 60)
    print("持仓系统 V10 - 表格增强版")
    print("=" * 60)
    print("新增功能:")
    print("✓ 通达信扫雷风险评估")
    print("✓ 扫雷分数实时显示")
    print("✓ 风险等级颜色标识")
    print("✓ 扫雷数据缓存优化")
    print("=" * 60)

    # 启动后台更新线程
    print("启动后台数据更新线程...")
    update_thread = threading.Thread(target=background_update, daemon=True)
    update_thread.start()

    # 启动Flask应用
    print("启动Web服务器...")
    print("访问地址: http://localhost:5000")
    print("=" * 60)

    app.run(host='0.0.0.0', port=5000, debug=False, threaded=True)
