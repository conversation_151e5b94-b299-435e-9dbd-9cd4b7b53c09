#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据持久化模块
==============

负责数据的保存和加载，包括：
- 股票数据持久化
- 导入列表持久化
- 自动更新配置持久化
- 策略配置持久化

作者: AI Assistant
版本: 1.0
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Optional, Any


class DataPersistence:
    """数据持久化管理器"""

    def __init__(self, data_dir='data'):
        self.data_dir = data_dir
        self.stock_data_file = os.path.join(data_dir, 'stock_data_cache.json')
        self.imported_list_file = os.path.join(data_dir, 'imported_stock_list.json')
        self.auto_update_config_file = os.path.join(data_dir, 'auto_update_config.json')
        
        # 确保数据目录存在
        os.makedirs(data_dir, exist_ok=True)
        
        # 初始化状态
        self.last_update_time = None
        self.auto_update_enabled = True

    def save_stock_data(self, stock_data: Dict, last_update_time: Optional[str] = None):
        """保存股票数据到文件"""
        try:
            if last_update_time is None:
                last_update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
            data_to_save = {
                'stock_data': stock_data,
                'last_update_time': last_update_time,
                'save_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            with open(self.stock_data_file, 'w', encoding='utf-8') as f:
                json.dump(data_to_save, f, ensure_ascii=False, indent=2)
            
            self.last_update_time = last_update_time
            print(f"✅ 股票数据已保存到 {self.stock_data_file}")
            return True
            
        except Exception as e:
            print(f"❌ 保存股票数据失败: {e}")
            return False

    def load_stock_data(self) -> tuple:
        """
        从文件加载股票数据
        返回: (stock_data, last_update_time, success)
        """
        try:
            if os.path.exists(self.stock_data_file):
                with open(self.stock_data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                stock_data = data.get('stock_data', {})
                last_update_time = data.get('last_update_time')
                
                print(f"📂 从文件加载股票数据，包含 {len(stock_data)} 只股票")
                print(f"   📅 最后更新时间: {last_update_time}")
                
                self.last_update_time = last_update_time
                return stock_data, last_update_time, True
            else:
                print("⚠️ 股票数据文件不存在")
                return {}, None, False
                
        except Exception as e:
            print(f"❌ 加载股票数据失败: {e}")
            return {}, None, False

    def save_imported_list(self, imported_stock_list: List[Dict]):
        """保存导入的股票列表"""
        try:
            data_to_save = {
                'imported_stock_list': imported_stock_list,
                'save_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            with open(self.imported_list_file, 'w', encoding='utf-8') as f:
                json.dump(data_to_save, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 导入列表已保存到 {self.imported_list_file}")
            return True
            
        except Exception as e:
            print(f"❌ 保存导入列表失败: {e}")
            return False

    def load_imported_list(self) -> List[Dict]:
        """从文件加载导入的股票列表"""
        try:
            if os.path.exists(self.imported_list_file):
                with open(self.imported_list_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                imported_list = data.get('imported_stock_list', [])
                print(f"📂 从文件加载导入列表，包含 {len(imported_list)} 只股票")
                return imported_list
            else:
                print("⚠️ 导入列表文件不存在")
                return []
                
        except Exception as e:
            print(f"❌ 加载导入列表失败: {e}")
            return []

    def save_auto_update_config(self, enabled: bool):
        """保存自动更新配置"""
        try:
            config_data = {
                'auto_update_enabled': enabled,
                'save_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            with open(self.auto_update_config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            self.auto_update_enabled = enabled
            print(f"✅ 自动更新配置已保存: {'启用' if enabled else '禁用'}")
            return True
            
        except Exception as e:
            print(f"❌ 保存自动更新配置失败: {e}")
            return False

    def load_auto_update_config(self) -> bool:
        """从文件加载自动更新配置"""
        try:
            if os.path.exists(self.auto_update_config_file):
                with open(self.auto_update_config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                enabled = data.get('auto_update_enabled', True)
                self.auto_update_enabled = enabled
                print(f"📂 从文件加载自动更新配置: {'启用' if enabled else '禁用'}")
                return enabled
            else:
                print("⚠️ 自动更新配置文件不存在，使用默认配置（启用）")
                self.auto_update_enabled = True
                return True
                
        except Exception as e:
            print(f"❌ 加载自动更新配置失败: {e}")
            self.auto_update_enabled = True
            return True

    def get_last_update_time(self) -> Optional[str]:
        """获取最后更新时间"""
        return self.last_update_time

    def set_last_update_time(self, update_time: str):
        """设置最后更新时间"""
        self.last_update_time = update_time

    def get_auto_update_enabled(self) -> bool:
        """获取自动更新状态"""
        return self.auto_update_enabled

    def set_auto_update_enabled(self, enabled: bool):
        """设置自动更新状态"""
        self.save_auto_update_config(enabled)

    def backup_data(self, backup_suffix: Optional[str] = None) -> bool:
        """备份当前数据"""
        try:
            if backup_suffix is None:
                backup_suffix = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            backup_files = [
                (self.stock_data_file, f"{self.stock_data_file}.backup_{backup_suffix}"),
                (self.imported_list_file, f"{self.imported_list_file}.backup_{backup_suffix}"),
                (self.auto_update_config_file, f"{self.auto_update_config_file}.backup_{backup_suffix}")
            ]
            
            backup_count = 0
            for source, backup in backup_files:
                if os.path.exists(source):
                    import shutil
                    shutil.copy2(source, backup)
                    backup_count += 1
            
            print(f"✅ 数据备份完成，备份了 {backup_count} 个文件")
            return True
            
        except Exception as e:
            print(f"❌ 数据备份失败: {e}")
            return False

    def restore_data(self, backup_suffix: str) -> bool:
        """从备份恢复数据"""
        try:
            backup_files = [
                (f"{self.stock_data_file}.backup_{backup_suffix}", self.stock_data_file),
                (f"{self.imported_list_file}.backup_{backup_suffix}", self.imported_list_file),
                (f"{self.auto_update_config_file}.backup_{backup_suffix}", self.auto_update_config_file)
            ]
            
            restore_count = 0
            for backup, target in backup_files:
                if os.path.exists(backup):
                    import shutil
                    shutil.copy2(backup, target)
                    restore_count += 1
            
            print(f"✅ 数据恢复完成，恢复了 {restore_count} 个文件")
            
            # 重新加载配置
            self.load_auto_update_config()
            
            return True
            
        except Exception as e:
            print(f"❌ 数据恢复失败: {e}")
            return False

    def get_data_info(self) -> Dict[str, Any]:
        """获取数据文件信息"""
        info = {}
        
        files = [
            ('stock_data', self.stock_data_file),
            ('imported_list', self.imported_list_file),
            ('auto_update_config', self.auto_update_config_file)
        ]
        
        for name, filepath in files:
            if os.path.exists(filepath):
                stat = os.stat(filepath)
                info[name] = {
                    'exists': True,
                    'size': stat.st_size,
                    'modified_time': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                }
            else:
                info[name] = {
                    'exists': False,
                    'size': 0,
                    'modified_time': None
                }
        
        return info

    def clean_old_backups(self, keep_count: int = 5) -> int:
        """清理旧的备份文件，保留最新的几个"""
        try:
            backup_files = []
            
            # 查找所有备份文件
            for filename in os.listdir(self.data_dir):
                if '.backup_' in filename:
                    filepath = os.path.join(self.data_dir, filename)
                    mtime = os.path.getmtime(filepath)
                    backup_files.append((filepath, mtime))
            
            # 按修改时间排序，保留最新的
            backup_files.sort(key=lambda x: x[1], reverse=True)
            
            deleted_count = 0
            for filepath, _ in backup_files[keep_count:]:
                try:
                    os.remove(filepath)
                    deleted_count += 1
                except Exception as e:
                    print(f"删除备份文件失败 {filepath}: {e}")
            
            if deleted_count > 0:
                print(f"✅ 清理了 {deleted_count} 个旧备份文件")
            
            return deleted_count
            
        except Exception as e:
            print(f"❌ 清理备份文件失败: {e}")
            return 0


# 创建全局数据持久化实例
data_persistence = DataPersistence()
