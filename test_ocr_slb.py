"""
测试OCR识别扫雷宝评分功能
"""
import os
import re
try:
    from PIL import Image
    import pytesseract
    OCR_AVAILABLE = True
    print("✅ OCR环境检查通过")
except ImportError as e:
    OCR_AVAILABLE = False
    print(f"❌ OCR环境检查失败: {e}")
    print("请安装依赖: pip install pillow pytesseract")

def test_ocr_slb_score(code: str):
    """测试OCR识别扫雷宝评分"""
    if not OCR_AVAILABLE:
        print("❌ OCR功能不可用")
        return None
        
    try:
        # 检查是否有截图文件
        screenshot_files = [
            f"slb_screenshot_{code}.png",
            f"扫雷宝_{code}.png",
            f"{code}_slb.png",
            f"screenshot_{code}.png",
            f"slb_{code}.png"
        ]
        
        screenshot_path = None
        for file_path in screenshot_files:
            if os.path.exists(file_path):
                screenshot_path = file_path
                break
        
        if not screenshot_path:
            print(f"❌ 未找到 {code} 的截图文件，请保存扫雷宝截图为以下任一文件名:")
            for file_path in screenshot_files:
                print(f"   - {file_path}")
            
            # 提供扫雷宝链接
            slb_url = f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code={code}&color=0"
            print(f"\n📱 扫雷宝链接: {slb_url}")
            print("📸 请打开链接，截图后保存到当前目录")
            return None
        
        print(f"🖼️ 找到截图文件: {screenshot_path}")
        
        # 使用OCR识别截图中的分数
        image = Image.open(screenshot_path)
        print(f"📏 图片尺寸: {image.size}")
        
        # OCR识别 - 使用中英文混合识别
        text = pytesseract.image_to_string(image, lang='chi_sim+eng')
        print(f"🔍 OCR识别原始结果:")
        print("-" * 50)
        print(text)
        print("-" * 50)
        
        # 从OCR结果中提取分数
        score_patterns = [
            r'(\d+)分',  # "74分"
            r'评分[：:]\s*(\d+)',  # "评分：74"
            r'得分[：:]\s*(\d+)',  # "得分：74"
            r'分数[：:]\s*(\d+)',  # "分数：74"
            r'(\d{1,3})\s*分',  # 数字+分
            r'总分[：:]\s*(\d+)',  # "总分：74"
            r'(\d{1,3})\s*$',  # 单独的数字（行末）
        ]
        
        found_scores = []
        for i, pattern in enumerate(score_patterns):
            matches = re.findall(pattern, text)
            if matches:
                print(f"🎯 模式 {i+1} 匹配到: {matches}")
                for match in matches:
                    try:
                        score = int(match)
                        if 0 <= score <= 100:
                            found_scores.append(score)
                    except ValueError:
                        continue
        
        if found_scores:
            # 如果找到多个分数，选择最合理的一个
            final_score = max(set(found_scores), key=found_scores.count)  # 选择出现最多的分数
            print(f"✅ OCR提取到评分: {final_score}分")
            return float(final_score)
        else:
            print("❌ OCR未能识别到有效评分")
            
            # 尝试更宽松的匹配
            print("🔄 尝试更宽松的数字匹配...")
            all_numbers = re.findall(r'\d+', text)
            valid_scores = [int(num) for num in all_numbers if 0 <= int(num) <= 100]
            if valid_scores:
                print(f"🔍 找到可能的分数: {valid_scores}")
                return float(valid_scores[0])  # 返回第一个有效分数
            
            return None
        
    except Exception as e:
        print(f"❌ OCR识别失败: {e}")
        return None

if __name__ == '__main__':
    # 测试神思电子
    code = '300479'
    print(f"🧪 测试OCR识别 {code} 的扫雷宝评分")
    print("=" * 60)
    
    score = test_ocr_slb_score(code)
    
    if score is not None:
        print(f"\n🎉 最终结果: {code} 的扫雷宝评分为 {score}分")
    else:
        print(f"\n❌ 无法获取 {code} 的扫雷宝评分")
        print("\n💡 使用说明:")
        print("1. 打开扫雷宝页面")
        print("2. 截图保存为 slb_screenshot_300479.png")
        print("3. 重新运行此脚本")
