<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>持仓系统 V14 持仓编辑版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
        }
        
        .header .subtitle {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .update-info {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 5px;
            margin-top: 15px;
        }
        
        .controls {
            background: white;
            padding: 20px;
            margin: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .search-box {
            flex: 1;
            max-width: 300px;
            margin-right: 20px;
        }
        
        .search-box input {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .stats {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 0.9em;
            color: #666;
        }
        
        /* 分类筛选按钮 */
        .filter-buttons {
            background: white;
            padding: 15px 20px;
            margin: 0 20px 20px 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .filter-btn {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            color: #495057;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
        }
        
        .filter-btn:hover {
            background: #e9ecef;
            border-color: #adb5bd;
            transform: translateY(-2px);
        }
        
        .filter-btn.active {
            background: #667eea;
            border-color: #667eea;
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        
        /* 统计面板 */
        .stats-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 0 20px 20px 20px;
        }
        
        .stats-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stats-card h3 {
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .total-assets {
            border-left: 4px solid #28a745;
        }
        
        .asset-allocation {
            border-left: 4px solid #17a2b8;
        }
        
        .industry-distribution {
            border-left: 4px solid #ffc107;
        }
        
        .stats-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .stats-item:last-child {
            border-bottom: none;
        }
        
        .stats-value {
            font-weight: bold;
            color: #667eea;
        }
        
        /* 数据管理按钮 */
        .management-buttons {
            background: white;
            padding: 15px 20px;
            margin: 0 20px 20px 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .management-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        
        .management-btn:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }
        
        .management-btn.danger {
            background: #dc3545;
        }
        
        .management-btn.danger:hover {
            background: #c82333;
        }
        
        .management-btn.success {
            background: #28a745;
        }
        
        .management-btn.success:hover {
            background: #218838;
        }
        
        .management-btn.primary {
            background: #007bff;
        }
        
        .management-btn.primary:hover {
            background: #0056b3;
        }
        
        /* 表格样式 */
        .table-container {
            background: white;
            margin: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        
        th, td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #eee;
            white-space: nowrap;
        }
        
        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #495057;
            cursor: pointer;
            user-select: none;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        th:hover {
            background: #e9ecef;
        }
        
        .sort-indicator {
            margin-left: 5px;
            opacity: 0.5;
        }
        
        .sort-indicator.asc::after {
            content: '↑';
            opacity: 1;
        }
        
        .sort-indicator.desc::after {
            content: '↓';
            opacity: 1;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .positive {
            color: #dc3545;
        }
        
        .negative {
            color: #28a745;
        }
        
        .scan-score {
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
        }

        /* 持仓编辑相关样式 */
        .editable-holdings {
            cursor: pointer;
        }
        
        .editable-holdings:hover {
            background-color: #e9ecef;
        }
        
        .holdings-input {
            width: 80px;
            padding: 5px;
            text-align: center;
            border: 1px solid #667eea;
            border-radius: 4px;
            font-size: 14px;
        }

        /* 减半按钮样式 */
        .reduce-btn {
            background-color: #ffc107;
            color: #333;
        }
        
        .reduce-btn:hover {
            background-color: #e0a800;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                gap: 15px;
            }
            
            .search-box {
                max-width: none;
                margin-right: 0;
            }
            
            .stats {
                justify-content: center;
            }
            
            .stats-panel {
                grid-template-columns: 1fr;
                margin: 0 10px 20px 10px;
            }
            
            .filter-buttons, .management-buttons {
                margin: 0 10px 20px 10px;
            }
            
            .table-container {
                margin: 10px;
                overflow-x: auto;
            }
            
            table {
                min-width: 1200px;
            }
            
            th, td {
                padding: 8px 4px;
                font-size: 12px;
            }
            
            /* 移动端隐藏次要列 */
            .mobile-hidden {
                display: none;
            }
        }
        
        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.8em;
            }
            
            .header .subtitle {
                font-size: 0.9em;
            }
            
            .filter-btn, .management-btn {
                padding: 8px 12px;
                font-size: 12px;
                margin: 3px;
            }
        }

        .filter-group {
            margin-bottom: 5px;
        }
        
        .signal-btn {
            background-color: #f8f9fa;
            border: 2px solid #dee2e6;
        }
        
        .signal-btn[data-signal="clearance"] {
            border-color: #dc3545;
            color: #dc3545;
        }
        
        .signal-btn[data-signal="clearance"].active {
            background-color: #dc3545;
            color: white;
        }
        
        .signal-btn[data-signal="sell"] {
            border-color: #fd7e14;
            color: #fd7e14;
        }
        
        .signal-btn[data-signal="sell"].active {
            background-color: #fd7e14;
            color: white;
        }
        
        .signal-btn[data-signal="warning"] {
            border-color: #ffc107;
            color: #ffc107;
        }
        
        .signal-btn[data-signal="warning"].active {
            background-color: #ffc107;
            color: white;
        }
        
        .signal-btn[data-signal="hold"] {
            border-color: #28a745;
            color: #28a745;
        }
        
        .signal-btn[data-signal="hold"].active {
            background-color: #28a745;
            color: white;
        }
        
        .signal-btn[data-signal="pending"] {
            border-color: #6c757d;
            color: #6c757d;
        }
        
        .signal-btn[data-signal="pending"].active {
            background-color: #6c757d;
            color: white;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 持仓系统 V14 持仓编辑版</h1>
        <div class="subtitle">实时股票数据监控 | 持仓数量直接编辑 | 一键减半 | 扫雷风险评估</div>
        <div class="update-info">
            <span id="updateTime">等待数据加载...</span> |
            <span id="stockCount">0</span> 只股票 |
            <span id="tradingStatus">检查交易状态中...</span>
        </div>
    </div>

    <!-- 搜索和统计 -->
    <div class="controls">
        <div class="search-box">
            <input type="text" id="searchInput" placeholder="搜索股票代码或名称...">
        </div>
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number" id="totalValue">¥0</div>
                <div class="stat-label">总市值</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="stockCountStat">0</div>
                <div class="stat-label">股票数量</div>
            </div>
        </div>
    </div>

    <!-- 分类筛选按钮 -->
    <div class="filter-buttons">
        <!-- 信号筛选按钮 -->
        <div class="filter-group">
            <button class="filter-btn signal-btn active" data-signal="all">全部股票</button>
            <button class="filter-btn signal-btn" data-signal="pending">待处理信号</button>
            <button class="filter-btn signal-btn" data-signal="clearance">清仓信号</button>
            <button class="filter-btn signal-btn" data-signal="sell">卖出信号</button>
            <button class="filter-btn signal-btn" data-signal="warning">卖出预警</button>
            <button class="filter-btn signal-btn" data-signal="hold">持有</button>
        </div>
        <!-- 类型筛选按钮 -->
        <div class="filter-group" style="margin-top: 10px;">
            <button class="filter-btn type-btn active" data-type="all">全部</button>
            <button class="filter-btn type-btn" data-type="股票">股票</button>
            <button class="filter-btn type-btn" data-type="ETF">ETF</button>
            <button class="filter-btn type-btn" data-type="基金">基金</button>
        </div>
    </div>

    <!-- 统计面板 -->
    <div class="stats-panel">
        <div class="stats-card total-assets">
            <h3>💰 总资产统计</h3>
            <div class="stats-item">
                <span>总市值</span>
                <span class="stats-value" id="totalMarketValue">¥0</span>
            </div>
        </div>

        <div class="stats-card asset-allocation">
            <h3>📊 资产配置</h3>
            <div id="assetAllocationContent">
                <div class="stats-item">
                    <span>暂无数据</span>
                    <span class="stats-value">-</span>
                </div>
            </div>
        </div>

        <div class="stats-card industry-distribution">
            <h3>🏭 行业分布</h3>
            <div id="industryDistributionContent">
                <div class="stats-item">
                    <span>暂无数据</span>
                    <span class="stats-value">-</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据管理按钮 -->
    <div class="management-buttons">
        <button class="management-btn danger" onclick="clearData()">🗑️ 清空数据</button>
        <input type="file" id="fileInput" accept=".xlsx,.xls" style="display: none;" onchange="uploadFile()">
        <button class="management-btn primary" onclick="document.getElementById('fileInput').click()">📁 导入持仓表格</button>
        <button class="management-btn success" onclick="refreshData()">🔄 重新获取数据</button>
        <button class="management-btn" onclick="checkCacheStatus()">📊 缓存状态</button>
    </div>

    <!-- 表格容器 -->
    <div class="table-container">
        <table id="stockTable">
            <thead>
                <tr>
                    <th onclick="sortTable('code')">代码 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('name')">名称 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('price')">最新价 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('change_pct')">涨跌幅 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('yearly_low')" class="mobile-hidden">年内最低价 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('low_date')" class="mobile-hidden">最低价日期 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('distance_from_low_pct')" class="mobile-hidden">距最低点涨幅 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('pb_ratio')" class="mobile-hidden">市净率 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('pe_ratio')" class="mobile-hidden">TTM市盈率 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('dividend_yield')" class="mobile-hidden">股息率 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('industry')">行业 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('scan_score')">扫雷分数 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('quantity')">持仓数量 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('market_value')">市值 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('update_time')" class="mobile-hidden">更新时间 <span class="sort-indicator"></span></th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="stockTableBody">
                <tr>
                    <td colspan="15" style="text-align: center; padding: 40px;">
                        <div style="color: #666;">
                            <div style="font-size: 48px; margin-bottom: 20px;">📊</div>
                            <div style="font-size: 18px; margin-bottom: 10px;">暂无股票数据</div>
                            <div style="font-size: 14px;">请导入持仓表格或等待数据加载</div>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        let stockData = [];
        let filteredData = [];
        let currentFilter = 'all';
        let currentSignalFilter = 'all';
        let sortColumn = '';
        let sortDirection = 'asc';

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStockData();
            loadTradingStatus();

            // 设置定时刷新
            setInterval(loadStockData, 30000); // 30秒刷新一次
            setInterval(loadTradingStatus, 60000); // 1分钟刷新一次交易状态

            // 搜索功能
            document.getElementById('searchInput').addEventListener('input', function() {
                filterAndDisplayData();
            });

            // 类型筛选
            document.querySelectorAll('.type-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.type-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    currentFilter = this.dataset.type;
                    filterAndDisplayData();
                });
            });

            // 信号筛选
            document.querySelectorAll('.signal-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.signal-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    currentSignalFilter = this.dataset.signal;
                    filterAndDisplayData();
                });
            });
        });

        // 加载股票数据
        async function loadStockData() {
            try {
                const response = await fetch('/api/stocks');
                const result = await response.json();

                if (result.success) {
                    stockData = result.data;
                    updateHeader(result);
                    updateStatistics(result.stats);
                    filterAndDisplayData();
                } else {
                    console.error('加载数据失败:', result.message);
                }
            } catch (error) {
                console.error('请求失败:', error);
            }
        }

        // 加载交易状态
        async function loadTradingStatus() {
            try {
                const response = await fetch('/api/trading-status');
                const result = await response.json();

                if (result.success) {
                    document.getElementById('tradingStatus').textContent = result.data.message;
                }
            } catch (error) {
                console.error('获取交易状态失败:', error);
            }
        }

        // 更新页面头部信息
        function updateHeader(result) {
            document.getElementById('updateTime').textContent =
                result.last_update ? `最后更新: ${result.last_update}` : '暂未更新';
            document.getElementById('stockCount').textContent = result.count;
        }

        // 更新统计信息
        function updateStatistics(stats) {
            if (!stats) return;

            // 更新总市值
            const totalValue = formatCurrency(stats.total_market_value);
            document.getElementById('totalValue').textContent = totalValue;
            document.getElementById('totalMarketValue').textContent = totalValue;
            document.getElementById('stockCountStat').textContent = stockData.length;

            // 更新资产配置
            const assetContent = document.getElementById('assetAllocationContent');
            if (stats.asset_allocation && Object.keys(stats.asset_allocation).length > 0) {
                assetContent.innerHTML = '';
                for (const [type, data] of Object.entries(stats.asset_allocation)) {
                    const item = document.createElement('div');
                    item.className = 'stats-item';
                    item.innerHTML = `
                        <span>${type} (${data.count}只)</span>
                        <span class="stats-value">${formatCurrency(data.market_value)} (${data.percentage.toFixed(1)}%)</span>
                    `;
                    assetContent.appendChild(item);
                }
            } else {
                assetContent.innerHTML = '<div class="stats-item"><span>暂无数据</span><span class="stats-value">-</span></div>';
            }

            // 更新行业分布
            const industryContent = document.getElementById('industryDistributionContent');
            if (stats.industry_distribution && Object.keys(stats.industry_distribution).length > 0) {
                industryContent.innerHTML = '';
                for (const [industry, data] of Object.entries(stats.industry_distribution)) {
                    const item = document.createElement('div');
                    item.className = 'stats-item';
                    item.innerHTML = `
                        <span>${industry} (${data.count}只)</span>
                        <span class="stats-value">${formatCurrency(data.market_value)} (${data.percentage.toFixed(1)}%)</span>
                    `;
                    industryContent.appendChild(item);
                }
            } else {
                industryContent.innerHTML = '<div class="stats-item"><span>暂无数据</span><span class="stats-value">-</span></div>';
            }
        }

        // 筛选和显示数据
        function filterAndDisplayData() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();

            // 应用筛选条件
            filteredData = stockData.filter(stock => {
                // 类型筛选
                if (currentFilter !== 'all' && stock.security_type !== currentFilter) {
                    return false;
                }

                // 信号筛选
                if (currentSignalFilter !== 'all') {
                    if (currentSignalFilter === 'pending' && stock.sell_signal !== 'pending') {
                        return false;
                    } else if (currentSignalFilter === 'clearance' && stock.sell_signal !== 'clearance') {
                        return false;
                    } else if (currentSignalFilter === 'sell' && stock.sell_signal !== 'sell') {
                        return false;
                    } else if (currentSignalFilter === 'warning' && stock.sell_signal !== 'warning') {
                        return false;
                    } else if (currentSignalFilter === 'hold' && stock.sell_signal !== 'hold') {
                        return false;
                    }
                }

                // 搜索筛选
                if (searchTerm &&
                    !stock.code.toLowerCase().includes(searchTerm) &&
                    !stock.name.toLowerCase().includes(searchTerm)) {
                    return false;
                }

                return true;
            });

            // 应用排序
            if (sortColumn) {
                filteredData.sort((a, b) => {
                    let aVal = a[sortColumn];
                    let bVal = b[sortColumn];

                    // 特殊处理一些字段
                    if (['price', 'change_pct', 'yearly_low', 'distance_from_low_pct', 'pb_ratio', 'pe_ratio', 'dividend_yield', 'market_value', 'quantity'].includes(sortColumn)) {
                        aVal = parseFloat(aVal) || 0;
                        bVal = parseFloat(bVal) || 0;
                    }

                    if (aVal < bVal) return sortDirection === 'asc' ? -1 : 1;
                    if (aVal > bVal) return sortDirection === 'asc' ? 1 : -1;
                    return 0;
                });
            }

            displayStockTable();
        }

        // 显示股票表格
        function displayStockTable() {
            const tbody = document.getElementById('stockTableBody');

            if (filteredData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="15" style="text-align: center; padding: 40px;">
                            <div style="color: #666;">
                                <div style="font-size: 48px; margin-bottom: 20px;">📊</div>
                                <div style="font-size: 18px; margin-bottom: 10px;">暂无匹配的股票数据</div>
                                <div style="font-size: 14px;">请调整筛选条件或导入持仓表格</div>
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = filteredData.map(stock => {
                // 获取信号的显示文本和颜色
                function getSellSignalDisplay(signal) {
                    switch(signal) {
                        case 'clearance': return { text: '清仓', color: '#dc3545' };
                        case 'sell': return { text: '卖出', color: '#fd7e14' };
                        case 'warning': return { text: '预警', color: '#ffc107' };
                        case 'hold': return { text: '持有', color: '#28a745' };
                        case 'reduced': return { text: '已减半', color: '#6f42c1' };
                        default: return { text: '待处理', color: '#6c757d' };
                    }
                }
                
                const signal = getSellSignalDisplay(stock.sell_signal);
                
                return `
                <tr>
                    <td>${stock.code}</td>
                    <td>${stock.name}</td>
                    <td>${formatNumber(stock.price, 2)}</td>
                    <td class="${stock.change_pct >= 0 ? 'positive' : 'negative'}">
                        ${formatPercent(stock.change_pct)}
                    </td>
                    <td class="mobile-hidden">${formatNumber(stock.yearly_low, 2)}</td>
                    <td class="mobile-hidden">${stock.low_date || '-'}</td>
                    <td class="mobile-hidden ${stock.distance_from_low_pct >= 0 ? 'positive' : 'negative'}">
                        ${stock.distance_from_low_pct ? formatPercent(stock.distance_from_low_pct) : '-'}
                    </td>
                    <td class="mobile-hidden">${formatNumber(stock.pb_ratio, 2)}</td>
                    <td class="mobile-hidden">${formatNumber(stock.pe_ratio, 2)}</td>
                    <td class="mobile-hidden">${formatPercent(stock.dividend_yield)}</td>
                    <td>${stock.industry || '-'}</td>
                    <td>
                        <span class="scan-score" style="background-color: ${stock.scan_risk_color || '#cccccc'}">
                            ${stock.scan_display_text || '-'}
                        </span>
                    </td>
                    <td>
                        <span class="sell-signal" style="background-color: ${signal.color}">
                            ${signal.text}
                        </span>
                    </td>
                    <td class="editable-holdings" onclick="editHoldingsInline('${stock.code}', ${stock.quantity || 0}, this)">
                        ${formatNumber(stock.quantity, 0)}
                    </td>
                    <td>${formatCurrency(stock.market_value)}</td>
                    <td class="mobile-hidden">${stock.update_time || '-'}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="management-btn" onclick="updateSingleStock('${stock.code}')" title="更新数据">🔄</button>
                            ${(stock.sell_signal === 'sell' || stock.sell_signal === 'clearance') ? 
                                `<button class="management-btn reduce-btn" onclick="markReduced('${stock.code}')" title="标记已减半">减半</button>` : ''
                            }
                        </div>
                    </td>
                </tr>
                `;
            }).join('');
        }

        // 表格排序
        function sortTable(column) {
            if (sortColumn === column) {
                sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                sortColumn = column;
                sortDirection = 'asc';
            }

            // 更新排序指示器
            document.querySelectorAll('.sort-indicator').forEach(indicator => {
                indicator.className = 'sort-indicator';
            });

            const currentIndicator = document.querySelector(`th[onclick="sortTable('${column}')"] .sort-indicator`);
            if (currentIndicator) {
                currentIndicator.className = `sort-indicator ${sortDirection}`;
            }

            filterAndDisplayData();
        }

        // 数据管理功能
        async function clearData() {
            if (!confirm('确定要清空所有数据吗？此操作不可恢复。')) {
                return;
            }

            try {
                const response = await fetch('/api/clear-data', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    alert('数据已清空');
                    stockData = [];
                    filterAndDisplayData();
                    updateStatistics({});
                } else {
                    alert('清空失败: ' + result.message);
                }
            } catch (error) {
                alert('清空失败: ' + error.message);
            }
        }

        async function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];

            if (!file) {
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            try {
                const response = await fetch('/api/upload-holdings', {
                    method: 'POST',
                    body: formData
                });
                const result = await response.json();

                if (result.success) {
                    alert(`导入成功: ${result.message}`);
                } else {
                    alert('导入失败: ' + result.message);
                }
            } catch (error) {
                alert('导入失败: ' + error.message);
            }

            fileInput.value = '';
        }

        async function refreshData() {
            if (!confirm('确定要重新获取所有股票数据吗？这可能需要几分钟时间。')) {
                return;
            }

            try {
                const response = await fetch('/api/refresh-data', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    alert(result.message);
                } else {
                    alert('更新失败: ' + result.message);
                }
            } catch (error) {
                alert('更新失败: ' + error.message);
            }
        }

        async function checkCacheStatus() {
            try {
                const [cacheResponse, scanResponse] = await Promise.all([
                    fetch('/api/cache-status'),
                    fetch('/api/scan-cache-info')
                ]);

                const cacheResult = await cacheResponse.json();
                const scanResult = await scanResponse.json();

                let message = '缓存状态信息:\n\n';

                if (cacheResult.success) {
                    const cache = cacheResult.data;
                    message += `年内最低价缓存:\n`;
                    message += `- 状态: ${cache.exists ? '存在' : '不存在'}\n`;
                    message += `- 有效: ${cache.valid ? '是' : '否'}\n`;
                    if (cache.success_rate) {
                        message += `- 成功率: ${cache.success_rate.toFixed(1)}%\n`;
                    }
                } else {
                    message += `年内最低价缓存: 获取失败\n`;
                }

                message += '\n';

                if (scanResult.success) {
                    const scan = scanResult.data;
                    message += `扫雷数据缓存:\n`;
                    message += `- 缓存股票数: ${scan.cached_stocks}\n`;
                    message += `- 缓存时长: ${scan.cache_duration}秒\n`;
                } else {
                    message += `扫雷数据缓存: 获取失败\n`;
                }

                alert(message);
            } catch (error) {
                alert('获取缓存状态失败: ' + error.message);
            }
        }

        // 内联编辑持仓数量功能
        function editHoldingsInline(stockCode, currentHoldings, cell) {
            if (cell.querySelector('input')) {
                return; // 已经在编辑模式
            }

            const originalValue = cell.textContent.trim();
            cell.innerHTML = `<input type="number" class="holdings-input" value="${currentHoldings}" />`;
            const input = cell.querySelector('input');
            input.focus();
            input.select();

            const saveChanges = async () => {
                const newHoldings = parseInt(input.value, 10);
                if (isNaN(newHoldings) || newHoldings < 0) {
                    cell.innerHTML = originalValue;
                    alert('请输入有效的持仓数量。');
                    return;
                }

                if (newHoldings === currentHoldings) {
                    cell.innerHTML = originalValue;
                    return;
                }

                cell.innerHTML = '保存中...';
                try {
                    const response = await fetch('/api/update-holdings', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ stock_code: stockCode, holdings: newHoldings })
                    });
                    const result = await response.json();
                    if (result.success) {
                        loadStockData(); // 重新加载数据刷新表格
                    } else {
                        cell.innerHTML = originalValue;
                        alert('保存失败: ' + result.message);
                    }
                } catch (error) {
                    cell.innerHTML = originalValue;
                    alert('保存失败: ' + error.message);
                }
            };

            input.addEventListener('blur', saveChanges);
            input.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    input.blur();
                } else if (e.key === 'Escape') {
                    cell.innerHTML = originalValue;
                }
            });
        }
        
        // 标记股票为已减半
        async function markReduced(stockCode) {
            if (!confirm(`确定要将 ${stockCode} 标记为已减半吗？持仓数量将自动减半，并停止该股票的所有提醒。`)) {
                return;
            }
            try {
                const response = await fetch('/api/mark-reduced', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ stock_code: stockCode })
                });
                const result = await response.json();
                if (result.success) {
                    alert(result.message);
                    loadStockData();
                } else {
                    alert('操作失败: ' + result.message);
                }
            } catch (error) {
                alert('操作失败: ' + error.message);
            }
        }

        // 单个股票刷新
        async function updateSingleStock(stockCode) {
            const button = event.target;
            button.disabled = true;
            button.classList.add('updating');
            button.textContent = '⌛';
            
            try {
                const response = await fetch(`/api/update-single-stock/${stockCode}`, {
                    method: 'POST',
                });
                
                const result = await response.json();
                
                if (result.success) {
                    const data = result.data;
                    
                    // 计算变化百分比
                    const changeText = data.price_change >= 0 ? 
                        `+${data.price_change.toFixed(2)}` : 
                        `${data.price_change.toFixed(2)}`;
                    
                    const changePctText = data.price_change_pct >= 0 ? 
                        `+${data.price_change_pct.toFixed(2)}%` : 
                        `${data.price_change_pct.toFixed(2)}%`;
                    
                    alert(`✅ ${data.name} (${data.code}) 更新成功\n\n📊 价格变化:\n• 原价格: ${data.old_price.toFixed(2)}元\n• 新价格: ${data.new_price.toFixed(2)}元\n• 变化: ${changeText}元 (${changePctText})\n• 更新时间: ${data.update_time}`);

                    // 重新加载数据以更新表格
                    await loadStockData();
                } else {
                    alert(`❌ 更新失败: ${result.message}`);
                }
            } catch (error) {
                console.error('更新单只股票失败:', error);
                alert(`❌ 更新失败: ${error.message}`);
            } finally {
                // 恢复按钮状态
                button.disabled = false;
                button.classList.remove('updating');
                button.textContent = '🔄';
            }
        }

        // 工具函数
        function formatNumber(value, decimals = 2) {
            if (value === null || value === undefined || value === '') return '-';
            return Number(value).toFixed(decimals);
        }

        function formatPercent(value) {
            if (value === null || value === undefined || value === '') return '-';
            return Number(value).toFixed(2) + '%';
        }

        function formatCurrency(value) {
            if (value === null || value === undefined || value === '' || value === 0) return '¥0';
            return '¥' + Number(value).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
        }
    </script>
</body>
</html>
