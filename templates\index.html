<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>持仓股票实时行情</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .update-info {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 5px;
            margin-top: 15px;
        }
        
        .controls {
            background: white;
            padding: 20px;
            margin: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .search-box {
            flex: 1;
            max-width: 300px;
            margin-right: 20px;
        }
        
        .search-box input {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .stats {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 0.9em;
            color: #666;
        }
        
        .table-container {
            background: white;
            margin: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th {
            background: #f8f9fa;
            padding: 15px 10px;
            text-align: center;
            font-weight: bold;
            border-bottom: 2px solid #dee2e6;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        td {
            padding: 12px 10px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }
        
        tr:hover {
            background-color: #f8f9fa;
        }
        
        .positive {
            color: #dc3545;
        }
        
        .negative {
            color: #28a745;
        }
        
        .neutral {
            color: #6c757d;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            font-size: 1.2em;
            color: #666;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            margin: 20px;
            border-radius: 5px;
            border: 1px solid #f5c6cb;
        }
        
        .refresh-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }
        
        .refresh-btn:hover {
            background: #5a6fd8;
        }
        
        .refresh-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                gap: 15px;
            }
            
            .search-box {
                max-width: 100%;
                margin-right: 0;
            }
            
            .stats {
                justify-content: center;
            }
            
            table {
                font-size: 14px;
            }
            
            th, td {
                padding: 8px 5px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📈 持仓股票实时行情</h1>
        <div class="update-info">
            <div>最后更新时间: <span id="lastUpdate">加载中...</span></div>
            <div>数据来源: AKSHARE</div>
        </div>
    </div>
    
    <div class="controls">
        <div class="search-box">
            <input type="text" id="searchInput" placeholder="搜索股票代码或名称...">
        </div>
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number" id="totalCount">-</div>
                <div class="stat-label">总股票数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="upCount">-</div>
                <div class="stat-label">上涨</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="downCount">-</div>
                <div class="stat-label">下跌</div>
            </div>
        </div>
        <button class="refresh-btn" id="refreshBtn" onclick="refreshData()">刷新数据</button>
    </div>
    
    <div class="table-container">
        <div id="loading" class="loading">正在加载股票数据...</div>
        <div id="error" class="error" style="display: none;"></div>
        <table id="stockTable" style="display: none;">
            <thead>
                <tr>
                    <th>股票代码</th>
                    <th>股票名称</th>
                    <th>最新价</th>
                    <th>涨跌额</th>
                    <th>涨跌幅</th>
                    <th>今开</th>
                    <th>昨收</th>
                    <th>最高</th>
                    <th>最低</th>
                    <th>成交量</th>
                    <th>成交额</th>
                    <th>更新时间</th>
                </tr>
            </thead>
            <tbody id="stockTableBody">
            </tbody>
        </table>
    </div>

    <script>
        let stocksData = [];
        let filteredData = [];
        
        // 格式化数字
        function formatNumber(num, decimals = 2) {
            if (num === 0 || num === null || num === undefined) return '-';
            return parseFloat(num).toFixed(decimals);
        }
        
        // 格式化成交量
        function formatVolume(volume) {
            if (volume === 0 || volume === null || volume === undefined) return '-';
            if (volume >= 100000000) {
                return (volume / 100000000).toFixed(2) + '亿';
            } else if (volume >= 10000) {
                return (volume / 10000).toFixed(2) + '万';
            }
            return volume.toString();
        }
        
        // 格式化成交额
        function formatAmount(amount) {
            if (amount === 0 || amount === null || amount === undefined) return '-';
            if (amount >= 100000000) {
                return (amount / 100000000).toFixed(2) + '亿';
            } else if (amount >= 10000) {
                return (amount / 10000).toFixed(2) + '万';
            }
            return amount.toString();
        }
        
        // 获取涨跌颜色类
        function getChangeClass(change) {
            if (change > 0) return 'positive';
            if (change < 0) return 'negative';
            return 'neutral';
        }
        
        // 渲染股票表格
        function renderTable(data) {
            const tbody = document.getElementById('stockTableBody');
            tbody.innerHTML = '';
            
            data.forEach(stock => {
                const row = document.createElement('tr');
                const changeClass = getChangeClass(stock.change);
                
                row.innerHTML = `
                    <td>${stock.code}</td>
                    <td>${stock.name}</td>
                    <td class="${changeClass}">${formatNumber(stock.price)}</td>
                    <td class="${changeClass}">${stock.change > 0 ? '+' : ''}${formatNumber(stock.change)}</td>
                    <td class="${changeClass}">${stock.change_pct > 0 ? '+' : ''}${formatNumber(stock.change_pct)}%</td>
                    <td>${formatNumber(stock.open)}</td>
                    <td>${formatNumber(stock.yesterday_close)}</td>
                    <td>${formatNumber(stock.high)}</td>
                    <td>${formatNumber(stock.low)}</td>
                    <td>${formatVolume(stock.volume)}</td>
                    <td>${formatAmount(stock.amount)}</td>
                    <td>${stock.update_time || '-'}</td>
                `;
                tbody.appendChild(row);
            });
        }
        
        // 更新统计信息
        function updateStats(data) {
            const totalCount = data.length;
            const upCount = data.filter(stock => stock.change > 0).length;
            const downCount = data.filter(stock => stock.change < 0).length;
            
            document.getElementById('totalCount').textContent = totalCount;
            document.getElementById('upCount').textContent = upCount;
            document.getElementById('downCount').textContent = downCount;
        }
        
        // 搜索功能
        function filterStocks() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            filteredData = stocksData.filter(stock => 
                stock.code.toLowerCase().includes(searchTerm) || 
                stock.name.toLowerCase().includes(searchTerm)
            );
            renderTable(filteredData);
            updateStats(filteredData);
        }
        
        // 获取股票数据
        async function fetchStocks() {
            try {
                const response = await fetch('/api/stocks');
                const data = await response.json();
                
                stocksData = data.stocks || [];
                filteredData = stocksData;
                
                document.getElementById('lastUpdate').textContent = data.last_update || '未知';
                
                renderTable(filteredData);
                updateStats(filteredData);
                
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error').style.display = 'none';
                document.getElementById('stockTable').style.display = 'table';
                
            } catch (error) {
                console.error('获取股票数据失败:', error);
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error').style.display = 'block';
                document.getElementById('error').textContent = '获取股票数据失败: ' + error.message;
            }
        }
        
        // 刷新数据
        function refreshData() {
            const btn = document.getElementById('refreshBtn');
            btn.disabled = true;
            btn.textContent = '刷新中...';
            
            fetchStocks().finally(() => {
                btn.disabled = false;
                btn.textContent = '刷新数据';
            });
        }
        
        // 搜索事件监听
        document.getElementById('searchInput').addEventListener('input', filterStocks);
        
        // 页面加载完成后获取数据
        document.addEventListener('DOMContentLoaded', function() {
            fetchStocks();
            
            // 每60秒自动刷新一次
            setInterval(fetchStocks, 60000);
        });
    </script>
</body>
</html>
