"""
直接分析截图中的扫雷宝评分
"""

import os
import re
from PIL import Image, ImageEnhance
import pytesseract

def setup_ocr():
    """设置OCR环境"""
    pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"

def find_screenshot_files():
    """查找截图文件"""
    
    screenshot_files = [
        "debug_screenshot.png",
        "test_screenshot.png", 
        "slb_300479.png",
        "slb_selenium_300479.png",
    ]
    
    found_files = []
    for filename in screenshot_files:
        if os.path.exists(filename):
            file_size = os.path.getsize(filename)
            print(f"✅ 找到截图: {filename} ({file_size} 字节)")
            found_files.append(filename)
    
    return found_files

def analyze_screenshot_simple(image_path: str):
    """简单分析截图"""
    
    setup_ocr()
    
    print(f"\n🔍 分析截图: {image_path}")
    print("-" * 50)
    
    try:
        # 打开图片
        image = Image.open(image_path)
        print(f"📸 图片尺寸: {image.size}")
        
        # OCR配置列表
        ocr_configs = [
            ('基本配置', '--psm 6'),
            ('数字+分字', '--psm 6 -c tessedit_char_whitelist=0123456789分'),
            ('单词模式', '--psm 8 -c tessedit_char_whitelist=0123456789'),
            ('单行模式', '--psm 7 -c tessedit_char_whitelist=0123456789分'),
        ]
        
        all_scores = []
        
        # 尝试不同的OCR配置
        for config_name, config in ocr_configs:
            try:
                print(f"\n🔍 {config_name}:")
                text = pytesseract.image_to_string(image, lang='chi_sim+eng', config=config)
                text = text.strip()
                
                print(f"  识别文本: '{text}'")
                
                if text:
                    # 提取分数
                    scores = extract_scores(text)
                    if scores:
                        print(f"  提取分数: {scores}")
                        all_scores.extend(scores)
                    else:
                        print(f"  未找到分数")
                
            except Exception as e:
                print(f"  ❌ 失败: {e}")
        
        # 增强对比度后再试
        print(f"\n🔍 增强对比度后重试:")
        try:
            enhancer = ImageEnhance.Contrast(image)
            enhanced_image = enhancer.enhance(2.0)
            
            text = pytesseract.image_to_string(enhanced_image, lang='chi_sim+eng', config='--psm 6 -c tessedit_char_whitelist=0123456789分')
            text = text.strip()
            
            print(f"  增强后文本: '{text}'")
            
            if text:
                scores = extract_scores(text)
                if scores:
                    print(f"  增强后分数: {scores}")
                    all_scores.extend(scores)
        
        except Exception as e:
            print(f"  ❌ 增强处理失败: {e}")
        
        # 分析结果
        if all_scores:
            print(f"\n📊 所有识别分数: {all_scores}")
            
            # 过滤合理分数
            valid_scores = [s for s in all_scores if 50 <= s <= 100]
            
            if valid_scores:
                from collections import Counter
                score_counts = Counter(valid_scores)
                print(f"📊 有效分数统计: {dict(score_counts)}")
                
                # 最常出现的分数
                final_score = score_counts.most_common(1)[0][0]
                print(f"🎯 推测评分: {final_score}分")
                
                return final_score
            else:
                print("❌ 没有找到合理范围内的分数")
        else:
            print("❌ 未识别到任何分数")
        
        return None
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def extract_scores(text: str) -> list:
    """从文本中提取分数"""
    scores = []
    
    patterns = [
        r'(\d{1,3})分',           # "97分"
        r'(\d{1,3})\s*分',        # "97 分"  
        r'分\s*(\d{1,3})',        # "分97"
        r'(\d{2,3})',             # 两位或三位数字
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, text)
        for match in matches:
            try:
                score = int(match)
                if 0 <= score <= 100:
                    scores.append(score)
            except ValueError:
                continue
    
    return scores

def main():
    """主函数"""
    
    print("🎯 扫雷宝截图评分分析")
    print("=" * 40)
    
    # 查找截图文件
    screenshot_files = find_screenshot_files()
    
    if not screenshot_files:
        print("❌ 未找到截图文件")
        print("💡 请确保以下文件存在:")
        print("  - debug_screenshot.png")
        print("  - test_screenshot.png")
        print("  - slb_300479.png")
        return
    
    # 分析每个截图
    results = {}
    
    for filename in screenshot_files:
        score = analyze_screenshot_simple(filename)
        results[filename] = score
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("🎉 分析结果汇总:")
    print("=" * 50)
    
    for filename, score in results.items():
        if score is not None:
            print(f"✅ {filename}: {score}分")
            
            # 特别验证神思电子
            if "300479" in filename:
                if 90 <= score <= 100:
                    print(f"   ✅ 神思电子评分合理 (期望97分)")
                else:
                    print(f"   ⚠️ 神思电子评分异常")
        else:
            print(f"❌ {filename}: 识别失败")
    
    # 如果有多个结果，选择最可信的
    valid_results = {k: v for k, v in results.items() if v is not None}
    
    if len(valid_results) > 1:
        scores = list(valid_results.values())
        from collections import Counter
        score_counts = Counter(scores)
        
        print(f"\n📊 多个结果统计: {dict(score_counts)}")
        
        if len(score_counts) == 1:
            print(f"✅ 所有结果一致: {scores[0]}分")
        else:
            most_common = score_counts.most_common(1)[0][0]
            print(f"🎯 最可能的评分: {most_common}分")

if __name__ == "__main__":
    main()
