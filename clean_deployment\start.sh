#!/bin/bash

# 持仓系统启动脚本 - 模块化版本

echo "=== 🚀 启动持仓系统 V2.0 模块化版本 ==="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装"
    exit 1
fi

# 检查依赖
echo "📦 检查Python依赖..."
pip3 install -r requirements.txt

# 创建必要目录
echo "📁 创建必要目录..."
mkdir -p logs cache uploads data config

# 启动应用
echo "🚀 启动应用..."
if [ "$1" = "dev" ]; then
    echo "🔧 开发模式启动..."
    python3 app.py
else
    echo "🏭 生产模式启动..."
    gunicorn --config gunicorn_config.py app:app
fi
