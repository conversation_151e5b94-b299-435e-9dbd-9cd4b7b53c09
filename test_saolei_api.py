import requests
import re
import json
from bs4 import BeautifulSoup

def test_saolei_api(code):
    """测试扫雷宝API"""
    try:
        # 扫雷宝API接口
        api_url = f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code={code}&color=0"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Referer': 'http://page3.tdx.com.cn:7615/',
        }
        
        print(f"🔍 测试股票 {code} 的扫雷宝数据...")
        response = requests.get(api_url, headers=headers, timeout=15)
        response.encoding = 'utf-8'
        
        if response.status_code == 200:
            content = response.text
            print(f"✅ 响应成功，内容长度: {len(content)}")
            
            # 保存完整响应到文件
            with open(f'saolei_response_{code}.html', 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"📄 完整响应已保存到 saolei_response_{code}.html")
            
            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(content, 'html.parser')
            
            # 查找可能包含分数的元素
            print("\n🔍 查找可能的分数元素:")
            
            # 查找所有包含数字的文本
            all_text = soup.get_text()
            numbers = re.findall(r'\b(\d{1,3}\.?\d*)\b', all_text)
            valid_scores = [float(n) for n in numbers if n and 50 <= float(n) <= 100]
            print(f"有效分数范围(50-100): {valid_scores[:10]}")
            
            # 查找JavaScript变量
            scripts = soup.find_all('script')
            for i, script in enumerate(scripts):
                if script.string:
                    script_content = script.string
                    # 查找可能的分数变量
                    score_patterns = [
                        r'score\s*[:=]\s*(\d{1,3}\.?\d*)',
                        r'安全分\s*[:=]\s*(\d{1,3}\.?\d*)',
                        r'safeScore\s*[:=]\s*(\d{1,3}\.?\d*)',
                        r'value\s*[:=]\s*(\d{1,3}\.?\d*)',
                    ]
                    
                    for pattern in score_patterns:
                        matches = re.findall(pattern, script_content, re.IGNORECASE)
                        if matches:
                            print(f"Script {i} 找到分数: {matches}")
            
            # 查找特定的HTML元素
            score_elements = soup.find_all(['span', 'div', 'td'], string=re.compile(r'\d{2,3}'))
            for elem in score_elements[:5]:
                print(f"元素: {elem.name}, 内容: {elem.get_text().strip()}")
            
            # 查找data属性
            elements_with_data = soup.find_all(attrs={'data-score': True})
            for elem in elements_with_data:
                print(f"Data属性: {elem.get('data-score')}")
            
            return content
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        return None

if __name__ == '__main__':
    # 测试几个股票代码
    test_codes = ['000001', '600000', '000002']
    for code in test_codes:
        print(f"\n{'='*50}")
        test_saolei_api(code)
        print(f"{'='*50}")
