# 持仓系统开发规则

## 👤 **用户背景**
- 用户是编程小白
- 用户是中国人，偏好中文
- 需要简单易懂的操作方式
- 需要稳定可靠的版本管理

---

## 🇨🇳 **中文命名规则**

### 文件命名
- ✅ 主程序：`持仓系统_v1.py`, `持仓系统_v2.py`
- ✅ 模板文件：`持仓页面_v1.html`, `持仓页面_v2.html`
- ✅ 功能模块：`微信推送.py`, `数据获取.py`
- ✅ 配置文件：`股票列表.csv`, `系统配置.json`
- ✅ 说明文档：`使用说明.md`, `版本说明.md`

### 代码注释
- 所有注释使用中文
- 函数说明用中文描述
- 变量名可以用英文，但要有中文注释

### 界面文字
- 按钮文字：更新数据、发送摘要、测试微信
- 表格标题：股票代码、股票名称、最新价格
- 提示信息：数据更新成功、连接测试失败

---

## 📦 **版本管理规则**

### 版本命名规范
```
持仓系统_v1_完整版.py     # 第一个稳定版本
持仓系统_v2_增强版.py     # 第二个版本（新增功能）
持仓系统_v3_专业版.py     # 第三个版本（更多功能）
```

### 模板文件命名
```
templates/持仓页面_v1.html
templates/持仓页面_v2.html
templates/持仓页面_v3.html
```

### 备份规则
- ✅ 每完成一个版本，立即备份
- ✅ 备份包含：主程序 + 模板文件 + 说明文档
- ✅ 备份文件不允许修改
- ✅ 新功能必须创建新版本

---

## 🔄 **开发流程**

### 新功能开发流程
1. **需求确认**
   - 明确用户需要什么功能
   - 确认在哪个版本基础上开发

2. **创建新版本**
   ```bash
   # 复制上一版本
   copy 持仓系统_v1_完整版.py 持仓系统_v2_新功能版.py
   copy templates\持仓页面_v1.html templates\持仓页面_v2.html
   ```

3. **开发新功能**
   - 在新版本文件中添加功能
   - 不修改老版本文件
   - 保持向下兼容

4. **测试验证**
   - 确保新功能正常工作
   - 确保老功能不受影响
   - 用户验收通过

5. **版本备份**
   - 更新版本说明文档
   - 备份完整版本
   - 标记版本状态

### 版本回退流程
```bash
# 如果新版本有问题，可以立即回退
python 持仓系统_v1_完整版.py  # 回到V1
python 持仓系统_v2_增强版.py  # 回到V2
```

---

## 🛡️ **安全规则**

### 禁止操作
- ❌ 不允许直接修改已备份的版本
- ❌ 不允许删除历史版本文件
- ❌ 不允许在生产环境直接测试新功能
- ❌ 不允许同时运行多个版本

### 必须操作
- ✅ 新功能必须创建新版本
- ✅ 每个版本必须有完整备份
- ✅ 每个版本必须有说明文档
- ✅ 测试通过后才能交付用户

---

## 📋 **文件结构规范**

```
持仓系统/
├── 开发规则.md                    # 本文件
├── 版本说明.md                    # 版本历史记录
├── 使用说明.md                    # 用户使用指南
├── 持仓系统_v1_完整版.py          # V1主程序
├── 持仓系统_v2_增强版.py          # V2主程序（如果有）
├── 微信推送.py                    # 微信推送模块
├── 股票列表.csv                   # 股票代码配置
├── templates/
│   ├── 持仓页面_v1.html           # V1前端页面
│   ├── 持仓页面_v2.html           # V2前端页面（如果有）
└── 备份/
    ├── v1_备份_20250721/          # V1完整备份
    └── v2_备份_20250722/          # V2完整备份（如果有）
```

---

## 🎯 **质量标准**

### 代码质量
- 代码注释清晰易懂
- 错误处理完善
- 用户提示友好
- 性能稳定可靠

### 用户体验
- 界面简洁明了
- 操作步骤简单
- 错误信息清楚
- 功能响应及时

### 文档质量
- 说明文档完整
- 操作步骤详细
- 常见问题解答
- 版本更新记录

---

## 📞 **支持规则**

### 用户反馈处理
1. **问题确认**: 明确用户遇到的具体问题
2. **版本确认**: 确认用户使用的版本号
3. **问题重现**: 在对应版本中重现问题
4. **解决方案**: 提供解决方案或创建新版本
5. **验证确认**: 确保问题得到解决

### 功能请求处理
1. **需求理解**: 充分理解用户需求
2. **可行性评估**: 评估技术可行性
3. **版本规划**: 确定在哪个版本实现
4. **开发实现**: 按规范开发新功能
5. **交付验收**: 用户验收通过

---

## 📝 **记录规则**

### 必须记录的信息
- 每个版本的功能清单
- 每次修改的详细说明
- 用户反馈和处理结果
- 已知问题和解决方案
- 性能数据和优化记录

### 文档更新规则
- 新版本发布时更新版本说明
- 功能变更时更新使用说明
- 问题解决时更新FAQ文档
- 定期检查文档的准确性

---

## ⚡ **应急规则**

### 紧急情况处理
1. **系统故障**: 立即回退到上一稳定版本
2. **数据异常**: 停止自动更新，手动检查
3. **网络问题**: 启用离线模式或降级功能
4. **用户投诉**: 优先处理，快速响应

### 恢复流程
1. 确认问题影响范围
2. 选择合适的恢复方案
3. 执行恢复操作
4. 验证系统正常
5. 通知用户恢复情况

---

**制定时间**: 2025年7月21日  
**适用范围**: 持仓系统所有版本  
**更新频率**: 根据实际需要调整  
**执行责任**: 开发人员严格遵守
