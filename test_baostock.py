#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import baostock as bs
import pandas as pd
from datetime import datetime, timedelta

def test_baostock():
    """测试baostock获取股票历史数据"""
    
    try:
        print("🔍 测试baostock获取000001历史数据...")
        
        # 计算一年前的日期
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365)
        
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')
        
        print(f"📅 时间范围: {start_date_str} 到 {end_date_str}")
        
        # 登录baostock
        lg = bs.login()
        if lg.error_code != '0':
            print(f"❌ baostock登录失败: {lg.error_msg}")
            return False
        
        print("✅ baostock登录成功")
        
        # 获取平安银行(000001)历史数据
        rs = bs.query_history_k_data_plus(
            "sz.000001",
            "date,code,open,high,low,close,preclose,volume,amount,adjustflag,turn,tradestatus,pctChg,isST",
            start_date=start_date_str,
            end_date=end_date_str,
            frequency="d",   # 日线数据
            adjustflag="2"   # 前复权
        )
        
        if rs.error_code != '0':
            print(f"❌ baostock查询失败: {rs.error_msg}")
            bs.logout()
            return False
        
        print("✅ baostock查询成功")
        
        # 转换为DataFrame
        data_list = []
        while (rs.error_code == '0') & rs.next():
            data_list.append(rs.get_row_data())
        
        bs.logout()
        print("✅ baostock登出成功")
        
        if not data_list:
            print("❌ 没有获取到数据")
            return False
        
        # 创建DataFrame
        df = pd.DataFrame(data_list, columns=rs.fields)
        
        print(f"📊 数据形状: {df.shape}")
        print(f"📋 列名: {list(df.columns)}")
        print(f"📈 前5行数据:")
        print(df.head())
        
        # 转换数据类型并查找最低价
        df['low'] = pd.to_numeric(df['low'], errors='coerce')
        df['date'] = pd.to_datetime(df['date'])
        
        # 过滤有效数据
        df = df[df['low'] > 0]
        
        if not df.empty:
            min_low_idx = df['low'].idxmin()
            yearly_low = df.loc[min_low_idx, 'low']
            low_date = df.loc[min_low_idx, 'date'].strftime('%Y-%m-%d')
            print(f"💰 一年内最低价: {yearly_low}, 日期: {low_date}")
        
        return True
        
    except Exception as e:
        print(f"❌ baostock测试失败: {e}")
        try:
            bs.logout()
        except:
            pass
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_baostock()
