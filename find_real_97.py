import requests
import re
import json

def find_real_score(code):
    """找到真正的97分"""
    try:
        # 扫雷宝API接口
        api_url = f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code={code}&color=0"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Referer': 'http://page3.tdx.com.cn:7615/',
        }
        
        print(f"🔍 查找股票 {code} 的真实97分...")
        response = requests.get(api_url, headers=headers, timeout=15)
        response.encoding = 'utf-8'
        
        if response.status_code == 200:
            content = response.text
            
            # 查找所有包含97的地方
            print("🎯 查找所有包含97的位置:")
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if '97' in line:
                    print(f"行 {i+1}: {line.strip()}")
            
            # 专门查找JavaScript中的变量赋值
            print("\n🔍 查找JavaScript变量:")
            
            # 查找所有可能的分数变量
            patterns = [
                (r'realvalue\s*=\s*(\d+)', 'realvalue'),
                (r'showvalue\s*=\s*(\d+)', 'showvalue'), 
                (r'max\s*=\s*(\d+)', 'max'),
                (r'pieRar\s*=\s*(\d+)', 'pieRar'),
                (r'isScan\s*=\s*(\d+)', 'isScan'),
                (r'ix\s*=\s*(\d+)', 'ix'),
                (r'deg\s*=\s*(\d+)', 'deg'),
            ]
            
            for pattern, name in patterns:
                matches = re.findall(pattern, content)
                if matches:
                    print(f"{name}: {matches}")
            
            # 查找Canvas绘制的文本
            print("\n🎨 查找Canvas绘制:")
            canvas_patterns = [
                r'fillText\([^)]*"([^"]*)"[^)]*\)',
                r'fillText\(([^,]+),',
            ]
            
            for pattern in canvas_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    print(f"Canvas文本: {matches}")
            
            # 查找具体的97分显示
            print("\n🎯 专门查找97分:")
            score_97_patterns = [
                r'97[分]?',
                r'"97"',
                r'=97[,;]',
                r'97\+',
                r'showvalue.*97',
                r'realvalue.*97',
            ]
            
            for pattern in score_97_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    print(f"找到97: {pattern} -> {matches}")
                    
            # 查找所有数字97周围的上下文
            print("\n📍 97的上下文:")
            contexts = re.findall(r'.{0,30}97.{0,30}', content)
            for i, ctx in enumerate(contexts):
                if not any(css in ctx.lower() for css in ['color', 'rgb', 'background', 'border', 'font']):
                    print(f"上下文 {i+1}: {ctx.strip()}")
            
            return None
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        return None

if __name__ == '__main__':
    find_real_score('000001')
