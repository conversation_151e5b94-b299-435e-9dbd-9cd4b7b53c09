from flask import Flask, render_template, jsonify
import requests
import time
import json
import pandas as pd
from datetime import datetime
import threading
import random
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

app = Flask(__name__)

# 全局变量存储股票数据
stock_data = {}
last_update_time = None

# 创建带重试机制的session
def create_session():
    """创建带重试和防限制的session"""
    session = requests.Session()

    # 设置重试策略
    retry_strategy = Retry(
        total=3,
        backoff_factor=1,
        status_forcelist=[429, 500, 502, 503, 504],
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    # 设置User-Agent，模拟浏览器
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Referer': 'http://quote.eastmoney.com/'
    }
    session.headers.update(headers)

    return session

# 全局session
session = create_session()

def get_eastmoney_stock_data(stock_code):
    """从东方财富获取单只股票实时数据 - 防限制版本"""

    # 根据股票代码确定市场
    if stock_code.startswith('6'):
        secid = f'1.{stock_code}'  # 上海
    else:
        secid = f'0.{stock_code}'  # 深圳

    url = 'http://push2.eastmoney.com/api/qt/stock/get'
    params = {
        'fltt': '2',
        'invt': '2',
        'fields': 'f43,f44,f45,f46,f47,f48,f57,f58,f60,f169,f170',
        'secid': secid,
        '_': str(int(time.time() * 1000))
    }

    try:
        # 使用全局session，带重试机制
        response = session.get(url, params=params, timeout=10)

        if response.status_code == 200:
            data = json.loads(response.text)['data']
            if data:
                return {
                    'code': data['f57'],
                    'name': data['f58'],
                    'price': data['f43'],
                    'open': data['f46'],
                    'high': data['f44'],
                    'low': data['f45'],
                    'yesterday_close': data['f60'],
                    'volume': data['f47'],
                    'amount': data['f48'],
                    'change': data['f169'],
                    'change_pct': data['f170'],
                    'update_time': datetime.now().strftime('%H:%M:%S')
                }
        elif response.status_code == 429:
            print(f"⚠️  请求过于频繁，等待后重试...")
            time.sleep(random.uniform(2, 5))
            return None

    except Exception as e:
        print(f"获取股票 {stock_code} 数据失败: {e}")
        return None

def load_stock_list():
    """加载股票列表"""
    try:
        df = pd.read_csv('stocks_list.csv', encoding='utf-8-sig')
        return df.to_dict('records')
    except Exception as e:
        print(f"加载股票列表失败: {e}")
        return []

def update_all_stocks():
    """更新所有股票数据 - 防限制版本"""
    global stock_data, last_update_time

    stock_list = load_stock_list()
    print(f"🚀 开始更新 {len(stock_list)} 只股票数据...")

    # 清空旧数据
    stock_data.clear()

    success_count = 0
    failed_count = 0

    # 分批处理，避免一次性请求太多
    batch_size = 10
    total_batches = (len(stock_list) + batch_size - 1) // batch_size

    for batch_idx in range(total_batches):
        start_idx = batch_idx * batch_size
        end_idx = min(start_idx + batch_size, len(stock_list))
        batch_stocks = stock_list[start_idx:end_idx]

        print(f"📦 处理第 {batch_idx + 1}/{total_batches} 批 ({len(batch_stocks)} 只股票)")

        for stock in batch_stocks:
            code = str(stock['代码']).zfill(6)

            # 获取实时数据
            real_data = get_eastmoney_stock_data(code)

            if real_data:
                stock_data[code] = real_data
                success_count += 1
                print(f"✅ {code} {real_data['name']} {real_data['price']}")
            else:
                # 如果获取失败，使用基本信息
                stock_data[code] = {
                    'code': code,
                    'name': stock['名称'],
                    'price': 0, 'change': 0, 'change_pct': 0,
                    'volume': 0, 'amount': 0, 'high': 0, 'low': 0,
                    'open': 0, 'yesterday_close': 0,
                    'update_time': datetime.now().strftime('%H:%M:%S'),
                    'error': '暂无数据'
                }
                failed_count += 1
                print(f"❌ {code} 获取失败")

            # 随机延时，避免请求过于频繁
            delay = random.uniform(0.2, 0.5)
            time.sleep(delay)

        # 批次间稍长延时
        if batch_idx < total_batches - 1:
            batch_delay = random.uniform(1, 3)
            print(f"⏱️  批次间等待 {batch_delay:.1f} 秒...")
            time.sleep(batch_delay)

    last_update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"✅ 更新完成！成功 {success_count}/{len(stock_list)} 只股票，失败 {failed_count} 只，时间: {last_update_time}")

def background_update():
    """后台定时更新股票数据 - 防限制版本"""
    while True:
        try:
            update_all_stocks()
            # 更长的更新间隔，避免频繁请求
            update_interval = random.uniform(120, 180)  # 2-3分钟随机间隔
            print(f"⏰ 下次更新将在 {update_interval/60:.1f} 分钟后...")
            time.sleep(update_interval)
        except Exception as e:
            print(f"❌ 后台更新出错: {e}")
            # 出错后等待更长时间
            time.sleep(300)  # 5分钟后重试

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/stocks')
def get_stocks():
    """获取所有股票数据API"""
    return jsonify({
        'stocks': list(stock_data.values()),
        'last_update': last_update_time,
        'total_count': len(stock_data)
    })

@app.route('/api/stock/<stock_code>')
def get_stock(stock_code):
    """获取单只股票数据API"""
    if stock_code in stock_data:
        return jsonify(stock_data[stock_code])
    else:
        return jsonify({'error': '股票代码不存在'}), 404

@app.route('/api/refresh')
def refresh_data():
    """手动刷新数据"""
    try:
        update_all_stocks()
        return jsonify({
            'success': True,
            'message': '数据刷新成功',
            'last_update': last_update_time,
            'total_count': len(stock_data)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'刷新失败: {str(e)}'
        }), 500

if __name__ == '__main__':
    print("=== 🚀 启动东方财富股票实时行情系统 ===")
    
    # 初始加载数据
    print("📊 正在初始化数据...")
    update_all_stocks()
    
    # 启动后台更新线程
    print("🔄 启动后台更新线程...")
    update_thread = threading.Thread(target=background_update, daemon=True)
    update_thread.start()
    
    print("🌐 启动Flask Web服务...")
    print("🔗 访问 http://localhost:5000 查看股票行情")
    print("📈 数据来源：东方财富网")
    
    # 启动Flask应用
    app.run(debug=True, host='0.0.0.0', port=5000)
