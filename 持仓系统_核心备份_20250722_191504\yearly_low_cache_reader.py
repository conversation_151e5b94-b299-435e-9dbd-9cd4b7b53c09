#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
年内最低价缓存读取模块
为主系统提供年内最低价数据读取功能
"""

import pandas as pd
import os
from datetime import datetime
from typing import Dict, Optional, Union

class YearlyLowCacheReader:
    def __init__(self, cache_dir='cache'):
        self.cache_dir = cache_dir
        self._cache_data = None
        self._cache_file = None
        self._last_load_time = None
    
    def get_cache_filename(self, date_str=None):
        """获取缓存文件名"""
        if date_str is None:
            date_str = datetime.now().strftime('%Y%m%d')
        return os.path.join(self.cache_dir, f'yearly_low_cache_{date_str}.csv')
    
    def is_cache_valid(self, cache_file=None):
        """检查缓存是否有效（当天的缓存）"""
        if cache_file is None:
            cache_file = self.get_cache_filename()
        
        if not os.path.exists(cache_file):
            return False
        
        # 检查文件是否是今天创建的
        file_time = datetime.fromtimestamp(os.path.getmtime(cache_file))
        today = datetime.now().date()
        
        return file_time.date() == today
    
    def load_cache(self, force_reload=False):
        """加载缓存数据到内存"""
        cache_file = self.get_cache_filename()

        # 如果已经加载且文件未变化，直接返回
        if (not force_reload and
            self._cache_data is not None and
            self._cache_file == cache_file and
            os.path.exists(cache_file)):

            # 检查文件是否被修改
            try:
                current_mtime = os.path.getmtime(cache_file)
                if self._last_load_time and current_mtime <= self._last_load_time:
                    return True
            except:
                pass

        try:
            if os.path.exists(cache_file):
                self._cache_data = pd.read_csv(cache_file, encoding='utf-8-sig')
                self._cache_file = cache_file
                self._last_load_time = os.path.getmtime(cache_file)

                # 创建股票代码到数据的映射，提高查询效率
                self._cache_dict = {}
                for _, row in self._cache_data.iterrows():
                    code = str(row['股票代码']).zfill(6)
                    self._cache_dict[code] = {
                        'yearly_low': row['年内最低价'] if pd.notna(row['年内最低价']) else None,
                        'low_date': row['最低价日期'] if pd.notna(row['最低价日期']) else None,
                        'data_points': row['数据点数'] if pd.notna(row['数据点数']) else 0,
                        'update_time': row['获取时间'] if pd.notna(row['获取时间']) else None,
                        'status': row['状态'] if pd.notna(row['状态']) else '未知',
                        'success': row['状态'] == '成功' if pd.notna(row['状态']) else False
                    }

                return True
            else:
                self._cache_data = None
                self._cache_dict = {}
                return False

        except Exception as e:
            print(f"❌ 加载年内最低价缓存失败: {e}")
            self._cache_data = None
            self._cache_dict = {}
            return False
    
    def get_yearly_low(self, code: str) -> Dict:
        """获取指定股票的年内最低价数据"""
        # 确保代码格式正确
        code = str(code).zfill(6)

        # 尝试加载缓存
        if not self.load_cache():
            return {
                'yearly_low': None,
                'low_date': None,
                'data_points': 0,
                'source': 'cache_not_available',
                'success': False,
                'message': '缓存文件不存在或加载失败'
            }

        # 确保 _cache_dict 存在
        if not hasattr(self, '_cache_dict') or self._cache_dict is None:
            return {
                'yearly_low': None,
                'low_date': None,
                'data_points': 0,
                'source': 'cache_error',
                'success': False,
                'message': '缓存数据结构错误'
            }

        # 从缓存中查找数据
        if code in self._cache_dict:
            data = self._cache_dict[code].copy()
            data['source'] = 'cache'
            data['message'] = '从缓存读取成功'
            return data
        else:
            return {
                'yearly_low': None,
                'low_date': None,
                'data_points': 0,
                'source': 'cache',
                'success': False,
                'message': f'缓存中未找到股票 {code} 的数据'
            }
    
    def get_cache_status(self):
        """获取缓存状态信息"""
        cache_file = self.get_cache_filename()
        
        if not os.path.exists(cache_file):
            return {
                'exists': False,
                'valid': False,
                'file_path': cache_file,
                'message': '今日缓存文件不存在，请运行 yearly_low_cache_system.py 生成缓存'
            }
        
        try:
            # 文件信息
            file_stat = os.stat(cache_file)
            file_time = datetime.fromtimestamp(file_stat.st_mtime)
            is_today = file_time.date() == datetime.now().date()
            
            # 数据信息
            df = pd.read_csv(cache_file, encoding='utf-8-sig')
            total_count = len(df)
            success_count = len(df[df['状态'] == '成功'])
            
            return {
                'exists': True,
                'valid': is_today,
                'file_path': cache_file,
                'file_time': file_time.strftime('%Y-%m-%d %H:%M:%S'),
                'total_count': total_count,
                'success_count': success_count,
                'failed_count': total_count - success_count,
                'success_rate': success_count / total_count * 100 if total_count > 0 else 0,
                'message': '缓存可用' if is_today else '缓存已过期，建议重新生成'
            }
        except Exception as e:
            return {
                'exists': True,
                'valid': False,
                'file_path': cache_file,
                'error': str(e),
                'message': '缓存文件损坏或格式错误'
            }
    
    def get_all_cached_data(self):
        """获取所有缓存的年内最低价数据"""
        if not self.load_cache():
            return {}
        
        return self._cache_dict.copy()
    
    def get_failed_stocks(self):
        """获取获取失败的股票列表"""
        if not self.load_cache():
            return []
        
        failed_stocks = []
        for code, data in self._cache_dict.items():
            if not data['success']:
                failed_stocks.append({
                    'code': code,
                    'status': data['status'],
                    'update_time': data['update_time']
                })
        
        return failed_stocks


# 全局实例，供主系统使用
_cache_reader = None

def get_cache_reader():
    """获取缓存读取器实例（单例模式）"""
    global _cache_reader
    if _cache_reader is None:
        _cache_reader = YearlyLowCacheReader()
    return _cache_reader

def get_yearly_low_from_cache(code: str) -> Dict:
    """便捷函数：从缓存获取年内最低价"""
    reader = get_cache_reader()
    return reader.get_yearly_low(code)

def check_cache_status():
    """便捷函数：检查缓存状态"""
    reader = get_cache_reader()
    return reader.get_cache_status()

def is_cache_available():
    """便捷函数：检查缓存是否可用"""
    status = check_cache_status()
    return status['exists'] and status['valid']


# 兼容性函数，用于替换原有的 get_yearly_low_price 函数
def get_yearly_low_price_from_cache(code: str) -> dict:
    """
    兼容性函数，返回格式与原函数一致
    用于替换主系统中的 get_yearly_low_price 函数
    """
    result = get_yearly_low_from_cache(code)
    
    return {
        'yearly_low': result['yearly_low'],
        'low_date': result['low_date'],
        'data_points': result['data_points']
    }


if __name__ == '__main__':
    # 测试代码
    reader = YearlyLowCacheReader()
    
    print("=== 📊 年内最低价缓存读取器测试 ===")
    
    # 检查缓存状态
    status = reader.get_cache_status()
    print(f"\n📋 缓存状态:")
    print(f"   存在: {status['exists']}")
    print(f"   有效: {status['valid']}")
    print(f"   文件: {status['file_path']}")
    print(f"   消息: {status['message']}")
    
    if status['exists'] and 'total_count' in status:
        print(f"   总数: {status['total_count']}")
        print(f"   成功: {status['success_count']}")
        print(f"   失败: {status['failed_count']}")
        print(f"   成功率: {status['success_rate']:.1f}%")
        print(f"   更新时间: {status['file_time']}")
    
    # 测试获取数据
    if status['exists']:
        print(f"\n🔍 测试获取数据:")
        test_codes = ['000001', '000002', '600000']
        
        for code in test_codes:
            result = reader.get_yearly_low(code)
            if result['success']:
                print(f"   {code}: 最低价 {result['yearly_low']:.2f} ({result['low_date']})")
            else:
                print(f"   {code}: {result['message']}")
