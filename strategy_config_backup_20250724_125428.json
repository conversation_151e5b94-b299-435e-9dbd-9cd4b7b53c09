{"distance_from_low": {"name": "距最低点涨幅策略", "description": "基于距年内最低价的涨幅判断", "enabled": true, "params": {"sell_threshold": 70, "warning_threshold": 60}, "weight": 1, "category": "technical"}, "pe_abnormal": {"name": "PE异常策略", "description": "PE异常且已回本时卖出", "enabled": true, "params": {"pe_max": 25, "pe_min": 0, "require_profit": true}, "weight": 1, "category": "fundamental"}, "pb_overvalued": {"name": "PB高估策略", "description": "PB过高时卖出", "enabled": false, "params": {"pb_threshold": 5, "require_profit": true}, "weight": 0.8, "category": "fundamental"}, "profit_target": {"name": "盈利目标策略", "description": "达到目标盈利率时卖出", "enabled": false, "params": {"profit_threshold": 30, "warning_threshold": 25}, "weight": 0.9, "category": "profit"}, "dividend_yield_low": {"name": "股息率过低策略", "description": "股息率过低且高估时卖出", "enabled": false, "params": {"dividend_max": 2, "pe_min": 20}, "weight": 0.6, "category": "fundamental"}, "scan_risk_high": {"name": "扫雷高风险策略", "description": "扫雷评分过低时卖出", "enabled": false, "params": {"require_profit": false, "score_threshold": 70}, "weight": 0.7, "category": "risk"}, "high_gain_sell": {"name": "高涨幅卖出策略", "description": "距最低点涨幅>=70%时卖出", "enabled": true, "params": {"gain_threshold": 70}, "weight": 1.2, "category": "gain"}, "high_ttm_medium_gain": {"name": "高估值中涨幅策略", "description": "TTM市盈率>=30且距最低点涨幅>=50%时卖出", "enabled": true, "params": {"gain_threshold": 50, "ttm_threshold": 30}, "weight": 1.3, "category": "valuation"}, "limit_up_high_pb_clearance": {"name": "涨停高估值清仓策略", "description": "涨停且PB>=1.75且距最低点涨幅>=70%时清仓", "enabled": true, "params": {"gain_threshold": 70, "limit_up_threshold": 9.8, "pb_threshold": 1.75}, "weight": 2, "category": "clearance"}, "extreme_ttm_profit_clearance": {"name": "极值TTM回本清仓策略", "description": "TTM>=40或TTM<0且盈利时清仓", "enabled": true, "params": {"high_ttm_threshold": 40, "require_profit": true}, "weight": 2, "category": "clearance"}}