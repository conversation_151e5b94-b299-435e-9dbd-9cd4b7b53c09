# 年内最低价缓存系统使用说明

## 📋 系统概述

年内最低价缓存系统是一个独立的数据获取和缓存模块，专门为持仓系统提供年内最低价数据。通过预先获取和缓存数据，避免了主系统运行时的API调用延迟和限制问题。

## 🏗️ 系统架构

```
年内最低价缓存系统
├── yearly_low_cache_system.py     # 独立获取脚本
├── yearly_low_cache_reader.py     # 缓存读取模块
├── cache/                         # 缓存目录
│   └── yearly_low_cache_YYYYMMDD.csv  # 每日缓存文件
└── 持仓系统_v9_移动端适配版.py      # 主系统（已集成）
```

## 🚀 快速开始

### 1. 生成缓存数据

```bash
# 基本用法：获取所有股票的年内最低价
python yearly_low_cache_system.py

# 强制刷新缓存（即使今日已有缓存）
python yearly_low_cache_system.py --force

# 指定股票列表文件
python yearly_low_cache_system.py --stock-file my_stocks.csv
```

### 2. 查看缓存状态

```bash
# 查看缓存信息
python yearly_low_cache_system.py --info

# 示例输出：
# 📋 缓存信息:
#    文件路径: cache/yearly_low_cache_20250722.csv
#    📊 总数量: 103
#    ✅ 成功: 98
#    ❌ 失败: 5
#    📈 成功率: 95.1%
#    🕐 更新时间: 2025-07-22 09:30:15
#    📅 是否今日: 是
```

### 3. 重试失败的股票

```bash
# 重试获取失败的股票
python yearly_low_cache_system.py --retry
```

### 4. 清理旧缓存

```bash
# 清理7天前的旧缓存文件
python yearly_low_cache_system.py --clean
```

## 📊 缓存文件格式

每日生成的缓存文件包含以下字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| 股票代码 | string | 6位股票代码 |
| 股票名称 | string | 股票名称 |
| 年内最低价 | float | 前复权年内最低价 |
| 最低价日期 | string | 最低价出现日期 |
| 数据点数 | int | 获取的历史数据点数量 |
| 获取时间 | string | 数据获取时间戳 |
| 状态 | string | 获取状态（成功/失败） |
| 错误信息 | string | 失败时的错误信息 |

## 🔧 配置参数

在 `yearly_low_cache_system.py` 中可以调整以下参数：

```python
self.config = {
    'request_timeout': 10,           # 请求超时时间（秒）
    'retry_count': 3,                # 重试次数
    'delay_between_stocks': 1.5,     # 股票间延时（秒）
    'delay_between_retries': 2.0,    # 重试间延时（秒）
}
```

## 🔗 主系统集成

主系统已自动集成缓存功能：

### API接口

1. **检查缓存状态**
   ```
   GET /api/cache/status
   ```

2. **检查缓存可用性**
   ```
   GET /api/cache/check
   ```

### 数据获取优先级

1. **优先使用缓存**：从当日缓存文件读取
2. **缓存失败时**：回退到实时API获取
3. **向后兼容**：保持原有接口不变

## 📅 建议的使用流程

### 每日工作流程

1. **早上开盘前**（建议8:30-9:00）
   ```bash
   python yearly_low_cache_system.py
   ```

2. **检查获取结果**
   ```bash
   python yearly_low_cache_system.py --info
   ```

3. **如有失败股票，进行重试**
   ```bash
   python yearly_low_cache_system.py --retry
   ```

4. **启动主系统**
   ```bash
   python 持仓系统_v9_移动端适配版.py
   ```

### 自动化脚本示例

创建 `daily_cache_update.bat`：

```batch
@echo off
echo 开始更新年内最低价缓存...
python yearly_low_cache_system.py

echo 检查缓存状态...
python yearly_low_cache_system.py --info

echo 重试失败的股票...
python yearly_low_cache_system.py --retry

echo 缓存更新完成！
pause
```

## ⚠️ 注意事项

### 1. 网络和API限制
- 系统会在股票间添加1.5秒延时，避免API限制
- 如遇网络问题，会自动重试3次
- 建议在网络稳定时运行

### 2. 数据时效性
- 缓存文件按日期命名，每天只需生成一次
- 年内最低价数据相对稳定，日内变化较小
- 如需最新数据，可使用 `--force` 参数强制刷新

### 3. 存储空间
- 每个缓存文件约10-50KB（取决于股票数量）
- 系统会自动清理7天前的旧缓存
- 可手动运行 `--clean` 清理旧文件

### 4. 错误处理
- 获取失败的股票会记录在缓存文件中
- 可使用 `--retry` 重新获取失败的股票
- 主系统会自动回退到实时API

## 🛠️ 故障排除

### 常见问题

1. **缓存文件不存在**
   ```
   解决方案：运行 python yearly_low_cache_system.py
   ```

2. **大量股票获取失败**
   ```
   可能原因：网络问题、API限制
   解决方案：检查网络连接，稍后重试
   ```

3. **主系统提示缓存不可用**
   ```
   解决方案：检查 cache 目录和当日缓存文件
   ```

4. **数据不准确**
   ```
   解决方案：使用 --force 参数强制刷新缓存
   ```

### 调试模式

在脚本中添加详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📈 性能优化

### 1. 批量处理
- 系统已优化为批量获取，避免频繁API调用
- 使用内存缓存提高读取效率

### 2. 并发控制
- 合理的延时设置避免API限制
- 重试机制确保数据完整性

### 3. 存储优化
- CSV格式便于查看和处理
- 自动清理机制节省存储空间

## 🔄 版本更新

### V1.0 特性
- ✅ 独立的年内最低价获取系统
- ✅ 每日缓存机制
- ✅ 错误重试和记录
- ✅ 主系统无缝集成
- ✅ API状态检查接口
- ✅ 命令行工具支持

### 后续计划
- 🔄 支持多种数据源（baostock、tushare等）
- 🔄 增加数据验证和校正功能
- 🔄 Web界面管理缓存
- 🔄 定时任务自动化

## 📞 技术支持

如遇问题，请检查：
1. 股票列表文件格式是否正确
2. 网络连接是否正常
3. cache目录权限是否正确
4. Python依赖包是否完整

---

**最后更新：2025-07-22**
