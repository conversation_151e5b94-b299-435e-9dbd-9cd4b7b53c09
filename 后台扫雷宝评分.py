"""
后台扫雷宝评分获取器 - 不影响用户电脑使用
专门设计为后台运行，静默处理
"""

import os
import time
import re
import threading
import logging
from PIL import Image

# 静默模式 - 减少日志输出
logging.getLogger('selenium').setLevel(logging.ERROR)
logging.getLogger('urllib3').setLevel(logging.ERROR)

# OCR导入
try:
    import pytesseract
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False
    print("❌ 需要安装: pip install pytesseract")

# Selenium导入
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium.webdriver.chrome.service import Service
        WEBDRIVER_MANAGER_AVAILABLE = True
    except ImportError:
        WEBDRIVER_MANAGER_AVAILABLE = False
    
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    print("❌ 需要安装: pip install selenium webdriver-manager")

class SilentSLBScorer:
    """静默扫雷宝评分获取器"""
    
    def __init__(self):
        self.setup_ocr()
    
    def setup_ocr(self):
        """设置OCR"""
        if not OCR_AVAILABLE:
            return False
        
        # 常见tesseract路径
        paths = [
            r"C:\Program Files\Tesseract-OCR\tesseract.exe",
            r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
        ]
        
        for path in paths:
            if os.path.exists(path):
                pytesseract.pytesseract.tesseract_cmd = path
                return True
        return False
    
    def create_silent_browser(self):
        """创建完全静默的浏览器"""
        if not SELENIUM_AVAILABLE:
            return None
        
        try:
            options = Options()
            # 完全后台运行配置
            options.add_argument('--headless')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--disable-software-rasterizer')
            options.add_argument('--disable-extensions')
            options.add_argument('--disable-plugins')
            options.add_argument('--disable-images')
            options.add_argument('--mute-audio')
            options.add_argument('--disable-notifications')
            options.add_argument('--disable-popup-blocking')
            options.add_argument('--disable-background-timer-throttling')
            options.add_argument('--disable-backgrounding-occluded-windows')
            options.add_argument('--disable-renderer-backgrounding')
            options.add_argument('--window-size=1200,800')
            options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
            
            # 禁用日志
            options.add_argument('--log-level=3')
            options.add_argument('--silent')
            options.add_experimental_option('excludeSwitches', ['enable-logging'])
            options.add_experimental_option('useAutomationExtension', False)
            
            # 尝试使用webdriver-manager
            if WEBDRIVER_MANAGER_AVAILABLE:
                try:
                    service = Service(ChromeDriverManager().install())
                    driver = webdriver.Chrome(service=service, options=options)
                    return driver
                except:
                    pass
            
            # 回退到系统ChromeDriver
            driver = webdriver.Chrome(options=options)
            return driver
            
        except Exception as e:
            print(f"❌ 浏览器启动失败: {e}")
            return None
    
    def get_score_silent(self, stock_code: str) -> float:
        """静默获取评分"""
        driver = None
        try:
            driver = self.create_silent_browser()
            if not driver:
                return None
            
            # 访问页面
            url = f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code={stock_code}&color=0"
            driver.get(url)
            
            # 等待页面加载
            time.sleep(3)
            
            # 截图
            screenshot_path = f"temp_slb_{stock_code}.png"
            driver.save_screenshot(screenshot_path)
            
            # OCR识别
            score = self.extract_score_ocr(screenshot_path)
            
            # 清理文件
            try:
                os.remove(screenshot_path)
            except:
                pass
            
            return score
            
        except Exception as e:
            return None
        finally:
            if driver:
                try:
                    driver.quit()
                except:
                    pass
    
    def extract_score_ocr(self, image_path: str) -> float:
        """OCR提取评分"""
        if not OCR_AVAILABLE:
            return None
        
        try:
            image = Image.open(image_path)
            
            # 多种OCR配置
            configs = [
                '--psm 6 -c tessedit_char_whitelist=0123456789分',
                '--psm 8 -c tessedit_char_whitelist=0123456789',
                '--psm 7 -c tessedit_char_whitelist=0123456789分',
            ]
            
            all_scores = []
            
            for config in configs:
                try:
                    text = pytesseract.image_to_string(image, lang='chi_sim+eng', config=config)
                    scores = self.parse_scores(text)
                    all_scores.extend(scores)
                except:
                    continue
            
            if all_scores:
                # 选择最常见的分数
                from collections import Counter
                score_counts = Counter(all_scores)
                return float(score_counts.most_common(1)[0][0])
            
            return None
            
        except Exception as e:
            return None
    
    def parse_scores(self, text: str) -> list:
        """解析分数"""
        scores = []
        patterns = [r'(\d{1,3})分', r'(\d{1,3})\s*分', r'(\d{1,3})']
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                try:
                    score = int(match)
                    if 50 <= score <= 100:  # 合理范围
                        scores.append(score)
                except:
                    continue
        
        return scores

def background_batch_process(stock_codes: list, progress_callback=None):
    """后台批量处理"""
    scorer = SilentSLBScorer()
    results = {}
    
    print(f"🔄 后台开始处理 {len(stock_codes)} 只股票...")
    
    for i, code in enumerate(stock_codes):
        print(f"📊 处理 [{i+1}/{len(stock_codes)}]: {code}")
        
        score = scorer.get_score_silent(code)
        results[code] = score
        
        if score is not None:
            print(f"✅ {code}: {score}分")
        else:
            print(f"❌ {code}: 获取失败")
        
        if progress_callback:
            progress_callback(i+1, len(stock_codes), code, score)
        
        # 后台处理间隔
        time.sleep(2)
    
    return results

def test_background_slb():
    """测试后台处理"""
    
    # 测试股票
    test_stocks = [
        "300479",  # 神思电子
        "000001",  # 平安银行
        "600000",  # 浦发银行
    ]
    
    print("=== 后台扫雷宝评分测试 ===")
    print("💡 程序在后台运行，不会影响您的电脑使用")
    print("-" * 50)
    
    def progress_update(current, total, code, score):
        """进度更新"""
        percent = current / total * 100
        print(f"📈 进度: {percent:.1f}% ({current}/{total})")
    
    # 执行后台处理
    results = background_batch_process(test_stocks, progress_update)
    
    # 显示结果
    print("\n" + "=" * 50)
    print("🎉 处理完成！结果汇总:")
    print("=" * 50)
    
    success_count = 0
    for code, score in results.items():
        if score is not None:
            print(f"✅ {code}: {score:5.1f}分")
            success_count += 1
            
            # 验证神思电子
            if code == "300479":
                if 90 <= score <= 100:
                    print(f"   ✅ 神思电子评分合理 (期望97分左右)")
                else:
                    print(f"   ⚠️ 神思电子评分异常")
        else:
            print(f"❌ {code}: 获取失败")
    
    print(f"\n📊 成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
    return results

if __name__ == "__main__":
    print("🚀 后台扫雷宝评分系统")
    print("💡 本程序专为后台运行设计，不会影响您的电脑使用")
    print()
    
    try:
        test_background_slb()
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        print("💡 请确保已安装Chrome浏览器和相关依赖")
