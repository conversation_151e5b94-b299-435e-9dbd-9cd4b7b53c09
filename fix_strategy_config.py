#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略配置修复工具
用于检查和修复策略配置文件的完整性
"""

import json
import os
from datetime import datetime

def get_default_strategies():
    """获取默认策略配置"""
    return {
        "distance_from_low": {
            "name": "距最低点涨幅策略",
            "description": "基于距年内最低价的涨幅判断",
            "enabled": True,
            "params": {
                "sell_threshold": 70.0,
                "warning_threshold": 60.0
            },
            "weight": 1.0,
            "category": "technical"
        },
        "pe_abnormal": {
            "name": "PE异常策略",
            "description": "PE异常且已回本时卖出",
            "enabled": True,
            "params": {
                "pe_max": 25.0,
                "pe_min": 0.0,
                "require_profit": True
            },
            "weight": 1.0,
            "category": "fundamental"
        },
        "pb_overvalued": {
            "name": "PB高估策略",
            "description": "PB过高时卖出",
            "enabled": False,
            "params": {
                "pb_threshold": 5.0,
                "require_profit": True
            },
            "weight": 0.8,
            "category": "fundamental"
        },
        "profit_target": {
            "name": "盈利目标策略",
            "description": "达到目标盈利率时卖出",
            "enabled": False,
            "params": {
                "profit_threshold": 30.0,
                "warning_threshold": 25.0
            },
            "weight": 0.9,
            "category": "profit"
        },
        "dividend_yield_low": {
            "name": "股息率过低策略",
            "description": "股息率过低且高估时卖出",
            "enabled": False,
            "params": {
                "dividend_max": 2.0,
                "pe_min": 20.0
            },
            "weight": 0.6,
            "category": "fundamental"
        },
        "scan_risk_high": {
            "name": "扫雷高风险策略",
            "description": "扫雷评分过低时卖出",
            "enabled": False,
            "params": {
                "score_threshold": 70,
                "require_profit": False
            },
            "weight": 0.7,
            "category": "risk"
        },
        "high_gain_sell": {
            "name": "高涨幅卖出策略",
            "description": "距最低点涨幅>=70%时卖出",
            "enabled": True,
            "params": {
                "gain_threshold": 70.0
            },
            "weight": 1.2,
            "category": "gain"
        },
        "high_ttm_medium_gain": {
            "name": "高估值中涨幅策略",
            "description": "TTM市盈率>=30且距最低点涨幅>=50%时卖出",
            "enabled": True,
            "params": {
                "ttm_threshold": 30.0,
                "gain_threshold": 50.0
            },
            "weight": 1.3,
            "category": "valuation"
        },
        "limit_up_high_pb_clearance": {
            "name": "涨停高估值清仓策略",
            "description": "涨停且PB>=1.75且距最低点涨幅>=70%时清仓",
            "enabled": True,
            "params": {
                "limit_up_threshold": 9.8,
                "pb_threshold": 1.75,
                "gain_threshold": 70.0
            },
            "weight": 2.0,
            "category": "clearance"
        },
        "extreme_ttm_profit_clearance": {
            "name": "极值TTM回本清仓策略",
            "description": "TTM>=40或TTM<0且盈利时清仓",
            "enabled": True,
            "params": {
                "high_ttm_threshold": 40.0,
                "require_profit": True
            },
            "weight": 2.0,
            "category": "clearance"
        }
    }

def check_and_fix_config():
    """检查并修复策略配置"""
    config_file = 'strategy_config.json'
    backup_file = f'strategy_config_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
    
    default_strategies = get_default_strategies()
    current_config = {}
    
    print("🔍 开始检查策略配置文件...")
    
    # 读取当前配置
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                current_config = json.load(f)
            print(f"✅ 成功读取当前配置文件，包含 {len(current_config)} 个策略")
            
            # 备份当前配置
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(current_config, f, ensure_ascii=False, indent=2)
            print(f"💾 已备份当前配置到: {backup_file}")
            
        except Exception as e:
            print(f"❌ 读取配置文件失败: {e}")
            current_config = {}
    else:
        print("⚠️ 配置文件不存在")
    
    # 检查缺失的策略
    missing_strategies = []
    updated_strategies = []
    
    for strategy_id, default_strategy in default_strategies.items():
        if strategy_id not in current_config:
            missing_strategies.append(strategy_id)
            current_config[strategy_id] = default_strategy
        else:
            # 保留用户的enabled设置，但更新其他默认值
            user_enabled = current_config[strategy_id].get('enabled', default_strategy['enabled'])
            user_params = current_config[strategy_id].get('params', {})
            
            # 合并参数，保留用户设置
            merged_params = default_strategy['params'].copy()
            merged_params.update(user_params)
            
            current_config[strategy_id] = default_strategy.copy()
            current_config[strategy_id]['enabled'] = user_enabled
            current_config[strategy_id]['params'] = merged_params
            updated_strategies.append(strategy_id)
    
    # 报告结果
    if missing_strategies:
        print(f"🆕 添加了 {len(missing_strategies)} 个缺失的策略:")
        for strategy_id in missing_strategies:
            strategy = current_config[strategy_id]
            print(f"  + {strategy['name']} (enabled: {strategy['enabled']})")
    
    if updated_strategies:
        print(f"🔄 更新了 {len(updated_strategies)} 个现有策略的默认值")
    
    # 保存修复后的配置
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(current_config, f, ensure_ascii=False, indent=2)
        print(f"✅ 配置文件已修复并保存")
        
        # 显示最终状态
        enabled_count = sum(1 for config in current_config.values() if config.get('enabled', False))
        print(f"📊 最终配置: 总共 {len(current_config)} 个策略，启用 {enabled_count} 个")
        
        print("\n🔥 启用的策略:")
        for strategy_id, config in current_config.items():
            if config.get('enabled', False):
                print(f"  ✓ {config['name']} ({config['category']})")
        
        print("\n⏸️ 禁用的策略:")
        for strategy_id, config in current_config.items():
            if not config.get('enabled', False):
                print(f"  ✗ {config['name']} ({config['category']})")
                
    except Exception as e:
        print(f"❌ 保存配置文件失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("🔧 策略配置修复工具")
    print("=" * 60)
    
    success = check_and_fix_config()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 策略配置修复完成！")
        print("💡 现在重启持仓系统，卖出信号配置将会持久保存。")
    else:
        print("❌ 策略配置修复失败！")
    print("=" * 60)
