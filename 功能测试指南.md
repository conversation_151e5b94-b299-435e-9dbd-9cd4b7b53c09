# 卖出信号点击操作功能测试指南

## 测试环境
- 程序已启动：http://localhost:5000
- 浏览器已自动打开

## 测试步骤

### 1. 查看卖出信号
1. 在浏览器中打开 http://localhost:5000
2. 查看股票表格中的"卖出信号"列
3. 寻找显示🚨卖出、🔥清仓、⚠️预警的股票

### 2. 测试点击功能
1. **找到有卖出信号的股票**
   - 根据程序输出，山东海化有🔥清仓信号
   - 河钢股份有🚨卖出信号
   
2. **点击卖出信号标签**
   - 点击🚨卖出、🔥清仓或⚠️预警标签
   - 应该弹出操作菜单

3. **操作菜单测试**
   - 菜单应包含：
     - ✅ 已减半（停止提醒）
     - 🔥 已清仓（停止提醒）
     - ❌ 取消
   
4. **选择操作**
   - 点击"✅ 已减半"或"🔥 已清仓"
   - 确认对话框应该出现
   - 点击确认

5. **验证结果**
   - 页面应该刷新
   - 该股票的卖出信号列应显示操作状态
   - 操作列也应显示相应标记

### 3. 测试界面效果

#### 可点击状态
- 卖出信号标签应该有鼠标悬停效果
- 鼠标悬停时标签会放大并有边框
- 鼠标指针变为手型

#### 菜单定位
- 菜单应出现在点击位置附近
- 菜单不应超出屏幕边界
- 点击其他地方菜单应关闭

#### 状态显示
- 已减半：显示"✅已减半"
- 已清仓：显示"🔥已清仓"（红色背景）
- 已处理的股票卖出信号不再可点击

### 4. 企业微信提醒测试
1. 标记股票后，该股票应停止企业微信提醒
2. 可以通过程序日志确认提醒状态

## 预期结果

### 成功标记后：
1. 股票状态更新
2. 卖出信号列显示操作状态
3. 操作列显示相应标记
4. 企业微信提醒停止
5. 数据自动保存

### 界面反馈：
1. 确认对话框显示操作详情
2. 成功提示包含股票信息和时间
3. 表格自动刷新显示新状态

## 故障排除

### 如果点击无反应：
1. 检查浏览器控制台是否有JavaScript错误
2. 确认股票确实有卖出信号且未被处理
3. 刷新页面重试

### 如果菜单不显示：
1. 检查CSS样式是否正确加载
2. 确认点击的是卖出信号标签而不是其他区域

### 如果操作失败：
1. 检查网络连接
2. 查看程序后台日志
3. 确认API接口正常工作

## 测试数据

根据程序输出，以下股票有卖出信号可供测试：
- 山东海化：🔥清仓信号（极值TTM回本清仓策略）
- 河钢股份：🚨卖出信号（高估值中涨幅策略）

## 注意事项

1. **操作不可逆**：标记后无法通过界面撤销
2. **数据持久化**：操作会保存到文件，重启程序后仍然有效
3. **提醒停止**：标记后该股票的所有企业微信提醒将永久停止
4. **兼容性**：保留原有的"✅"按钮功能作为备选方案
