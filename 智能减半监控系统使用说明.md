# 智能减半股票监控系统使用说明

## 🎯 系统概述

智能减半监控系统是持仓系统的核心功能之一，专门用于管理已减半股票的后续监控。系统通过冷却期机制、基本面监控和智能策略切换，帮助您更好地管理减半后的股票投资。

## 🔄 交易流程管理

### 完整的交易生命周期

```
1. 系统发出卖出信号
   ↓
2. 用户手动挂单 → 设置状态为"已挂单"
   ↓
3. 订单成交 → 用户手动更新状态
   ├── "已减半" → 进入智能监控
   └── "已清仓" → 停止监控
```

### 状态转换说明

- **已挂单**: 临时状态，等待成交
- **已减半**: 进入智能监控系统，应用特殊规则
- **已清仓**: 完全退出，停止所有监控

## 📊 智能监控机制

### 1. 冷却期机制

**目的**: 避免频繁的减半操作，给股票价格调整时间

**工作原理**:
- 减半后自动进入冷却期（默认1年）
- 冷却期内：屏蔽减半信号，保留清仓信号
- 冷却期后：恢复所有信号的正常提醒

**可视化显示**:
```
🧊冷却期 (还剩152天)
```

### 2. 基本面恶化检测

**监控指标**: TTM市盈率

**触发条件**:
- TTM ≤ 0（变负）→ 切换到"负TTM策略"
- TTM ≥ 阈值（默认30）→ 切换到"高TTM策略"

**策略切换**:
- 立即忽略冷却期限制
- 应用更严格的卖出策略
- 恢复所有类型的信号提醒

**可视化显示**:
```
⚠️负TTM (TTM: -5.2)
📊高TTM (TTM: 35.8)
```

### 3. 策略模式

| 模式 | 图标 | 说明 | 信号处理 |
|------|------|------|----------|
| 正常模式 | ✅正常 | 标准监控 | 所有信号正常 |
| 冷却期模式 | 🧊冷却期 | 减半后冷却 | 屏蔽减半信号 |
| 高TTM模式 | 📊高TTM | 基本面恶化 | 严格策略，所有信号 |
| 负TTM模式 | ⚠️负TTM | 严重恶化 | 最严格策略 |

## ⚙️ 配置管理

### 打开配置面板

点击管理按钮区域的 **"📊 减半监控配置"** 按钮

### 配置项说明

#### 冷却期设置
- **启用冷却期**: 是否启用冷却期机制
- **冷却期时长**: 1-3650天，默认365天（1年）

#### 基本面监控
- **TTM阈值**: 触发高TTM策略的阈值，默认30.0
- **检查间隔**: 基本面检查频率，1-30天，默认1天
- **自动策略切换**: 是否自动切换策略模式

### 监控状态概览

配置面板顶部显示实时统计：
- **总股票数**: 系统中的股票总数
- **已减半股票**: 设置为已减半状态的股票数量
- **冷却期股票**: 当前在冷却期内的股票数量
- **策略切换股票**: 因基本面恶化而切换策略的股票数量

## 📱 用户界面功能

### 股票列表显示

在股票表格的"卖出信号"列中，已减半股票会显示：

1. **主要状态**: 已减半标签
2. **监控信息**: 策略模式标签
3. **详细信息**: 冷却期剩余时间、TTM值等

### 状态菜单增强

点击已减半股票的状态标签，菜单中会显示：
- **🔄 取消自定义信号**: 取消减半状态，恢复正常监控
- 其他状态选项

### 批量操作

- **批量取消自定义信号**: 一次性取消多只股票的减半状态

## 🔧 API接口

### 配置管理
- `GET /api/reduction-monitoring-config`: 获取配置
- `POST /api/reduction-monitoring-config`: 更新配置

### 状态查询
- `GET /api/reduction-monitoring-status/<stock_code>`: 获取单只股票监控状态

### 信号管理
- 集成到现有的卖出信号计算逻辑中
- 自动应用信号过滤规则

## 📋 使用场景示例

### 场景1：正常减半流程

```
1. 平安银行触发减半信号（涨幅70%）
2. 用户挂单卖出50%仓位
3. 订单成交，标记为"已减半"
4. 系统自动：
   - 添加减半记录
   - 设置1年冷却期
   - 切换到冷却期模式
5. 后续监控：
   - 屏蔽减半信号1年
   - 保留清仓信号监控
   - 显示冷却期剩余时间
```

### 场景2：基本面恶化处理

```
1. 某股票已减半，在冷却期内
2. TTM从25变为35（超过阈值30）
3. 系统自动：
   - 切换到高TTM策略模式
   - 忽略冷却期限制
   - 恢复所有信号提醒
4. 用户收到新的卖出信号提醒
```

### 场景3：配置调整

```
1. 用户发现1年冷却期太长
2. 打开减半监控配置面板
3. 调整冷却期为6个月（180天）
4. 保存配置
5. 系统重新计算所有已减半股票状态
```

## ⚠️ 注意事项

### 重要提醒

1. **不可逆操作**: 取消减半状态后，历史记录将丢失
2. **配置影响**: 修改配置会影响所有已减半股票
3. **信号优先级**: 基本面恶化信号优先于冷却期规则

### 最佳实践

1. **合理设置冷却期**: 根据个人投资风格调整
2. **关注基本面变化**: 定期检查TTM等指标
3. **及时响应信号**: 基本面恶化时及时处理
4. **记录重要决策**: 手动记录重要的投资决策

## 🔍 故障排除

### 常见问题

**Q: 为什么减半后还收到减半信号？**
A: 检查是否启用了冷却期，或者基本面是否恶化导致策略切换

**Q: 冷却期时间不对？**
A: 检查配置设置，确认冷却期时长设置正确

**Q: 策略模式显示异常？**
A: 刷新页面或重新加载数据，检查TTM数据是否正常

### 技术支持

如遇到问题，请：
1. 检查浏览器控制台错误信息
2. 确认网络连接正常
3. 尝试刷新页面重新加载
4. 记录具体操作步骤和错误信息

## 📈 系统优势

1. **智能化**: 自动识别基本面变化，智能切换策略
2. **可配置**: 灵活的配置选项，适应不同投资风格
3. **可视化**: 直观的状态显示，一目了然
4. **集成化**: 与现有系统无缝集成，操作简便
5. **可靠性**: 完善的错误处理和数据保护机制
