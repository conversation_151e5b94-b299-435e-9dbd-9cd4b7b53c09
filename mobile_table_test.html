<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端表格固定功能测试</title>
    <style>
        body {
            margin: 0;
            padding: 10px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 10px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .table-container {
            background: white;
            margin: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: auto;
            max-height: 85vh;
            position: relative;
            -webkit-overflow-scrolling: touch;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
            border-spacing: 0;
            line-height: 1.2;
            min-width: 1200px;
        }

        th, td {
            text-align: left;
            white-space: nowrap;
            margin: 0;
            border: none;
        }

        th {
            padding: 8px 10px;
            line-height: 1.3;
            background: #f8f9fa;
            font-weight: bold;
            color: #495057;
            position: sticky;
            top: 0;
            z-index: 20;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        tbody td {
            padding: 2px 6px;
            line-height: 1.0;
            height: 24px;
            vertical-align: middle;
        }

        /* 固定左侧列样式 */
        .sticky-left-1 {
            position: sticky;
            left: 0;
            min-width: 80px;
            z-index: 15;
            background: #f8f9fa;
            box-shadow: 2px 0 4px rgba(0,0,0,0.08);
        }

        .sticky-left-2 {
            position: sticky;
            left: 80px;
            min-width: 100px;
            z-index: 15;
            background: #f8f9fa;
            box-shadow: 2px 0 4px rgba(0,0,0,0.08);
        }

        /* 表头固定列样式 - 最高优先级 */
        th.sticky-left-1 {
            z-index: 25;
            background: #f8f9fa;
        }

        th.sticky-left-2 {
            z-index: 25;
            background: #f8f9fa;
        }

        /* 数据行的固定列样式 */
        tbody td.sticky-left-1 {
            background: white;
        }

        tbody td.sticky-left-2 {
            background: white;
        }

        tbody tr:hover {
            background-color: #f8f9fa;
        }

        tbody tr:hover td {
            background-color: #f8f9fa !important;
        }

        tbody tr:hover td.sticky-left-1,
        tbody tr:hover td.sticky-left-2 {
            background: #e3f2fd !important;
        }

        .positive {
            color: #dc3545;
        }

        .negative {
            color: #28a745;
        }

        .sell-signal {
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            font-size: 12px;
            text-align: center;
            min-width: 60px;
            display: inline-block;
        }

        .sell-signal.clearance {
            background-color: #8b0000;
            animation: pulse 2s infinite;
        }

        .sell-signal.sell {
            background-color: #ff4757;
        }

        .sell-signal.warning {
            background-color: #ffa502;
        }

        .sell-signal.hold {
            background-color: #2ed573;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        /* 移动端特定样式 */
        @media (max-width: 768px) {
            .table-container {
                margin: 5px;
                max-height: 90vh;
            }

            table {
                font-size: 11px;
                min-width: 1200px;
            }

            th {
                padding: 6px 4px;
                font-size: 11px;
            }

            tbody td {
                padding: 1px 4px;
                font-size: 10px;
                height: 20px;
            }

            .sticky-left-1 {
                min-width: 60px;
                left: 0;
            }

            .sticky-left-2 {
                min-width: 80px;
                left: 60px;
            }
        }

        @media (max-width: 480px) {
            .table-container {
                margin: 2px;
                max-height: 92vh;
            }

            table {
                font-size: 10px;
                min-width: 1000px;
            }

            th, td {
                padding: 3px 2px;
                font-size: 10px;
            }

            .sticky-left-1 {
                min-width: 50px;
                left: 0;
            }

            .sticky-left-2 {
                min-width: 70px;
                left: 50px;
            }
        }

        .test-info {
            background: #e3f2fd;
            padding: 15px;
            margin: 10px;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }

        .test-info h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }

        .test-info ul {
            margin: 0;
            padding-left: 20px;
        }

        .test-info li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📱 移动端表格固定功能测试</h1>
        <p>测试固定表头和固定列功能</p>
    </div>

    <div class="test-info">
        <h3>🧪 测试说明</h3>
        <ul>
            <li><strong>固定表头：</strong>垂直滚动时，表头应始终保持在顶部可见</li>
            <li><strong>固定列：</strong>水平滚动时，第一列（代码）和第二列（名称）应始终保持在左侧可见</li>
            <li><strong>移动端优化：</strong>在768px以下的屏幕上启用固定功能</li>
            <li><strong>触摸滚动：</strong>支持平滑的触摸滚动体验</li>
        </ul>
    </div>

    <div class="table-container">
        <table id="testTable">
            <thead>
                <tr>
                    <th class="sticky-left-1">代码</th>
                    <th class="sticky-left-2">名称</th>
                    <th>最新价</th>
                    <th>涨跌幅</th>
                    <th>年内最低价</th>
                    <th>最低价日期</th>
                    <th>距最低点涨幅</th>
                    <th>市净率</th>
                    <th>TTM市盈率</th>
                    <th>股息率</th>
                    <th>行业</th>
                    <th>卖出信号</th>
                    <th>扫雷</th>
                    <th>持仓</th>
                    <th>回本涨幅</th>
                    <th>市值</th>
                    <th>更新时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="testTableBody">
                <!-- 测试数据将通过JavaScript生成 -->
            </tbody>
        </table>
    </div>

    <script>
        // 生成测试数据
        function generateTestData() {
            const testData = [];
            const stockCodes = ['000001', '000002', '000858', '002415', '600036', '600519', '000858', '002142', '300059', '600887'];
            const stockNames = ['平安银行', '万科A', '五粮液', '海康威视', '招商银行', '贵州茅台', '五粮液', '宁波银行', '东方财富', '伊利股份'];
            const industries = ['银行', '房地产', '白酒', '安防', '银行', '白酒', '白酒', '银行', '证券', '食品饮料'];
            const signals = ['clearance', 'sell', 'warning', 'hold', 'clearance', 'hold', 'sell', 'warning', 'hold', 'sell'];
            const signalTexts = ['🔥 立即清仓', '🚨 卖出信号', '⚠️ 预警信号', '✅ 持有', '🔥 立即清仓', '✅ 持有', '🚨 卖出信号', '⚠️ 预警信号', '✅ 持有', '🚨 卖出信号'];

            for (let i = 0; i < 50; i++) {
                const index = i % 10;
                testData.push({
                    code: stockCodes[index],
                    name: stockNames[index],
                    price: (Math.random() * 100 + 10).toFixed(2),
                    change_pct: (Math.random() * 20 - 10).toFixed(2),
                    yearly_low: (Math.random() * 50 + 5).toFixed(2),
                    low_date: '2024-01-15',
                    distance_from_low_pct: (Math.random() * 100).toFixed(2),
                    pb_ratio: (Math.random() * 5 + 0.5).toFixed(2),
                    pe_ratio: (Math.random() * 50 + 5).toFixed(2),
                    dividend_yield: (Math.random() * 5).toFixed(2),
                    industry: industries[index],
                    sell_signal: signals[index],
                    sell_signal_text: signalTexts[index],
                    scan_score: Math.floor(Math.random() * 100),
                    holdings: Math.floor(Math.random() * 1000 + 100),
                    profit_margin: (Math.random() * 50 - 25).toFixed(2),
                    market_value: Math.floor(Math.random() * 100000 + 10000),
                    update_time: '2024-01-20 15:30:00'
                });
            }
            return testData;
        }

        // 渲染表格
        function renderTable() {
            const testData = generateTestData();
            const tbody = document.getElementById('testTableBody');
            
            tbody.innerHTML = testData.map(stock => `
                <tr>
                    <td class="sticky-left-1">${stock.code}</td>
                    <td class="sticky-left-2">${stock.name}</td>
                    <td>${stock.price}</td>
                    <td class="${parseFloat(stock.change_pct) >= 0 ? 'positive' : 'negative'}">${stock.change_pct}%</td>
                    <td>${stock.yearly_low}</td>
                    <td>${stock.low_date}</td>
                    <td>${stock.distance_from_low_pct}%</td>
                    <td>${stock.pb_ratio}</td>
                    <td>${stock.pe_ratio}</td>
                    <td>${stock.dividend_yield}%</td>
                    <td>${stock.industry}</td>
                    <td><span class="sell-signal ${stock.sell_signal}">${stock.sell_signal_text}</span></td>
                    <td>${stock.scan_score}</td>
                    <td>${stock.holdings}</td>
                    <td class="${parseFloat(stock.profit_margin) >= 0 ? 'positive' : 'negative'}">${stock.profit_margin}%</td>
                    <td>${stock.market_value.toLocaleString()}</td>
                    <td>${stock.update_time}</td>
                    <td>
                        <button style="padding: 2px 6px; font-size: 10px;">🔄</button>
                        <button style="padding: 2px 6px; font-size: 10px;">✏️</button>
                    </td>
                </tr>
            `).join('');
        }

        // 页面加载完成后渲染表格
        document.addEventListener('DOMContentLoaded', function() {
            renderTable();
            
            // 显示当前屏幕宽度信息
            function showScreenInfo() {
                const width = window.innerWidth;
                const isMobile = width <= 768;
                console.log(`屏幕宽度: ${width}px, 移动端模式: ${isMobile ? '是' : '否'}`);
            }
            
            showScreenInfo();
            window.addEventListener('resize', showScreenInfo);
        });
    </script>
</body>
</html>
