#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试华泰证券价格计算
"""

import json

def debug_huatai_price():
    """调试华泰证券价格计算"""
    
    # 从缓存文件读取华泰证券数据
    try:
        with open('stock_data_cache.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        huatai = data.get('601688')
        if not huatai:
            print("❌ 未找到华泰证券数据")
            return
        
        print("📊 华泰证券(601688)数据分析:")
        print(f"   股票名称: {huatai['name']}")
        print(f"   当前价格: {huatai['price']}元")
        print(f"   年内最低价: {huatai['yearly_low']}元")
        print(f"   成本价格: {huatai['unit_cost']}元")
        print(f"   距最低点涨幅: {huatai['distance_from_low_pct']:.2f}%")
        print(f"   卖出信号: {huatai['sell_signal']}")
        print(f"   卖出原因: {huatai['sell_reason']}")
        
        print("\n🧮 价格计算验证:")
        
        # 模拟JavaScript计算逻辑
        current_price = float(huatai['price'])
        yearly_low = float(huatai['yearly_low'])
        unit_cost = float(huatai['unit_cost'])
        
        print(f"   当前价格: {current_price}元")
        print(f"   年内最低价: {yearly_low}元")
        print(f"   成本价格: {unit_cost}元")
        
        # 计算70%涨幅目标价格
        target_price_70 = yearly_low * 1.7
        print(f"   70%涨幅目标价: {yearly_low} × 1.7 = {target_price_70:.2f}元")
        
        # 计算预警挂单价格
        warning_price = current_price * 1.02
        print(f"   预警挂单价格: {current_price} × 1.02 = {warning_price:.2f}元")
        
        # 检查是否有错误的计算
        wrong_calc_1 = current_price * 1.7
        print(f"   ❌ 错误计算1(用当前价): {current_price} × 1.7 = {wrong_calc_1:.2f}元")
        
        wrong_calc_2 = unit_cost * 1.7
        print(f"   ❌ 错误计算2(用成本价): {unit_cost} × 1.7 = {wrong_calc_2:.2f}元")
        
        print("\n🎯 正确的显示应该是:")
        print(f"   策略: 减半预警")
        print(f"   目标: {target_price_70:.2f}元")
        
        print("\n🔍 如果显示34.03元，可能的原因:")
        if abs(wrong_calc_1 - 34.03) < 0.1:
            print("   ✅ 发现问题：系统错误地使用了当前价格作为基准！")
        else:
            print("   ❓ 34.03元的来源不明，需要进一步调试")
            
        # 检查34.03的可能来源
        possible_base = 34.03 / 1.7
        print(f"   34.03 ÷ 1.7 = {possible_base:.2f}元")
        
        if abs(possible_base - current_price) < 0.1:
            print("   ✅ 确认：系统使用了当前价格而不是最低价作为基准！")
        
    except Exception as e:
        print(f"❌ 读取数据失败: {e}")

if __name__ == "__main__":
    debug_huatai_price()
