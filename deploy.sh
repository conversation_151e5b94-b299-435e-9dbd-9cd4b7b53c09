#!/bin/bash
# 持仓系统服务器部署脚本

echo "🚀 开始部署持仓系统..."

# 创建必要的目录
mkdir -p logs
mkdir -p cache
mkdir -p uploads

# 检查Python版本
echo "📋 检查Python环境..."
python3 --version

# 安装依赖
echo "📦 安装Python依赖..."
pip3 install -r requirements.txt

# 设置权限
echo "🔒 设置文件权限..."
chmod +x deploy.sh
chmod +x start.sh
chmod +x stop.sh

# 创建systemd服务文件
echo "⚙️ 创建系统服务..."
sudo tee /etc/systemd/system/stock-portfolio.service > /dev/null <<EOF
[Unit]
Description=Stock Portfolio System
After=network.target

[Service]
Type=notify
User=www-data
Group=www-data
WorkingDirectory=/path/to/your/project
Environment=PATH=/usr/local/bin:/usr/bin:/bin
ExecStart=/usr/local/bin/gunicorn --config gunicorn_config.py 持仓系统_v12_完整整合版:app
ExecReload=/bin/kill -s HUP \$MAINPID
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 重新加载systemd
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable stock-portfolio

echo "✅ 部署完成！"
echo ""
echo "🔧 接下来的步骤："
echo "1. 修改 nginx_config.conf 中的域名和路径"
echo "2. 将nginx配置复制到 /etc/nginx/sites-available/"
echo "3. 创建软链接到 /etc/nginx/sites-enabled/"
echo "4. 重启nginx: sudo systemctl restart nginx"
echo "5. 启动服务: sudo systemctl start stock-portfolio"
echo ""
echo "📊 查看状态："
echo "sudo systemctl status stock-portfolio"
echo "tail -f logs/error.log"
