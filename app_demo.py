from flask import Flask, render_template, jsonify
import pandas as pd
import random
from datetime import datetime
import threading
import time

app = Flask(__name__)

# 全局变量存储股票数据
stock_data = {}
last_update_time = None

def load_stock_list():
    """加载股票列表"""
    try:
        df = pd.read_csv('stocks_list.csv', encoding='utf-8-sig')
        return df.to_dict('records')
    except Exception as e:
        print(f"加载股票列表失败: {e}")
        return []

def generate_mock_data():
    """生成模拟股票数据用于演示"""
    global stock_data, last_update_time

    stock_list = load_stock_list()
    print(f"生成 {len(stock_list)} 只股票的模拟数据...")

    for stock in stock_list:
        # 确保代码格式统一
        code = str(stock['代码']).zfill(6)

        # 生成模拟数据
        base_price = random.uniform(5, 100)  # 基础价格
        change = random.uniform(-5, 5)  # 涨跌额
        change_pct = (change / base_price) * 100  # 涨跌幅

        stock_data[code] = {
            'code': code,
            'name': stock['名称'],
            'price': round(base_price + change, 2),
            'change': round(change, 2),
            'change_pct': round(change_pct, 2),
            'volume': random.randint(1000000, 100000000),
            'amount': random.randint(10000000, 1000000000),
            'high': round(base_price + change + random.uniform(0, 2), 2),
            'low': round(base_price + change - random.uniform(0, 2), 2),
            'open': round(base_price + random.uniform(-1, 1), 2),
            'yesterday_close': round(base_price, 2),
            'update_time': datetime.now().strftime('%H:%M:%S')
        }

    last_update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"模拟数据生成完成，更新时间: {last_update_time}")

def update_mock_data():
    """更新模拟数据（模拟价格变动）"""
    global stock_data, last_update_time
    
    for code in stock_data:
        # 模拟小幅价格变动
        current_price = stock_data[code]['price']
        yesterday_close = stock_data[code]['yesterday_close']
        
        # 随机变动 -1% 到 +1%
        change_factor = random.uniform(-0.01, 0.01)
        new_price = current_price * (1 + change_factor)
        new_change = new_price - yesterday_close
        new_change_pct = (new_change / yesterday_close) * 100
        
        stock_data[code].update({
            'price': round(new_price, 2),
            'change': round(new_change, 2),
            'change_pct': round(new_change_pct, 2),
            'high': max(stock_data[code]['high'], round(new_price, 2)),
            'low': min(stock_data[code]['low'], round(new_price, 2)),
            'volume': stock_data[code]['volume'] + random.randint(1000, 100000),
            'amount': stock_data[code]['amount'] + random.randint(100000, 10000000),
            'update_time': datetime.now().strftime('%H:%M:%S')
        })
    
    last_update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"模拟数据更新完成，更新时间: {last_update_time}")

def background_update():
    """后台定时更新股票数据"""
    while True:
        update_mock_data()
        time.sleep(10)  # 每10秒更新一次模拟数据

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/stocks')
def get_stocks():
    """获取所有股票数据API"""
    return jsonify({
        'stocks': list(stock_data.values()),
        'last_update': last_update_time,
        'total_count': len(stock_data)
    })

@app.route('/api/stock/<stock_code>')
def get_stock(stock_code):
    """获取单只股票数据API"""
    if stock_code in stock_data:
        return jsonify(stock_data[stock_code])
    else:
        return jsonify({'error': '股票代码不存在'}), 404

if __name__ == '__main__':
    # 初始生成模拟数据
    generate_mock_data()
    
    # 启动后台更新线程
    update_thread = threading.Thread(target=background_update, daemon=True)
    update_thread.start()
    
    print("启动演示服务器...")
    print("访问 http://localhost:5000 查看股票行情")
    print("注意：这是使用模拟数据的演示版本")
    
    # 启动Flask应用
    app.run(debug=True, host='0.0.0.0', port=5000)
