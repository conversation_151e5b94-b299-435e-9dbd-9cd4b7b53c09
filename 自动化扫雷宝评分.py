"""
自动化扫雷宝评分获取系统
使用selenium + OCR实现全自动评分识别
"""

import os
import time
import re
import threading
import queue
from PIL import Image
import requests
from io import BytesIO
import base64
import logging

# 设置日志级别，减少输出干扰
logging.getLogger('selenium').setLevel(logging.WARNING)
logging.getLogger('urllib3').setLevel(logging.WARNING)

# OCR相关导入
try:
    import pytesseract
    OCR_AVAILABLE = True
    print("✅ OCR环境可用")
except ImportError:
    OCR_AVAILABLE = False
    print("❌ 警告: 未安装pytesseract，请运行: pip install pytesseract")

# Selenium相关导入
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.service import Service
    SELENIUM_AVAILABLE = True
    print("✅ Selenium环境可用")

    # 尝试导入webdriver-manager
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        WEBDRIVER_MANAGER_AVAILABLE = True
        print("✅ WebDriver Manager可用")
    except ImportError:
        WEBDRIVER_MANAGER_AVAILABLE = False
        print("⚠️ 建议安装webdriver-manager: pip install webdriver-manager")

except ImportError:
    SELENIUM_AVAILABLE = False
    WEBDRIVER_MANAGER_AVAILABLE = False
    print("❌ 警告: 未安装selenium，请运行: pip install selenium")

class AutoSLBScorer:
    """自动化扫雷宝评分获取器"""
    
    def __init__(self):
        self.driver = None
        self.setup_ocr()
        
    def setup_ocr(self):
        """设置OCR环境"""
        if not OCR_AVAILABLE:
            return False
            
        # 尝试设置tesseract路径
        possible_paths = [
            r"C:\Program Files\Tesseract-OCR\tesseract.exe",
            r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
            "/usr/bin/tesseract",
            "/usr/local/bin/tesseract"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                pytesseract.pytesseract.tesseract_cmd = path
                print(f"✅ 找到Tesseract: {path}")
                return True
                
        print("⚠️ 未找到Tesseract，请确保已正确安装")
        return False
    
    def setup_browser(self):
        """设置浏览器"""
        if not SELENIUM_AVAILABLE:
            print("❌ Selenium不可用，无法启动浏览器")
            return False

        try:
            chrome_options = Options()
            # 完全后台运行，不影响用户操作
            chrome_options.add_argument('--headless')  # 无头模式，不显示浏览器窗口
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-software-rasterizer')
            chrome_options.add_argument('--disable-background-timer-throttling')
            chrome_options.add_argument('--disable-backgrounding-occluded-windows')
            chrome_options.add_argument('--disable-renderer-backgrounding')
            chrome_options.add_argument('--window-size=1200,800')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--disable-images')  # 不加载图片，提高速度
            chrome_options.add_argument('--mute-audio')  # 静音
            chrome_options.add_argument('--disable-notifications')  # 禁用通知
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # 设置进程优先级为低，避免影响系统性能
            chrome_options.add_argument('--process-per-site')
            chrome_options.add_argument('--max_old_space_size=512')  # 限制内存使用

            # 尝试使用webdriver-manager自动管理ChromeDriver
            if WEBDRIVER_MANAGER_AVAILABLE:
                try:
                    service = Service(ChromeDriverManager().install())
                    self.driver = webdriver.Chrome(service=service, options=chrome_options)
                    print("✅ Chrome浏览器启动成功 (使用webdriver-manager)")
                    return True
                except Exception as e:
                    print(f"⚠️ webdriver-manager启动失败: {e}")

            # 回退到系统ChromeDriver
            self.driver = webdriver.Chrome(options=chrome_options)
            print("✅ Chrome浏览器启动成功 (使用系统ChromeDriver)")
            return True

        except Exception as e:
            print(f"❌ 浏览器启动失败: {e}")
            print("💡 请确保已安装Chrome浏览器和ChromeDriver")
            print("💡 或运行: pip install webdriver-manager")
            return False
    
    def get_slb_score_auto(self, stock_code: str, max_retries: int = 3) -> float:
        """自动获取扫雷宝评分"""
        
        if not self.setup_browser():
            return None
            
        try:
            url = f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code={stock_code}&color=0"
            print(f"🌐 访问扫雷宝页面: {stock_code}")
            
            # 访问页面
            self.driver.get(url)
            
            # 等待页面加载
            time.sleep(3)
            
            # 等待Canvas元素加载
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "canvas"))
                )
                print("✅ 页面加载完成")
            except:
                print("⚠️ 页面加载超时，继续尝试截图")
            
            # 额外等待确保内容渲染完成
            time.sleep(2)
            
            # 截图
            screenshot_path = f"slb_screenshot_{stock_code}.png"
            self.driver.save_screenshot(screenshot_path)
            print(f"📸 截图保存: {screenshot_path}")
            
            # OCR识别
            score = self.extract_score_from_image(screenshot_path, stock_code)
            
            # 清理截图文件
            try:
                os.remove(screenshot_path)
            except:
                pass
                
            return score
            
        except Exception as e:
            print(f"❌ 自动获取评分失败: {e}")
            return None
        finally:
            if self.driver:
                self.driver.quit()
                self.driver = None
    
    def extract_score_from_image(self, image_path: str, stock_code: str) -> float:
        """从截图中提取评分"""
        
        if not OCR_AVAILABLE:
            print("❌ OCR不可用")
            return None
            
        try:
            # 打开图片
            image = Image.open(image_path)
            print(f"🖼️ 图片尺寸: {image.size}")
            
            # 尝试多种OCR配置
            ocr_configs = [
                '--psm 6 -c tessedit_char_whitelist=0123456789分',  # 只识别数字和"分"字
                '--psm 8 -c tessedit_char_whitelist=0123456789',    # 单词模式，只识别数字
                '--psm 7 -c tessedit_char_whitelist=0123456789分',  # 单行模式
                '--psm 13 -c tessedit_char_whitelist=0123456789',   # 原始行模式
            ]
            
            all_found_scores = []
            
            for i, config in enumerate(ocr_configs):
                try:
                    print(f"🔍 OCR配置 {i+1}: {config}")
                    text = pytesseract.image_to_string(image, lang='chi_sim+eng', config=config)
                    print(f"   识别结果: '{text.strip()}'")
                    
                    # 提取分数
                    scores = self.parse_scores_from_text(text)
                    if scores:
                        all_found_scores.extend(scores)
                        print(f"   提取分数: {scores}")
                        
                except Exception as e:
                    print(f"   OCR配置失败: {e}")
                    continue
            
            # 分析所有找到的分数
            if all_found_scores:
                # 过滤合理的分数范围
                valid_scores = [s for s in all_found_scores if 0 <= s <= 100]
                
                if valid_scores:
                    # 选择最常出现的分数
                    from collections import Counter
                    score_counts = Counter(valid_scores)
                    final_score = score_counts.most_common(1)[0][0]
                    
                    print(f"✅ 最终评分: {final_score}分 (候选: {valid_scores})")
                    return float(final_score)
            
            print("❌ 未能识别出有效评分")
            return None
            
        except Exception as e:
            print(f"❌ 图片处理失败: {e}")
            return None
    
    def parse_scores_from_text(self, text: str) -> list:
        """从OCR文本中解析分数"""
        scores = []
        
        # 多种分数模式
        patterns = [
            r'(\d{1,3})分',           # "97分"
            r'(\d{1,3})\s*分',        # "97 分"
            r'分\s*(\d{1,3})',        # "分97"
            r'(\d{1,3})',             # 纯数字
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                try:
                    score = int(match)
                    if 0 <= score <= 100:
                        scores.append(score)
                except ValueError:
                    continue
        
        return scores
    
    def batch_get_scores(self, stock_codes: list) -> dict:
        """批量获取多个股票的扫雷宝评分"""
        results = {}
        
        print(f"🚀 开始批量获取 {len(stock_codes)} 只股票的扫雷宝评分")
        print("=" * 60)
        
        for i, code in enumerate(stock_codes):
            print(f"\n[{i+1:2d}/{len(stock_codes)}] 处理股票: {code}")
            print("-" * 40)
            
            score = self.get_slb_score_auto(code)
            results[code] = score
            
            if score is not None:
                print(f"✅ {code}: {score}分")
            else:
                print(f"❌ {code}: 获取失败")
            
            # 添加延迟避免请求过快
            if i < len(stock_codes) - 1:
                print("⏳ 等待2秒...")
                time.sleep(2)
        
        return results

class BackgroundSLBProcessor:
    """后台扫雷宝评分处理器 - 不影响用户操作"""

    def __init__(self):
        self.scorer = AutoSLBScorer()
        self.results = {}
        self.progress = {"current": 0, "total": 0, "status": "idle"}

    def process_stocks_background(self, stock_codes: list, callback=None):
        """在后台线程中处理股票评分"""

        def background_worker():
            try:
                print(f"🔄 后台开始处理 {len(stock_codes)} 只股票...")
                self.progress = {"current": 0, "total": len(stock_codes), "status": "running"}

                for i, code in enumerate(stock_codes):
                    print(f"📊 后台处理 [{i+1}/{len(stock_codes)}]: {code}")

                    # 获取评分
                    score = self.scorer.get_slb_score_auto(code)
                    self.results[code] = score

                    # 更新进度
                    self.progress["current"] = i + 1

                    if score is not None:
                        print(f"✅ {code}: {score}分")
                    else:
                        print(f"❌ {code}: 获取失败")

                    # 后台处理间隔更长，避免影响系统
                    time.sleep(3)

                self.progress["status"] = "completed"
                print("🎉 后台处理完成！")

                if callback:
                    callback(self.results)

            except Exception as e:
                print(f"❌ 后台处理出错: {e}")
                self.progress["status"] = "error"

        # 启动后台线程
        thread = threading.Thread(target=background_worker, daemon=True)
        thread.start()
        return thread

    def get_progress(self):
        """获取处理进度"""
        return self.progress.copy()

    def get_results(self):
        """获取处理结果"""
        return self.results.copy()

def test_background_processing():
    """测试后台处理功能"""

    processor = BackgroundSLBProcessor()

    # 测试股票列表
    test_stocks = [
        "300479",  # 神思电子 - 应该是97分
        "000001",  # 平安银行
        "600000",  # 浦发银行
    ]

    print("=== 后台扫雷宝评分测试 ===")
    print("💡 程序将在后台运行，不会影响您的其他操作")
    print("-" * 50)

    def on_complete(results):
        """处理完成回调"""
        print("\n🎉 后台处理完成！最终结果:")
        print("=" * 40)

        success_count = 0
        for code, score in results.items():
            if score is not None:
                print(f"✅ {code}: {score:5.1f}分")
                success_count += 1

                # 验证神思电子
                if code == "300479":
                    if abs(score - 97) < 5:
                        print(f"   ✅ 神思电子评分验证通过")
                    else:
                        print(f"   ⚠️ 神思电子评分可能有误 (期望97分)")
            else:
                print(f"❌ {code}: 获取失败")

        print(f"\n📊 成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")

    # 启动后台处理
    thread = processor.process_stocks_background(test_stocks, on_complete)

    # 显示进度（非阻塞）
    print("⏳ 后台处理中，您可以继续使用电脑...")

    while thread.is_alive():
        progress = processor.get_progress()
        if progress["status"] == "running":
            print(f"📈 进度: {progress['current']}/{progress['total']} ({progress['current']/progress['total']*100:.1f}%)")
        time.sleep(5)  # 每5秒更新一次进度

    return processor.get_results()

def test_auto_slb_scorer():
    """测试自动化扫雷宝评分功能"""
    
    scorer = AutoSLBScorer()
    
    # 测试股票列表
    test_stocks = [
        "300479",  # 神思电子 - 应该是97分
        "000001",  # 平安银行
        "600000",  # 浦发银行
        "000002",  # 万科A
        "600036",  # 招商银行
    ]
    
    print("=== 自动化扫雷宝评分测试 ===\n")
    
    # 批量获取评分
    results = scorer.batch_get_scores(test_stocks)
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("汇总结果:")
    print("=" * 60)
    
    success_count = 0
    for code, score in results.items():
        if score is not None:
            print(f"✅ {code}: {score:5.1f}分")
            success_count += 1
            
            # 验证已知结果
            if code == "300479" and abs(score - 97) < 5:
                print(f"   ✅ 神思电子评分验证通过 (期望97分，实际{score}分)")
            elif code == "300479":
                print(f"   ⚠️ 神思电子评分可能有误 (期望97分，实际{score}分)")
        else:
            print(f"❌ {code}: 获取失败")
    
    print(f"\n成功率: {success_count}/{len(test_stocks)} ({success_count/len(test_stocks)*100:.1f}%)")

if __name__ == "__main__":
    print("🚀 自动化扫雷宝评分系统")
    print("=" * 50)
    print("选择运行模式:")
    print("1. 后台处理模式 (推荐，不影响电脑使用)")
    print("2. 前台处理模式 (传统模式)")

    try:
        choice = input("\n请选择 (1/2，默认1): ").strip()
        if choice == "2":
            print("\n🔄 启动前台处理模式...")
            test_auto_slb_scorer()
        else:
            print("\n🔄 启动后台处理模式...")
            test_background_processing()
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
