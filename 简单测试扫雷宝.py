"""
简单测试扫雷宝评分获取
先验证基本功能是否正常
"""

import requests
import time

def test_slb_page_access():
    """测试扫雷宝页面访问"""
    
    test_codes = ["300479", "000001", "600000"]
    
    print("=== 测试扫雷宝页面访问 ===")
    
    for code in test_codes:
        print(f"\n🔍 测试股票: {code}")
        
        url = f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code={code}&color=0"
        
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                content = response.text
                print(f"✅ 页面访问成功")
                print(f"   页面大小: {len(content)} 字符")
                
                # 检查是否包含关键内容
                if 'canvas' in content.lower():
                    print(f"   ✅ 包含Canvas元素")
                else:
                    print(f"   ⚠️ 未找到Canvas元素")
                
                if 'showvalue' in content or 'realvalue' in content:
                    print(f"   ✅ 包含分数变量")
                else:
                    print(f"   ⚠️ 未找到分数变量")
                
                # 查找数字
                import re
                numbers = re.findall(r'\b\d{2,3}\b', content)
                if numbers:
                    print(f"   📊 找到数字: {numbers[:10]}...")
                
            else:
                print(f"❌ 页面访问失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 访问出错: {e}")
        
        time.sleep(1)

def check_dependencies():
    """检查依赖环境"""
    
    print("=== 检查依赖环境 ===")
    
    # 检查selenium
    try:
        import selenium
        print("✅ Selenium已安装")
        
        try:
            from webdriver_manager.chrome import ChromeDriverManager
            print("✅ WebDriver Manager已安装")
        except ImportError:
            print("⚠️ WebDriver Manager未安装")
            
    except ImportError:
        print("❌ Selenium未安装")
    
    # 检查OCR
    try:
        import pytesseract
        print("✅ PyTesseract已安装")
        
        # 检查tesseract路径
        import os
        paths = [
            r"C:\Program Files\Tesseract-OCR\tesseract.exe",
            r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
        ]
        
        tesseract_found = False
        for path in paths:
            if os.path.exists(path):
                print(f"✅ 找到Tesseract: {path}")
                tesseract_found = True
                break
        
        if not tesseract_found:
            print("⚠️ 未找到Tesseract OCR")
            
    except ImportError:
        print("❌ PyTesseract未安装")
    
    # 检查PIL
    try:
        from PIL import Image
        print("✅ Pillow已安装")
    except ImportError:
        print("❌ Pillow未安装")

def test_chrome_availability():
    """测试Chrome浏览器可用性"""
    
    print("\n=== 测试Chrome浏览器 ===")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        
        # 尝试启动Chrome
        try:
            from webdriver_manager.chrome import ChromeDriverManager
            from selenium.webdriver.chrome.service import Service
            
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=options)
            
            print("✅ Chrome浏览器启动成功 (webdriver-manager)")
            
            # 简单测试
            driver.get("https://www.baidu.com")
            title = driver.title
            print(f"   测试页面标题: {title}")
            
            driver.quit()
            return True
            
        except Exception as e:
            print(f"⚠️ webdriver-manager失败: {e}")
            
            # 尝试系统ChromeDriver
            try:
                driver = webdriver.Chrome(options=options)
                print("✅ Chrome浏览器启动成功 (系统ChromeDriver)")
                
                driver.get("https://www.baidu.com")
                title = driver.title
                print(f"   测试页面标题: {title}")
                
                driver.quit()
                return True
                
            except Exception as e2:
                print(f"❌ 系统ChromeDriver也失败: {e2}")
                return False
                
    except ImportError:
        print("❌ Selenium未安装")
        return False

def main():
    """主测试函数"""
    
    print("🚀 扫雷宝评分系统环境测试")
    print("=" * 50)
    
    # 1. 检查依赖
    check_dependencies()
    
    # 2. 测试Chrome
    chrome_ok = test_chrome_availability()
    
    # 3. 测试页面访问
    test_slb_page_access()
    
    print("\n" + "=" * 50)
    print("📋 测试总结:")
    
    if chrome_ok:
        print("✅ Chrome浏览器环境正常")
        print("💡 可以尝试运行完整的自动化评分程序")
    else:
        print("❌ Chrome浏览器环境有问题")
        print("💡 请安装Chrome浏览器和相关依赖")
        print("   pip install selenium webdriver-manager")

if __name__ == "__main__":
    main()
