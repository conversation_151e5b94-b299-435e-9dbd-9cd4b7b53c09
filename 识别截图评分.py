"""
识别扫雷宝截图中的评分
专门处理Canvas绘制的圆形评分显示
"""

import os
import re
from PIL import Image, ImageEnhance, ImageFilter
import pytesseract
import cv2
import numpy as np

def setup_ocr():
    """设置OCR环境"""
    pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"

def preprocess_image_for_score(image_path: str) -> list:
    """预处理图片以提高OCR识别率"""
    
    try:
        # 打开图片
        image = Image.open(image_path)
        print(f"📸 原始图片尺寸: {image.size}")
        
        # 转换为OpenCV格式
        img_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        
        processed_images = []
        
        # 方法1: 原图直接处理
        processed_images.append(("原图", image))
        
        # 方法2: 增强对比度
        enhancer = ImageEnhance.Contrast(image)
        contrast_img = enhancer.enhance(2.0)
        processed_images.append(("增强对比度", contrast_img))
        
        # 方法3: 转灰度并二值化
        gray_img = image.convert('L')
        processed_images.append(("灰度图", gray_img))
        
        # 方法4: 查找圆形区域（扫雷宝评分通常在圆形区域内）
        gray_cv = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        
        # 使用霍夫圆检测找到圆形区域
        circles = cv2.HoughCircles(
            gray_cv,
            cv2.HOUGH_GRADIENT,
            dp=1,
            minDist=50,
            param1=50,
            param2=30,
            minRadius=30,
            maxRadius=200
        )
        
        if circles is not None:
            circles = np.round(circles[0, :]).astype("int")
            print(f"🔍 找到 {len(circles)} 个圆形区域")
            
            for i, (x, y, r) in enumerate(circles):
                # 提取圆形区域
                mask = np.zeros(gray_cv.shape, dtype=np.uint8)
                cv2.circle(mask, (x, y), r, 255, -1)
                
                # 应用掩码
                circle_region = cv2.bitwise_and(gray_cv, gray_cv, mask=mask)
                
                # 裁剪到圆形边界框
                x1, y1 = max(0, x-r), max(0, y-r)
                x2, y2 = min(gray_cv.shape[1], x+r), min(gray_cv.shape[0], y+r)
                cropped = circle_region[y1:y2, x1:x2]
                
                if cropped.size > 0:
                    circle_img = Image.fromarray(cropped)
                    processed_images.append((f"圆形区域{i+1}", circle_img))
        
        return processed_images
        
    except Exception as e:
        print(f"❌ 图片预处理失败: {e}")
        return [("原图", Image.open(image_path))]

def extract_score_with_multiple_methods(image_path: str) -> float:
    """使用多种方法提取评分"""
    
    setup_ocr()
    
    if not os.path.exists(image_path):
        print(f"❌ 截图文件不存在: {image_path}")
        return None
    
    print(f"🔍 分析截图: {image_path}")
    
    # 预处理图片
    processed_images = preprocess_image_for_score(image_path)
    
    all_scores = []
    
    # OCR配置
    ocr_configs = [
        ('基本配置', '--psm 6'),
        ('数字+分', '--psm 6 -c tessedit_char_whitelist=0123456789分'),
        ('单词模式', '--psm 8 -c tessedit_char_whitelist=0123456789'),
        ('单行模式', '--psm 7 -c tessedit_char_whitelist=0123456789分'),
        ('原始行', '--psm 13 -c tessedit_char_whitelist=0123456789'),
        ('圆形数字', '--psm 10 -c tessedit_char_whitelist=0123456789'),  # 单字符模式
    ]
    
    # 对每个预处理图片尝试OCR
    for img_name, img in processed_images:
        print(f"\n🔍 处理 {img_name}...")
        
        for config_name, config in ocr_configs:
            try:
                # OCR识别
                text = pytesseract.image_to_string(img, lang='chi_sim+eng', config=config)
                text = text.strip()
                
                if text:
                    print(f"  {config_name}: '{text}'")
                    
                    # 提取分数
                    scores = extract_scores_from_text(text)
                    if scores:
                        print(f"    提取分数: {scores}")
                        all_scores.extend(scores)
                
            except Exception as e:
                print(f"  {config_name}: 失败 - {e}")
    
    # 分析所有找到的分数
    if all_scores:
        print(f"\n📊 所有识别到的分数: {all_scores}")
        
        # 过滤合理的分数
        valid_scores = [s for s in all_scores if 50 <= s <= 100]
        
        if valid_scores:
            from collections import Counter
            score_counts = Counter(valid_scores)
            print(f"📊 有效分数统计: {dict(score_counts)}")
            
            # 选择出现频率最高的分数
            final_score = score_counts.most_common(1)[0][0]
            confidence = score_counts[final_score] / len(valid_scores)
            
            print(f"🎯 最终评分: {final_score}分 (置信度: {confidence:.2f})")
            return float(final_score)
    
    print("❌ 未能识别出有效评分")
    return None

def extract_scores_from_text(text: str) -> list:
    """从文本中提取分数"""
    scores = []
    
    # 多种分数模式
    patterns = [
        r'(\d{1,3})分',           # "97分"
        r'(\d{1,3})\s*分',        # "97 分"
        r'分\s*(\d{1,3})',        # "分97"
        r'(\d{2,3})',             # 两位或三位数字
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, text)
        for match in matches:
            try:
                score = int(match)
                if 0 <= score <= 100:
                    scores.append(score)
            except ValueError:
                continue
    
    return scores

def test_existing_screenshots():
    """测试现有的截图文件"""
    
    print("🔍 查找现有截图文件...")
    
    # 可能的截图文件名
    screenshot_files = [
        "debug_screenshot.png",
        "test_screenshot.png",
        "slb_300479.png",
        "slb_selenium_300479.png",
    ]
    
    found_files = []
    for filename in screenshot_files:
        if os.path.exists(filename):
            file_size = os.path.getsize(filename)
            print(f"✅ 找到截图: {filename} ({file_size} 字节)")
            found_files.append(filename)
    
    if not found_files:
        print("❌ 未找到截图文件")
        return
    
    # 分析每个截图
    for filename in found_files:
        print(f"\n{'='*50}")
        print(f"分析截图: {filename}")
        print('='*50)
        
        score = extract_score_with_multiple_methods(filename)
        
        if score is not None:
            print(f"🎉 识别结果: {score}分")
            
            # 如果是神思电子的截图，验证结果
            if "300479" in filename or "神思电子" in filename:
                if 90 <= score <= 100:
                    print("✅ 神思电子评分合理（期望97分左右）")
                else:
                    print("⚠️ 神思电子评分可能有误")
        else:
            print("❌ 识别失败")

def manual_score_input():
    """手动录入评分功能"""
    
    print("\n📝 手动录入评分")
    print("-" * 30)
    
    scores_file = "manual_slb_scores.json"
    
    # 加载现有评分
    scores = {}
    if os.path.exists(scores_file):
        import json
        try:
            with open(scores_file, 'r', encoding='utf-8') as f:
                scores = json.load(f)
            print(f"✅ 加载现有评分: {len(scores)} 条")
        except:
            print("⚠️ 现有评分文件损坏，将创建新文件")
    
    # 录入新评分
    while True:
        try:
            code = input("\n请输入股票代码（6位数字，或输入q退出）: ").strip()
            
            if code.lower() == 'q':
                break
            
            if not re.match(r'^\d{6}$', code):
                print("❌ 请输入6位数字的股票代码")
                continue
            
            name = input("请输入股票名称: ").strip()
            score_str = input("请输入扫雷宝评分（0-100）: ").strip()
            
            try:
                score = float(score_str)
                if not (0 <= score <= 100):
                    print("❌ 评分必须在0-100之间")
                    continue
            except ValueError:
                print("❌ 请输入有效的数字")
                continue
            
            # 保存评分
            scores[code] = {
                'name': name,
                'score': score,
                'update_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'method': 'manual'
            }
            
            print(f"✅ 已保存: {name}({code}) = {score}分")
            
        except KeyboardInterrupt:
            break
    
    # 保存到文件
    if scores:
        import json
        import time
        
        with open(scores_file, 'w', encoding='utf-8') as f:
            json.dump(scores, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 评分已保存到: {scores_file}")
        print(f"📊 总计: {len(scores)} 条评分记录")

def main():
    """主函数"""
    
    print("🎯 扫雷宝评分识别工具")
    print("=" * 40)
    
    print("选择功能:")
    print("1. 分析现有截图")
    print("2. 手动录入评分")
    print("3. 查看已录入评分")
    
    try:
        choice = input("\n请选择 (1/2/3): ").strip()
        
        if choice == "1":
            test_existing_screenshots()
        elif choice == "2":
            manual_score_input()
        elif choice == "3":
            # 显示已录入的评分
            scores_file = "manual_slb_scores.json"
            if os.path.exists(scores_file):
                import json
                with open(scores_file, 'r', encoding='utf-8') as f:
                    scores = json.load(f)
                
                print(f"\n📊 已录入评分 ({len(scores)} 条):")
                print("-" * 50)
                for code, info in scores.items():
                    print(f"{info['name']:10} ({code}): {info['score']:5.1f}分 [{info['update_time']}]")
            else:
                print("❌ 暂无已录入的评分")
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")

if __name__ == "__main__":
    main()
