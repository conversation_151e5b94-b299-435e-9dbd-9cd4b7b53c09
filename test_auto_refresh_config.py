#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动刷新配置功能
"""

import json
import os
import requests
import time

def test_config_api():
    """测试配置API"""
    base_url = "http://localhost:5000"
    
    print("🧪 测试自动刷新配置功能")
    print("=" * 50)
    
    # 测试获取配置
    print("\n1. 测试获取配置...")
    try:
        response = requests.get(f"{base_url}/api/config")
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                config = result['config']
                print(f"✅ 获取配置成功")
                print(f"   auto_refresh_on_startup: {config.get('auto_refresh_on_startup', 'Not found')}")
                print(f"   其他配置项: {list(config.keys())}")
            else:
                print(f"❌ 获取配置失败: {result['message']}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    # 测试更新配置 - 开启自动刷新
    print("\n2. 测试开启自动刷新...")
    try:
        response = requests.post(f"{base_url}/api/config", 
                               json={"auto_refresh_on_startup": True},
                               headers={"Content-Type": "application/json"})
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                print(f"✅ 开启自动刷新成功: {result['message']}")
            else:
                print(f"❌ 开启失败: {result['message']}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    # 等待一下
    time.sleep(1)
    
    # 测试更新配置 - 关闭自动刷新
    print("\n3. 测试关闭自动刷新...")
    try:
        response = requests.post(f"{base_url}/api/config", 
                               json={"auto_refresh_on_startup": False},
                               headers={"Content-Type": "application/json"})
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                print(f"✅ 关闭自动刷新成功: {result['message']}")
            else:
                print(f"❌ 关闭失败: {result['message']}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    # 验证配置文件
    print("\n4. 验证配置文件...")
    config_file = "app_config.json"
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                saved_config = json.load(f)
            print(f"✅ 配置文件存在: {config_file}")
            print(f"   auto_refresh_on_startup: {saved_config.get('auto_refresh_on_startup')}")
            print(f"   保存时间: {saved_config.get('save_time', '未知')}")
        except Exception as e:
            print(f"❌ 读取配置文件失败: {e}")
    else:
        print(f"❌ 配置文件不存在: {config_file}")

def test_config_persistence():
    """测试配置持久化"""
    print("\n" + "=" * 50)
    print("🔄 测试配置持久化")
    
    config_file = "app_config.json"
    
    # 创建测试配置
    test_config = {
        "stock_interval": 0.2,
        "round_interval": 600,
        "request_timeout": 15,
        "alert_threshold": 70.0,
        "auto_refresh_on_startup": True,
        "save_time": "2025-07-24 10:30:00"
    }
    
    print("\n1. 创建测试配置文件...")
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(test_config, f, ensure_ascii=False, indent=2)
        print(f"✅ 测试配置文件已创建")
    except Exception as e:
        print(f"❌ 创建配置文件失败: {e}")
        return
    
    # 模拟加载配置
    print("\n2. 模拟加载配置...")
    try:
        # 导入配置加载函数（需要修改导入路径）
        import sys
        sys.path.append('.')
        
        # 这里应该导入实际的配置加载函数
        # 由于文件名包含特殊字符，我们直接测试文件读取
        with open(config_file, 'r', encoding='utf-8') as f:
            loaded_config = json.load(f)
        
        print(f"✅ 配置加载成功")
        print(f"   auto_refresh_on_startup: {loaded_config.get('auto_refresh_on_startup')}")
        
        # 验证配置完整性
        expected_keys = ['stock_interval', 'round_interval', 'request_timeout', 
                        'alert_threshold', 'auto_refresh_on_startup']
        missing_keys = [key for key in expected_keys if key not in loaded_config]
        
        if missing_keys:
            print(f"⚠️ 缺失配置项: {missing_keys}")
        else:
            print(f"✅ 所有必需配置项都存在")
            
    except Exception as e:
        print(f"❌ 加载配置失败: {e}")

if __name__ == "__main__":
    print("🚀 自动刷新配置功能测试")
    print("请确保持仓系统正在运行 (http://localhost:5000)")
    
    # 询问是否继续
    try:
        input("\n按回车键开始测试，或Ctrl+C取消...")
    except KeyboardInterrupt:
        print("\n测试已取消")
        exit()
    
    # 测试API
    test_config_api()
    
    # 测试持久化
    test_config_persistence()
    
    print("\n" + "=" * 50)
    print("✅ 测试完成！")
    print("\n📋 测试总结:")
    print("1. API接口测试 - 验证配置的获取和更新")
    print("2. 配置文件测试 - 验证配置的持久化存储")
    print("3. 界面测试 - 请手动测试网页界面中的设置开关")
    print("\n💡 下一步:")
    print("1. 重启持仓系统，观察启动日志中的自动刷新状态")
    print("2. 在网页界面中切换自动刷新开关")
    print("3. 验证设置在重启后是否保持")
