#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单个股票更新状态保护测试脚本
测试单个股票更新时手动状态是否被保护
"""

import requests
import time
import json

def test_single_stock_update_protection():
    """测试单个股票更新时的状态保护功能"""
    base_url = 'http://localhost:5000'
    
    print("🧪 开始测试单个股票更新状态保护功能")
    print("=" * 60)
    
    # 步骤1: 确保手动优先级已启用
    print("\n📋 步骤1: 确保手动优先级已启用")
    try:
        response = requests.post(f'{base_url}/api/sell-signal-priority', 
                               json={'manual_priority_over_auto': True})
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 手动优先级已启用: {result['message']}")
            else:
                print(f"❌ 设置失败: {result.get('message')}")
                return False
        else:
            print(f"❌ API请求失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 设置优先级异常: {e}")
        return False
    
    # 步骤2: 获取股票列表，选择一个测试股票
    print("\n📋 步骤2: 获取股票列表")
    test_stock_code = None
    test_stock_name = None
    
    try:
        response = requests.get(f'{base_url}/api/stocks')
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                stocks = result['data']
                if stocks:
                    # 选择第一个股票作为测试对象
                    test_stock = stocks[0]
                    test_stock_code = test_stock['code']
                    test_stock_name = test_stock['name']
                    print(f"✅ 选择测试股票: {test_stock_name} ({test_stock_code})")
                    print(f"   当前状态: {test_stock.get('sell_signal', 'unknown')} - {test_stock.get('sell_reason', 'unknown')}")
                else:
                    print("❌ 没有找到股票数据")
                    return False
            else:
                print(f"❌ 获取股票失败: {result.get('message')}")
                return False
        else:
            print(f"❌ API请求失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取股票异常: {e}")
        return False
    
    # 步骤3: 设置手动状态
    print("\n📋 步骤3: 设置手动状态为'已清仓'")
    try:
        response = requests.post(f'{base_url}/api/set-custom-status', json={
            'stock_code': test_stock_code,
            'status_type': 'cleared',
            'status_text': '已清仓',
            'status_emoji': '🔥'
        })
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 手动状态设置成功: {result['message']}")
            else:
                print(f"❌ 设置手动状态失败: {result.get('message')}")
                return False
        else:
            print(f"❌ API请求失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 设置手动状态异常: {e}")
        return False
    
    # 步骤4: 验证手动状态已设置
    print("\n📋 步骤4: 验证手动状态已设置")
    time.sleep(1)  # 等待状态更新
    
    try:
        response = requests.get(f'{base_url}/api/stocks')
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                stocks = result['data']
                test_stock = None
                for stock in stocks:
                    if stock['code'] == test_stock_code:
                        test_stock = stock
                        break
                
                if test_stock:
                    current_signal = test_stock.get('sell_signal', 'unknown')
                    current_reason = test_stock.get('sell_reason', 'unknown')
                    is_custom = test_stock.get('custom_status', False)
                    
                    print(f"   当前信号: {current_signal}")
                    print(f"   当前原因: {current_reason}")
                    print(f"   自定义状态: {is_custom}")
                    
                    if is_custom and '已清仓' in current_reason:
                        print("✅ 手动状态设置验证成功")
                    else:
                        print("❌ 手动状态设置验证失败")
                        return False
                else:
                    print("❌ 未找到测试股票")
                    return False
            else:
                print(f"❌ 获取股票失败: {result.get('message')}")
                return False
        else:
            print(f"❌ API请求失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 验证手动状态异常: {e}")
        return False
    
    # 步骤5: 执行单个股票更新
    print("\n📋 步骤5: 执行单个股票更新（关键测试）")
    try:
        response = requests.post(f'{base_url}/api/update-single-stock/{test_stock_code}')
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 单个股票更新成功: {result['message']}")
            else:
                print(f"❌ 单个股票更新失败: {result.get('message')}")
                return False
        else:
            print(f"❌ API请求失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 单个股票更新异常: {e}")
        return False
    
    # 步骤6: 验证手动状态是否被保护
    print("\n📋 步骤6: 验证手动状态是否被保护（核心验证）")
    time.sleep(2)  # 等待更新完成
    
    try:
        response = requests.get(f'{base_url}/api/stocks')
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                stocks = result['data']
                test_stock = None
                for stock in stocks:
                    if stock['code'] == test_stock_code:
                        test_stock = stock
                        break
                
                if test_stock:
                    current_signal = test_stock.get('sell_signal', 'unknown')
                    current_reason = test_stock.get('sell_reason', 'unknown')
                    is_custom = test_stock.get('custom_status', False)
                    
                    print(f"   更新后信号: {current_signal}")
                    print(f"   更新后原因: {current_reason}")
                    print(f"   自定义状态: {is_custom}")
                    
                    if is_custom and '已清仓' in current_reason:
                        print("🎉 状态保护成功！手动状态未被单个更新覆盖")
                        protection_success = True
                    else:
                        print("❌ 状态保护失败！手动状态被单个更新覆盖")
                        protection_success = False
                else:
                    print("❌ 未找到测试股票")
                    return False
            else:
                print(f"❌ 获取股票失败: {result.get('message')}")
                return False
        else:
            print(f"❌ API请求失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 验证状态保护异常: {e}")
        return False
    
    # 步骤7: 清理测试数据（恢复原始状态）
    print("\n📋 步骤7: 清理测试数据")
    try:
        response = requests.post(f'{base_url}/api/restore-original-status', json={
            'stock_code': test_stock_code,
            'status_type': 'hold'
        })
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 测试数据清理成功: {result['message']}")
            else:
                print(f"⚠️ 清理失败: {result.get('message')}")
        else:
            print(f"⚠️ 清理请求失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"⚠️ 清理异常: {e}")
    
    print("\n" + "=" * 60)
    if protection_success:
        print("🎉 测试通过！单个股票更新时手动状态保护功能正常工作")
        return True
    else:
        print("❌ 测试失败！单个股票更新时手动状态未被保护")
        return False

if __name__ == '__main__':
    success = test_single_stock_update_protection()
    if success:
        print("\n✅ 测试结果: 成功")
        exit(0)
    else:
        print("\n❌ 测试结果: 失败")
        exit(1)
