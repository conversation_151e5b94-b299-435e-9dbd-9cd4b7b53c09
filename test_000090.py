#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试000090的爆雷宝分数
"""

from bxb_integration import BXBIntegration

def test_000090():
    """测试000090股票"""
    # 初始化爆雷宝集成模块
    bxb = BXBIntegration()
    
    # 检测000090
    print('=== 检测 000090 ===')
    result = bxb.get_bxb_score('000090')
    if result:
        print(f'股票名称: {result["stock_name"]}')
        print(f'爆雷宝分数: {result["score"]} 分')
        print(f'风险等级: {result["risk_level"]}')
        print(f'触发风险: {result["triggered_risks"]} 项')
        print(f'触发率: {result["trigger_rate"]}%')
        print(f'显示格式: {bxb.format_for_display(result)}')
        
        if result['details']:
            print(f'\n触发的风险详情 (共{len(result["details"])}项):')
            total_deduction = 0
            
            # 按类别分组显示
            categories = {}
            for detail in result['details']:
                category = detail['category']
                if category not in categories:
                    categories[category] = []
                categories[category].append(detail)
                total_deduction += detail['score']
            
            for category, risks in categories.items():
                print(f'\n【{category}】- {len(risks)}项:')
                for risk in risks:
                    print(f'  - {risk["name"]} (扣分: {risk["score"]})')
                    if risk.get('reason'):
                        reason = risk['reason'][:150] + '...' if len(risk['reason']) > 150 else risk['reason']
                        print(f'    原因: {reason}')
            
            print(f'\n计算过程: 100 - {total_deduction} = {result["score"]}分')
        else:
            print('未触发任何风险项')
    else:
        print('获取000090数据失败')

if __name__ == "__main__":
    test_000090()
