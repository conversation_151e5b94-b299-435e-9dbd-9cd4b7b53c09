# 通达信扫雷模块集成指南

## 📦 文件清单

已为您创建以下文件，用于集成扫雷功能到持仓系统：

### 核心文件
- **`tdx_scan_module.py`** - 主要的扫雷模块（推荐使用）
- **`tdx_bxb_module.py`** - 旧版本模块（已更新名称）

### 文档文件
- **`TDX_SCAN_MODULE_README.md`** - 详细使用文档
- **`BXB_MODULE_README.md`** - 旧版本文档（已更新）

### 示例和测试文件
- **`bxb_integration_example.py`** - 集成示例代码
- **`test_bxb_integration.py`** - 完整测试脚本

## 🚀 快速集成步骤

### 1. 选择模块文件
推荐使用 **`tdx_scan_module.py`**，它使用了更清晰的命名：

```python
from tdx_scan_module import TdxScanClient

# 初始化扫雷客户端
scanner = TdxScanClient()

# 获取股票扫雷分数
result = scanner.get_stock_score('000001')
```

### 2. 集成到持仓系统主文件

在 `持仓系统_v8_饼图版.py` 中添加以下代码：

#### 2.1 导入模块（文件顶部）
```python
from tdx_scan_module import TdxScanClient
```

#### 2.2 初始化客户端（全局变量区域）
```python
# 初始化扫雷客户端
scan_client = TdxScanClient(cache_duration=1800)  # 缓存30分钟
```

#### 2.3 添加API路由（Flask应用初始化后）
```python
@app.route('/api/scan_score/<stock_code>')
def get_scan_score_api(stock_code):
    """获取股票扫雷分数API"""
    try:
        result = scan_client.get_stock_score(stock_code)
        if result:
            return jsonify(result)
        else:
            return jsonify({'error': f'无法获取 {stock_code} 的扫雷数据'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/scan_batch', methods=['POST'])
def get_scan_batch_api():
    """批量获取扫雷分数API"""
    try:
        data = request.get_json()
        stock_codes = data.get('codes', [])
        
        if not stock_codes:
            return jsonify({'error': '股票代码列表不能为空'}), 400
        
        results = scan_client.get_batch_scores(stock_codes)
        return jsonify(results)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500
```

#### 2.4 增强股票数据（在数据更新函数中）
```python
def update_stock_data():
    # ... 原有的股票数据获取逻辑 ...
    
    # 为每只股票添加扫雷数据
    for code, data in stock_data.items():
        try:
            scan_result = scan_client.get_stock_score(code)
            if scan_result:
                data.update({
                    'scan_score': scan_result['score'],
                    'scan_risk_level': scan_result['risk_level'],
                    'scan_risk_color': scan_result['risk_color'],
                    'scan_triggered_risks': scan_result['triggered_risks'],
                    'scan_display_text': f"{scan_result['score']}分 ({scan_result['risk_level']})",
                    'scan_update_time': scan_result['update_time']
                })
            else:
                # 添加默认值
                data.update({
                    'scan_score': 0,
                    'scan_risk_level': '数据异常',
                    'scan_risk_color': '#cccccc',
                    'scan_triggered_risks': 0,
                    'scan_display_text': '数据获取失败',
                    'scan_update_time': ''
                })
        except Exception as e:
            print(f"获取 {code} 扫雷数据失败: {e}")
```

### 3. 前端显示修改

#### 3.1 在HTML模板中添加扫雷信息
```html
<!-- 在股票信息显示区域添加 -->
<div class="scan-info">
    <span class="scan-score" style="color: {{stock.scan_risk_color}}">
        扫雷: {{stock.scan_display_text}}
    </span>
</div>
```

#### 3.2 添加CSS样式（可选）
```css
.scan-info {
    margin: 5px 0;
    font-size: 12px;
}

.scan-score {
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 3px;
    background-color: rgba(255, 255, 255, 0.1);
}
```

## 🎯 功能特性

### ✅ 已验证的计算准确性
- 平安银行(000001): 97分 ✅
- 万科A(000002): 69分 ✅
- 招商银行(600036): 95分 ✅

### 🚀 性能优化
- **智能缓存**: 默认缓存30分钟，避免重复请求
- **批量处理**: 支持同时处理多只股票
- **异步支持**: 可在后台异步更新数据

### 🛡️ 错误处理
- 网络异常自动重试
- 数据格式错误优雅处理
- 超时控制和资源管理

## 📊 风险等级说明

| 分数 | 等级 | 颜色 | 建议 |
|------|------|------|------|
| 90-100 | 极低风险 | 🟢 绿色 | 相对安全 |
| 80-89 | 低风险 | 🟢 浅绿 | 适度关注 |
| 70-79 | 中低风险 | 🟡 黄色 | 需要关注 |
| 60-69 | 中等风险 | 🟠 橙色 | 需要警惕 |
| 50-59 | 中高风险 | 🟠 深橙 | 需要谨慎 |
| 40-49 | 高风险 | 🔴 红色 | 建议回避 |
| 1-39 | 极高风险 | 🔴 深红 | 强烈回避 |

## 🔧 高级配置

### 自定义缓存时间
```python
# 缓存1小时
scanner = TdxScanClient(cache_duration=3600)

# 缓存10分钟（适合实时性要求高的场景）
scanner = TdxScanClient(cache_duration=600)
```

### 批量更新策略
```python
def batch_update_scan_data():
    """批量更新所有股票的扫雷数据"""
    stock_codes = list(stock_data.keys())
    
    # 分批处理，每批20只股票
    batch_size = 20
    for i in range(0, len(stock_codes), batch_size):
        batch = stock_codes[i:i+batch_size]
        results = scan_client.get_batch_scores(batch)
        
        # 更新数据
        for code, scan_data in results.items():
            if code in stock_data and scan_data:
                stock_data[code].update({
                    'scan_score': scan_data['score'],
                    'scan_risk_level': scan_data['risk_level'],
                    'scan_risk_color': scan_data['risk_color']
                })
```

## 🚨 注意事项

1. **请求频率**: 建议批量请求间隔0.5秒，避免被服务器限制
2. **缓存管理**: 定期清理缓存，确保数据时效性
3. **错误处理**: 在生产环境中添加完善的日志记录
4. **性能监控**: 监控API响应时间和成功率

## 📞 技术支持

如果在集成过程中遇到问题：

1. **检查网络连接**: 确保能访问通达信服务器
2. **验证股票代码**: 确保使用正确的6位股票代码格式
3. **查看错误日志**: 检查控制台输出的错误信息
4. **测试单只股票**: 先测试单只股票，再进行批量处理

## 🎉 集成完成检查清单

- [ ] 已将 `tdx_scan_module.py` 放入项目根目录
- [ ] 已在主文件中导入模块
- [ ] 已初始化扫雷客户端
- [ ] 已添加API路由
- [ ] 已在数据更新函数中集成扫雷数据
- [ ] 已在前端模板中添加显示
- [ ] 已测试单只股票查询
- [ ] 已测试批量查询
- [ ] 已验证前端显示效果

完成以上步骤后，您的持仓系统就成功集成了通达信扫雷功能！

---

**最后更新**: 2025-01-22  
**模块版本**: 1.0  
**兼容性**: Python 3.6+, Flask 1.0+
