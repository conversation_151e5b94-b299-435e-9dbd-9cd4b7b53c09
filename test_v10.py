#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试V10版本的导入和基本功能
"""

print("开始测试V10版本...")

try:
    print("1. 测试基本导入...")
    from flask import Flask, render_template, jsonify, request
    import requests
    import time
    import json
    import pandas as pd
    import os
    from datetime import datetime, timedelta
    import threading
    from urllib.parse import urlencode
    from werkzeug.utils import secure_filename
    print("✓ 基本模块导入成功")
    
    print("2. 测试扫雷模块导入...")
    from tdx_scan_module import TdxScanClient
    print("✓ 扫雷模块导入成功")
    
    print("3. 测试扫雷客户端初始化...")
    scan_client = TdxScanClient(cache_duration=1800)
    print("✓ 扫雷客户端初始化成功")
    
    print("4. 测试企业微信模块导入...")
    try:
        from wechat_alert import WeChatAlert
        print("✓ 企业微信模块导入成功")
        
        # 测试初始化（使用占位符URL）
        wechat_alert = WeChatAlert("https://placeholder.webhook.url")
        print("✓ 企业微信客户端初始化成功")
    except Exception as e:
        print(f"⚠️ 企业微信模块问题: {e}")
        wechat_alert = None
    
    print("5. 测试交易时间监控模块导入...")
    try:
        from A股交易时间监测_简化版 import AStockTradingTimeMonitor
        trading_monitor = AStockTradingTimeMonitor()
        print("✓ 交易时间监控模块导入成功")
    except Exception as e:
        print(f"⚠️ 交易时间监控模块问题: {e}")
        trading_monitor = None
    
    print("6. 测试Flask应用创建...")
    app = Flask(__name__)
    print("✓ Flask应用创建成功")
    
    print("7. 测试扫雷功能...")
    result = scan_client.get_stock_score('000001')
    if result:
        print(f"✓ 扫雷功能测试成功: {result['stock_name']} - {result['score']}分")
    else:
        print("⚠️ 扫雷功能测试失败")
    
    print("\n" + "="*50)
    print("✅ V10版本测试完成！")
    print("主要功能:")
    print("- ✓ 基本模块导入正常")
    print("- ✓ 扫雷模块集成成功")
    print("- ✓ Flask应用可以创建")
    print("- ✓ 扫雷功能可以使用")
    if wechat_alert:
        print("- ✓ 企业微信模块可用")
    else:
        print("- ⚠️ 企业微信模块需要配置")
    if trading_monitor:
        print("- ✓ 交易时间监控可用")
    else:
        print("- ⚠️ 交易时间监控需要检查")
    print("="*50)
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
