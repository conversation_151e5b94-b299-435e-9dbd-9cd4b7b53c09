#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的显示逻辑
验证策略价格计算和显示信息是否正确
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入策略管理器
from 持仓系统_v14 import SellStrategyManager, calculate_sell_signal

def test_display_logic():
    """测试显示逻辑"""
    print("🧪 开始测试新的显示逻辑...")
    
    # 测试数据
    test_cases = [
        {
            'name': '基础涨幅减半测试',
            'data': {
                'name': '测试股票A',
                'price': 10.50,
                'low_price': 6.00,  # 最低价
                'unit_cost': 8.00,  # 成本价
                'distance_from_low_pct': 75.0,  # 触发70%减半
                'pe_ratio': 15.0,
                'pb_ratio': 1.5,
                'change_pct': 5.0,
                'dividend_yield': 3.0,
                'scan_score': 80,
                'profit_status': 'profit',
                'profit_margin': 20.0
            },
            'expected_target_price': 6.00 * 1.7  # 70%涨幅策略目标价格
        },
        {
            'name': '基础涨幅清仓测试',
            'data': {
                'name': '测试股票B',
                'price': 15.80,
                'low_price': 6.50,
                'unit_cost': 9.00,
                'distance_from_low_pct': 145.0,  # 触发140%清仓
                'pe_ratio': 20.0,
                'pb_ratio': 2.0,
                'change_pct': 3.0,
                'dividend_yield': 2.5,
                'scan_score': 75,
                'profit_status': 'profit',
                'profit_margin': 50.0
            },
            'expected_target_price': 6.50 * 2.4  # 140%涨幅策略目标价格
        },
        {
            'name': '负TTM回本清仓测试',
            'data': {
                'name': '测试股票C',
                'price': 8.20,
                'low_price': 5.00,
                'unit_cost': 7.50,  # 成本价
                'distance_from_low_pct': 20.0,
                'pe_ratio': -2.0,  # 触发负TTM
                'pb_ratio': 1.0,
                'change_pct': 0.5,
                'dividend_yield': 2.5,  # 触发≤3%股息
                'scan_score': 80,
                'profit_status': 'profit',  # 触发已回本
                'profit_margin': 5.0
            },
            'expected_target_price': 7.50  # 回本价格
        },
        {
            'name': '预警测试',
            'data': {
                'name': '测试股票D',
                'price': 12.00,
                'low_price': 7.00,
                'unit_cost': 9.50,
                'distance_from_low_pct': 67.0,  # 接近70%减半阈值
                'pe_ratio': 18.0,
                'pb_ratio': 1.3,
                'change_pct': 2.0,
                'dividend_yield': 3.5,
                'scan_score': 85,
                'profit_status': 'profit',
                'profit_margin': 12.0
            },
            'expected_target_price': 12.00 * 1.02  # 预警挂单价格
        }
    ]
    
    print(f"\n🧪 开始测试 {len(test_cases)} 个测试用例...")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试用例 {i}: {test_case['name']} ---")
        
        # 计算卖出信号
        result = calculate_sell_signal(test_case['data'])
        
        print(f"📊 测试数据:")
        print(f"   当前价格: {test_case['data']['price']:.2f}元")
        print(f"   最低价格: {test_case['data']['low_price']:.2f}元")
        print(f"   成本价格: {test_case['data']['unit_cost']:.2f}元")
        print(f"   涨幅: {test_case['data']['distance_from_low_pct']:.1f}%")
        
        print(f"🎯 策略结果:")
        print(f"   信号: {result['signal']}")
        print(f"   原因: {result['reason']}")
        
        # 模拟前端价格计算逻辑
        stock_data = test_case['data'].copy()
        stock_data['sell_signal'] = result['signal']
        stock_data['sell_reason'] = result['reason']
        
        # 这里我们需要模拟JavaScript的calculateStrategyPrice函数
        target_price = None
        current_price = stock_data['price']
        low_price = stock_data['low_price']
        cost_price = stock_data['unit_cost']
        
        if result['signal'] == 'clearance':
            if '140%' in result['reason']:
                target_price = low_price * 2.4
            elif '100%' in result['reason']:
                target_price = low_price * 2.0
            elif '50%' in result['reason']:
                target_price = low_price * 1.5
            elif '回本' in result['reason']:
                target_price = cost_price
            else:
                target_price = current_price
        elif result['signal'] == 'reduce':
            if '70%' in result['reason']:
                target_price = low_price * 1.7
            elif '50%' in result['reason']:
                target_price = low_price * 1.5
            elif '30%' in result['reason']:
                target_price = low_price * 1.3
            else:
                target_price = current_price
        elif result['signal'] == 'warning':
            target_price = current_price * 1.02
        
        print(f"💰 价格计算:")
        if target_price:
            print(f"   目标价格: {target_price:.2f}元")
            print(f"   预期价格: {test_case['expected_target_price']:.2f}元")
            
            # 验证价格计算是否正确
            price_diff = abs(target_price - test_case['expected_target_price'])
            if price_diff < 0.01:  # 允许0.01元的误差
                print(f"   ✅ 价格计算正确")
            else:
                print(f"   ❌ 价格计算错误，差异: {price_diff:.2f}元")
        else:
            print(f"   ⚠️ 无目标价格（市价操作）")

def main():
    """主函数"""
    try:
        test_display_logic()
        print("\n✅ 显示逻辑测试完成!")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
