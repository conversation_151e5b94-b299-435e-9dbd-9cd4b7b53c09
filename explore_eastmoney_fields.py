#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import time
import json
from datetime import datetime

def explore_eastmoney_fields(stock_code):
    """
    探索东方财富API的所有字段，寻找股息率和行业信息
    """
    
    # 根据股票代码确定市场
    if stock_code.startswith('6'):
        secid = f'1.{stock_code}'
    else:
        secid = f'0.{stock_code}'
    
    # 东方财富API URL
    url = 'http://push2.eastmoney.com/api/qt/stock/get'
    
    # 请求所有可能的字段
    params = {
        'fltt': '2',
        'invt': '2',
        'fields': 'f120,f121,f122,f174,f175,f59,f163,f43,f57,f58,f169,f170,f46,f44,f51,f168,f47,f164,f116,f60,f45,f52,f50,f48,f167,f117,f71,f161,f49,f530,f135,f136,f137,f138,f139,f141,f142,f144,f145,f147,f148,f140,f143,f146,f149,f55,f62,f162,f92,f173,f104,f105,f84,f85,f183,f184,f185,f186,f187,f188,f189,f190,f191,f192,f107,f111,f86,f177,f78,f110,f262,f263,f264,f267,f268,f255,f256,f257,f258,f127,f199,f128,f198,f259,f260,f261,f171,f277,f278,f279,f288,f152,f250,f251,f252,f253,f254,f269,f270,f271,f272,f273,f274,f275,f276,f265,f266,f289,f290,f286,f285,f292,f293,f294,f295,f133,f134,f131,f132,f129,f130',
        'secid': secid,
        '_': str(int(time.time() * 1000))
    }
    
    try:
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = json.loads(response.text)['data']
            
            if data:
                print(f"\n=== {stock_code} 所有字段探索 ===")
                print(f"股票名称: {data.get('f58', 'N/A')}")
                print(f"最新价: {data.get('f43', 'N/A')}")
                
                # 按字段编号排序显示
                sorted_fields = sorted(data.items(), key=lambda x: int(x[0][1:]) if x[0].startswith('f') and x[0][1:].isdigit() else 999)
                
                print("\n所有字段值:")
                for key, value in sorted_fields:
                    if value is not None and value != '' and value != 0:
                        print(f"{key}: {value}")
                
                # 重点关注可能的股息率字段
                dividend_fields = ['f133', 'f134', 'f131', 'f132', 'f129', 'f130', 'f187', 'f188', 'f189', 'f190']
                print(f"\n=== 可能的股息率字段 ===")
                for field in dividend_fields:
                    if field in data:
                        print(f"{field}: {data[field]}")
                
                # 重点关注可能的行业字段
                industry_fields = ['f127', 'f128', 'f199', 'f198', 'f259', 'f260', 'f261']
                print(f"\n=== 可能的行业字段 ===")
                for field in industry_fields:
                    if field in data:
                        print(f"{field}: {data[field]}")
                
                return data
            
    except Exception as e:
        print(f"获取股票 {stock_code} 数据失败: {e}")
        return None

def test_multiple_stocks():
    """
    测试多只股票，寻找规律
    """
    test_codes = ['600036', '000001', '600519', '000002', '600000']  # 招商银行、平安银行、茅台、万科、浦发银行
    
    print("=== 探索东方财富API字段 ===")
    print("寻找股息率和行业信息...")
    
    for code in test_codes:
        explore_eastmoney_fields(code)
        print("\n" + "="*50)
        time.sleep(1)  # 避免请求过快

if __name__ == "__main__":
    test_multiple_stocks()
