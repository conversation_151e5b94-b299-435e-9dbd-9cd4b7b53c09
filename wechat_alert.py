#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
企业微信提醒功能模块
当股票距离最低点涨幅大于70%时发送提醒
"""

import requests
import json
from datetime import datetime

class WeChatAlert:
    def __init__(self, webhook_url):
        """
        初始化企业微信提醒
        
        Args:
            webhook_url: 企业微信机器人webhook地址
        """
        self.webhook_url = webhook_url
        self.sent_alerts = set()  # 记录已发送的提醒，避免重复发送
    
    def send_message(self, message, mentioned_list=None):
        """
        发送消息到企业微信
        
        Args:
            message: 要发送的消息内容
            mentioned_list: @的用户列表，["@all"] 表示@所有人
        
        Returns:
            bool: 发送是否成功
        """
        
        headers = {
            "Content-Type": "application/json"
        }
        
        data = {
            "msgtype": "text",
            "text": {
                "content": message,
                "mentioned_list": mentioned_list or []
            }
        }
        
        try:
            response = requests.post(self.webhook_url, headers=headers, json=data, timeout=10)
            result = response.json()
            
            if result.get('errcode') == 0:
                print(f"✅ 企业微信消息发送成功")
                return True
            else:
                print(f"❌ 企业微信消息发送失败: {result}")
                return False
                
        except Exception as e:
            print(f"❌ 发送企业微信消息异常: {e}")
            return False
    
    def send_markdown_message(self, content):
        """
        发送Markdown格式消息
        
        Args:
            content: Markdown格式的消息内容
        
        Returns:
            bool: 发送是否成功
        """
        
        headers = {
            "Content-Type": "application/json"
        }
        
        data = {
            "msgtype": "markdown",
            "markdown": {
                "content": content
            }
        }
        
        try:
            response = requests.post(self.webhook_url, headers=headers, json=data, timeout=10)
            result = response.json()
            
            if result.get('errcode') == 0:
                print(f"✅ 企业微信Markdown消息发送成功")
                return True
            else:
                print(f"❌ 企业微信Markdown消息发送失败: {result}")
                return False
                
        except Exception as e:
            print(f"❌ 发送企业微信Markdown消息异常: {e}")
            return False
    
    def check_and_alert_high_gains(self, stocks_data, threshold=70.0):
        """
        检查股票涨幅并发送提醒
        
        Args:
            stocks_data: 股票数据列表
            threshold: 涨幅阈值（默认70%）
        
        Returns:
            list: 发送提醒的股票列表
        """
        
        high_gain_stocks = []
        
        for stock in stocks_data:
            # 检查距离最低点涨幅
            distance_pct = stock.get('distance_from_low_pct')
            
            if distance_pct is not None and distance_pct > threshold:
                # 生成唯一标识，避免重复发送
                alert_key = f"{stock['code']}_{int(distance_pct/10)*10}"  # 按10%区间分组
                
                if alert_key not in self.sent_alerts:
                    high_gain_stocks.append(stock)
                    self.sent_alerts.add(alert_key)
        
        if high_gain_stocks:
            self.send_high_gain_alert(high_gain_stocks, threshold)
        
        return high_gain_stocks
    
    def send_high_gain_alert(self, stocks, threshold):
        """
        发送高涨幅股票提醒
        
        Args:
            stocks: 高涨幅股票列表
            threshold: 涨幅阈值
        """
        
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 构建消息内容
        message_lines = [
            f"🚀 股票高涨幅提醒 🚀",
            f"",
            f"⏰ 时间: {current_time}",
            f"📈 阈值: 距离最低点涨幅 > {threshold}%",
            f"📊 发现 {len(stocks)} 只高涨幅股票:",
            f""
        ]
        
        for i, stock in enumerate(stocks, 1):
            yearly_low = stock.get('yearly_low', 0)
            current_price = stock.get('price', 0)
            distance_pct = stock.get('distance_from_low_pct', 0)
            low_date = stock.get('low_date', '未知')
            
            message_lines.append(
                f"{i}. {stock['code']} {stock['name']}\n"
                f"   💰 当前价: {current_price:.2f}\n"
                f"   📉 年内最低: {yearly_low:.2f} ({low_date})\n"
                f"   🚀 距最低点涨幅: +{distance_pct:.1f}%\n"
            )
        
        message_lines.extend([
            f"",
            f"📱 数据来源: 东方财富网"
        ])
        
        message = "\n".join(message_lines)
        
        # 发送消息（@所有人）
        self.send_message(message, mentioned_list=["@all"])
    
    def send_daily_summary(self, stocks_data):
        """
        发送每日股票摘要
        
        Args:
            stocks_data: 股票数据列表
        """
        
        if not stocks_data:
            return
        
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 统计数据
        total_stocks = len(stocks_data)
        
        # 有效数据统计
        valid_distance_stocks = [s for s in stocks_data if s.get('distance_from_low_pct') is not None]
        
        if not valid_distance_stocks:
            return
        
        # 涨幅分布统计
        high_gain_stocks = [s for s in valid_distance_stocks if s['distance_from_low_pct'] > 50]
        medium_gain_stocks = [s for s in valid_distance_stocks if 20 <= s['distance_from_low_pct'] <= 50]
        low_gain_stocks = [s for s in valid_distance_stocks if s['distance_from_low_pct'] < 20]
        
        # 找出涨幅最高的前3只
        top_gainers = sorted(valid_distance_stocks, key=lambda x: x['distance_from_low_pct'], reverse=True)[:3]
        
        # 找出接近最低点的前3只
        near_low_stocks = sorted(valid_distance_stocks, key=lambda x: x['distance_from_low_pct'])[:3]
        
        # 构建Markdown消息
        markdown_content = f"""# 📈 持仓股票每日摘要
        
**⏰ 更新时间:** {current_time}
**📊 统计范围:** {total_stocks} 只持仓股票

## 📈 涨幅分布统计
- 🚀 高涨幅 (>50%): **{len(high_gain_stocks)}** 只
- 📈 中等涨幅 (20%-50%): **{len(medium_gain_stocks)}** 只  
- 📉 低涨幅 (<20%): **{len(low_gain_stocks)}** 只

## 🏆 涨幅榜 TOP3
"""
        
        for i, stock in enumerate(top_gainers, 1):
            markdown_content += f"{i}. **{stock['code']} {stock['name']}** +{stock['distance_from_low_pct']:.1f}%\n"
        
        markdown_content += f"""
## 💎 价值发现 (接近最低点)
"""
        
        for i, stock in enumerate(near_low_stocks, 1):
            markdown_content += f"{i}. **{stock['code']} {stock['name']}** +{stock['distance_from_low_pct']:.1f}%\n"
        
        markdown_content += f"""
---
📱 **数据来源:** 东方财富网
"""
        
        # 发送Markdown消息
        self.send_markdown_message(markdown_content)
    
    def test_connection(self):
        """
        测试企业微信连接
        
        Returns:
            bool: 连接是否成功
        """
        
        test_message = f"🤖 大A机器人连接测试\n⏰ 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n✅ 连接正常！"
        
        return self.send_message(test_message)
    
    def clear_sent_alerts(self):
        """清空已发送提醒记录"""
        self.sent_alerts.clear()
        print("✅ 已清空提醒记录")

# 使用示例
if __name__ == "__main__":
    # 您的企业微信机器人webhook地址
    WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7bddd706-ef0f-4d6c-9e1f-3cc8a0a18d9e"
    
    # 创建提醒实例
    alert = WeChatAlert(WEBHOOK_URL)
    
    # 测试连接
    print("测试企业微信连接...")
    if alert.test_connection():
        print("✅ 企业微信连接测试成功！")
    else:
        print("❌ 企业微信连接测试失败！")
    
    # 模拟股票数据测试
    test_stocks = [
        {
            'code': '000001',
            'name': '平安银行',
            'price': 15.50,
            'yearly_low': 9.00,
            'distance_from_low_pct': 72.2,
            'low_date': '2024-02-15'
        },
        {
            'code': '600519',
            'name': '贵州茅台',
            'price': 1800.00,
            'yearly_low': 1500.00,
            'distance_from_low_pct': 20.0,
            'low_date': '2024-01-10'
        }
    ]
    
    # 测试高涨幅提醒
    print("\n测试高涨幅提醒...")
    high_gain_stocks = alert.check_and_alert_high_gains(test_stocks, threshold=70.0)
    
    if high_gain_stocks:
        print(f"✅ 发现 {len(high_gain_stocks)} 只高涨幅股票，已发送提醒")
    else:
        print("ℹ️  暂无高涨幅股票")
    
    # 测试每日摘要
    print("\n测试每日摘要...")
    alert.send_daily_summary(test_stocks)
