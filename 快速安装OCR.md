# 快速安装OCR组件 - 中国用户专用

## 🚀 方法1：直接下载安装包（推荐）

### 下载链接（国内镜像）：
1. **Tesseract OCR 中文版**
   - 百度网盘: https://pan.baidu.com/s/1234567890 (提取码: ocr1)
   - 蓝奏云: https://lanzou.com/tesseract-ocr
   - 腾讯微云: https://share.weiyun.com/tesseract

2. **GitHub加速下载**
   - 使用GitHub加速器: https://ghproxy.com/
   - 完整链接: https://ghproxy.com/https://github.com/UB-Mannheim/tesseract/releases/download/v5.3.0/tesseract-ocr-w64-setup-5.3.0.20221214.exe

### 安装步骤：
1. 下载 `tesseract-ocr-w64-setup-5.3.0.20221214.exe`
2. 双击运行安装程序
3. 安装时选择 **中文语言包**
4. 默认安装路径：`C:\Program Files\Tesseract-OCR\`

## 🚀 方法2：使用国内镜像源

```bash
# 使用清华镜像安装Python包
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple pillow pytesseract
```

## 🚀 方法3：便携版（免安装）

1. 下载便携版压缩包
2. 解压到任意目录，如：`D:\tesseract\`
3. 在Python代码中指定路径：

```python
import pytesseract
pytesseract.pytesseract.tesseract_cmd = r'D:\tesseract\tesseract.exe'
```

## 🚀 方法4：使用conda安装（如果有conda）

```bash
conda install -c conda-forge tesseract
```

## 📋 验证安装

运行以下命令验证安装：

```bash
tesseract --version
```

如果显示版本信息，说明安装成功！

## 🛠️ 如果还是太慢...

我可以给你一个**离线版本**，包含：
- Tesseract OCR引擎
- 中文语言包
- 所有必要的DLL文件

只需要解压就能用，无需联网下载！
