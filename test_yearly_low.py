#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
from datetime import datetime, timedelta
from urllib.parse import urlencode

def gen_secid(rawcode: str) -> str:
    """生成东方财富专用的secid"""
    if rawcode[0] == '6':
        return f'1.{rawcode}'  # 沪市
    return f'0.{rawcode}'      # 深市

def test_yearly_low_price(code: str) -> dict:
    """测试获取股票一年内的最低价（前复权）"""
    
    print(f"\n{'='*60}")
    print(f"🧪 测试股票 {code} 的一年内最低价获取")
    print(f"{'='*60}")
    
    # 计算一年前的日期
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365)
    
    start_date_str = start_date.strftime('%Y%m%d')
    end_date_str = end_date.strftime('%Y%m%d')
    
    print(f"📅 查询时间范围: {start_date_str} 到 {end_date_str}")
    
    secid = gen_secid(code)
    print(f"🏷️  股票secid: {secid}")
    
    params = {
        'fields1': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13',
        'fields2': 'f51,f52,f53,f54,f55',  # 日期,开盘,收盘,最高,最低
        'beg': start_date_str,
        'end': end_date_str,
        'rtntype': '6',
        'secid': secid,
        'klt': '101',  # 日K
        'fqt': '1',    # 前复权
    }
    
    base_url = 'https://push2his.eastmoney.com/api/qt/stock/kline/get'
    url = base_url + '?' + urlencode(params)
    
    print(f"🌐 请求URL: {url}")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; WOW64; Trident/7.0; Touch; rv:11.0) like Gecko',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.8',
        'Referer': 'http://quote.eastmoney.com/',
    }
    
    try:
        print("📡 发送请求...")
        response = requests.get(url, headers=headers, timeout=30)
        
        print(f"📊 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            json_data = response.json()
            
            print(f"📋 响应数据结构:")
            print(f"  - rc: {json_data.get('rc')}")
            print(f"  - rt: {json_data.get('rt')}")
            print(f"  - svr: {json_data.get('svr')}")
            print(f"  - lt: {json_data.get('lt')}")
            print(f"  - full: {json_data.get('full')}")
            print(f"  - data存在: {'data' in json_data}")
            
            if 'data' in json_data and json_data['data']:
                data = json_data['data']
                print(f"  - data.code: {data.get('code')}")
                print(f"  - data.market: {data.get('market')}")
                print(f"  - data.name: {data.get('name')}")
                print(f"  - data.decimal: {data.get('decimal')}")
                print(f"  - data.dktotal: {data.get('dktotal')}")
                print(f"  - data.preKPrice: {data.get('preKPrice')}")
                print(f"  - klines存在: {'klines' in data}")
                
                if 'klines' in data and data['klines']:
                    klines = data['klines']
                    print(f"  - klines数量: {len(klines)}")
                    
                    if klines:
                        print(f"📈 前3条K线数据示例:")
                        for i, kline in enumerate(klines[:3]):
                            kline_data = kline.split(',')
                            print(f"    {i+1}. {kline_data[0]} 开:{kline_data[1]} 收:{kline_data[2]} 高:{kline_data[3]} 低:{kline_data[4]}")
                        
                        # 找到最低价
                        min_low = float('inf')
                        low_date = None
                        low_kline = None
                        
                        for kline in klines:
                            kline_data = kline.split(',')
                            if len(kline_data) >= 5:
                                low_price = float(kline_data[4])  # 最低价
                                if low_price < min_low:
                                    min_low = low_price
                                    low_date = kline_data[0]  # 日期
                                    low_kline = kline_data
                        
                        print(f"\n🎯 分析结果:")
                        print(f"  - 数据点数量: {len(klines)}")
                        print(f"  - 一年内最低价: {min_low}")
                        print(f"  - 最低价日期: {low_date}")
                        if low_kline:
                            print(f"  - 最低价当日: 开:{low_kline[1]} 收:{low_kline[2]} 高:{low_kline[3]} 低:{low_kline[4]}")
                        
                        # 获取最新价格
                        latest_kline = klines[-1].split(',')
                        current_price = float(latest_kline[2])  # 收盘价
                        distance_pct = ((current_price - min_low) / min_low) * 100
                        
                        print(f"  - 最新收盘价: {current_price}")
                        print(f"  - 距最低点涨幅: {distance_pct:.2f}%")
                        
                        return {
                            'success': True,
                            'yearly_low': min_low,
                            'low_date': low_date,
                            'current_price': current_price,
                            'distance_pct': distance_pct,
                            'data_points': len(klines)
                        }
                    else:
                        print("❌ klines数组为空")
                else:
                    print("❌ 响应中没有klines数据")
            else:
                print("❌ 响应中没有data数据")
                print(f"完整响应: {json.dumps(json_data, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ HTTP请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
        
        return {'success': False, 'error': '获取数据失败'}
        
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return {'success': False, 'error': str(e)}

def main():
    """主测试函数"""
    print("🧪 一年内最低价获取功能测试")
    print("=" * 60)
    
    # 测试几只不同的股票
    test_codes = [
        '000001',  # 平安银行 - 深市
        '600036',  # 招商银行 - 沪市
    ]
    
    results = []
    
    for code in test_codes:
        result = test_yearly_low_price(code)
        results.append({
            'code': code,
            'result': result
        })
        
        print(f"\n⏱️  等待1秒避免请求过频...")
        import time
        time.sleep(1)
    
    print(f"\n{'='*60}")
    print("📊 测试结果汇总")
    print(f"{'='*60}")
    
    success_count = 0
    for item in results:
        code = item['code']
        result = item['result']
        
        if result.get('success'):
            success_count += 1
            print(f"✅ {code}: 最低价 {result['yearly_low']:.2f}, 距最低点 {result['distance_pct']:.2f}%, 数据点 {result['data_points']}")
        else:
            print(f"❌ {code}: {result.get('error', '未知错误')}")
    
    print(f"\n📈 成功率: {success_count}/{len(test_codes)} ({success_count/len(test_codes)*100:.1f}%)")

if __name__ == "__main__":
    main()
