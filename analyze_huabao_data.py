#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import cv2
import numpy as np
from PIL import Image
import pandas as pd
import re

def analyze_huabao_screenshot():
    """分析华宝证券截图"""
    print("=== 分析华宝证券截图 ===")
    
    try:
        # 读取截图
        image = cv2.imread("huabao_window.png")
        if image is None:
            print("❌ 无法读取截图文件")
            return
        
        print(f"✅ 截图尺寸: {image.shape[1]}x{image.shape[0]}")
        
        # 转换为RGB格式（用于显示）
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 保存分析结果
        analyze_image_structure(image)
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

def analyze_image_structure(image):
    """分析图像结构，寻找表格数据"""
    print("\n=== 分析图像结构 ===")
    
    # 转换为灰度图
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # 检测水平线（表格行）
    horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (40, 1))
    horizontal_lines = cv2.morphologyEx(gray, cv2.MORPH_OPEN, horizontal_kernel)
    
    # 检测垂直线（表格列）
    vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 40))
    vertical_lines = cv2.morphologyEx(gray, cv2.MORPH_OPEN, vertical_kernel)
    
    # 合并水平和垂直线
    table_structure = cv2.addWeighted(horizontal_lines, 0.5, vertical_lines, 0.5, 0.0)
    
    # 保存表格结构图
    cv2.imwrite("table_structure.png", table_structure)
    print("✅ 已保存表格结构分析: table_structure.png")
    
    # 寻找文字区域
    find_text_regions(gray)

def find_text_regions(gray_image):
    """寻找文字区域"""
    print("\n=== 寻找文字区域 ===")
    
    # 使用阈值处理
    _, thresh = cv2.threshold(gray_image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    
    # 寻找轮廓
    contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # 过滤出可能的文字区域
    text_regions = []
    for contour in contours:
        x, y, w, h = cv2.boundingRect(contour)
        
        # 过滤条件：合适的宽高比和大小
        if 10 < w < 200 and 10 < h < 50:
            text_regions.append((x, y, w, h))
    
    print(f"找到 {len(text_regions)} 个可能的文字区域")
    
    # 在原图上标记文字区域
    image_with_boxes = cv2.imread("huabao_window.png")
    for x, y, w, h in text_regions:
        cv2.rectangle(image_with_boxes, (x, y), (x+w, y+h), (0, 255, 0), 2)
    
    cv2.imwrite("text_regions.png", image_with_boxes)
    print("✅ 已保存文字区域标记: text_regions.png")

def extract_stock_data_simple():
    """简单的股票数据提取方法"""
    print("\n=== 尝试简单数据提取 ===")
    
    # 这里我们可以尝试一些简单的方法
    # 1. 查找特定的颜色区域（红色/绿色表示涨跌）
    # 2. 查找数字模式
    # 3. 查找股票代码模式
    
    try:
        image = cv2.imread("huabao_window.png")
        
        # 查找红色区域（上涨）
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # 红色范围
        red_lower1 = np.array([0, 50, 50])
        red_upper1 = np.array([10, 255, 255])
        red_lower2 = np.array([170, 50, 50])
        red_upper2 = np.array([180, 255, 255])
        
        red_mask1 = cv2.inRange(hsv, red_lower1, red_upper1)
        red_mask2 = cv2.inRange(hsv, red_lower2, red_upper2)
        red_mask = cv2.bitwise_or(red_mask1, red_mask2)
        
        # 绿色范围（下跌）
        green_lower = np.array([40, 50, 50])
        green_upper = np.array([80, 255, 255])
        green_mask = cv2.inRange(hsv, green_lower, green_upper)
        
        # 保存颜色分析结果
        cv2.imwrite("red_regions.png", red_mask)
        cv2.imwrite("green_regions.png", green_mask)
        
        print("✅ 已保存颜色分析结果:")
        print("  - red_regions.png (上涨区域)")
        print("  - green_regions.png (下跌区域)")
        
    except Exception as e:
        print(f"❌ 颜色分析失败: {e}")

def main():
    print("=== 华宝证券数据分析 ===")
    
    # 分析截图
    analyze_huabao_screenshot()
    
    # 提取股票数据
    extract_stock_data_simple()
    
    print("\n=== 分析完成 ===")
    print("生成的文件:")
    print("- table_structure.png (表格结构)")
    print("- text_regions.png (文字区域)")
    print("- red_regions.png (红色区域)")
    print("- green_regions.png (绿色区域)")
    
    print("\n下一步建议:")
    print("1. 查看这些分析图片")
    print("2. 确认是否能识别出股票数据的位置")
    print("3. 如果可以，我们继续开发数据提取算法")

if __name__ == "__main__":
    main()
