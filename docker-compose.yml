version: '3.8'

services:
  stock-portfolio:
    build: .
    container_name: stock-portfolio-app
    restart: unless-stopped
    environment:
      - FLASK_ENV=production
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./cache:/app/cache
      - ./uploads:/app/uploads
    ports:
      - "8000:8000"
    networks:
      - stock-network

  nginx:
    image: nginx:alpine
    container_name: stock-portfolio-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - ./ssl:/etc/nginx/ssl  # SSL证书目录
    depends_on:
      - stock-portfolio
    networks:
      - stock-network

networks:
  stock-network:
    driver: bridge
