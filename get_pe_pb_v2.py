#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
从东方财富获取股票市净率(PB)和TTM市盈率(PE)的脚本 V2
尝试不同的API接口
"""

import requests
import time
import json
import pandas as pd
from datetime import datetime

def get_stock_pe_pb_from_list_api(stock_codes):
    """
    使用东方财富股票列表API批量获取市净率和TTM市盈率
    这个API可能包含更多的估值数据
    """
    
    # 构建股票代码列表
    secids = []
    for code in stock_codes:
        if code.startswith('6'):
            secids.append(f'1.{code}')  # 上海
        else:
            secids.append(f'0.{code}')  # 深圳
    
    secids_str = ','.join(secids)
    
    url = 'http://push2.eastmoney.com/api/qt/ulist.np/get'
    
    params = {
        'fltt': '2',
        'invt': '2',
        'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152,f45,f46,f47,f48,f19,f117,f135,f114,f130,f131,f133',
        'secids': secids_str,
        '_': str(int(time.time() * 1000))
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'http://quote.eastmoney.com/',
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=15)
        
        if response.status_code == 200:
            data = json.loads(response.text)
            
            if 'data' in data and 'diff' in data['data']:
                results = []
                
                for item in data['data']['diff']:
                    try:
                        result = {
                            'code': item.get('f12', ''),           # 股票代码
                            'name': item.get('f14', ''),           # 股票名称
                            'price': item.get('f2', 0),            # 最新价
                            'change_pct': item.get('f3', 0),       # 涨跌幅
                            'pb_ratio': item.get('f23', None),     # 市净率
                            'pe_ttm': item.get('f115', None),      # TTM市盈率
                            'pe_static': item.get('f114', None),   # 静态市盈率
                            'pe_dynamic': item.get('f9', None),    # 动态市盈率
                            'ps_ttm': item.get('f130', None),      # 市销率TTM
                            'pcf_ttm': item.get('f131', None),     # 市现率TTM
                            'dividend_yield': item.get('f133', None), # 股息率
                            'update_time': datetime.now().strftime('%H:%M:%S')
                        }
                        results.append(result)
                    except Exception as e:
                        print(f"处理股票数据时出错: {e}")
                        continue
                
                return results
        
        return []
        
    except Exception as e:
        print(f"批量获取股票数据失败: {e}")
        return []

def get_stock_pe_pb_from_individual_api(stock_code):
    """
    使用个股详情API获取市净率和TTM市盈率
    """
    
    # 根据股票代码确定市场
    if stock_code.startswith('6'):
        secid = f'1.{stock_code}'
    else:
        secid = f'0.{stock_code}'
    
    url = 'http://push2.eastmoney.com/api/qt/stock/get'
    
    params = {
        'fltt': '2',
        'invt': '2',
        'fields': 'f57,f58,f43,f169,f170,f23,f115,f114,f9,f130,f131,f133,f112,f113',
        'secid': secid,
        '_': str(int(time.time() * 1000))
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'http://quote.eastmoney.com/',
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = json.loads(response.text)['data']
            
            if data:
                return {
                    'code': data.get('f57', ''),
                    'name': data.get('f58', ''),
                    'price': data.get('f43', 0),
                    'change': data.get('f169', 0),
                    'change_pct': data.get('f170', 0),
                    'pb_ratio': data.get('f23', None),
                    'pe_ttm': data.get('f115', None),
                    'pe_static': data.get('f114', None),
                    'pe_dynamic': data.get('f9', None),
                    'ps_ttm': data.get('f130', None),
                    'pcf_ttm': data.get('f131', None),
                    'dividend_yield': data.get('f133', None),
                    'eps': data.get('f112', None),  # 每股收益
                    'bps': data.get('f113', None),  # 每股净资产
                    'update_time': datetime.now().strftime('%H:%M:%S')
                }
        
        return None
        
    except Exception as e:
        print(f"获取股票 {stock_code} 数据失败: {e}")
        return None

def load_stock_codes_from_csv():
    """从CSV文件加载股票代码"""
    try:
        df = pd.read_csv('stocks_list.csv', encoding='utf-8-sig')
        stock_codes = df['代码'].astype(str).str.zfill(6).tolist()
        print(f"从CSV文件加载了 {len(stock_codes)} 只股票代码")
        return stock_codes
    except Exception as e:
        print(f"加载股票代码失败: {e}")
        return []

def save_to_csv(data_list, filename=None):
    """保存数据到CSV文件"""
    if not data_list:
        print("没有数据可保存")
        return False
    
    if filename is None:
        filename = f"pe_pb_ratio_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    df = pd.DataFrame(data_list)
    
    # 重新排列列的顺序
    columns_order = ['code', 'name', 'price', 'change_pct', 'pb_ratio', 'pe_ttm', 'pe_static', 'pe_dynamic', 'ps_ttm', 'pcf_ttm', 'dividend_yield', 'eps', 'bps', 'update_time']
    
    # 只保留存在的列
    existing_columns = [col for col in columns_order if col in df.columns]
    df = df[existing_columns]
    
    # 添加中文列名
    column_names = {
        'code': '股票代码',
        'name': '股票名称', 
        'price': '最新价',
        'change_pct': '涨跌幅(%)',
        'pb_ratio': '市净率(PB)',
        'pe_ttm': 'TTM市盈率',
        'pe_static': '静态市盈率',
        'pe_dynamic': '动态市盈率',
        'ps_ttm': '市销率TTM',
        'pcf_ttm': '市现率TTM',
        'dividend_yield': '股息率(%)',
        'eps': '每股收益',
        'bps': '每股净资产',
        'update_time': '更新时间'
    }
    
    df.columns = [column_names.get(col, col) for col in df.columns]
    
    df.to_csv(filename, index=False, encoding='utf-8-sig')
    print(f"✅ 数据已保存到 {filename}")
    return True

def test_apis():
    """测试不同的API"""
    print("=== 测试不同的API接口 ===")
    
    test_codes = ['000001', '600000', '600036', '000002', '600519']
    
    print("\n1. 测试批量API:")
    batch_results = get_stock_pe_pb_from_list_api(test_codes)
    
    if batch_results:
        print(f"✅ 批量API成功获取 {len(batch_results)} 只股票数据")
        for result in batch_results:
            print(f"   {result['code']} {result['name']} PB:{result['pb_ratio']} PE(TTM):{result['pe_ttm']}")
    else:
        print("❌ 批量API获取失败")
    
    print("\n2. 测试单个API:")
    for code in test_codes[:3]:
        result = get_stock_pe_pb_from_individual_api(code)
        if result:
            print(f"✅ {result['code']} {result['name']} PB:{result['pb_ratio']} PE(TTM):{result['pe_ttm']}")
        else:
            print(f"❌ {code} 获取失败")
        time.sleep(0.5)
    
    return batch_results

def main():
    """主函数"""
    print("=== 东方财富股票市盈率市净率获取工具 V2 ===")
    
    # 选择运行模式
    print("\n请选择运行模式:")
    print("1. 测试模式（测试不同API接口）")
    print("2. 批量模式（获取所有持仓股票的估值数据）")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == '1':
        test_apis()
    
    elif choice == '2':
        # 批量模式
        stock_codes = load_stock_codes_from_csv()
        
        if not stock_codes:
            print("未找到股票代码，退出程序")
            return
        
        print(f"\n开始获取 {len(stock_codes)} 只股票的估值数据...")
        
        # 使用批量API
        data_list = get_stock_pe_pb_from_list_api(stock_codes)
        
        if data_list:
            print(f"✅ 成功获取 {len(data_list)} 只股票的估值数据")
            
            # 保存数据
            save_to_csv(data_list)
            
            # 显示统计
            valid_pb = [d for d in data_list if d['pb_ratio'] is not None and d['pb_ratio'] != '-' and d['pb_ratio'] > 0]
            valid_pe = [d for d in data_list if d['pe_ttm'] is not None and d['pe_ttm'] != '-' and d['pe_ttm'] > 0]
            
            print(f"\n📊 数据统计:")
            print(f"   有效市净率数据: {len(valid_pb)} 只")
            print(f"   有效TTM市盈率数据: {len(valid_pe)} 只")
            
            if valid_pb:
                pb_values = [d['pb_ratio'] for d in valid_pb]
                print(f"   市净率范围: {min(pb_values):.2f} - {max(pb_values):.2f}")
            
            if valid_pe:
                pe_values = [d['pe_ttm'] for d in valid_pe]
                print(f"   TTM市盈率范围: {min(pe_values):.2f} - {max(pe_values):.2f}")
        else:
            print("❌ 未获取到任何数据")
    
    else:
        print("无效选择，退出程序")

if __name__ == "__main__":
    main()
