<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>持仓股票综合数据实时监控</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
        }
        
        .header .subtitle {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .update-info {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 5px;
            margin-top: 15px;
        }
        
        .controls {
            background: white;
            padding: 20px;
            margin: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .search-box {
            flex: 1;
            max-width: 300px;
            margin-right: 20px;
        }
        
        .search-box input {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .stats {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 0.9em;
            color: #666;
        }
        
        .table-container {
            background: white;
            margin: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th {
            background: #f8f9fa;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            border-bottom: 2px solid #dee2e6;
            position: sticky;
            top: 0;
            z-index: 10;
            font-size: 0.9em;
        }
        
        td {
            padding: 10px 8px;
            text-align: center;
            border-bottom: 1px solid #eee;
            font-size: 0.9em;
        }
        
        tr:hover {
            background-color: #f8f9fa;
        }
        
        .positive {
            color: #dc3545;
            font-weight: bold;
        }
        
        .negative {
            color: #28a745;
            font-weight: bold;
        }
        
        .neutral {
            color: #6c757d;
        }
        
        .highlight-low {
            background-color: #fff3cd;
            color: #856404;
            font-weight: bold;
        }
        
        .highlight-high {
            background-color: #f8d7da;
            color: #721c24;
            font-weight: bold;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            font-size: 1.2em;
            color: #666;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            margin: 20px;
            border-radius: 5px;
            border: 1px solid #f5c6cb;
        }
        
        .refresh-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }
        
        .refresh-btn:hover {
            background: #5a6fd8;
        }

        .filter-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 0 2px;
        }

        .filter-btn:hover {
            background: #5a6268;
            transform: translateY(-1px);
        }

        .filter-btn.active {
            background: #28a745;
            box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
        }

        .filter-btn.active:hover {
            background: #218838;
        }

        /* 统计面板样式 */
        .stats-container {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin: 20px;
            margin-top: 30px;
        }

        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #007bff;
        }

        .stats-card h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 18px;
            font-weight: 600;
        }

        .total-assets {
            border-left-color: #28a745;
        }

        .total-amount {
            font-size: 32px;
            font-weight: bold;
            color: #28a745;
            text-align: center;
            padding: 10px 0;
        }

        .asset-allocation {
            border-left-color: #17a2b8;
        }

        .asset-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .asset-item:last-child {
            border-bottom: none;
        }

        .asset-label {
            font-weight: 500;
            color: #666;
        }

        .asset-value {
            font-weight: 600;
            color: #333;
        }

        .industry-stats {
            border-left-color: #ffc107;
        }

        .industry-breakdown {
            max-height: 200px;
            overflow-y: auto;
        }

        .industry-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
        }

        .industry-item:last-child {
            border-bottom: none;
        }

        .industry-name {
            font-weight: 500;
            color: #666;
            flex: 1;
        }

        .industry-value {
            font-weight: 600;
            color: #333;
            text-align: right;
            margin-left: 10px;
        }

        .no-data {
            text-align: center;
            color: #999;
            font-style: italic;
            padding: 20px 0;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .stats-container {
                grid-template-columns: 1fr 1fr;
            }
        }

        @media (max-width: 768px) {
            .stats-container {
                grid-template-columns: 1fr;
            }
        }
        
        .refresh-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .sort-btn {
            background: none;
            border: none;
            cursor: pointer;
            color: #667eea;
            font-weight: bold;
        }
        
        .sort-btn:hover {
            color: #5a6fd8;
        }
        
        @media (max-width: 1200px) {
            .controls {
                flex-direction: column;
                gap: 15px;
            }
            
            .search-box {
                max-width: 100%;
                margin-right: 0;
            }
            
            .stats {
                justify-content: center;
            }
            
            table {
                font-size: 12px;
            }
            
            th, td {
                padding: 6px 4px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📈 持仓股票综合数据监控</h1>
        <div class="subtitle">最新价 | 一年内最低价 | 距最低点涨幅 | 市净率 | TTM市盈率</div>
        <div class="update-info">
            <div>最后更新时间: <span id="lastUpdate">加载中...</span></div>
            <div>数据来源: 东方财富网</div>
        </div>
    </div>
    
    <div class="controls">
        <div class="search-box">
            <input type="text" id="searchInput" placeholder="搜索股票代码或名称...">
        </div>
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number" id="totalCount">-</div>
                <div class="stat-label">总股票数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="nearLowCount">-</div>
                <div class="stat-label">接近最低价</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="lowPbCount">-</div>
                <div class="stat-label">低市净率</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="lowPeCount">-</div>
                <div class="stat-label">低市盈率</div>
            </div>
        </div>
        <!-- 分类筛选按钮区域 -->
        <div style="display: flex; gap: 10px; margin-bottom: 15px; flex-wrap: wrap;">
            <div style="display: flex; gap: 10px; align-items: center; padding: 10px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #28a745;">
                <span style="font-weight: bold; color: #28a745;">🏷️ 分类筛选:</span>
                <button class="filter-btn active" onclick="filterByType('股票')" id="filter-股票">股票</button>
                <button class="filter-btn" onclick="filterByType('可转债')" id="filter-可转债">可转债</button>
                <button class="filter-btn" onclick="filterByType('ETF/基金')" id="filter-ETF/基金">ETF/基金</button>
                <button class="filter-btn" onclick="filterByType('全部')" id="filter-全部">全部</button>
            </div>
        </div>

        <!-- 数据管理按钮区域 -->
        <div style="display: flex; gap: 10px; margin-bottom: 15px; flex-wrap: wrap;">
            <div style="display: flex; gap: 10px; align-items: center; padding: 10px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff;">
                <span style="font-weight: bold; color: #007bff;">📁 数据管理:</span>
                <button class="refresh-btn" onclick="clearData()" style="background: #dc3545;">清空数据</button>
                <input type="file" id="fileInput" accept=".xlsx,.xls" style="display: none;" onchange="uploadFile()">
                <button class="refresh-btn" onclick="document.getElementById('fileInput').click()" style="background: #28a745;">导入持仓表格并获取数据</button>
                <button class="refresh-btn" onclick="refreshAllData()" style="background: #17a2b8;">重新获取数据</button>
            </div>
        </div>

        <!-- 原有功能按钮 -->
        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
            <button class="refresh-btn" id="refreshBtn" onclick="refreshData()">刷新数据</button>
            <button class="refresh-btn" onclick="testWechat()">测试微信</button>
            <button class="refresh-btn" onclick="sendSummary()">发送摘要</button>
            <button class="refresh-btn" onclick="checkHighGain()">检查高涨幅</button>
        </div>
    </div>
    
    <div class="table-container">
        <div id="loading" class="loading">正在加载综合股票数据...</div>
        <div id="error" class="error" style="display: none;"></div>
        <table id="stockTable" style="display: none;">
            <thead>
                <tr>
                    <th>股票代码</th>
                    <th>股票名称</th>
                    <th>最新价</th>
                    <th>涨跌额</th>
                    <th>涨跌幅</th>
                    <th>
                        <button class="sort-btn" onclick="sortTable('yearly_low')">一年内最低价 ↕</button>
                    </th>
                    <th>最低价日期</th>
                    <th>
                        <button class="sort-btn" onclick="sortTable('distance_from_low_pct')">距最低点涨幅 ↕</button>
                    </th>
                    <th>
                        <button class="sort-btn" onclick="sortTable('pb_ratio')">市净率(PB) ↕</button>
                    </th>
                    <th>
                        <button class="sort-btn" onclick="sortTable('pe_ttm')">TTM市盈率 ↕</button>
                    </th>
                    <th>
                        <button class="sort-btn" onclick="sortTable('dividend_yield')">股息率 ↕</button>
                    </th>
                    <th>
                        <button class="sort-btn" onclick="sortTable('industry')">行业 ↕</button>
                    </th>
                    <th>
                        <button class="sort-btn" onclick="sortTable('holdings')">持仓数量 ↕</button>
                    </th>
                    <th>
                        <button class="sort-btn" onclick="sortTable('market_value')">市值 ↕</button>
                    </th>
                    <th>更新时间</th>
                </tr>
            </thead>
            <tbody id="stockTableBody">
            </tbody>
        </table>
    </div>

    <!-- 统计分析面板 -->
    <div class="stats-container">
        <!-- 总资产统计 -->
        <div class="stats-card total-assets">
            <h3>💰 总资产</h3>
            <div class="total-amount" id="totalAssets">¥0.00</div>
        </div>

        <!-- 资产类别统计 -->
        <div class="stats-card asset-allocation">
            <h3>📊 资产配置</h3>
            <div class="asset-breakdown" id="assetBreakdown">
                <div class="asset-item">
                    <span class="asset-label">股票:</span>
                    <span class="asset-value" id="stockValue">¥0.00 (0.0%)</span>
                </div>
                <div class="asset-item">
                    <span class="asset-label">可转债:</span>
                    <span class="asset-value" id="bondValue">¥0.00 (0.0%)</span>
                </div>
                <div class="asset-item">
                    <span class="asset-label">ETF/基金:</span>
                    <span class="asset-value" id="etfValue">¥0.00 (0.0%)</span>
                </div>
            </div>
        </div>

        <!-- 行业市值统计 -->
        <div class="stats-card industry-stats">
            <h3>🏭 行业分布</h3>
            <div class="industry-breakdown" id="industryBreakdown">
                <div class="no-data">暂无数据</div>
            </div>
        </div>
    </div>

    <script>
        let stocksData = [];
        let filteredData = [];
        let currentFilter = '股票';  // 默认显示股票
        let sortColumn = null;
        let sortDirection = 'asc';
        
        // 格式化数字
        function formatNumber(num, decimals = 2) {
            if (num === 0 || num === null || num === undefined) return '-';
            return parseFloat(num).toFixed(decimals);
        }

        // 格式化金额（带千分位分隔符）
        function formatCurrency(amount, decimals = 2) {
            if (amount === 0 || amount === null || amount === undefined) return '¥0.00';
            return '¥' + parseFloat(amount).toLocaleString('zh-CN', {
                minimumFractionDigits: decimals,
                maximumFractionDigits: decimals
            });
        }

        // 格式化百分比
        function formatPercentage(value, decimals = 1) {
            if (value === 0 || value === null || value === undefined) return '0.0%';
            return parseFloat(value).toFixed(decimals) + '%';
        }
        
        // 获取涨跌颜色类
        function getChangeClass(change) {
            if (change > 0) return 'positive';
            if (change < 0) return 'negative';
            return 'neutral';
        }
        
        // 获取距离最低价的颜色类
        function getDistanceClass(distance) {
            if (distance === null || distance === undefined) return '';
            if (distance < 10) return 'highlight-low';  // 接近最低价
            if (distance > 100) return 'highlight-high'; // 远离最低价
            return '';
        }
        
        // 渲染股票表格
        function renderTable(data) {
            const tbody = document.getElementById('stockTableBody');
            tbody.innerHTML = '';
            
            data.forEach(stock => {
                const row = document.createElement('tr');
                const changeClass = getChangeClass(stock.change);
                const distanceClass = getDistanceClass(stock.distance_from_low_pct);
                
                row.innerHTML = `
                    <td>${stock.code}</td>
                    <td>${stock.name}</td>
                    <td class="${changeClass}">${formatNumber(stock.price)}</td>
                    <td class="${changeClass}">${stock.change > 0 ? '+' : ''}${formatNumber(stock.change)}</td>
                    <td class="${changeClass}">${stock.change_pct > 0 ? '+' : ''}${formatNumber(stock.change_pct)}%</td>
                    <td>${formatNumber(stock.yearly_low)}</td>
                    <td>${stock.low_date || '-'}</td>
                    <td class="${distanceClass}">${stock.distance_from_low_pct !== null ? formatNumber(stock.distance_from_low_pct) + '%' : '-'}</td>
                    <td>${formatNumber(stock.pb_ratio)}</td>
                    <td>${formatNumber(stock.pe_ttm)}</td>
                    <td>${stock.dividend_yield > 0 ? formatNumber(stock.dividend_yield) + '%' : '-'}</td>
                    <td>${stock.industry || '-'}</td>
                    <td>${formatNumber(stock.holdings) || '-'}</td>
                    <td>${formatNumber(stock.market_value) || '-'}</td>
                    <td>${stock.update_time || '-'}</td>
                `;
                tbody.appendChild(row);
            });
        }
        
        // 更新统计信息
        function updateStats(data) {
            const totalCount = data.length;
            
            // 接近最低价的股票（距离最低价 < 20%）
            const nearLowCount = data.filter(stock => 
                stock.distance_from_low_pct !== null && stock.distance_from_low_pct < 20
            ).length;
            
            // 低市净率股票（PB < 1.5）
            const lowPbCount = data.filter(stock => 
                stock.pb_ratio !== null && stock.pb_ratio < 1.5
            ).length;
            
            // 低市盈率股票（PE < 20）
            const lowPeCount = data.filter(stock => 
                stock.pe_ttm !== null && stock.pe_ttm < 20
            ).length;
            
            document.getElementById('totalCount').textContent = totalCount;
            document.getElementById('nearLowCount').textContent = nearLowCount;
            document.getElementById('lowPbCount').textContent = lowPbCount;
            document.getElementById('lowPeCount').textContent = lowPeCount;
        }
        
        // 排序功能
        function sortTable(column) {
            if (sortColumn === column) {
                sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                sortColumn = column;
                sortDirection = 'asc';
            }
            
            filteredData.sort((a, b) => {
                let aVal = a[column];
                let bVal = b[column];

                // 处理null值
                if (aVal === null || aVal === undefined) aVal = '';
                if (bVal === null || bVal === undefined) bVal = '';

                // 判断是数值还是字符串
                if (column === 'industry' || column === 'name' || column === 'code') {
                    // 字符串排序
                    aVal = String(aVal).toLowerCase();
                    bVal = String(bVal).toLowerCase();

                    if (sortDirection === 'asc') {
                        return aVal.localeCompare(bVal);
                    } else {
                        return bVal.localeCompare(aVal);
                    }
                } else {
                    // 数值排序
                    if (aVal === '') aVal = sortDirection === 'asc' ? Infinity : -Infinity;
                    if (bVal === '') bVal = sortDirection === 'asc' ? Infinity : -Infinity;

                    if (sortDirection === 'asc') {
                        return aVal - bVal;
                    } else {
                        return bVal - aVal;
                    }
                }
            });
            
            renderTable(filteredData);
        }
        
        // 搜索功能
        function filterStocks() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();

            // 先获取按类型筛选后的数据
            let typeFilteredData;
            if (currentFilter === '全部') {
                typeFilteredData = [...stocksData];
            } else {
                typeFilteredData = stocksData.filter(stock => stock.type === currentFilter);
            }

            // 再应用搜索筛选
            if (searchTerm) {
                filteredData = typeFilteredData.filter(stock =>
                    stock.code.toLowerCase().includes(searchTerm) ||
                    stock.name.toLowerCase().includes(searchTerm)
                );
            } else {
                filteredData = typeFilteredData;
            }

            renderTable(filteredData);
            updateStats(filteredData);
        }
        
        // 获取股票数据
        async function fetchStocks() {
            try {
                const response = await fetch('/api/stocks');
                const data = await response.json();
                
                stocksData = data.stocks || [];

                document.getElementById('lastUpdate').textContent = data.last_update || '未知';

                // 应用默认筛选（股票）
                applyFilter();

                // 更新统计数据
                updateStatistics();

                document.getElementById('loading').style.display = 'none';
                document.getElementById('error').style.display = 'none';
                document.getElementById('stockTable').style.display = 'table';
                
            } catch (error) {
                console.error('获取股票数据失败:', error);
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error').style.display = 'block';
                document.getElementById('error').textContent = '获取股票数据失败: ' + error.message;
            }
        }
        
        // 刷新数据
        function refreshData() {
            const btn = document.getElementById('refreshBtn');
            btn.disabled = true;
            btn.textContent = '刷新中...';

            fetch('/api/refresh')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        setTimeout(fetchStocks, 2000); // 2秒后获取新数据
                    } else {
                        alert('刷新失败: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('刷新失败: ' + error.message);
                })
                .finally(() => {
                    btn.disabled = false;
                    btn.textContent = '刷新数据';
                });
        }

        // 测试企业微信连接
        function testWechat() {
            fetch('/api/wechat/test')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('✅ 企业微信连接测试成功！');
                    } else {
                        alert('❌ 企业微信连接测试失败: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('❌ 测试失败: ' + error.message);
                });
        }

        // 发送每日摘要
        function sendSummary() {
            fetch('/api/wechat/summary')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('✅ 每日摘要已发送到企业微信！');
                    } else {
                        alert('❌ 发送失败: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('❌ 发送失败: ' + error.message);
                });
        }

        // 检查高涨幅股票
        function checkHighGain() {
            const threshold = prompt('请输入涨幅阈值（默认70%）:', '70');
            if (threshold === null) return;

            const thresholdValue = parseFloat(threshold) || 70;

            fetch(`/api/wechat/alert/${thresholdValue}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('✅ ' + data.message);
                    } else {
                        alert('❌ ' + data.message);
                    }
                })
                .catch(error => {
                    alert('❌ 检查失败: ' + error.message);
                });
        }

        // 统计分析功能
        function updateStatistics() {
            // 计算总资产统计
            updateTotalAssets();

            // 计算资产类别统计
            updateAssetAllocation();

            // 计算行业统计（仅对当前筛选的数据）
            updateIndustryStats();
        }

        function updateTotalAssets() {
            const totalValue = stocksData.reduce((sum, stock) => sum + (stock.market_value || 0), 0);
            document.getElementById('totalAssets').textContent = formatCurrency(totalValue);
        }

        function updateAssetAllocation() {
            // 按类型分组计算市值
            const assetStats = {
                '股票': 0,
                '可转债': 0,
                'ETF/基金': 0
            };

            stocksData.forEach(stock => {
                const value = stock.market_value || 0;
                if (assetStats.hasOwnProperty(stock.type)) {
                    assetStats[stock.type] += value;
                }
            });

            const totalValue = Object.values(assetStats).reduce((sum, value) => sum + value, 0);

            // 更新显示
            document.getElementById('stockValue').textContent =
                `${formatCurrency(assetStats['股票'])} (${formatPercentage(totalValue > 0 ? assetStats['股票'] / totalValue * 100 : 0)})`;

            document.getElementById('bondValue').textContent =
                `${formatCurrency(assetStats['可转债'])} (${formatPercentage(totalValue > 0 ? assetStats['可转债'] / totalValue * 100 : 0)})`;

            document.getElementById('etfValue').textContent =
                `${formatCurrency(assetStats['ETF/基金'])} (${formatPercentage(totalValue > 0 ? assetStats['ETF/基金'] / totalValue * 100 : 0)})`;
        }

        function updateIndustryStats() {
            // 根据当前筛选状态决定统计范围
            let dataToAnalyze;
            if (currentFilter === '股票') {
                dataToAnalyze = stocksData.filter(stock => stock.type === '股票');
            } else if (currentFilter === '全部') {
                dataToAnalyze = stocksData.filter(stock => stock.type === '股票');
            } else {
                // 对于可转债和ETF，不显示行业统计
                document.getElementById('industryBreakdown').innerHTML = '<div class="no-data">该类别无行业分类</div>';
                return;
            }

            // 按行业分组统计
            const industryStats = {};
            dataToAnalyze.forEach(stock => {
                const industry = stock.industry || '未知';
                const value = stock.market_value || 0;

                if (!industryStats[industry]) {
                    industryStats[industry] = 0;
                }
                industryStats[industry] += value;
            });

            // 计算总市值（仅股票）
            const totalStockValue = Object.values(industryStats).reduce((sum, value) => sum + value, 0);

            // 转换为数组并排序
            const sortedIndustries = Object.entries(industryStats)
                .sort((a, b) => b[1] - a[1])
                .filter(([industry, value]) => value > 0);

            // 更新显示
            const container = document.getElementById('industryBreakdown');
            if (sortedIndustries.length === 0) {
                container.innerHTML = '<div class="no-data">暂无数据</div>';
            } else {
                container.innerHTML = sortedIndustries.map(([industry, value]) => `
                    <div class="industry-item">
                        <span class="industry-name">${industry}</span>
                        <span class="industry-value">
                            ${formatCurrency(value)} (${formatPercentage(totalStockValue > 0 ? value / totalStockValue * 100 : 0)})
                        </span>
                    </div>
                `).join('');
            }
        }

        // 分类筛选功能
        function filterByType(type) {
            currentFilter = type;

            // 更新按钮状态
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.getElementById('filter-' + type).classList.add('active');

            // 应用筛选
            applyFilter();
        }

        function applyFilter() {
            if (currentFilter === '全部') {
                filteredData = [...stocksData];
            } else {
                filteredData = stocksData.filter(stock => stock.type === currentFilter);
            }

            // 重新应用搜索筛选
            filterStocks();

            // 更新统计数据
            updateStatistics();
        }

        // 数据管理功能
        function clearData() {
            if (!confirm('⚠️ 确定要清空所有股票数据吗？此操作不可撤销！')) {
                return;
            }

            fetch('/api/clear-data', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('✅ ' + data.message);
                    fetchStocks(); // 刷新页面显示
                } else {
                    alert('❌ 清空失败: ' + data.error);
                }
            })
            .catch(error => {
                alert('❌ 清空失败: ' + error.message);
            });
        }

        function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];

            if (!file) {
                return;
            }

            if (!file.name.match(/\.(xlsx|xls)$/i)) {
                alert('❌ 请选择Excel文件（.xlsx或.xls格式）');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            // 显示上传进度
            const originalText = '导入持仓表格并获取数据';
            const buttons = document.querySelectorAll('button');
            let uploadBtn = null;
            buttons.forEach(btn => {
                if (btn.textContent === originalText) {
                    uploadBtn = btn;
                    btn.disabled = true;
                    btn.textContent = '导入并获取数据中...';
                }
            });

            fetch('/api/upload-holdings', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('✅ ' + data.message);
                    fileInput.value = ''; // 清空文件选择
                    fetchStocks(); // 自动刷新页面显示数据
                } else {
                    alert('❌ 导入失败: ' + data.error);
                }
            })
            .catch(error => {
                alert('❌ 导入失败: ' + error.message);
            })
            .finally(() => {
                if (uploadBtn) {
                    uploadBtn.disabled = false;
                    uploadBtn.textContent = originalText;
                }
            });
        }

        function refreshAllData() {
            if (!confirm('🔄 确定要重新获取所有股票数据吗？这可能需要几分钟时间。')) {
                return;
            }

            const originalText = '重新获取数据';
            const buttons = document.querySelectorAll('button');
            let refreshBtn = null;
            buttons.forEach(btn => {
                if (btn.textContent === originalText) {
                    refreshBtn = btn;
                    btn.disabled = true;
                    btn.textContent = '获取中...';
                }
            });

            fetch('/api/refresh-data', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('✅ ' + data.message);
                    fetchStocks(); // 刷新页面显示
                } else {
                    alert('❌ 获取失败: ' + data.error);
                }
            })
            .catch(error => {
                alert('❌ 获取失败: ' + error.message);
            })
            .finally(() => {
                if (refreshBtn) {
                    refreshBtn.disabled = false;
                    refreshBtn.textContent = originalText;
                }
            });
        }

        // 搜索事件监听
        document.getElementById('searchInput').addEventListener('input', filterStocks);
        
        // 页面加载完成后获取数据
        document.addEventListener('DOMContentLoaded', function() {
            fetchStocks();
            
            // 每5分钟自动刷新一次
            setInterval(fetchStocks, 300000);
        });
    </script>
</body>
</html>
