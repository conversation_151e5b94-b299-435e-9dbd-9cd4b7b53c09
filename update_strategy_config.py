#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新策略配置文件，添加missing的warning_offset参数
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入策略管理器
from 持仓系统_v14 import SellStrategyManager

def update_strategy_config():
    """更新策略配置文件"""
    print("🔧 更新策略配置文件...")
    
    # 创建策略管理器实例
    strategy_manager = SellStrategyManager()
    
    print(f"📋 当前策略数量: {len(strategy_manager.strategies)}")
    
    # 检查负TTM回本清仓策略的参数
    ttm_profit_strategy = strategy_manager.strategies.get('negative_ttm_profit_clearance')
    if ttm_profit_strategy:
        print(f"\n📊 负TTM回本清仓策略当前参数:")
        for key, value in ttm_profit_strategy['params'].items():
            print(f"   {key}: {value}")
        
        # 确保有warning_offset参数
        if 'warning_offset' not in ttm_profit_strategy['params']:
            print(f"⚠️ 缺少warning_offset参数，正在添加...")
            ttm_profit_strategy['params']['warning_offset'] = 10.0
        else:
            print(f"✅ warning_offset参数已存在: {ttm_profit_strategy['params']['warning_offset']}")
    
    # 检查其他策略的warning_offset参数
    strategies_with_warning = [
        'negative_ttm_low_dividend_low_scan_reduce',
        'negative_ttm_low_dividend_low_scan_clearance',
        'basic_gain_reduce',
        'basic_gain_clearance'
    ]
    
    for strategy_id in strategies_with_warning:
        strategy = strategy_manager.strategies.get(strategy_id)
        if strategy and 'warning_offset' not in strategy['params']:
            print(f"⚠️ {strategy['name']} 缺少warning_offset参数，正在添加...")
            if strategy_id == 'negative_ttm_profit_clearance':
                strategy['params']['warning_offset'] = 10.0
            else:
                strategy['params']['warning_offset'] = 5.0
    
    # 保存配置到文件
    print(f"\n💾 保存策略配置到文件...")
    strategy_manager.save_strategy_config()
    
    print(f"✅ 策略配置更新完成!")
    
    # 验证配置文件
    print(f"\n🔍 验证配置文件...")
    try:
        import json
        with open('strategy_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        ttm_profit_config = config.get('negative_ttm_profit_clearance', {})
        if 'params' in ttm_profit_config and 'warning_offset' in ttm_profit_config['params']:
            print(f"✅ 配置文件验证成功: warning_offset = {ttm_profit_config['params']['warning_offset']}")
        else:
            print(f"❌ 配置文件验证失败: 未找到warning_offset参数")
            
    except Exception as e:
        print(f"❌ 验证配置文件失败: {e}")

def main():
    """主函数"""
    try:
        update_strategy_config()
        print("\n🎉 策略配置更新完成!")
        print("💡 现在可以刷新网页查看新的预警偏移量参数")
    except Exception as e:
        print(f"\n❌ 更新失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
