#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通达信扫雷模块集成示例
========================

展示如何将 tdx_bxb_module 集成到持仓系统中

这个文件展示了：
1. 如何在Flask应用中集成扫雷功能
2. 如何为股票数据添加风险评估
3. 如何在前端显示风险信息
4. 如何处理批量数据更新

使用方法：
1. 将 tdx_bxb_module.py 放在项目根目录
2. 在主系统文件中导入并使用
3. 参考本文件的集成方式
"""

from flask import Flask, jsonify, request
from tdx_bxb_module import TdxBxbClient, get_stock_bxb_score
import threading
import time

# 全局扫雷客户端实例
bxb_client = TdxBxbClient(cache_duration=1800)  # 缓存30分钟

def add_bxb_routes(app: Flask):
    """
    为Flask应用添加扫雷相关路由

    在您的持仓系统主文件中调用此函数来添加API路由
    """

    @app.route('/api/bxb_score/<stock_code>')
    def get_bxb_score_api(stock_code):
        """获取单只股票的扫雷分数API"""
        try:
            result = bxb_client.get_stock_score(stock_code)
            if result:
                return jsonify(result)
            else:
                return jsonify({'error': f'无法获取 {stock_code} 的扫雷数据'}), 404
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/bxb_batch', methods=['POST'])
    def get_bxb_batch_api():
        """批量获取扫雷分数API"""
        try:
            data = request.get_json()
            stock_codes = data.get('codes', [])
            
            if not stock_codes:
                return jsonify({'error': '股票代码列表不能为空'}), 400
            
            results = bxb_client.get_batch_scores(stock_codes)
            return jsonify(results)
            
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/bxb_cache_info')
    def get_bxb_cache_info():
        """获取缓存信息API"""
        return jsonify(bxb_client.get_cache_info())


def enhance_stock_data_with_bxb(stock_data_dict: dict) -> dict:
    """
    为股票数据字典添加扫雷信息
    
    Args:
        stock_data_dict: 原始股票数据字典
        
    Returns:
        增强后的股票数据字典
        
    使用示例:
        # 在您的股票数据更新函数中
        for code, data in stock_data.items():
            stock_data[code] = enhance_stock_data_with_bxb(data)
    """
    stock_code = stock_data_dict.get('code', '')
    
    if stock_code:
        bxb_result = bxb_client.get_stock_score(stock_code)
        if bxb_result:
            stock_data_dict.update({
                'bxb_score': bxb_result['score'],
                'bxb_risk_level': bxb_result['risk_level'],
                'bxb_risk_color': bxb_result['risk_color'],
                'bxb_triggered_risks': bxb_result['triggered_risks'],
                'bxb_display_text': bxb_client.format_display_text(bxb_result),
                'bxb_update_time': bxb_result['update_time']
            })
        else:
            # 添加默认值
            stock_data_dict.update({
                'bxb_score': 0,
                'bxb_risk_level': '数据异常',
                'bxb_risk_color': '#cccccc',
                'bxb_triggered_risks': 0,
                'bxb_display_text': '数据获取失败',
                'bxb_update_time': ''
            })
    
    return stock_data_dict


def batch_update_bxb_scores(stock_codes: list, callback=None):
    """
    批量更新扫雷分数 (异步)
    
    Args:
        stock_codes: 股票代码列表
        callback: 完成后的回调函数
        
    使用示例:
        # 在后台异步更新所有股票的扫雷分数
        def on_bxb_update_complete(results):
            print(f"更新完成，共处理 {len(results)} 只股票")

        batch_update_bxb_scores(['000001', '000002'], on_bxb_update_complete)
    """
    def update_worker():
        try:
            results = bxb_client.get_batch_scores(stock_codes, delay=0.3)
            if callback:
                callback(results)
        except Exception as e:
            print(f"批量更新扫雷分数失败: {e}")
    
    thread = threading.Thread(target=update_worker)
    thread.daemon = True
    thread.start()


def get_risk_summary(stock_data_list: list) -> dict:
    """
    获取投资组合的风险摘要

    Args:
        stock_data_list: 包含扫雷数据的股票列表
        
    Returns:
        风险摘要统计
    """
    if not stock_data_list:
        return {}
    
    # 统计风险等级分布
    risk_distribution = {}
    scores = []
    high_risk_stocks = []
    
    for stock in stock_data_list:
        bxb_score = stock.get('bxb_score', 0)
        risk_level = stock.get('bxb_risk_level', '未知')
        
        if bxb_score > 0:  # 有效分数
            scores.append(bxb_score)
            risk_distribution[risk_level] = risk_distribution.get(risk_level, 0) + 1
            
            # 收集高风险股票
            if bxb_score < 60:
                high_risk_stocks.append({
                    'code': stock.get('code', ''),
                    'name': stock.get('name', ''),
                    'score': bxb_score,
                    'risk_level': risk_level
                })
    
    # 计算统计数据
    avg_score = sum(scores) / len(scores) if scores else 0
    
    return {
        'total_stocks': len(stock_data_list),
        'valid_scores': len(scores),
        'average_score': round(avg_score, 1),
        'max_score': max(scores) if scores else 0,
        'min_score': min(scores) if scores else 0,
        'risk_distribution': risk_distribution,
        'high_risk_stocks': high_risk_stocks,
        'high_risk_count': len(high_risk_stocks)
    }


# 在持仓系统主文件中的集成示例
def integrate_to_main_system():
    """
    集成到主系统的示例代码
    
    将以下代码添加到您的 持仓系统_v8_饼图版.py 中：
    """
    
    # 1. 在文件顶部导入
    # from tdx_bxb_module import TdxBxbClient
    # from bxb_integration_example import add_bxb_routes, enhance_stock_data_with_bxb
    
    # 2. 在全局变量区域添加
    # bxb_client = TdxBxbClient(cache_duration=1800)
    
    # 3. 在Flask应用初始化后添加路由
    # add_bxb_routes(app)
    
    # 4. 在股票数据更新函数中添加爆雷宝数据
    # def update_stock_data():
    #     # ... 原有的股票数据获取逻辑 ...
    #     
    #     # 为每只股票添加爆雷宝数据
    #     for code in stock_data:
    #         stock_data[code] = enhance_stock_data_with_bxb(stock_data[code])
    
    # 5. 在前端模板中显示爆雷宝信息
    # 在HTML模板中添加：
    # <div class="bxb-info">
    #     <span class="bxb-score" style="color: {{stock.bxb_risk_color}}">
    #         {{stock.bxb_display_text}}
    #     </span>
    # </div>
    
    pass


if __name__ == "__main__":
    # 测试集成功能
    print("=== 爆雷宝集成示例测试 ===")
    
    # 测试单只股票数据增强
    sample_stock_data = {
        'code': '000001',
        'name': '平安银行',
        'price': 10.50,
        'change_percent': 2.3
    }
    
    enhanced_data = enhance_stock_data_with_bxb(sample_stock_data)
    print(f"增强后的数据: {enhanced_data}")
    
    # 测试风险摘要
    sample_portfolio = [
        {'code': '000001', 'name': '平安银行', 'bxb_score': 97, 'bxb_risk_level': '极低风险'},
        {'code': '000002', 'name': '万科A', 'bxb_score': 69, 'bxb_risk_level': '中等风险'},
        {'code': '600036', 'name': '招商银行', 'bxb_score': 95, 'bxb_risk_level': '极低风险'}
    ]
    
    risk_summary = get_risk_summary(sample_portfolio)
    print(f"风险摘要: {risk_summary}")
    
    print("测试完成!")
