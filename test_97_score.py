import requests
import re
import json

def get_mine_clearance_score(code: str) -> float:
    """获取股票扫雷宝分数 - 修正版"""
    try:
        # 扫雷宝API接口
        api_url = f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code={code}&color=0"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Referer': 'http://page3.tdx.com.cn:7615/',
        }
        
        print(f"🔍 获取股票 {code} 的扫雷宝分数...")
        response = requests.get(api_url, headers=headers, timeout=15)
        response.encoding = 'utf-8'
        
        if response.status_code == 200:
            content = response.text
            
            # 方法1: 尝试获取JSON数据
            try:
                json_url = f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/json/{code}.json"
                json_response = requests.get(json_url, headers=headers, timeout=10)
                
                if json_response.status_code == 200:
                    json_data = json_response.json()
                    print(f"📊 JSON数据获取成功")
                    
                    # 首先查找直接的分数字段
                    score_fields = ['score', 'safeScore', 'rating', 'value', 'point']
                    for field in score_fields:
                        if field in json_data:
                            score = float(json_data[field])
                            print(f"✅ 找到直接分数字段 {field}: {score}")
                            if 0 <= score <= 100:
                                return score
                    
                    # 如果没有直接分数，使用扫雷宝的特殊算法
                    if 'total' in json_data and 'num' in json_data:
                        total = json_data['total']
                        risk_num = json_data['num']
                        print(f"📈 总检查: {total}, 风险项: {risk_num}")
                        
                        if total > 0:
                            # 扫雷宝的评分算法（根据观察调整）
                            if risk_num == 0:
                                score = 100.0
                            elif risk_num == 1:
                                score = 97.0  # 观察到的实际值
                            elif risk_num == 2:
                                score = 94.0
                            elif risk_num == 3:
                                score = 91.0
                            else:
                                # 其他情况使用近似算法
                                score = max(100 - (risk_num * 3), 0)
                            
                            print(f"✅ 计算扫雷宝分数: {score}")
                            return score
            except Exception as e:
                print(f"❌ JSON处理异常: {e}")
            
            # 方法2: 从JavaScript代码中提取分数
            print("🔍 从JavaScript提取分数...")
            
            # 查找realvalue变量
            realvalue_pattern = r'realvalue\s*=\s*(\d{1,3})'
            realvalue_matches = re.findall(realvalue_pattern, content)
            print(f"realvalue: {realvalue_matches}")
            
            # 查找showvalue变量
            showvalue_pattern = r'showvalue\s*=\s*(\d{1,3})'
            showvalue_matches = re.findall(showvalue_pattern, content)
            print(f"showvalue: {showvalue_matches}")
            
            # 如果找到了合理的分数值
            for matches, name in [(realvalue_matches, 'realvalue'), (showvalue_matches, 'showvalue')]:
                for match in matches:
                    score = float(match)
                    if 50 <= score <= 100:  # 合理的分数范围
                        print(f"✅ 从{name}获取分数: {score}")
                        return score
            
            print("❌ 未找到有效分数")
            return None
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        return None

if __name__ == '__main__':
    # 测试000001
    score = get_mine_clearance_score('000001')
    print(f"\n🎯 最终结果: 股票000001的扫雷宝分数为 {score} 分")
    
    # 测试其他股票
    for code in ['600000', '000002']:
        print(f"\n{'='*50}")
        score = get_mine_clearance_score(code)
        print(f"🎯 股票{code}的扫雷宝分数为 {score} 分")
