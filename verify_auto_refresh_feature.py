#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证自动刷新功能是否正确实现
"""

import json
import os
import re

def check_config_structure():
    """检查配置结构是否正确"""
    print("🔍 检查配置结构...")
    
    # 读取主文件
    main_file = "持仓系统_v13 - 副本 - 副本.py"
    
    if not os.path.exists(main_file):
        print(f"❌ 主文件不存在: {main_file}")
        return False
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查配置项是否添加
    if "'auto_refresh_on_startup': False" in content:
        print("✅ 配置项 auto_refresh_on_startup 已添加")
    else:
        print("❌ 配置项 auto_refresh_on_startup 未找到")
        return False
    
    # 检查配置文件路径
    if "CONFIG_FILE = 'app_config.json'" in content:
        print("✅ 配置文件路径已定义")
    else:
        print("❌ 配置文件路径未定义")
        return False
    
    return True

def check_config_functions():
    """检查配置管理函数是否存在"""
    print("\n🔍 检查配置管理函数...")
    
    main_file = "持仓系统_v13 - 副本 - 副本.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    functions_to_check = [
        'def load_app_config():',
        'def save_app_config():',
    ]
    
    for func in functions_to_check:
        if func in content:
            print(f"✅ 函数已实现: {func}")
        else:
            print(f"❌ 函数未找到: {func}")
            return False
    
    return True

def check_api_endpoints():
    """检查API端点是否添加"""
    print("\n🔍 检查API端点...")
    
    main_file = "持仓系统_v13 - 副本 - 副本.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    api_patterns = [
        r"@app\.route\('/api/config', methods=\['GET'\]\)",
        r"@app\.route\('/api/config', methods=\['POST'\]\)",
        r"def get_config\(\):",
        r"def update_config\(\):",
    ]
    
    for pattern in api_patterns:
        if re.search(pattern, content):
            print(f"✅ API端点已实现: {pattern}")
        else:
            print(f"❌ API端点未找到: {pattern}")
            return False
    
    return True

def check_startup_logic():
    """检查启动逻辑是否修改"""
    print("\n🔍 检查启动逻辑...")
    
    main_file = "持仓系统_v13 - 副本 - 副本.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    startup_checks = [
        "load_app_config()",
        "auto_refresh = CONFIG.get('auto_refresh_on_startup', False)",
        "自动刷新开关:",
        "if auto_refresh and imported_stock_list:",
    ]
    
    for check in startup_checks:
        if check in content:
            print(f"✅ 启动逻辑已添加: {check}")
        else:
            print(f"❌ 启动逻辑未找到: {check}")
            return False
    
    return True

def check_ui_elements():
    """检查UI元素是否添加"""
    print("\n🔍 检查UI元素...")
    
    main_file = "持仓系统_v13 - 副本 - 副本.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    ui_elements = [
        "app-settings",
        "autoRefreshCheckbox",
        "toggleAutoRefresh()",
        "启动时自动刷新股票数据",
    ]
    
    for element in ui_elements:
        if element in content:
            print(f"✅ UI元素已添加: {element}")
        else:
            print(f"❌ UI元素未找到: {element}")
            return False
    
    return True

def check_javascript_functions():
    """检查JavaScript函数是否添加"""
    print("\n🔍 检查JavaScript函数...")
    
    main_file = "持仓系统_v13 - 副本 - 副本.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    js_functions = [
        "function showNotification(",
        "async function toggleAutoRefresh()",
        "fetch('/api/config'",
    ]
    
    for func in js_functions:
        if func in content:
            print(f"✅ JavaScript函数已添加: {func}")
        else:
            print(f"❌ JavaScript函数未找到: {func}")
            return False
    
    return True

def create_test_config():
    """创建测试配置文件"""
    print("\n🔧 创建测试配置文件...")
    
    test_config = {
        "stock_interval": 0.2,
        "round_interval": 600,
        "request_timeout": 15,
        "alert_threshold": 70.0,
        "auto_refresh_on_startup": True,
        "save_time": "2025-07-24 12:00:00"
    }
    
    try:
        with open('app_config.json', 'w', encoding='utf-8') as f:
            json.dump(test_config, f, ensure_ascii=False, indent=2)
        print("✅ 测试配置文件已创建: app_config.json")
        return True
    except Exception as e:
        print(f"❌ 创建测试配置文件失败: {e}")
        return False

def verify_config_file():
    """验证配置文件"""
    print("\n🔍 验证配置文件...")
    
    if not os.path.exists('app_config.json'):
        print("❌ 配置文件不存在")
        return False
    
    try:
        with open('app_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        required_keys = ['auto_refresh_on_startup']
        for key in required_keys:
            if key in config:
                print(f"✅ 配置项存在: {key} = {config[key]}")
            else:
                print(f"❌ 配置项缺失: {key}")
                return False
        
        return True
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False

def main():
    """主验证函数"""
    print("🚀 自动刷新功能验证")
    print("=" * 60)
    
    all_checks = [
        ("配置结构", check_config_structure),
        ("配置管理函数", check_config_functions),
        ("API端点", check_api_endpoints),
        ("启动逻辑", check_startup_logic),
        ("UI元素", check_ui_elements),
        ("JavaScript函数", check_javascript_functions),
    ]
    
    passed = 0
    total = len(all_checks)
    
    for name, check_func in all_checks:
        print(f"\n📋 检查 {name}...")
        if check_func():
            passed += 1
            print(f"✅ {name} 检查通过")
        else:
            print(f"❌ {name} 检查失败")
    
    # 创建和验证配置文件
    print(f"\n📋 检查配置文件...")
    if create_test_config() and verify_config_file():
        passed += 1
        total += 1
        print("✅ 配置文件检查通过")
    else:
        total += 1
        print("❌ 配置文件检查失败")
    
    # 总结
    print("\n" + "=" * 60)
    print(f"📊 验证结果: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("🎉 所有功能验证通过！")
        print("\n📋 下一步操作:")
        print("1. 启动持仓系统: python '持仓系统_v13 - 副本 - 副本.py'")
        print("2. 打开网页界面: http://localhost:5000")
        print("3. 测试设置开关: 点击 '⚙️ 策略配置' → 切换自动刷新开关")
        print("4. 验证重启保持: 重启系统后检查设置是否保持")
        return True
    else:
        print("⚠️ 部分功能验证失败，请检查实现")
        return False

if __name__ == "__main__":
    main()
