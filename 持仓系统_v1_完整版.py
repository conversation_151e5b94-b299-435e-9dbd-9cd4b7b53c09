# 持仓系统 V1 完整版 - 包含股息率和行业信息
# 备份时间: 2025-07-21
# 功能: 基础持仓管理 + 股息率 + 行业信息

from flask import Flask, render_template, jsonify
import requests
import time
import json
import pandas as pd
from datetime import datetime, timedelta
import threading
from urllib.parse import urlencode
from wechat_alert import WeChatAlert

app = Flask(__name__)

# 全局变量存储股票数据
stock_data = {}
last_update_time = None

# 配置
CONFIG = {
    'stock_interval': 0.1,      # 每只股票间隔时间（秒）
    'round_interval': 600,      # 每轮更新间隔时间（秒）
    'request_timeout': 15,      # 请求超时时间（秒）
    'alert_threshold': 70.0,    # 企业微信提醒阈值（距离最低点涨幅%）
}

# 企业微信提醒配置
WECHAT_WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7bddd706-ef0f-4d6c-9e1f-3cc8a0a18d9e"

# 初始化企业微信提醒
wechat_alert = WeChatAlert(WECHAT_WEBHOOK_URL)

def gen_secid(rawcode: str) -> str:
    """生成东方财富专用的secid"""
    if rawcode[0] == '6':
        return f'1.{rawcode}'  # 沪市
    return f'0.{rawcode}'      # 深市

def get_yearly_low_price(code: str) -> dict:
    """获取股票一年内的最低价（前复权）"""
    
    # 计算一年前的日期
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365)
    
    start_date_str = start_date.strftime('%Y%m%d')
    end_date_str = end_date.strftime('%Y%m%d')
    
    secid = gen_secid(code)
    
    params = {
        'fields1': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13',
        'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
        'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
        'klt': '101',
        'fqt': '1',
        'secid': secid,
        'beg': start_date_str,
        'end': end_date_str,
        '_': str(int(time.time() * 1000))
    }
    
    url = 'http://push2his.eastmoney.com/api/qt/stock/kline/get'
    
    try:
        response = requests.get(url, params=params, timeout=CONFIG['request_timeout'])
        
        if response.status_code == 200:
            data = json.loads(response.text)
            
            if 'data' in data and data['data'] and 'klines' in data['data']:
                klines = data['data']['klines']
                
                if klines:
                    min_price = float('inf')
                    min_date = None
                    
                    for kline in klines:
                        parts = kline.split(',')
                        date = parts[0]
                        low_price = float(parts[4])  # 最低价
                        
                        if low_price < min_price:
                            min_price = low_price
                            min_date = date
                    
                    return {
                        'yearly_low': min_price if min_price != float('inf') else None,
                        'low_date': min_date,
                        'data_points': len(klines)
                    }
        
        return {'yearly_low': None, 'low_date': None, 'data_points': 0}
        
    except Exception as e:
        print(f"获取股票 {code} 历史数据失败: {e}")
        return {'yearly_low': None, 'low_date': None, 'data_points': 0}

def get_stock_realtime_and_valuation(stock_codes):
    """批量获取股票实时价格和估值数据 - 包含股息率和行业"""
    
    # 构建股票代码列表
    secids = []
    for code in stock_codes:
        if code.startswith('6'):
            secids.append(f'1.{code}')
        else:
            secids.append(f'0.{code}')
    
    secids_str = ','.join(secids)
    
    url = 'http://push2.eastmoney.com/api/qt/ulist.np/get'
    
    params = {
        'fltt': '2',
        'invt': '2',
        'fields': 'f12,f14,f2,f3,f4,f23,f115,f114,f9,f133,f127',  # 添加f133股息率和f127行业
        'secids': secids_str,
        '_': str(int(time.time() * 1000))
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'http://quote.eastmoney.com/',
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=CONFIG['request_timeout'])
        
        if response.status_code == 200:
            data = json.loads(response.text)
            
            if 'data' in data and 'diff' in data['data']:
                results = {}
                
                for item in data['data']['diff']:
                    try:
                        code = item.get('f12', '')
                        
                        # 处理数据
                        pb_ratio = item.get('f23', None)
                        if pb_ratio == '-':
                            pb_ratio = None
                        
                        pe_ttm = item.get('f115', None)
                        if pe_ttm == '-':
                            pe_ttm = None
                        
                        # 处理股息率
                        dividend_yield = item.get('f133', None)
                        if dividend_yield == '-' or dividend_yield == 0:
                            dividend_yield = 0
                        
                        # 处理行业
                        industry = item.get('f127', '')
                        if industry == '-':
                            industry = ''
                        
                        results[code] = {
                            'code': code,
                            'name': item.get('f14', ''),
                            'price': item.get('f2', 0),
                            'change': item.get('f4', 0),
                            'change_pct': item.get('f3', 0),
                            'pb_ratio': pb_ratio,
                            'pe_ttm': pe_ttm,
                            'dividend_yield': dividend_yield,  # 新增股息率
                            'industry': industry,              # 新增行业
                            'update_time': datetime.now().strftime('%H:%M:%S')
                        }
                        
                    except Exception as e:
                        print(f"处理股票数据时出错: {e}")
                        continue
                
                return results
        
        return {}
        
    except Exception as e:
        print(f"批量获取股票数据失败: {e}")
        return {}

def load_stock_list():
    """加载股票列表"""
    try:
        df = pd.read_csv('stocks_list.csv', encoding='utf-8-sig')
        return df.to_dict('records')
    except Exception as e:
        print(f"加载股票列表失败: {e}")
        return []

def update_all_stocks():
    """更新所有股票数据"""
    global stock_data, last_update_time
    
    stock_list = load_stock_list()
    stock_codes = [str(stock['代码']).zfill(6) for stock in stock_list]
    
    print(f"\n🚀 开始更新 {len(stock_codes)} 只股票的综合数据...")
    print(f"⚙️  配置: 股票间隔 {CONFIG['stock_interval']}秒, 轮次间隔 {CONFIG['round_interval']}秒")
    
    # 第一步：批量获取实时价格和估值数据
    print("📊 第一步：获取实时价格和估值数据...")
    realtime_data = get_stock_realtime_and_valuation(stock_codes)
    
    if not realtime_data:
        print("❌ 获取实时数据失败")
        return
    
    print(f"✅ 成功获取 {len(realtime_data)} 只股票的实时数据")
    
    # 第二步：逐个获取历史最低价数据
    print("📈 第二步：获取历史最低价数据...")
    
    for i, code in enumerate(stock_codes):
        if code in realtime_data:
            print(f"📊 获取 {code} {realtime_data[code]['name']} 的历史数据... ({i+1}/{len(stock_codes)})")
            
            # 获取历史最低价
            historical_data = get_yearly_low_price(code)
            
            # 合并数据
            stock_info = realtime_data[code].copy()
            stock_info.update(historical_data)
            
            # 计算距离最低价的涨幅
            if stock_info['yearly_low'] is not None and stock_info['price'] > 0:
                distance_pct = ((stock_info['price'] - stock_info['yearly_low']) / stock_info['yearly_low']) * 100
                stock_info['distance_from_low_pct'] = distance_pct
            else:
                stock_info['distance_from_low_pct'] = None
            
            # 更新全局数据
            stock_data[code] = stock_info
            
            # 显示进度
            if stock_info['distance_from_low_pct'] is not None:
                print(f"✅ {code} {stock_info['name']} 价格:{stock_info['price']} 距最低点:{stock_info['distance_from_low_pct']:.1f}% 股息率:{stock_info['dividend_yield']} 行业:{stock_info['industry']}")
            else:
                print(f"⚠️  {code} {stock_info['name']} 价格:{stock_info['price']} (无历史数据)")
            
            # 检查是否需要发送企业微信提醒
            if (stock_info['distance_from_low_pct'] is not None and 
                stock_info['distance_from_low_pct'] >= CONFIG['alert_threshold']):
                
                try:
                    wechat_alert.send_high_gain_alert(stock_info, CONFIG['alert_threshold'])
                except Exception as e:
                    print(f"发送企业微信提醒失败: {e}")
            
            # 间隔时间
            time.sleep(CONFIG['stock_interval'])
    
    last_update_time = datetime.now()
    print(f"\n🎉 更新完成！共处理 {len(stock_data)} 只股票")
    print(f"⏰ 更新时间: {last_update_time.strftime('%Y-%m-%d %H:%M:%S')}")

@app.route('/')
def index():
    return render_template('持仓页面_v1.html')

@app.route('/api/stocks')
def get_stocks():
    """获取股票数据API"""
    stocks_list = list(stock_data.values())
    
    return jsonify({
        'stocks': stocks_list,
        'last_update': last_update_time.strftime('%Y-%m-%d %H:%M:%S') if last_update_time else None,
        'config': CONFIG
    })

@app.route('/api/config', methods=['GET', 'POST'])
def handle_config():
    """处理配置"""
    global CONFIG
    
    if request.method == 'POST':
        new_config = request.get_json()
        CONFIG.update(new_config)
        return jsonify({'status': 'success', 'config': CONFIG})
    
    return jsonify({'config': CONFIG})

@app.route('/api/wechat/test')
def test_wechat():
    """测试企业微信连接"""
    try:
        success = wechat_alert.test_connection()
        return jsonify({'status': 'success' if success else 'error'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/api/wechat/summary')
def send_summary():
    """发送每日摘要"""
    try:
        stocks_list = list(stock_data.values())
        wechat_alert.send_daily_summary(stocks_list)
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

def background_update():
    """后台定时更新"""
    while True:
        try:
            update_all_stocks()
            print(f"⏰ 下次更新时间: {(datetime.now() + timedelta(seconds=CONFIG['round_interval'])).strftime('%H:%M:%S')}")
            time.sleep(CONFIG['round_interval'])
        except Exception as e:
            print(f"后台更新出错: {e}")
            time.sleep(60)  # 出错后等待1分钟再重试

if __name__ == '__main__':
    print("🚀 启动持仓系统 V1 完整版...")
    print("📊 功能: 基础持仓管理 + 股息率 + 行业信息")
    
    # 启动后台更新线程
    update_thread = threading.Thread(target=background_update, daemon=True)
    update_thread.start()
    
    # 启动Flask应用
    app.run(debug=True, host='0.0.0.0', port=5000)
