#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
防限制配置文件
"""

import random
import time

# 防限制策略配置
ANTI_LIMIT_CONFIG = {
    # 请求间隔配置
    'request_delay': {
        'min': 0.3,  # 最小延时（秒）
        'max': 0.8,  # 最大延时（秒）
    },
    
    # 批次处理配置
    'batch_config': {
        'size': 8,           # 每批处理股票数量
        'delay_min': 2,      # 批次间最小延时（秒）
        'delay_max': 5,      # 批次间最大延时（秒）
    },
    
    # 更新频率配置
    'update_interval': {
        'min': 150,  # 最小更新间隔（秒）
        'max': 240,  # 最大更新间隔（秒）
    },
    
    # 错误处理配置
    'error_handling': {
        'retry_delay': 5,     # 重试延时（秒）
        'max_retries': 3,     # 最大重试次数
        'backoff_factor': 2,  # 退避因子
    },
    
    # User-Agent轮换
    'user_agents': [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    ],
    
    # 请求头配置
    'headers': {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
    }
}

def get_random_delay(delay_type='request'):
    """获取随机延时"""
    if delay_type == 'request':
        config = ANTI_LIMIT_CONFIG['request_delay']
    elif delay_type == 'batch':
        config = ANTI_LIMIT_CONFIG['batch_config']
        return random.uniform(config['delay_min'], config['delay_max'])
    elif delay_type == 'update':
        config = ANTI_LIMIT_CONFIG['update_interval']
    else:
        return 1.0
    
    return random.uniform(config['min'], config['max'])

def get_random_user_agent():
    """获取随机User-Agent"""
    return random.choice(ANTI_LIMIT_CONFIG['user_agents'])

def get_headers():
    """获取请求头"""
    headers = ANTI_LIMIT_CONFIG['headers'].copy()
    headers['User-Agent'] = get_random_user_agent()
    headers['Referer'] = random.choice([
        'http://quote.eastmoney.com/',
        'http://quote.eastmoney.com/center/gridlist.html',
        'http://data.eastmoney.com/',
    ])
    return headers

def should_use_proxy():
    """是否使用代理（可扩展）"""
    return False

def get_proxy_list():
    """获取代理列表（可扩展）"""
    return []

def log_request_info(code, success, response_time=None):
    """记录请求信息"""
    status = "✅" if success else "❌"
    time_info = f" ({response_time:.2f}s)" if response_time else ""
    print(f"{status} {code}{time_info}")

def adaptive_delay(consecutive_failures=0):
    """自适应延时：失败次数越多，延时越长"""
    base_delay = get_random_delay('request')
    if consecutive_failures > 0:
        # 指数退避
        backoff = ANTI_LIMIT_CONFIG['error_handling']['backoff_factor'] ** consecutive_failures
        return min(base_delay * backoff, 10)  # 最大10秒
    return base_delay

# 使用示例
if __name__ == "__main__":
    print("=== 防限制配置测试 ===")
    
    print(f"请求延时: {get_random_delay('request'):.2f}s")
    print(f"批次延时: {get_random_delay('batch'):.2f}s")
    print(f"更新间隔: {get_random_delay('update'):.2f}s")
    print(f"User-Agent: {get_random_user_agent()}")
    print(f"请求头: {get_headers()}")
    
    print("\n自适应延时测试:")
    for i in range(5):
        delay = adaptive_delay(i)
        print(f"失败{i}次后延时: {delay:.2f}s")
