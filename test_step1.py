#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import akshare as ak

print("=== 第一步：测试AKSHARE接口 ===")
print("直接测试 stock_zh_a_spot 接口...")

try:
    df = ak.stock_zh_a_spot()
    print(f"✅ 成功！获取到 {len(df)} 只股票数据")
    print(f"列名: {df.columns.tolist()}")
    print("前3行数据:")
    print(df.head(3))
    print("代码列前5个值:")
    print(df['代码'].head(5).tolist())
except Exception as e:
    print(f"❌ 失败: {e}")
    print("尝试其他接口...")

    # 备用接口
    interfaces = [
        ('stock_zh_a_spot_em', '东方财富A股实时行情'),
        ('stock_info_a_code_name', 'A股代码名称'),
    ]

for func_name, desc in interfaces:
    print(f"\n--- 测试 {func_name} ({desc}) ---")
    try:
        if func_name == 'stock_individual_info_em':
            # 单只股票信息需要参数
            df = getattr(ak, func_name)(symbol='000001')
        else:
            df = getattr(ak, func_name)()

        print(f"✅ 成功获取数据！")
        print(f"📊 数据行数: {len(df)}")
        print(f"📋 列名: {df.columns.tolist()}")

        print("\n前3行数据:")
        print(df.head(3))

        if '代码' in df.columns:
            print("\n代码列的前5个值:")
            print(df['代码'].head(5).tolist())

        # 找到可用的接口就停止
        print(f"\n🎉 找到可用接口: {func_name}")
        break

    except Exception as e:
        print(f"❌ {func_name} 调用失败: {e}")
        continue
