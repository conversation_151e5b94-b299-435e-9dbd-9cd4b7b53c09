#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据获取模块
================

负责从各种数据源获取股票信息，包括：
- 东方财富API实时数据
- 股票行业信息
- 年内最低价数据
- 股息率计算

作者: AI Assistant
版本: 1.0
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, Optional, Union
import random

# 配置信息
CONFIG = {
    'request_timeout': 10,
    'retry_count': 3,
    'retry_delay': 1
}


def get_security_type(code):
    """根据代码判断证券类型"""
    code_str = str(code)
    
    # 只保留股票，过滤其他类型
    if code_str.startswith('1'):  # ETF
        return 'ETF'
    elif code_str.startswith('5'):  # 基金
        return '基金'
    elif code_str.startswith('8'):  # 新三板
        return '新三板'
    elif code_str.startswith('4'):  # 新三板等
        return '其他'
    else:
        return '股票'


def get_xueqiu_dividend_yield_ttm(stock_code):
    """
    从雪球网页爬取股息率TTM数据 - 带防限制措施
    """
    try:
        import random
        
        # 随机延迟，避免频繁请求
        time.sleep(random.uniform(0.1, 0.3))
        
        # 构建雪球股票页面URL
        if stock_code.startswith('6'):
            symbol = f'SH{stock_code}'
        else:
            symbol = f'SZ{stock_code}'
        
        url = f'https://stock.xueqiu.com/v5/stock/quote.json'
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': f'https://xueqiu.com/S/{symbol}',
            'X-Requested-With': 'XMLHttpRequest'
        }
        
        params = {
            'symbol': symbol,
            'extend': 'detail'
        }
        
        # 创建session以保持cookies
        session = requests.Session()
        
        # 先访问主页获取cookies
        session.get('https://xueqiu.com', headers=headers, timeout=10)
        
        # 再请求API
        response = session.get(url, params=params, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            # 从返回数据中提取股息率TTM
            if 'data' in data and 'quote' in data['data']:
                quote_data = data['data']['quote']
                dividend_yield = quote_data.get('dividend_yield', 0)
                
                # 雪球返回的股息率可能是百分比形式，需要转换
                if dividend_yield and dividend_yield > 0:
                    # 如果大于1，说明是百分比形式，需要除以100
                    if dividend_yield > 1:
                        dividend_yield = dividend_yield / 100
                    return round(dividend_yield * 100, 2)  # 转换为百分比显示
        
        return 0
        
    except Exception as e:
        print(f'❌ 从雪球爬取股息率TTM失败 {stock_code}: {e}')
        return 0


def calculate_dividend_yield_ttm(stock_code, current_price=None):
    """
    计算股息率TTM - 从雪球获取

    Args:
        stock_code (str): 股票代码
        current_price (float): 当前股价（可选）

    Returns:
        float: 股息率TTM（百分比）
    """
    try:
        # 从雪球获取股息率TTM
        dividend_yield = get_xueqiu_dividend_yield_ttm(stock_code)
        return dividend_yield
        
    except Exception as e:
        print(f'计算股息率TTM失败 {stock_code}: {e}')
        return 0


class StockDataFetcher:
    """股票数据获取器 - 统一管理所有数据获取逻辑"""

    def __init__(self):
        self.base_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Referer': 'http://quote.eastmoney.com/',
        }
        self.timeout = CONFIG.get('request_timeout', 10)

    def get_market_secid(self, stock_code):
        """根据股票代码确定市场标识"""
        return f'1.{stock_code}' if stock_code.startswith('6') else f'0.{stock_code}'

    def safe_get_value(self, data, key, default=None):
        """安全获取数据值，处理'-'等无效值"""
        value = data.get(key, default)
        return None if value == '-' else value

    def get_realtime_data(self, stock_code):
        """获取股票实时数据 - 东方财富API"""
        secid = self.get_market_secid(stock_code)

        url = 'http://push2.eastmoney.com/api/qt/ulist.np/get'
        params = {
            'fltt': '2',
            'invt': '2',
            'fields': 'f12,f14,f2,f3,f4,f23,f115,f114,f9,f133,f162,f173,f116,f127,f128,f129,f47,f48',
            'secids': secid,
            '_': str(int(time.time() * 1000))
        }

        try:
            response = requests.get(url, params=params, headers=self.base_headers, timeout=self.timeout)

            if response.status_code == 200:
                data = json.loads(response.text)

                if 'data' in data and 'diff' in data['data'] and len(data['data']['diff']) > 0:
                    stock_info = data['data']['diff'][0]

                    # 计算股息率TTM
                    current_price = stock_info.get('f2', 0)
                    dividend_yield = calculate_dividend_yield_ttm(stock_code, current_price)

                    return {
                        'code': stock_info.get('f12', ''),
                        'name': stock_info.get('f14', ''),
                        'price': stock_info.get('f2', 0),
                        'change': stock_info.get('f4', 0),
                        'change_pct': stock_info.get('f3', 0),
                        'dividend_yield': dividend_yield,
                        'pb_ratio': self.safe_get_value(stock_info, 'f23'),
                        'pe_ttm': self.safe_get_value(stock_info, 'f115'),
                        'industry': '',  # 后续单独获取
                        'volume': stock_info.get('f47', 0),
                        'turnover': stock_info.get('f48', 0),
                        'market_cap': self.safe_get_value(stock_info, 'f116', 0),
                    }

        except Exception as e:
            print(f"获取股票 {stock_code} 实时数据失败: {e}")

        return None

    def get_industry_info(self, stock_code):
        """获取股票行业信息"""
        secid = self.get_market_secid(stock_code)

        url = 'http://push2.eastmoney.com/api/qt/stock/get'
        params = {
            'fltt': '2',
            'invt': '2',
            'fields': 'f127,f128,f129',  # 行业、板块、概念
            'secid': secid,
            '_': str(int(time.time() * 1000))
        }

        try:
            response = requests.get(url, params=params, headers=self.base_headers, timeout=self.timeout)

            if response.status_code == 200:
                data = json.loads(response.text).get('data', {})

                if data:
                    return {
                        'industry': data.get('f127', ''),
                        'sector': data.get('f128', ''),
                        'concept': data.get('f129', ''),
                    }

        except Exception as e:
            print(f"获取股票 {stock_code} 行业信息失败: {e}")

        return {'industry': '', 'sector': '', 'concept': ''}


class StockInfoProcessor:
    """股票信息处理器 - 整合各种数据源"""

    def __init__(self, data_fetcher):
        self.data_fetcher = data_fetcher

    def get_complete_stock_info(self, code: str) -> dict:
        """获取完整的股票信息"""
        # 获取实时数据
        real_data = self.data_fetcher.get_realtime_data(code)
        if not real_data:
            return None

        # 获取年内最低价信息（需要导入相关模块）
        try:
            from yearly_low_cache import get_yearly_low_from_cache
            yearly_low_info = get_yearly_low_from_cache(code)
            if not yearly_low_info:
                yearly_low_info = {'yearly_low': None, 'low_date': None}
        except ImportError:
            yearly_low_info = {'yearly_low': None, 'low_date': None}

        # 获取行业信息（如果主接口没有获取到）
        if not real_data.get('industry'):
            industry_data = self.data_fetcher.get_industry_info(code)
            real_data['industry'] = industry_data.get('industry', '')

        # 计算距离最低点的涨幅
        distance_from_low_pct = self._calculate_distance_from_low(
            real_data['price'],
            yearly_low_info.get('yearly_low')
        )

        return {
            'code': code,
            'name': real_data['name'],
            'price': real_data['price'],
            'change': real_data['change'],
            'change_pct': real_data['change_pct'],
            'volume': real_data.get('volume', 0),
            'turnover': real_data.get('turnover', 0),
            'pe_ratio': real_data['pe_ttm'],
            'pb_ratio': real_data['pb_ratio'],
            'dividend_yield': real_data['dividend_yield'],
            'market_cap': real_data.get('market_cap', 0),
            'industry': real_data['industry'],
            'yearly_low': yearly_low_info.get('yearly_low'),
            'low_date': yearly_low_info.get('low_date'),
            'distance_from_low_pct': distance_from_low_pct,
            'security_type': get_security_type(code),
            'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

    def _calculate_distance_from_low(self, current_price, yearly_low):
        """计算距离年内最低点的涨幅"""
        if yearly_low and yearly_low > 0 and current_price > 0:
            return ((current_price - yearly_low) / yearly_low) * 100
        return None


# 创建全局实例
stock_data_fetcher = StockDataFetcher()
stock_info_processor = StockInfoProcessor(stock_data_fetcher)


# 向后兼容的函数
def get_eastmoney_stock_data_v2(stock_code):
    """获取股票实时数据 - 保持向后兼容"""
    return stock_data_fetcher.get_realtime_data(stock_code)


def get_stock_industry_info(stock_code):
    """获取股票行业信息 - 保持向后兼容"""
    return stock_data_fetcher.get_industry_info(stock_code)


def get_stock_info(code: str) -> dict:
    """获取股票基本信息 - 保持向后兼容"""
    return stock_info_processor.get_complete_stock_info(code)
