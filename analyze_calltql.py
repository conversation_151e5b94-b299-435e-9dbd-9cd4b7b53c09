import requests
import re
import json

def analyze_calltql_function(code):
    """深入分析CallTQL函数调用"""
    try:
        # 扫雷宝主页面
        api_url = f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code={code}&color=0"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Referer': 'http://page3.tdx.com.cn:7615/',
        }
        
        print(f"🔍 深入分析CallTQL函数...")
        response = requests.get(api_url, headers=headers, timeout=15)
        response.encoding = 'utf-8'
        
        if response.status_code == 200:
            content = response.text
            
            # 查找完整的CallTQL调用
            print("📡 查找完整的CallTQL调用:")
            
            # 更精确的CallTQL模式匹配
            calltql_pattern = r'CallTQL\([^)]+\)'
            calltql_matches = re.findall(calltql_pattern, content)
            
            for i, match in enumerate(calltql_matches):
                print(f"\nCallTQL调用 {i+1}: {match}")
                
                # 尝试解析参数
                params_pattern = r'CallTQL\(([^)]+)\)'
                params_match = re.search(params_pattern, match)
                if params_match:
                    params = params_match.group(1)
                    print(f"参数: {params}")
            
            # 查找CallTQL函数定义
            print("\n🎯 查找CallTQL函数定义:")
            
            # 查找function CallTQL或var CallTQL
            calltql_def_patterns = [
                r'function\s+CallTQL\s*\([^}]+\}',
                r'var\s+CallTQL\s*=\s*function[^}]+\}',
                r'CallTQL\s*=\s*function[^}]+\}',
                r'CallTQL\s*:\s*function[^}]+\}',
            ]
            
            for pattern in calltql_def_patterns:
                matches = re.findall(pattern, content, re.DOTALL)
                if matches:
                    for match in matches:
                        print(f"CallTQL定义: {match[:200]}...")
            
            # 查找所有包含"score"或"rating"的JavaScript代码
            print("\n📊 查找评分相关代码:")
            
            # 查找包含分数的变量赋值
            score_patterns = [
                r'(realvalue\s*=\s*[^,;]+)',
                r'(showvalue\s*=\s*[^,;]+)',
                r'(score\s*=\s*[^,;]+)',
                r'(rating\s*=\s*[^,;]+)',
            ]
            
            for pattern in score_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    for match in matches:
                        print(f"分数变量: {match}")
            
            # 查找可能的数据接口URL
            print("\n🌐 查找数据接口URL:")
            
            # 查找所有URL模式
            url_patterns = [
                r'["\']([^"\']*\.json[^"\']*)["\']',
                r'["\']([^"\']*api[^"\']*)["\']',
                r'["\']([^"\']*data[^"\']*)["\']',
                r'["\']([^"\']*bxb[^"\']*)["\']',
            ]
            
            for pattern in url_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    unique_matches = list(set(matches))
                    for match in unique_matches:
                        if len(match) > 10:  # 过滤掉太短的匹配
                            print(f"可能的接口: {match}")
            
            # 尝试直接调用通达信的TQL接口
            print("\n🧪 尝试直接调用TQL接口:")
            
            # 通达信TQL接口的可能格式
            tql_apis = [
                f"http://page3.tdx.com.cn:7615/tql?code={code}",
                f"http://page3.tdx.com.cn:7615/api/tql?code={code}",
                f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/tql?code={code}",
            ]
            
            for api_url in tql_apis:
                try:
                    print(f"测试TQL接口: {api_url}")
                    test_response = requests.get(api_url, headers=headers, timeout=5)
                    if test_response.status_code == 200:
                        print(f"✅ TQL接口成功: {test_response.text[:200]}")
                    else:
                        print(f"❌ TQL接口失败: {test_response.status_code}")
                except Exception as e:
                    print(f"❌ TQL接口异常: {e}")
            
            return content
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        return None

if __name__ == '__main__':
    # 测试神思电子
    analyze_calltql_function('300479')
