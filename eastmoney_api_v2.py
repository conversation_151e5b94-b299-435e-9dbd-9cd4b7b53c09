#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import time
import json
import pandas as pd
from datetime import datetime

def get_eastmoney_stock_data_v2(stock_code):
    """
    从东方财富网获取单只股票实时数据 - 第二版，包含股息率和行业信息
    使用正确的股息率字段 f133
    :param stock_code: 股票代码，如 '000001'
    :return: 股票数据字典
    """

    # 根据股票代码确定市场
    if stock_code.startswith('6'):
        # 上海股票
        secid = f'1.{stock_code}'
    else:
        # 深圳股票
        secid = f'0.{stock_code}'

    # 使用批量接口获取更准确的股息率数据
    url = 'http://push2.eastmoney.com/api/qt/ulist.np/get'

    # 请求参数 - 使用正确的股息率字段 f133
    params = {
        'fltt': '2',
        'invt': '2',
        'fields': 'f12,f14,f2,f3,f4,f23,f115,f114,f9,f133,f162,f173,f116,f127,f128,f129,f47,f48',
        'secids': secid,
        '_': str(int(time.time() * 1000))
    }

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'http://quote.eastmoney.com/',
    }

    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)

        if response.status_code == 200:
            data = json.loads(response.text)

            if 'data' in data and 'diff' in data['data'] and data['data']['diff']:
                item = data['data']['diff'][0]  # 取第一个结果

                # 处理股息率数据
                dividend_yield = item.get('f133', None)
                if dividend_yield == '-' or dividend_yield == 0 or dividend_yield is None:
                    dividend_yield = 0
                else:
                    try:
                        dividend_yield = float(dividend_yield)
                    except:
                        dividend_yield = 0

                # 处理市净率数据
                pb_ratio = item.get('f23', None)
                if pb_ratio == '-' or pb_ratio is None:
                    pb_ratio = 0
                else:
                    try:
                        pb_ratio = float(pb_ratio)
                    except:
                        pb_ratio = 0

                # 处理TTM市盈率数据
                pe_ttm = item.get('f115', None)
                if pe_ttm == '-' or pe_ttm is None:
                    pe_ttm = 0
                else:
                    try:
                        pe_ttm = float(pe_ttm)
                    except:
                        pe_ttm = 0

                # 解析数据，确保数值类型正确
                def safe_float(value, default=0):
                    if value is None or value == '-' or value == '':
                        return default
                    try:
                        return float(value)
                    except:
                        return default

                stock_info = {
                    'code': str(item.get('f12', '')),           # 股票代码
                    'name': str(item.get('f14', '')),           # 股票名称
                    'price': safe_float(item.get('f2', 0)),     # 最新价
                    'change': safe_float(item.get('f4', 0)),    # 涨跌额
                    'change_pct': safe_float(item.get('f3', 0)), # 涨跌幅
                    'pe_ratio': pe_ttm,                         # TTM市盈率
                    'pb_ratio': pb_ratio,                       # 市净率
                    'dividend_yield': dividend_yield,           # 股息率 (正确字段)
                    'volume': safe_float(item.get('f47', 0)),   # 成交量
                    'amount': safe_float(item.get('f48', 0)),   # 成交额
                    'update_time': datetime.now().strftime('%H:%M:%S')
                }

                return stock_info

    except Exception as e:
        print(f"获取股票 {stock_code} 数据失败: {e}")
        return None

def get_stock_industry_info(stock_code):
    """
    获取股票行业信息 - 使用单独的接口
    """
    # 根据股票代码确定市场
    if stock_code.startswith('6'):
        secid = f'1.{stock_code}'
    else:
        secid = f'0.{stock_code}'

    # 东方财富API URL - 获取详细信息
    url = 'http://push2.eastmoney.com/api/qt/stock/get'

    params = {
        'fltt': '2',
        'invt': '2',
        'fields': 'f127,f128,f129',  # 行业、板块、概念
        'secid': secid,
        '_': str(int(time.time() * 1000))
    }

    try:
        response = requests.get(url, params=params, timeout=10)

        if response.status_code == 200:
            data = json.loads(response.text)['data']

            if data:
                return {
                    'industry': data.get('f127', ''),      # 行业
                    'sector': data.get('f128', ''),        # 板块
                    'concept': data.get('f129', ''),       # 概念
                }

    except Exception as e:
        print(f"获取股票 {stock_code} 行业信息失败: {e}")

    return {'industry': '', 'sector': '', 'concept': ''}

def get_yearly_low_data_v2(stock_code):
    """
    获取股票年内最低价数据 - 第二版
    """
    # 根据股票代码确定市场
    if stock_code.startswith('6'):
        secid = f'1.{stock_code}'
    else:
        secid = f'0.{stock_code}'
    
    # 获取K线数据
    url = 'http://push2his.eastmoney.com/api/qt/stock/kline/get'
    params = {
        'secid': secid,
        'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
        'fields1': 'f1,f2,f3,f4,f5,f6',
        'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
        'klt': '101',  # 日K线
        'fqt': '1',    # 前复权
        'beg': '20240101',  # 从2024年开始
        'end': '20251231',
        '_': str(int(time.time() * 1000))
    }
    
    try:
        response = requests.get(url, params=params, timeout=10)
        if response.status_code == 200:
            data = json.loads(response.text)['data']
            if data and 'klines' in data:
                klines = data['klines']
                
                min_price = float('inf')
                min_date = ''
                
                for kline in klines:
                    parts = kline.split(',')
                    date = parts[0]
                    low = float(parts[4])  # 最低价
                    
                    if low < min_price:
                        min_price = low
                        min_date = date
                
                return min_price, min_date
    except Exception as e:
        print(f"获取 {stock_code} 年内最低价失败: {e}")
    
    return None, None

def get_multiple_stocks_data_v2(stock_codes, callback=None):
    """
    批量获取多只股票数据 - 第二版，包含年内最低价、股息率和行业
    :param stock_codes: 股票代码列表
    :param callback: 回调函数，每获取一只股票后调用
    """
    results = []

    for i, code in enumerate(stock_codes):
        print(f"正在获取 {code} 的数据... ({i+1}/{len(stock_codes)})")

        # 获取实时数据（包含股息率）
        stock_data = get_eastmoney_stock_data_v2(code)
        if not stock_data:
            print(f"❌ {code} 获取实时数据失败")
            continue

        # 获取行业信息
        industry_info = get_stock_industry_info(code)
        stock_data.update(industry_info)

        # 获取年内最低价
        yearly_low, low_date = get_yearly_low_data_v2(code)
        if yearly_low and yearly_low > 0:
            # 计算距离年内最低价的涨幅
            current_price = float(stock_data.get('price', 0))
            yearly_low = float(yearly_low)

            if current_price > 0 and yearly_low > 0:
                distance_from_low_pct = ((current_price - yearly_low) / yearly_low) * 100
            else:
                distance_from_low_pct = 0

            stock_data['yearly_low'] = yearly_low
            stock_data['low_date'] = low_date
            stock_data['distance_from_low_pct'] = distance_from_low_pct
        else:
            stock_data['yearly_low'] = 0
            stock_data['low_date'] = ''
            stock_data['distance_from_low_pct'] = 0

        results.append(stock_data)

        # 显示获取结果
        dividend_str = f"{stock_data['dividend_yield']:.2f}%" if stock_data['dividend_yield'] > 0 else "无"
        print(f"✅ {code} {stock_data['name']} 价格:{stock_data['price']} 涨跌幅:{stock_data['change_pct']}% 行业:{stock_data['industry']} 股息率:{dividend_str}")

        # 如果有回调函数，调用它（用于实时更新）
        if callback:
            callback(results.copy())

        # 避免请求过于频繁
        time.sleep(0.5)

    return results

def save_to_csv_v2(stock_data_list, filename='eastmoney_data_v2.csv'):
    """
    保存数据到CSV文件 - 第二版
    """
    if stock_data_list:
        df = pd.DataFrame(stock_data_list)
        # 重新排列列的顺序
        columns_order = [
            'code', 'name', 'price', 'change', 'change_pct', 
            'industry', 'dividend_yield', 'pe_ratio', 'pb_ratio',
            'yearly_low', 'low_date', 'distance_from_low_pct',
            'market_cap', 'sector', 'volume', 'amount', 'update_time'
        ]
        
        # 只保留存在的列
        existing_columns = [col for col in columns_order if col in df.columns]
        df = df[existing_columns]
        
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"✅ 数据已保存到 {filename}")
        return True
    return False

def load_stock_codes_from_excel():
    """
    从Excel文件中加载股票代码
    """
    try:
        df = pd.read_csv('stocks_list.csv', encoding='utf-8-sig')
        stock_codes = df['代码'].astype(str).str.zfill(6).tolist()
        print(f"从Excel加载了 {len(stock_codes)} 只股票代码")
        return stock_codes
    except Exception as e:
        print(f"加载股票代码失败: {e}")
        return []

def test_api_v2():
    """
    测试API功能 - 第二版
    """
    print("=== 测试东方财富API - 第二版 ===")
    
    # 测试几只股票
    test_codes = ['600036', '000001', '600519', '000002', '600000']
    
    print("测试批量获取（包含股息率和行业）...")
    batch_data = get_multiple_stocks_data_v2(test_codes)
    
    if batch_data:
        print(f"\n成功获取 {len(batch_data)} 只股票数据")
        
        # 显示汇总
        df = pd.DataFrame(batch_data)
        print("\n数据汇总:")
        display_columns = ['code', 'name', 'price', 'change_pct', 'industry', 'dividend_yield', 'distance_from_low_pct']
        existing_display_columns = [col for col in display_columns if col in df.columns]
        print(df[existing_display_columns])
        
        # 保存测试数据
        save_to_csv_v2(batch_data, 'test_eastmoney_data_v2.csv')
    
    return batch_data

def main_v2():
    """
    主函数：获取持仓股票的实时数据 - 第二版
    """
    print("=== 东方财富实时股票数据获取 - 第二版 ===")
    print("新增功能：股息率、行业信息")
    
    # 1. 加载股票代码
    stock_codes = load_stock_codes_from_excel()
    
    if not stock_codes:
        print("未找到股票代码，使用测试数据...")
        return test_api_v2()
    
    # 2. 批量获取数据
    print(f"\n开始获取 {len(stock_codes)} 只股票的实时数据...")
    stock_data_list = get_multiple_stocks_data_v2(stock_codes)
    
    # 3. 保存数据
    if stock_data_list:
        save_to_csv_v2(stock_data_list)
        
        # 显示统计信息
        df = pd.DataFrame(stock_data_list)
        print(f"\n=== 数据统计 ===")
        print(f"成功获取: {len(stock_data_list)} 只股票")
        print(f"上涨股票: {len(df[df['change_pct'] > 0])} 只")
        print(f"下跌股票: {len(df[df['change_pct'] < 0])} 只")
        print(f"平盘股票: {len(df[df['change_pct'] == 0])} 只")
        
        # 按行业分组统计
        if 'industry' in df.columns:
            print(f"\n=== 行业分布 ===")
            industry_stats = df['industry'].value_counts()
            print(industry_stats)
        
        # 股息率统计
        if 'dividend_yield' in df.columns:
            print(f"\n=== 股息率统计 ===")
            dividend_df = df[df['dividend_yield'] > 0].sort_values('dividend_yield', ascending=False)
            if not dividend_df.empty:
                print("股息率前5:")
                top_dividend = dividend_df.head(5)[['code', 'name', 'dividend_yield', 'industry']]
                print(top_dividend.to_string(index=False))
        
        # 显示涨幅前5和跌幅前5
        print("\n涨幅前5:")
        top_gainers = df.nlargest(5, 'change_pct')[['code', 'name', 'price', 'change_pct', 'industry']]
        print(top_gainers.to_string(index=False))
        
    else:
        print("❌ 未获取到任何数据")
    
    return stock_data_list

if __name__ == "__main__":
    # 可以选择运行测试或主程序
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'test':
        test_api_v2()
    else:
        main_v2()
