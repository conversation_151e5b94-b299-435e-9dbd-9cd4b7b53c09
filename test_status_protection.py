#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试状态保护功能
验证用户手动设置的状态不会被自动刷新覆盖
"""

import json
import time
import requests
from datetime import datetime

def test_status_protection():
    """测试状态保护功能"""
    print("🧪 状态保护功能测试")
    print("=" * 60)
    
    base_url = "http://localhost:5000"
    
    # 测试步骤
    print("\n📋 测试步骤:")
    print("1. 获取股票列表")
    print("2. 选择一只股票设置为'已清仓'状态")
    print("3. 触发数据刷新")
    print("4. 验证状态是否被保护")
    print("5. 测试强制刷新功能")
    
    try:
        # 步骤1: 获取股票数据
        print("\n🔍 步骤1: 获取股票数据...")
        response = requests.get(f"{base_url}/api/stocks")
        if response.status_code != 200:
            print(f"❌ 获取股票数据失败: {response.status_code}")
            return
        
        stocks = response.json()
        if not stocks:
            print("❌ 没有股票数据")
            return
        
        # 选择第一只股票进行测试
        test_stock = stocks[0]
        stock_code = test_stock['code']
        stock_name = test_stock['name']
        
        print(f"✅ 选择测试股票: {stock_name} ({stock_code})")
        print(f"   当前状态: {test_stock.get('sell_reason', '未知')}")
        
        # 步骤2: 设置为已清仓状态
        print(f"\n🔧 步骤2: 设置 {stock_name} 为'已清仓'状态...")
        
        custom_status_data = {
            "stock_code": stock_code,
            "status_type": "cleared",
            "status_text": "已清仓",
            "status_emoji": "🔥"
        }
        
        response = requests.post(
            f"{base_url}/api/set-custom-status",
            json=custom_status_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 状态设置成功: {result['message']}")
        else:
            print(f"❌ 状态设置失败: {response.status_code}")
            return
        
        # 验证状态设置
        response = requests.get(f"{base_url}/api/stocks")
        stocks = response.json()
        updated_stock = next((s for s in stocks if s['code'] == stock_code), None)
        
        if updated_stock:
            print(f"   ✅ 验证状态: custom_status={updated_stock.get('custom_status', False)}")
            print(f"   ✅ 验证状态: is_cleared={updated_stock.get('is_cleared', False)}")
            print(f"   ✅ 验证状态: custom_status_text={updated_stock.get('custom_status_text', 'None')}")
        
        # 步骤3: 触发数据刷新
        print(f"\n🔄 步骤3: 触发单只股票数据刷新...")
        
        response = requests.post(f"{base_url}/api/update-single-stock/{stock_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 数据刷新成功: {result['message']}")
        else:
            print(f"❌ 数据刷新失败: {response.status_code}")
        
        # 步骤4: 验证状态保护
        print(f"\n🔒 步骤4: 验证状态保护...")
        
        response = requests.get(f"{base_url}/api/stocks")
        stocks = response.json()
        protected_stock = next((s for s in stocks if s['code'] == stock_code), None)
        
        if protected_stock:
            is_protected = (
                protected_stock.get('custom_status', False) and
                protected_stock.get('is_cleared', False) and
                protected_stock.get('custom_status_text') == '已清仓'
            )
            
            if is_protected:
                print(f"✅ 状态保护成功！")
                print(f"   custom_status: {protected_stock.get('custom_status')}")
                print(f"   is_cleared: {protected_stock.get('is_cleared')}")
                print(f"   custom_status_text: {protected_stock.get('custom_status_text')}")
                print(f"   sell_reason: {protected_stock.get('sell_reason')}")
            else:
                print(f"❌ 状态保护失败！状态被覆盖了")
                print(f"   custom_status: {protected_stock.get('custom_status')}")
                print(f"   is_cleared: {protected_stock.get('is_cleared')}")
                print(f"   sell_reason: {protected_stock.get('sell_reason')}")
                return
        
        # 步骤5: 测试强制刷新
        print(f"\n🔄 步骤5: 测试强制刷新功能...")
        
        response = requests.post(f"{base_url}/api/force-refresh-signal/{stock_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 强制刷新成功: {result['message']}")
            print(f"   新信号: {result['data']['new_signal']['signal']} - {result['data']['new_signal']['reason']}")
        else:
            print(f"❌ 强制刷新失败: {response.status_code}")
        
        # 最终验证
        response = requests.get(f"{base_url}/api/stocks")
        stocks = response.json()
        final_stock = next((s for s in stocks if s['code'] == stock_code), None)
        
        if final_stock:
            print(f"\n📊 最终状态:")
            print(f"   sell_signal: {final_stock.get('sell_signal')}")
            print(f"   sell_reason: {final_stock.get('sell_reason')}")
            print(f"   custom_status: {final_stock.get('custom_status')}")
            print(f"   is_cleared: {final_stock.get('is_cleared')}")
        
        print(f"\n🎉 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_protection_scenarios():
    """测试不同的保护场景"""
    print("\n🧪 保护场景测试")
    print("=" * 40)
    
    scenarios = [
        {
            'name': '自定义状态保护',
            'setup': {'status_type': 'custom', 'status_text': '已卖出', 'status_emoji': '💰'},
            'description': '测试自定义状态是否被保护'
        },
        {
            'name': '已减半状态保护', 
            'setup': {'status_type': 'reduced', 'status_text': '已减半', 'status_emoji': '✅'},
            'description': '测试已减半状态是否被保护'
        },
        {
            'name': '已清仓状态保护',
            'setup': {'status_type': 'cleared', 'status_text': '已清仓', 'status_emoji': '🔥'},
            'description': '测试已清仓状态是否被保护'
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📋 {scenario['name']}")
        print(f"   {scenario['description']}")
        # 这里可以添加具体的测试逻辑

def show_usage():
    """显示使用说明"""
    print("\n📖 状态保护功能说明")
    print("=" * 50)
    
    print("\n🔒 保护机制:")
    print("1. 当用户手动设置股票状态时，系统会标记该状态为'受保护'")
    print("2. 在数据刷新时，系统会检查是否存在受保护的状态")
    print("3. 如果存在，则跳过卖出信号的重新计算，保持用户设置")
    
    print("\n🛡️ 保护范围:")
    print("- custom_status: 自定义状态标记")
    print("- is_cleared: 已清仓标记")
    print("- is_reduced: 已减半标记")
    print("- sell_signal, sell_reason, sell_color, sell_priority: 卖出信号相关字段")
    
    print("\n🔄 强制刷新:")
    print("- 如果需要重新计算信号，可以使用'强制刷新'功能")
    print("- 强制刷新会忽略状态保护，重新计算卖出信号")
    print("- 可通过界面菜单或API调用")
    
    print("\n🎯 使用场景:")
    print("✅ 适用: 已经执行了减仓/清仓操作，不希望再收到提醒")
    print("✅ 适用: 有特殊情况需要自定义状态标记")
    print("⚠️ 注意: 强制刷新会清除所有手动设置，请谨慎使用")

if __name__ == "__main__":
    print("🚀 状态保护功能测试工具")
    print("=" * 60)
    
    print("\n选择测试模式:")
    print("1. 完整功能测试")
    print("2. 保护场景测试")
    print("3. 查看使用说明")
    print("4. 退出")
    
    try:
        choice = input("\n请选择 (1-4): ").strip()
        
        if choice == "1":
            test_status_protection()
        elif choice == "2":
            test_protection_scenarios()
        elif choice == "3":
            show_usage()
        elif choice == "4":
            print("👋 退出测试")
        else:
            print("❌ 无效选择")
    except KeyboardInterrupt:
        print("\n👋 测试已取消")
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
