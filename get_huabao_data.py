#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pyautogui
import pygetwindow as gw
import time
import pandas as pd
from PIL import Image
import pytesseract
import cv2
import numpy as np

def find_huabao_window():
    """查找华宝证券软件窗口"""
    print("=== 查找华宝证券软件窗口 ===")
    
    # 获取所有窗口
    windows = gw.getAllWindows()
    
    # 查找包含"华宝"、"证券"等关键词的窗口
    keywords = ['华宝', '证券', 'huabao', 'Huabao', '股票', '行情']
    
    found_windows = []
    for window in windows:
        if window.title:  # 确保窗口有标题
            for keyword in keywords:
                if keyword.lower() in window.title.lower():
                    found_windows.append(window)
                    print(f"找到窗口: {window.title}")
                    break
    
    if not found_windows:
        print("未找到华宝证券软件窗口")
        print("所有窗口列表:")
        for window in windows:
            if window.title and len(window.title) > 3:  # 过滤掉空标题和太短的标题
                print(f"  - {window.title}")
        return None
    
    return found_windows[0]  # 返回第一个找到的窗口

def capture_window_screenshot(window):
    """截取窗口截图"""
    try:
        # 激活窗口
        window.activate()
        time.sleep(1)
        
        # 获取窗口位置和大小
        left, top, width, height = window.left, window.top, window.width, window.height
        
        # 截图
        screenshot = pyautogui.screenshot(region=(left, top, width, height))
        
        # 保存截图
        screenshot.save("huabao_screenshot.png")
        print(f"已保存窗口截图: huabao_screenshot.png")
        print(f"窗口位置: ({left}, {top}), 大小: {width}x{height}")
        
        return screenshot
        
    except Exception as e:
        print(f"截图失败: {e}")
        return None

def analyze_screenshot():
    """分析截图，尝试识别股票数据"""
    try:
        # 读取截图
        image = cv2.imread("huabao_screenshot.png")
        
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 使用OCR识别文字
        text = pytesseract.image_to_string(gray, lang='chi_sim+eng')
        
        print("=== OCR识别结果 ===")
        print(text)
        
        # 保存识别结果
        with open("ocr_result.txt", "w", encoding="utf-8") as f:
            f.write(text)
        
        return text
        
    except Exception as e:
        print(f"OCR识别失败: {e}")
        return None

def main():
    print("=== 华宝证券软件数据获取 ===")
    
    # 第一步：查找华宝证券窗口
    window = find_huabao_window()
    if not window:
        return
    
    # 第二步：截取窗口截图
    screenshot = capture_window_screenshot(window)
    if not screenshot:
        return
    
    # 第三步：分析截图内容
    text = analyze_screenshot()
    
    print("=== 完成 ===")
    print("请查看生成的文件:")
    print("- huabao_screenshot.png (窗口截图)")
    print("- ocr_result.txt (OCR识别结果)")

if __name__ == "__main__":
    main()
