import requests
import re
import json

def get_mine_clearance_score(code: str) -> float:
    """获取股票扫雷宝分数"""
    try:
        # 扫雷宝API接口
        api_url = f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code={code}&color=0"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Referer': 'http://page3.tdx.com.cn:7615/',
        }
        
        print(f"🔍 获取股票 {code} 的扫雷宝分数...")
        response = requests.get(api_url, headers=headers, timeout=15)
        response.encoding = 'utf-8'
        
        if response.status_code == 200:
            content = response.text
            print(f"✅ 响应成功，内容长度: {len(content)}")
            
            # 方法1: 尝试获取JSON数据
            try:
                json_url = f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/json/{code}.json"
                print(f"📡 尝试JSON接口: {json_url}")
                json_response = requests.get(json_url, headers=headers, timeout=10)
                
                if json_response.status_code == 200:
                    json_data = json_response.json()
                    print(f"📊 JSON数据: {json.dumps(json_data, ensure_ascii=False, indent=2)}")
                    
                    # 从JSON数据中计算扫雷宝分数
                    if 'total' in json_data and 'num' in json_data:
                        total = json_data['total']
                        risk_num = json_data['num']
                        if total > 0:
                            # 扫雷宝分数 = (总检查项 - 风险项) / 总检查项 * 100
                            score = ((total - risk_num) / total) * 100
                            print(f"✅ JSON计算分数: {score:.1f} (总检查:{total}, 风险:{risk_num})")
                            return round(score, 1)
                else:
                    print(f"❌ JSON请求失败: {json_response.status_code}")
            except Exception as e:
                print(f"❌ JSON处理异常: {e}")
            
            # 方法2: 从JavaScript代码中提取分数
            print("🔍 查找JavaScript变量...")
            
            # 查找realvalue变量（这是扫雷宝的实际分数）
            realvalue_pattern = r'realvalue\s*=\s*(\d{1,3})'
            realvalue_match = re.search(realvalue_pattern, content)
            if realvalue_match:
                score = float(realvalue_match.group(1))
                print(f"✅ 找到realvalue: {score}")
                if 0 <= score <= 100:
                    return score
            
            # 查找showvalue变量（显示的分数）
            showvalue_pattern = r'showvalue\s*=\s*(\d{1,3})'
            showvalue_match = re.search(showvalue_pattern, content)
            if showvalue_match:
                score = float(showvalue_match.group(1))
                print(f"✅ 找到showvalue: {score}")
                if 0 <= score <= 100:
                    return score
            
            # 方法3: 查找Canvas绘制中的分数文本
            print("🔍 查找Canvas绘制分数...")
            filltext_pattern = r'fillText\([^,]*?(\d{1,3})[^,]*?"分"'
            filltext_match = re.search(filltext_pattern, content)
            if filltext_match:
                score = float(filltext_match.group(1))
                print(f"✅ 找到Canvas分数: {score}")
                if 0 <= score <= 100:
                    return score
            
            # 方法4: 查找所有可能的分数变量
            print("🔍 查找其他分数变量...")
            score_patterns = [
                r'var\s+score\s*=\s*(\d{1,3})',
                r'score\s*[:=]\s*(\d{1,3})',
                r'安全分\s*[:=]\s*(\d{1,3})',
                r'safeScore\s*[:=]\s*(\d{1,3})',
            ]
            
            for pattern in score_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    print(f"找到模式 {pattern}: {matches}")
                    for match in matches:
                        try:
                            score = float(match)
                            if 0 <= score <= 100:
                                return score
                        except ValueError:
                            continue
            
            # 方法5: 查找所有两位数，优先返回高分数
            print("🔍 查找所有可能分数...")
            content_no_css = re.sub(r'<style[^>]*>.*?</style>', '', content, flags=re.DOTALL)
            content_no_css = re.sub(r'style\s*=\s*"[^"]*"', '', content_no_css)
            
            all_numbers = re.findall(r'\b(\d{2,3})\b', content_no_css)
            valid_scores = []
            
            for num_str in all_numbers:
                try:
                    num = float(num_str)
                    if 50 <= num <= 100:  # 扫雷宝分数通常在这个范围
                        valid_scores.append(num)
                except ValueError:
                    continue
            
            print(f"有效分数: {valid_scores[:10]}")
            
            if valid_scores:
                max_score = max(valid_scores)
                print(f"✅ 返回最高分数: {max_score}")
                return max_score
            
            print("❌ 未找到有效分数")
            return None
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        return None

if __name__ == '__main__':
    # 测试000001
    score = get_mine_clearance_score('000001')
    print(f"\n🎯 最终结果: 股票000001的扫雷宝分数为 {score} 分")
