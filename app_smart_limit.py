from flask import Flask, render_template, jsonify
import requests
import time
import json
import pandas as pd
from datetime import datetime
import threading
import random
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from anti_limit_config import ANTI_LIMIT_CONFIG, get_random_delay, get_headers, adaptive_delay

app = Flask(__name__)

# 全局变量
stock_data = {}
last_update_time = None
consecutive_failures = 0

class SmartSession:
    """智能防限制Session"""
    
    def __init__(self):
        self.session = self._create_session()
        self.request_count = 0
        self.last_request_time = 0
        
    def _create_session(self):
        session = requests.Session()
        
        # 设置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    def get(self, url, **kwargs):
        """智能GET请求"""
        # 更新请求头
        self.session.headers.update(get_headers())
        
        # 智能延时
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < 0.5:  # 如果距离上次请求太近
            delay = adaptive_delay(self.consecutive_failures)
            time.sleep(delay)
        
        try:
            response = self.session.get(url, **kwargs)
            self.request_count += 1
            self.last_request_time = time.time()
            
            # 检查是否被限制
            if response.status_code == 429:
                print("⚠️  检测到限制，增加延时...")
                time.sleep(random.uniform(5, 10))
                return None
            
            return response
            
        except Exception as e:
            print(f"请求异常: {e}")
            return None

# 全局智能session
smart_session = SmartSession()

def get_eastmoney_stock_data(stock_code):
    """智能获取股票数据"""
    global consecutive_failures
    
    # 根据股票代码确定市场
    if stock_code.startswith('6'):
        secid = f'1.{stock_code}'
    else:
        secid = f'0.{stock_code}'
    
    url = 'http://push2.eastmoney.com/api/qt/stock/get'
    params = {
        'fltt': '2',
        'invt': '2',
        'fields': 'f43,f44,f45,f46,f47,f48,f57,f58,f60,f169,f170',
        'secid': secid,
        '_': str(int(time.time() * 1000))
    }
    
    try:
        response = smart_session.get(url, params=params, timeout=10)
        
        if response and response.status_code == 200:
            data = json.loads(response.text)['data']
            if data:
                consecutive_failures = 0  # 重置失败计数
                return {
                    'code': data['f57'],
                    'name': data['f58'],
                    'price': data['f43'],
                    'open': data['f46'],
                    'high': data['f44'],
                    'low': data['f45'],
                    'yesterday_close': data['f60'],
                    'volume': data['f47'],
                    'amount': data['f48'],
                    'change': data['f169'],
                    'change_pct': data['f170'],
                    'update_time': datetime.now().strftime('%H:%M:%S')
                }
        
        consecutive_failures += 1
        return None
        
    except Exception as e:
        consecutive_failures += 1
        print(f"获取股票 {stock_code} 数据失败: {e}")
        return None

def load_stock_list():
    """加载股票列表"""
    try:
        df = pd.read_csv('stocks_list.csv', encoding='utf-8-sig')
        return df.to_dict('records')
    except Exception as e:
        print(f"加载股票列表失败: {e}")
        return []

def update_all_stocks():
    """智能更新所有股票数据"""
    global stock_data, last_update_time
    
    stock_list = load_stock_list()
    print(f"🚀 开始智能更新 {len(stock_list)} 只股票数据...")
    
    stock_data.clear()
    success_count = 0
    
    # 动态调整批次大小
    batch_size = ANTI_LIMIT_CONFIG['batch_config']['size']
    if consecutive_failures > 5:
        batch_size = max(3, batch_size // 2)  # 失败多时减小批次
        print(f"⚠️  调整批次大小为 {batch_size}")
    
    total_batches = (len(stock_list) + batch_size - 1) // batch_size
    
    for batch_idx in range(total_batches):
        start_idx = batch_idx * batch_size
        end_idx = min(start_idx + batch_size, len(stock_list))
        batch_stocks = stock_list[start_idx:end_idx]
        
        print(f"📦 处理第 {batch_idx + 1}/{total_batches} 批 ({len(batch_stocks)} 只股票)")
        
        batch_success = 0
        for stock in batch_stocks:
            code = str(stock['代码']).zfill(6)
            
            real_data = get_eastmoney_stock_data(code)
            
            if real_data:
                stock_data[code] = real_data
                success_count += 1
                batch_success += 1
                print(f"✅ {code} {real_data['name']} {real_data['price']}")
            else:
                stock_data[code] = {
                    'code': code,
                    'name': stock['名称'],
                    'price': 0, 'change': 0, 'change_pct': 0,
                    'volume': 0, 'amount': 0, 'high': 0, 'low': 0,
                    'open': 0, 'yesterday_close': 0,
                    'update_time': datetime.now().strftime('%H:%M:%S'),
                    'error': '暂无数据'
                }
                print(f"❌ {code} 获取失败")
            
            # 智能延时
            delay = adaptive_delay(consecutive_failures)
            time.sleep(delay)
        
        # 批次间延时
        if batch_idx < total_batches - 1:
            batch_delay = get_random_delay('batch')
            # 如果这批成功率低，增加延时
            if batch_success / len(batch_stocks) < 0.5:
                batch_delay *= 2
                print(f"⚠️  批次成功率低，增加延时...")
            
            print(f"⏱️  批次间等待 {batch_delay:.1f} 秒...")
            time.sleep(batch_delay)
    
    last_update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    success_rate = success_count / len(stock_list) * 100
    print(f"✅ 更新完成！成功率: {success_rate:.1f}% ({success_count}/{len(stock_list)})")

def background_update():
    """智能后台更新"""
    while True:
        try:
            update_all_stocks()
            
            # 根据成功率调整更新间隔
            if consecutive_failures > 10:
                interval = get_random_delay('update') * 2  # 失败多时延长间隔
                print(f"⚠️  连续失败较多，延长更新间隔到 {interval/60:.1f} 分钟")
            else:
                interval = get_random_delay('update')
            
            print(f"⏰ 下次更新将在 {interval/60:.1f} 分钟后...")
            time.sleep(interval)
            
        except Exception as e:
            print(f"❌ 后台更新出错: {e}")
            time.sleep(300)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/stocks')
def get_stocks():
    return jsonify({
        'stocks': list(stock_data.values()),
        'last_update': last_update_time,
        'total_count': len(stock_data),
        'success_rate': f"{(len([s for s in stock_data.values() if 'error' not in s]) / max(len(stock_data), 1) * 100):.1f}%"
    })

@app.route('/api/stock/<stock_code>')
def get_stock(stock_code):
    if stock_code in stock_data:
        return jsonify(stock_data[stock_code])
    else:
        return jsonify({'error': '股票代码不存在'}), 404

@app.route('/api/refresh')
def refresh_data():
    try:
        threading.Thread(target=update_all_stocks, daemon=True).start()
        return jsonify({
            'success': True,
            'message': '数据刷新已启动',
            'last_update': last_update_time
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'刷新失败: {str(e)}'
        }), 500

if __name__ == '__main__':
    print("=== 🚀 启动智能防限制股票行情系统 ===")
    print("🛡️  防限制策略已启用")
    
    # 初始加载数据
    print("📊 正在初始化数据...")
    update_all_stocks()
    
    # 启动后台更新线程
    print("🔄 启动智能后台更新...")
    update_thread = threading.Thread(target=background_update, daemon=True)
    update_thread.start()
    
    print("🌐 启动Flask Web服务...")
    print("🔗 访问 http://localhost:5001 查看股票行情")
    print("📈 数据来源：东方财富网（智能防限制版）")
    
    # 使用不同端口避免冲突
    app.run(debug=True, host='0.0.0.0', port=5001)
