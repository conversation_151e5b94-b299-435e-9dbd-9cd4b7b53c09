"""
改进的扫雷宝评分识别
基于发现页面中有很多100的情况，需要更精确的识别
"""

import requests
import re
import time
import os
from PIL import Image

def analyze_slb_page_content(stock_code: str):
    """分析扫雷宝页面内容"""
    
    print(f"🔍 分析 {stock_code} 的扫雷宝页面内容...")
    
    url = f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code={stock_code}&color=0"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'http://page3.tdx.com.cn:7615/',
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=15)
        
        if response.status_code == 200:
            content = response.text
            print(f"✅ 页面获取成功，长度: {len(content)} 字符")
            
            # 查找JavaScript中的关键变量
            print("\n🔍 查找关键JavaScript变量...")
            
            # 查找showvalue和realvalue
            showvalue_pattern = r'(?:var\s+)?showvalue\s*=\s*(\d{1,3})'
            realvalue_pattern = r'(?:var\s+)?realvalue\s*=\s*(\d{1,3})'
            
            showvalue_matches = re.findall(showvalue_pattern, content, re.IGNORECASE)
            realvalue_matches = re.findall(realvalue_pattern, content, re.IGNORECASE)
            
            print(f"showvalue匹配: {showvalue_matches}")
            print(f"realvalue匹配: {realvalue_matches}")
            
            # 查找Canvas绘制中的分数
            canvas_pattern = r'fillText\([^,]*?(\d{1,3})[^,]*?"分"'
            canvas_matches = re.findall(canvas_pattern, content, re.IGNORECASE)
            print(f"Canvas分数匹配: {canvas_matches}")
            
            # 查找所有可能的分数赋值
            score_assignment_patterns = [
                r'score\s*=\s*(\d{1,3})',
                r'安全分\s*=\s*(\d{1,3})',
                r'safeScore\s*=\s*(\d{1,3})',
                r'评分\s*=\s*(\d{1,3})',
            ]
            
            for pattern in score_assignment_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    print(f"分数赋值匹配 ({pattern}): {matches}")
            
            # 查找特定的分数显示逻辑
            print("\n🔍 查找分数显示逻辑...")
            
            # 查找包含分数的函数或代码块
            score_function_pattern = r'function[^{]*{[^}]*(\d{1,3})[^}]*分[^}]*}'
            score_function_matches = re.findall(score_function_pattern, content, re.IGNORECASE)
            if score_function_matches:
                print(f"分数函数匹配: {score_function_matches}")
            
            # 分析所有两位数和三位数的出现位置
            print("\n🔍 分析数字出现上下文...")
            
            all_numbers = re.finditer(r'\b(\d{2,3})\b', content)
            score_candidates = {}
            
            for match in all_numbers:
                number = int(match.group(1))
                if 50 <= number <= 100:  # 合理的评分范围
                    start = max(0, match.start() - 50)
                    end = min(len(content), match.end() + 50)
                    context = content[start:end]
                    
                    # 检查上下文是否包含评分相关关键词
                    score_keywords = ['分', 'score', '评分', '安全', 'safe', 'value', 'show', 'real']
                    keyword_count = sum(1 for keyword in score_keywords if keyword in context.lower())
                    
                    if keyword_count > 0:
                        if number not in score_candidates:
                            score_candidates[number] = 0
                        score_candidates[number] += keyword_count
            
            print("评分候选（按相关性排序）:")
            sorted_candidates = sorted(score_candidates.items(), key=lambda x: x[1], reverse=True)
            for score, relevance in sorted_candidates[:5]:
                print(f"  {score}分 (相关性: {relevance})")
            
            # 返回最可能的评分
            if sorted_candidates:
                best_score = sorted_candidates[0][0]
                print(f"\n🎯 推测评分: {best_score}分")
                return float(best_score)
            
            return None
            
    except Exception as e:
        print(f"❌ 页面分析失败: {e}")
        return None

def get_slb_score_with_selenium_wait(stock_code: str) -> float:
    """使用Selenium获取扫雷宝评分，增加等待时间"""
    
    print(f"🎯 使用Selenium获取 {stock_code} 的扫雷宝评分...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        from webdriver_manager.chrome import ChromeDriverManager
        
        # 配置浏览器
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1200,800')
        options.add_argument('--disable-blink-features=AutomationControlled')
        
        # 启动浏览器
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        
        try:
            # 访问页面
            url = f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code={stock_code}&color=0"
            print(f"🌐 访问: {url}")
            driver.get(url)
            
            # 等待Canvas元素加载
            print("⏳ 等待Canvas元素加载...")
            try:
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "canvas"))
                )
                print("✅ Canvas元素已加载")
            except:
                print("⚠️ Canvas元素加载超时")
            
            # 额外等待JavaScript执行
            print("⏳ 等待JavaScript执行...")
            time.sleep(5)
            
            # 执行JavaScript获取分数变量
            print("🔍 执行JavaScript获取分数...")
            
            js_commands = [
                "return typeof showvalue !== 'undefined' ? showvalue : null;",
                "return typeof realvalue !== 'undefined' ? realvalue : null;",
                "return typeof score !== 'undefined' ? score : null;",
                "return typeof safeScore !== 'undefined' ? safeScore : null;",
            ]
            
            for i, js_cmd in enumerate(js_commands):
                try:
                    result = driver.execute_script(js_cmd)
                    if result is not None:
                        score = float(result)
                        if 0 <= score <= 100:
                            print(f"✅ JavaScript获取评分: {score}分 (命令{i+1})")
                            return score
                except Exception as e:
                    print(f"JavaScript命令{i+1}失败: {e}")
            
            # 截图并OCR识别
            print("📸 截图并OCR识别...")
            screenshot_path = f"slb_selenium_{stock_code}.png"
            driver.save_screenshot(screenshot_path)
            
            if os.path.exists(screenshot_path):
                file_size = os.path.getsize(screenshot_path)
                print(f"截图大小: {file_size} 字节")
                
                # OCR识别
                score = ocr_extract_score(screenshot_path)
                if score is not None:
                    print(f"✅ OCR识别评分: {score}分")
                    return score
            
            return None
            
        finally:
            driver.quit()
            
    except Exception as e:
        print(f"❌ Selenium获取失败: {e}")
        return None

def ocr_extract_score(image_path: str) -> float:
    """OCR提取评分"""
    
    try:
        import pytesseract
        
        # 设置tesseract路径
        pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
        
        image = Image.open(image_path)
        
        # 多种OCR配置
        configs = [
            '--psm 6 -c tessedit_char_whitelist=0123456789分',
            '--psm 8 -c tessedit_char_whitelist=0123456789',
            '--psm 7 -c tessedit_char_whitelist=0123456789分',
            '--psm 13 -c tessedit_char_whitelist=0123456789',
        ]
        
        all_scores = []
        
        for config in configs:
            try:
                text = pytesseract.image_to_string(image, lang='chi_sim+eng', config=config)
                scores = extract_scores_from_text(text)
                all_scores.extend(scores)
            except:
                continue
        
        if all_scores:
            # 过滤合理分数
            valid_scores = [s for s in all_scores if 50 <= s <= 100]
            if valid_scores:
                from collections import Counter
                return float(Counter(valid_scores).most_common(1)[0][0])
        
        return None
        
    except Exception as e:
        print(f"OCR识别失败: {e}")
        return None

def extract_scores_from_text(text: str) -> list:
    """从文本中提取分数"""
    scores = []
    patterns = [r'(\d{1,3})分', r'(\d{1,3})\s*分', r'(\d{1,3})']
    
    for pattern in patterns:
        matches = re.findall(pattern, text)
        for match in matches:
            try:
                score = int(match)
                if 50 <= score <= 100:
                    scores.append(score)
            except:
                continue
    
    return scores

def test_improved_slb_recognition():
    """测试改进的扫雷宝识别"""
    
    test_stocks = ["300479", "000001", "600000"]
    
    print("🚀 改进的扫雷宝评分识别测试")
    print("=" * 50)
    
    for stock_code in test_stocks:
        print(f"\n--- 测试股票: {stock_code} ---")
        
        # 方法1: 页面内容分析
        print("方法1: 页面内容分析")
        score1 = analyze_slb_page_content(stock_code)
        
        # 方法2: Selenium + JavaScript
        print("\n方法2: Selenium + JavaScript")
        score2 = get_slb_score_with_selenium_wait(stock_code)
        
        # 汇总结果
        print(f"\n📊 {stock_code} 结果汇总:")
        print(f"  页面分析: {score1 if score1 else '失败'}")
        print(f"  Selenium: {score2 if score2 else '失败'}")
        
        # 选择最可信的结果
        if score1 and score2:
            if abs(score1 - score2) <= 5:
                final_score = (score1 + score2) / 2
                print(f"✅ 最终评分: {final_score:.1f}分 (两种方法一致)")
            else:
                print(f"⚠️ 两种方法结果差异较大")
        elif score1:
            print(f"✅ 最终评分: {score1}分 (页面分析)")
        elif score2:
            print(f"✅ 最终评分: {score2}分 (Selenium)")
        else:
            print(f"❌ 获取失败")
        
        time.sleep(2)

if __name__ == "__main__":
    test_improved_slb_recognition()
