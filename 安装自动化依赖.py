"""
自动化扫雷宝评分系统依赖安装脚本
"""

import subprocess
import sys
import os
import requests
import zipfile
from pathlib import Path

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ 成功安装: {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ 安装失败: {package}")
        return False

def check_chrome_installed():
    """检查Chrome是否已安装"""
    chrome_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        "/usr/bin/google-chrome",
        "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    ]
    
    for path in chrome_paths:
        if os.path.exists(path):
            print(f"✅ 找到Chrome浏览器: {path}")
            return True
    
    print("❌ 未找到Chrome浏览器")
    return False

def download_chromedriver():
    """下载ChromeDriver"""
    try:
        # 获取Chrome版本（简化版本，实际应该检测Chrome版本）
        print("🔍 下载ChromeDriver...")
        
        # 这里使用一个通用的ChromeDriver版本
        # 实际使用时应该根据Chrome版本选择对应的ChromeDriver
        driver_url = "https://chromedriver.storage.googleapis.com/114.0.5735.90/chromedriver_win32.zip"
        
        response = requests.get(driver_url, timeout=30)
        if response.status_code == 200:
            with open("chromedriver.zip", "wb") as f:
                f.write(response.content)
            
            # 解压
            with zipfile.ZipFile("chromedriver.zip", 'r') as zip_ref:
                zip_ref.extractall(".")
            
            os.remove("chromedriver.zip")
            print("✅ ChromeDriver下载完成")
            return True
        else:
            print("❌ ChromeDriver下载失败")
            return False
            
    except Exception as e:
        print(f"❌ ChromeDriver下载出错: {e}")
        return False

def check_tesseract():
    """检查Tesseract OCR是否已安装"""
    tesseract_paths = [
        r"C:\Program Files\Tesseract-OCR\tesseract.exe",
        r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
        "/usr/bin/tesseract",
        "/usr/local/bin/tesseract"
    ]
    
    for path in tesseract_paths:
        if os.path.exists(path):
            print(f"✅ 找到Tesseract OCR: {path}")
            return True
    
    print("❌ 未找到Tesseract OCR")
    return False

def main():
    """主安装流程"""
    print("🚀 开始安装自动化扫雷宝评分系统依赖")
    print("=" * 60)
    
    # 1. 安装Python包
    print("\n📦 安装Python包...")
    packages = [
        "selenium",
        "pytesseract", 
        "Pillow",
        "requests"
    ]
    
    for package in packages:
        install_package(package)
    
    # 2. 检查Chrome浏览器
    print("\n🌐 检查Chrome浏览器...")
    if not check_chrome_installed():
        print("💡 请手动安装Chrome浏览器:")
        print("   https://www.google.com/chrome/")
    
    # 3. 下载ChromeDriver（可选，selenium会自动管理）
    print("\n🚗 检查ChromeDriver...")
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        
        # 使用webdriver-manager自动管理ChromeDriver
        install_package("webdriver-manager")
        print("✅ 将使用webdriver-manager自动管理ChromeDriver")
        
    except ImportError:
        print("⚠️ 建议安装webdriver-manager: pip install webdriver-manager")
    
    # 4. 检查Tesseract OCR
    print("\n👁️ 检查Tesseract OCR...")
    if not check_tesseract():
        print("💡 请手动安装Tesseract OCR:")
        print("   Windows: https://github.com/UB-Mannheim/tesseract/wiki")
        print("   或下载: https://digi.bib.uni-mannheim.de/tesseract/")
        print("   Ubuntu: sudo apt install tesseract-ocr tesseract-ocr-chi-sim")
        print("   macOS: brew install tesseract")
    
    print("\n" + "=" * 60)
    print("✅ 依赖检查完成！")
    print("\n📋 安装总结:")
    print("   ✅ Python包: selenium, pytesseract, Pillow, requests")
    print("   🌐 Chrome浏览器: 需要手动安装")
    print("   🚗 ChromeDriver: 自动管理")
    print("   👁️ Tesseract OCR: 需要手动安装")
    
    print("\n🚀 安装完成后，运行以下命令测试:")
    print("   python 自动化扫雷宝评分.py")

if __name__ == "__main__":
    main()
