"""
调试页面访问问题
"""

import time

def test_page_access_step_by_step():
    """逐步测试页面访问"""
    
    print("🔍 逐步调试页面访问...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        
        # 配置浏览器 - 使用可见模式便于调试
        options = Options()
        # 不使用headless模式，这样可以看到浏览器
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-web-security')
        options.add_argument('--allow-running-insecure-content')
        options.add_argument('--window-size=1200,800')
        
        print("🔄 启动Chrome浏览器（可见模式）...")
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        
        print("✅ 浏览器启动成功！")
        
        # 先测试访问百度
        print("🔄 测试访问百度...")
        driver.get("https://www.baidu.com")
        time.sleep(2)
        
        title = driver.title
        print(f"✅ 百度访问成功，标题: {title}")
        
        # 测试访问扫雷宝页面
        print("🔄 测试访问扫雷宝页面...")
        slb_url = "http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code=300479&color=0"
        print(f"URL: {slb_url}")
        
        driver.get(slb_url)
        print("✅ 页面访问命令已发送")
        
        # 等待页面加载
        print("⏳ 等待页面加载...")
        for i in range(10):
            time.sleep(1)
            current_url = driver.current_url
            title = driver.title
            print(f"第{i+1}秒 - URL: {current_url}")
            print(f"第{i+1}秒 - 标题: '{title}'")
            
            # 检查页面源码
            if i == 5:  # 5秒后检查页面内容
                try:
                    page_source = driver.page_source
                    print(f"页面源码长度: {len(page_source)} 字符")
                    
                    # 检查关键内容
                    if 'canvas' in page_source.lower():
                        print("✅ 找到Canvas元素")
                    else:
                        print("❌ 未找到Canvas元素")
                    
                    if 'showvalue' in page_source or 'realvalue' in page_source:
                        print("✅ 找到分数变量")
                    else:
                        print("❌ 未找到分数变量")
                    
                    # 显示页面源码片段
                    print("页面源码片段:")
                    print(page_source[:500] + "...")
                    
                except Exception as e:
                    print(f"获取页面源码失败: {e}")
        
        # 尝试截图
        print("📸 尝试截图...")
        try:
            screenshot_path = "debug_screenshot.png"
            driver.save_screenshot(screenshot_path)
            
            import os
            if os.path.exists(screenshot_path):
                file_size = os.path.getsize(screenshot_path)
                print(f"✅ 截图成功，大小: {file_size} 字节")
            else:
                print("❌ 截图文件未生成")
                
        except Exception as e:
            print(f"❌ 截图失败: {e}")
        
        # 等待用户观察
        print("\n💡 请观察浏览器窗口，看看页面是否正常加载")
        print("💡 按回车键继续...")
        input()
        
        # 关闭浏览器
        driver.quit()
        print("✅ 浏览器已关闭")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_direct_http_request():
    """测试直接HTTP请求"""
    
    print("\n🔍 测试直接HTTP请求...")
    
    try:
        import requests
        
        url = "http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code=300479&color=0"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }
        
        print(f"🔄 发送HTTP请求到: {url}")
        response = requests.get(url, headers=headers, timeout=10)
        
        print(f"✅ HTTP响应状态: {response.status_code}")
        print(f"✅ 响应大小: {len(response.text)} 字符")
        
        if response.status_code == 200:
            content = response.text
            
            # 检查关键内容
            if 'canvas' in content.lower():
                print("✅ HTTP响应包含Canvas元素")
            else:
                print("❌ HTTP响应不包含Canvas元素")
            
            if 'showvalue' in content or 'realvalue' in content:
                print("✅ HTTP响应包含分数变量")
            else:
                print("❌ HTTP响应不包含分数变量")
            
            # 显示内容片段
            print("HTTP响应内容片段:")
            print(content[:500] + "...")
            
            # 查找数字
            import re
            numbers = re.findall(r'\b\d{2,3}\b', content)
            if numbers:
                print(f"找到的数字: {numbers[:10]}")
            
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ HTTP请求出错: {e}")

def test_different_urls():
    """测试不同的URL"""
    
    print("\n🔍 测试不同的扫雷宝URL...")
    
    urls = [
        "http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code=300479&color=0",
        "http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code=300479",
        "http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code=000001&color=0",
    ]
    
    import requests
    
    for i, url in enumerate(urls):
        print(f"\n测试URL {i+1}: {url}")
        
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            print(f"状态码: {response.status_code}")
            print(f"内容长度: {len(response.text)} 字符")
            
            if response.status_code == 200:
                content = response.text
                if 'canvas' in content.lower():
                    print("✅ 包含Canvas")
                if 'showvalue' in content or 'realvalue' in content:
                    print("✅ 包含分数变量")
            
        except Exception as e:
            print(f"❌ 请求失败: {e}")

def main():
    """主测试函数"""
    print("🔍 页面访问调试工具")
    print("=" * 50)
    
    # 1. 测试直接HTTP请求
    test_direct_http_request()
    
    # 2. 测试不同URL
    test_different_urls()
    
    # 3. 逐步测试浏览器访问
    print("\n" + "=" * 50)
    print("现在测试浏览器访问（会打开浏览器窗口）")
    choice = input("是否继续？(y/n): ").strip().lower()
    
    if choice == 'y':
        test_page_access_step_by_step()
    else:
        print("跳过浏览器测试")

if __name__ == "__main__":
    main()
