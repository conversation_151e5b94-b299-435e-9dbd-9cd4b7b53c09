# 卖出信号持久化解决方案

## 问题分析

### 根本原因
1. **策略配置不完整**：原始的 `strategy_config.json` 文件只包含了6个策略，但代码中定义了10个策略
2. **配置覆盖问题**：每次重启时，系统会先加载默认策略，然后用配置文件覆盖，导致新策略被重置
3. **缺失的重要策略**：
   - `high_gain_sell`（高涨幅卖出策略）
   - `high_ttm_medium_gain`（高估值中涨幅策略）
   - `limit_up_high_pb_clearance`（涨停高估值清仓策略）
   - `extreme_ttm_profit_clearance`（极值TTM回本清仓策略）

## 解决方案

### 1. 完整的策略配置文件
已创建包含所有10个策略的完整配置文件 `strategy_config.json`，包括：

**启用的策略（6个）：**
- ✓ 距最低点涨幅策略 (technical)
- ✓ PE异常策略 (fundamental)
- ✓ 高涨幅卖出策略 (gain)
- ✓ 高估值中涨幅策略 (valuation)
- ✓ 涨停高估值清仓策略 (clearance)
- ✓ 极值TTM回本清仓策略 (clearance)

**禁用的策略（4个）：**
- ✗ PB高估策略 (fundamental)
- ✗ 盈利目标策略 (profit)
- ✗ 股息率过低策略 (fundamental)
- ✗ 扫雷高风险策略 (risk)

### 2. 增强的持久化机制

#### 启动时自动检查和修复
- 程序启动时会自动检查策略配置的完整性
- 如果发现缺失策略，会自动添加并保存
- 保留用户的自定义设置（如enabled状态和参数）

#### 智能配置加载
```python
def load_strategy_config(self):
    """从文件加载策略配置"""
    # 检查配置文件完整性
    # 自动补充缺失策略
    # 保存完整配置
```

#### 自动备份机制
- 每次修复配置时会自动创建备份文件
- 备份文件命名格式：`strategy_config_backup_YYYYMMDD_HHMMSS.json`

### 3. 配置修复工具

#### `fix_strategy_config.py`
专用的配置修复工具，功能包括：
- 检查配置文件完整性
- 自动补充缺失策略
- 保留用户自定义设置
- 创建配置备份
- 生成详细报告

#### `create_strategy_config.py`
用于创建标准配置文件的工具

## 使用方法

### 立即修复（推荐）
```bash
# 运行配置修复工具
python fix_strategy_config.py

# 重启持仓系统
python "持仓系统_v13 - 副本 - 副本.py"
```

### 手动验证
```bash
# 检查配置文件
cat strategy_config.json

# 查看启用的策略数量（应该是10个策略）
python -c "import json; config=json.load(open('strategy_config.json')); print(f'总策略: {len(config)}, 启用: {sum(1 for c in config.values() if c.get(\"enabled\", False))}')"
```

## 技术细节

### 配置文件结构
```json
{
  "strategy_id": {
    "name": "策略名称",
    "description": "策略描述",
    "enabled": true/false,
    "params": {
      "参数名": 参数值
    },
    "weight": 权重,
    "category": "分类"
  }
}
```

### 持久化流程
1. **启动时**：加载配置 → 检查完整性 → 自动修复 → 保存
2. **运行时**：用户修改 → 实时保存 → 刷新信号
3. **重启后**：配置保持不变

### 关键改进
1. **完整性检查**：确保所有策略都在配置文件中
2. **智能合并**：保留用户设置，补充默认值
3. **自动备份**：防止配置丢失
4. **详细日志**：便于问题诊断

## 验证方法

### 1. 检查配置文件
```bash
# 应该看到10个策略
wc -l strategy_config.json
```

### 2. 检查启动日志
启动系统时应该看到：
```
⚙️ 正在初始化策略配置...
✅ 策略配置已保存并同步
🔥 当前启用的策略 (6 个):
  - 距最低点涨幅策略 (technical)
  - PE异常策略 (fundamental)
  - 高涨幅卖出策略 (gain)
  - 高估值中涨幅策略 (valuation)
  - 涨停高估值清仓策略 (clearance)
  - 极值TTM回本清仓策略 (clearance)
```

### 3. 测试持久化
1. 修改策略设置
2. 重启系统
3. 验证设置是否保持

## 故障排除

### 如果配置仍然丢失
1. 检查文件权限：`ls -la strategy_config.json`
2. 运行修复工具：`python fix_strategy_config.py`
3. 检查程序启动日志

### 如果策略不生效
1. 确认策略已启用：检查 `enabled: true`
2. 检查参数设置：确认阈值合理
3. 查看调试日志：观察策略评估过程

## 总结

通过这个解决方案，卖出信号的配置将会：
- ✅ **完整保存**：所有10个策略都会被正确保存
- ✅ **持久保持**：重启后配置不会丢失
- ✅ **自动修复**：系统会自动检查和修复配置
- ✅ **用户友好**：保留用户的自定义设置
- ✅ **安全可靠**：自动备份防止数据丢失

现在你可以放心地修改卖出信号设置，它们将在重启后完美保持！
