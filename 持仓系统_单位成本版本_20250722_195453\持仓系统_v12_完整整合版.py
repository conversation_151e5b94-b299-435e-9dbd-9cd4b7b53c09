#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持仓系统 V12 完整整合版
=======================

整合所有版本功能的完整版本：
- V1-V1.2: 基础功能（股价、年内最低价、股息率、行业信息）
- V2: 智能交易时间监测
- V3: 持仓数量和市值计算
- V4: 数据管理功能（Excel上传、清空数据）
- V5: 分类筛选功能
- V6: 统计分析功能
- V7: 界面优化
- V8: 饼图可视化
- V9: 移动端适配
- V10-V11: 扫雷功能和年内最低价缓存

作者: AI Assistant
版本: 12.0
日期: 2025-01-22
"""

from flask import Flask, render_template_string, jsonify, request
import requests
import time
import json
import pandas as pd
import os
from datetime import datetime, timedelta
import threading
from urllib.parse import urlencode
from werkzeug.utils import secure_filename
from wechat_alert import WeChatAlert
from A股交易时间监测_简化版 import AStockTradingTimeMonitor
from yearly_low_cache_reader import get_yearly_low_from_cache, check_cache_status, is_cache_available
from tdx_scan_module import TdxScanClient

app = Flask(__name__)

# 配置文件上传
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
ALLOWED_EXTENSIONS = {'xlsx', 'xls'}

# 确保上传目录存在
if not os.path.exists(app.config['UPLOAD_FOLDER']):
    os.makedirs(app.config['UPLOAD_FOLDER'])

# 全局变量存储股票数据
stock_data = {}
last_update_time = None
imported_stock_list = []  # 存储导入的股票列表

# 配置
CONFIG = {
    'stock_interval': 0.1,      # 每只股票间隔时间（秒）
    'round_interval': 600,      # 每轮更新间隔时间（秒）
    'request_timeout': 15,      # 请求超时时间（秒）
    'alert_threshold': 70.0,    # 企业微信提醒阈值（距离最低点涨幅%）
}

# 企业微信提醒配置
WECHAT_WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7bddd706-ef0f-4d6c-9e1f-3cc8a0a18d9e"

# 初始化企业微信提醒
wechat_alert = WeChatAlert(WECHAT_WEBHOOK_URL)

# 初始化A股交易时间监测器
trading_monitor = AStockTradingTimeMonitor()

# 初始化扫雷客户端
scan_client = TdxScanClient(cache_duration=1800)  # 缓存30分钟

def get_security_type(code):
    """根据代码判断证券类型"""
    code_str = str(code)

    # 只保留股票，过滤其他类型
    if code_str.startswith('1'):  # ETF
        return 'ETF'
    elif code_str.startswith('5'):  # 基金
        return '基金'
    elif code_str.startswith('11') or code_str.startswith('12') or code_str.startswith('13'):  # 可转债
        return '可转债'
    elif code_str.startswith('4'):  # 新三板等
        return '其他'
    else:
        return '股票'

def is_stock_only(code):
    """判断是否为股票（用于筛选）"""
    code_str = str(code).zfill(6)

    # 只保留6位数字的A股股票
    if len(code_str) != 6 or not code_str.isdigit():
        return False

    # 排除ETF、基金、可转债等
    if (code_str.startswith('1') or  # ETF和基金
        code_str.startswith('5') or  # 基金
        code_str.startswith('11') or  # 可转债
        code_str.startswith('12') or  # 可转债
        code_str.startswith('13') or  # 可转债
        code_str.startswith('4')):   # 新三板等
        return False

    # 只保留沪深A股
    if code_str.startswith('6'):  # 沪市A股
        return True
    elif code_str.startswith('0') or code_str.startswith('3'):  # 深市A股和创业板
        return True

    return False

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def gen_secid(rawcode: str) -> str:
    """生成东方财富专用的secid"""
    if rawcode[0] == '6':
        return f'1.{rawcode}'  # 沪市
    return f'0.{rawcode}'      # 深市

def get_yearly_low_price_original(code: str) -> dict:
    """获取股票一年内的最低价（前复权）- 原始实现"""
    
    # 计算一年前的日期
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365)
    
    start_date_str = start_date.strftime('%Y%m%d')
    end_date_str = end_date.strftime('%Y%m%d')
    
    secid = gen_secid(code)
    
    params = {
        'fields1': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13',
        'fields2': 'f51,f52,f53,f54,f55',
        'beg': start_date_str,
        'end': end_date_str,
        'rtntype': '6',
        'secid': secid,
        'klt': '101',  # 日K
        'fqt': '1',    # 前复权
    }
    
    base_url = 'https://push2his.eastmoney.com/api/qt/stock/kline/get'
    url = base_url + '?' + urlencode(params)
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; WOW64; Trident/7.0; Touch; rv:11.0) like Gecko',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.8',
        'Referer': 'http://quote.eastmoney.com/',
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=CONFIG['request_timeout'])
        
        if response.status_code == 200:
            json_data = response.json()
            
            if 'data' in json_data and json_data['data'] and 'klines' in json_data['data']:
                klines = json_data['data']['klines']
                
                if klines:
                    # 找到最低价
                    min_low = float('inf')
                    low_date = None
                    
                    for kline in klines:
                        kline_data = kline.split(',')
                        if len(kline_data) >= 5:
                            low_price = float(kline_data[4])  # 最低价
                            if low_price < min_low:
                                min_low = low_price
                                low_date = kline_data[0]  # 日期
                    
                    return {
                        'yearly_low': min_low,
                        'low_date': low_date,
                        'data_points': len(klines)
                    }
        
        return {'yearly_low': None, 'low_date': None, 'data_points': 0}
        
    except Exception as e:
        print(f"获取股票 {code} 历史数据失败: {e}")
        return {'yearly_low': None, 'low_date': None, 'data_points': 0}

def get_yearly_low_price(code: str) -> dict:
    """获取股票一年内的最低价（前复权）- 优先使用缓存"""
    
    # 首先尝试从缓存获取
    try:
        cache_result = get_yearly_low_from_cache(code)
        if cache_result['success']:
            return {
                'yearly_low': cache_result['yearly_low'],
                'low_date': cache_result['low_date'],
                'data_points': cache_result['data_points']
            }
    except Exception as e:
        print(f"从缓存获取 {code} 年内最低价失败: {e}")
    
    # 缓存获取失败，使用原始方法
    print(f"⚠️ 缓存获取失败，使用实时API获取 {code} 年内最低价...")
    return get_yearly_low_price_original(code)

def get_eastmoney_stock_data_v2(stock_code):
    """
    从东方财富网获取单只股票实时数据 - 使用验证过的V2版本
    包含正确的股息率和行业信息
    """
    # 根据股票代码确定市场
    if stock_code.startswith('6'):
        secid = f'1.{stock_code}'
    else:
        secid = f'0.{stock_code}'

    # 使用批量接口获取更准确的股息率数据
    url = 'http://push2.eastmoney.com/api/qt/ulist.np/get'

    # 请求参数 - 使用正确的股息率字段 f133
    params = {
        'fltt': '2',
        'invt': '2',
        'fields': 'f12,f14,f2,f3,f4,f23,f115,f114,f9,f133,f162,f173,f116,f127,f128,f129,f47,f48',
        'secids': secid,
        '_': str(int(time.time() * 1000))
    }

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Referer': 'http://quote.eastmoney.com/',
    }

    try:
        response = requests.get(url, params=params, headers=headers, timeout=CONFIG['request_timeout'])

        if response.status_code == 200:
            data = json.loads(response.text)

            if 'data' in data and 'diff' in data['data'] and len(data['data']['diff']) > 0:
                stock_info = data['data']['diff'][0]

                # 处理股息率 - f133字段
                dividend_yield = stock_info.get('f133', 0)
                if dividend_yield == '-' or dividend_yield is None:
                    dividend_yield = 0
                else:
                    try:
                        dividend_yield = float(dividend_yield)
                    except:
                        dividend_yield = 0

                # 处理行业信息 - f127字段在这个接口中不是行业，需要单独获取
                industry = ''  # 先设为空，后面单独获取

                return {
                    'code': stock_info.get('f12', ''),
                    'name': stock_info.get('f14', ''),
                    'price': stock_info.get('f2', 0),
                    'change': stock_info.get('f4', 0),
                    'change_pct': stock_info.get('f3', 0),
                    'dividend_yield': dividend_yield,
                    'pb_ratio': stock_info.get('f23', None) if stock_info.get('f23') != '-' else None,
                    'pe_ttm': stock_info.get('f115', None) if stock_info.get('f115') != '-' else None,
                    'industry': industry,
                    'volume': stock_info.get('f47', 0),
                    'turnover': stock_info.get('f48', 0),
                    'market_cap': stock_info.get('f116', 0) if stock_info.get('f116') != '-' else 0,
                }

    except Exception as e:
        print(f"获取股票 {stock_code} 数据失败: {e}")

    return None

def get_stock_industry_info(stock_code):
    """
    获取股票行业信息 - 使用单独的接口
    """
    # 根据股票代码确定市场
    if stock_code.startswith('6'):
        secid = f'1.{stock_code}'
    else:
        secid = f'0.{stock_code}'

    # 东方财富API URL - 获取详细信息
    url = 'http://push2.eastmoney.com/api/qt/stock/get'

    params = {
        'fltt': '2',
        'invt': '2',
        'fields': 'f127,f128,f129',  # 行业、板块、概念
        'secid': secid,
        '_': str(int(time.time() * 1000))
    }

    try:
        response = requests.get(url, params=params, timeout=CONFIG['request_timeout'])

        if response.status_code == 200:
            data = json.loads(response.text)['data']

            if data:
                return {
                    'industry': data.get('f127', ''),      # 行业
                    'sector': data.get('f128', ''),        # 板块
                    'concept': data.get('f129', ''),       # 概念
                }

    except Exception as e:
        print(f"获取股票 {stock_code} 行业信息失败: {e}")

    return {'industry': '', 'sector': '', 'concept': ''}

def get_stock_info(code: str) -> dict:
    """获取股票基本信息 - 整合版本"""

    # 获取实时数据（使用验证过的V2方法）
    real_data = get_eastmoney_stock_data_v2(code)
    if not real_data:
        return None

    # 获取年内最低价
    yearly_low_info = get_yearly_low_price(code)

    # 获取行业信息（如果主接口没有获取到）
    if not real_data.get('industry'):
        industry_data = get_stock_industry_info(code)
        real_data['industry'] = industry_data.get('industry', '')

    # 计算距离最低点的涨幅
    yearly_low = yearly_low_info.get('yearly_low')
    current_price = real_data['price']
    distance_from_low_pct = None

    if yearly_low and yearly_low > 0 and current_price > 0:
        distance_from_low_pct = ((current_price - yearly_low) / yearly_low) * 100

    return {
        'code': code,
        'name': real_data['name'],
        'price': current_price,
        'change': real_data['change'],
        'change_pct': real_data['change_pct'],
        'volume': real_data.get('volume', 0),
        'turnover': real_data.get('turnover', 0),
        'pe_ratio': real_data['pe_ttm'],
        'pb_ratio': real_data['pb_ratio'],
        'dividend_yield': real_data['dividend_yield'],
        'market_cap': real_data.get('market_cap', 0),
        'industry': real_data['industry'],
        'yearly_low': yearly_low,
        'low_date': yearly_low_info.get('low_date'),
        'distance_from_low_pct': distance_from_low_pct,
        'security_type': get_security_type(code),
        'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }

def load_stock_list():
    """加载股票列表 - 只加载股票，过滤其他证券类型"""
    global imported_stock_list

    # 优先使用导入的股票列表
    if imported_stock_list:
        # 过滤只保留股票
        filtered_list = []
        for stock in imported_stock_list:
            code = str(stock['代码']).zfill(6)
            if is_stock_only(code):
                filtered_list.append(stock)
            else:
                print(f"⚠️ 跳过非股票证券: {code} {stock.get('名称', '')}")
        print(f"📊 过滤后保留 {len(filtered_list)} 只股票")
        return filtered_list

    # 否则尝试从CSV文件加载
    csv_file = 'stocks_list.csv'
    if os.path.exists(csv_file):
        try:
            df = pd.read_csv(csv_file, encoding='utf-8-sig')
            print(f"📊 CSV文件列名: {list(df.columns)}")
            stock_list = []
            for _, row in df.iterrows():
                code = str(row['代码']).zfill(6)
                if is_stock_only(code):
                    # 支持多种持仓数量列名
                    holdings = 0
                    found_col = None
                    for col in ['持有数量', '持仓数量', '持仓', '数量', 'quantity']:
                        if col in row and pd.notna(row[col]):
                            holdings = int(float(row[col]))  # 先转float再转int，处理小数
                            found_col = col
                            break

                    if found_col and holdings > 0:
                        print(f"   📈 {code}: 从列'{found_col}'读取持仓 {holdings}")

                    stock_list.append({
                        '代码': code,
                        '名称': row.get('名称', ''),
                        '持仓数量': holdings
                    })
                else:
                    print(f"⚠️ 跳过非股票证券: {code} {row.get('名称', '')}")
            return stock_list
        except Exception as e:
            print(f"加载股票列表失败: {e}")

    return []

def update_single_stock(stock_info):
    """更新单只股票数据"""
    global stock_data

    code = stock_info['代码']
    name = stock_info.get('名称', '')
    holdings = stock_info.get('持仓数量', 0)
    unit_cost = stock_info.get('单位成本', 0)  # 获取单位成本

    print(f"正在处理 {code} {name}，持仓数量: {holdings}，单位成本: {unit_cost}...")

    # 获取股票信息
    info = get_stock_info(code)

    if info:
        # 添加持仓数量和成本信息
        info['holdings'] = int(holdings)  # 使用holdings作为键名
        info['quantity'] = int(holdings)  # 同时保留quantity以兼容前端
        info['unit_cost'] = float(unit_cost) if unit_cost > 0 else 0  # 添加单位成本
        info['name'] = name if name else info.get('name', '')

        # 计算市值
        current_price = info['price']
        if holdings > 0 and current_price > 0:
            info['market_value'] = round(current_price * holdings, 2)
            print(f"   💰 市值计算: {current_price} × {holdings} = {info['market_value']}")
        else:
            info['market_value'] = 0
            print(f"   ⚠️ 市值为0: 价格={current_price}, 持仓={holdings}")

        # 获取扫雷数据
        try:
            scan_result = scan_client.get_stock_score(code)
            if scan_result:
                info['scan_score'] = scan_result['score']
                info['scan_risk_level'] = scan_result['risk_level']
                info['scan_risk_color'] = scan_result['risk_color']
                info['scan_triggered_risks'] = scan_result['triggered_risks']
                info['scan_display_text'] = f"{scan_result['score']}分"
                print(f"🔍 扫雷: {scan_result['score']}分 ({scan_result['risk_level']})")
            else:
                # 添加默认扫雷数据
                info['scan_score'] = 0
                info['scan_risk_level'] = '数据异常'
                info['scan_risk_color'] = '#cccccc'
                info['scan_triggered_risks'] = 0
                info['scan_display_text'] = '获取失败'
        except Exception as e:
            print(f"获取扫雷数据失败: {e}")
            # 添加默认扫雷数据
            info['scan_score'] = 0
            info['scan_risk_level'] = '数据异常'
            info['scan_risk_color'] = '#cccccc'
            info['scan_triggered_risks'] = 0
            info['scan_display_text'] = '获取失败'

        # 存储到全局数据
        stock_data[code] = info

        print(f"✅ {code} {info['name']} 价格: {info['price']:.2f} 涨跌: {info['change_pct']:.2f}%")

        # 检查是否需要发送企业微信提醒
        if info.get('distance_from_low_pct') and info['distance_from_low_pct'] >= CONFIG['alert_threshold']:
            try:
                message = f"📈 高涨幅提醒\n股票: {info['name']} ({code})\n当前价格: {info['price']:.2f}元\n距最低点涨幅: {info['distance_from_low_pct']:.1f}%\n年内最低价: {info['yearly_low']:.2f}元 ({info['low_date']})"
                wechat_alert.send_message(message)
                print(f"📱 已发送企业微信提醒: {info['name']} 涨幅 {info['distance_from_low_pct']:.1f}%")
            except Exception as e:
                print(f"发送企业微信提醒失败: {e}")
    else:
        print(f"❌ {code} {name} 获取数据失败")

def update_all_stocks():
    """更新所有股票数据"""
    global last_update_time

    stock_list = load_stock_list()
    if not stock_list:
        print("❌ 没有找到股票列表")
        print(f"   导入列表状态: {len(imported_stock_list)} 只股票")
        return

    print(f"\n🚀 开始更新 {len(stock_list)} 只股票数据...")
    print(f"📋 股票列表预览:")
    for i, stock in enumerate(stock_list[:3]):  # 显示前3只股票
        print(f"   {i+1}. {stock['代码']} {stock.get('名称', '')} 持仓:{stock.get('持仓数量', 0)}股")
    start_time = time.time()
    success_count = 0

    for i, stock in enumerate(stock_list, 1):
        print(f"\n[{i}/{len(stock_list)}] ", end="")

        try:
            update_single_stock(stock)
            success_count += 1
        except Exception as e:
            print(f"❌ 更新失败: {e}")

        # 间隔时间
        if i < len(stock_list):
            time.sleep(CONFIG['stock_interval'])

    end_time = time.time()
    elapsed_time = end_time - start_time
    last_update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    success_rate = success_count / len(stock_list) * 100

    print(f"\n✅ 更新完成！")
    print(f"   📈 成功率: {success_rate:.1f}% ({success_count}/{len(stock_list)})")
    print(f"   ⏱️ 耗时: {elapsed_time:.1f}秒")
    print(f"   🕐 更新时间: {last_update_time}")

def background_update():
    """后台智能更新股票数据 - 只在交易时间更新"""
    round_count = 0

    while True:
        round_count += 1
        current_time = datetime.now()

        # 获取交易状态
        trading_status = trading_monitor.get_trading_status()
        should_update = trading_monitor.should_update_data(include_auction=True)  # 包含集合竞价
        update_interval = trading_monitor.get_update_interval()

        print(f"\n{'='*60}")
        print(f"🔄 第 {round_count} 轮检查 - {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📊 交易状态: {trading_status['message']}")
        print(f"{'='*60}")

        if should_update:
            print(f"✅ 当前为交易时间，开始更新股票数据...")
            update_all_stocks()
            print(f"✅ 股票数据更新完成")
        else:
            print(f"⏸️  当前为休市时间，跳过数据更新")
            print(f"📅 {trading_status['message']}")

        print(f"\n⏰ 下一轮检查将在 {update_interval} 秒后开始")
        if update_interval >= 60:
            print(f"   (约 {update_interval/60:.1f} 分钟后)")

        time.sleep(update_interval)

# Flask路由
@app.route('/')
def index():
    """主页面"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/stocks')
def get_stocks():
    """获取股票数据API"""
    try:
        # 计算统计数据
        stats = calculate_statistics()

        return jsonify({
            'success': True,
            'data': list(stock_data.values()),
            'last_update': last_update_time,
            'count': len(stock_data),
            'stats': stats
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取数据失败: {str(e)}'
        }), 500

@app.route('/api/trading-status')
def get_trading_status():
    """获取交易状态API"""
    try:
        status = trading_monitor.get_trading_status()
        return jsonify({
            'success': True,
            'data': status
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取交易状态失败: {str(e)}'
        }), 500

@app.route('/api/refresh-holidays', methods=['POST'])
def refresh_holidays():
    """刷新节假日数据API"""
    try:
        trading_monitor.refresh_holidays()
        return jsonify({
            'success': True,
            'message': '节假日数据已刷新'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'刷新失败: {str(e)}'
        }), 500

@app.route('/api/clear-data', methods=['POST'])
def clear_data():
    """清空数据API"""
    try:
        global stock_data, last_update_time
        stock_data = {}
        last_update_time = None

        return jsonify({
            'success': True,
            'message': '数据已清空'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'清空失败: {str(e)}'
        }), 500

@app.route('/api/upload-holdings', methods=['POST'])
def upload_holdings():
    """上传持仓表格API"""
    try:
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'message': '没有选择文件'
            }), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'success': False,
                'message': '没有选择文件'
            }), 400

        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{timestamp}_{filename}"
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)

            # 读取Excel文件
            try:
                df = pd.read_excel(filepath)

                # 验证必要的列 - 支持多种列名
                code_column = None
                quantity_column = None

                # 查找代码列
                for col in ['代码', 'code', '股票代码']:
                    if col in df.columns:
                        code_column = col
                        break

                # 查找数量列
                for col in ['持有数量', '持仓数量', '数量', 'quantity', '股数']:
                    if col in df.columns:
                        quantity_column = col
                        break

                if not code_column or not quantity_column:
                    return jsonify({
                        'success': False,
                        'message': f'缺少必要的列。需要包含代码列（代码/code/股票代码）和数量列（持有数量/持仓数量/数量/quantity/股数）'
                    }), 400

                # 处理数据 - 只处理股票，使用正确的列名
                global imported_stock_list
                imported_stock_list = []
                skipped_count = 0

                # 首先过滤掉代码列为空的行
                df = df.dropna(subset=[code_column])

                # 确保代码列转换为字符串
                df[code_column] = df[code_column].astype(str)

                # 处理持有数量，确保是数值类型
                df[quantity_column] = pd.to_numeric(df[quantity_column], errors='coerce').fillna(0).astype(int)

                # 过滤掉持有数量为0或负数的股票
                df = df[df[quantity_column] > 0]

                for _, row in df.iterrows():
                    code = str(row[code_column]).zfill(6)
                    quantity = int(row[quantity_column])
                    name = str(row.get('名称', '')) if pd.notna(row.get('名称', '')) else ''

                    # 读取单位成本
                    unit_cost = 0
                    for cost_col in ['单位成本', '成本价', '成本', 'unit_cost']:
                        if cost_col in row and pd.notna(row[cost_col]):
                            unit_cost = float(row[cost_col])
                            break

                    if len(code) == 6 and code.isdigit() and quantity > 0:
                        if is_stock_only(code):
                            imported_stock_list.append({
                                '代码': code,
                                '名称': name,
                                '持仓数量': quantity,
                                '单位成本': unit_cost
                            })
                        else:
                            skipped_count += 1
                            print(f"⚠️ 跳过非股票证券: {code} {name}")

                message = f'成功导入 {len(imported_stock_list)} 只股票'
                if skipped_count > 0:
                    message += f'，跳过 {skipped_count} 只非股票证券'

                # 上传成功后立即更新股票数据
                print(f"📈 开始更新股票数据...")
                update_all_stocks()

                return jsonify({
                    'success': True,
                    'message': message,
                    'count': len(imported_stock_list)
                })

            except Exception as e:
                return jsonify({
                    'success': False,
                    'message': f'读取文件失败: {str(e)}'
                }), 400

        return jsonify({
            'success': False,
            'message': '不支持的文件格式'
        }), 400

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'上传失败: {str(e)}'
        }), 500

@app.route('/api/refresh-data', methods=['POST'])
def refresh_data():
    """重新获取数据API"""
    try:
        # 在后台线程中更新数据
        def update_in_background():
            update_all_stocks()

        thread = threading.Thread(target=update_in_background)
        thread.daemon = True
        thread.start()

        return jsonify({
            'success': True,
            'message': '数据更新已开始，请稍后刷新页面查看结果'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'更新失败: {str(e)}'
        }), 500

def calculate_statistics():
    """计算统计数据 - 只统计股票"""
    if not stock_data:
        return {
            'total_market_value': 0,
            'stock_count': 0,
            'industry_distribution': {}
        }

    # 总市值（现在只有股票）
    total_market_value = sum(stock.get('market_value', 0) for stock in stock_data.values())
    stock_count = len(stock_data)

    # 行业分布统计
    industry_distribution = {}

    for stock in stock_data.values():
        industry = stock.get('industry', '未知行业')
        if not industry:
            industry = '未知行业'
        market_value = stock.get('market_value', 0)

        if industry not in industry_distribution:
            industry_distribution[industry] = {'market_value': 0, 'count': 0}

        industry_distribution[industry]['market_value'] += market_value
        industry_distribution[industry]['count'] += 1

    # 计算行业分布百分比
    for industry in industry_distribution:
        if total_market_value > 0:
            industry_distribution[industry]['percentage'] = (
                industry_distribution[industry]['market_value'] / total_market_value * 100
            )
        else:
            industry_distribution[industry]['percentage'] = 0

    # 按市值排序
    industry_distribution = dict(
        sorted(industry_distribution.items(),
               key=lambda x: x[1]['market_value'],
               reverse=True)
    )

    return {
        'total_market_value': total_market_value,
        'stock_count': stock_count,
        'industry_distribution': industry_distribution
    }

@app.route('/api/cache-status')
def get_cache_status():
    """获取缓存状态API"""
    try:
        cache_status = check_cache_status()
        return jsonify({
            'success': True,
            'data': cache_status
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取缓存状态失败: {str(e)}'
        }), 500

@app.route('/api/scan-cache-info')
def get_scan_cache_info():
    """获取扫雷缓存信息API"""
    try:
        cache_info = scan_client.get_cache_info()
        return jsonify({
            'success': True,
            'data': cache_info
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取扫雷缓存信息失败: {str(e)}'
        }), 500

@app.route('/api/stock-list-status')
def get_stock_list_status():
    """获取股票列表状态API"""
    try:
        stock_list = load_stock_list()
        return jsonify({
            'success': True,
            'data': {
                'imported_count': len(imported_stock_list),
                'filtered_count': len(stock_list),
                'sample_stocks': stock_list[:5] if stock_list else []
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取股票列表状态失败: {str(e)}'
        }), 500

# HTML模板
HTML_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>持仓系统 V12 完整整合版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
        }

        .header .subtitle {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .update-info {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 5px;
            margin-top: 15px;
        }

        .controls {
            background: white;
            padding: 20px;
            margin: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-box {
            flex: 1;
            max-width: 300px;
            margin-right: 20px;
        }

        .search-box input {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }

        .stats {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            font-size: 0.9em;
            color: #666;
        }

        /* 分类筛选按钮 */
        .filter-buttons {
            background: white;
            padding: 15px 20px;
            margin: 0 20px 20px 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .filter-btn {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            color: #495057;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
        }

        .filter-btn:hover {
            background: #e9ecef;
            border-color: #adb5bd;
            transform: translateY(-2px);
        }

        .filter-btn.active {
            background: #667eea;
            border-color: #667eea;
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        /* 统计面板 */
        .stats-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 0 20px 20px 20px;
        }

        .stats-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .stats-card h3 {
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .total-assets {
            border-left: 4px solid #28a745;
        }

        .asset-allocation {
            border-left: 4px solid #17a2b8;
        }



        .stats-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .stats-item:last-child {
            border-bottom: none;
        }

        .stats-value {
            font-weight: bold;
            color: #667eea;
        }

        /* 数据管理按钮 */
        .management-buttons {
            background: white;
            padding: 15px 20px;
            margin: 0 20px 20px 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .management-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .management-btn:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .management-btn.danger {
            background: #dc3545;
        }

        .management-btn.danger:hover {
            background: #c82333;
        }

        .management-btn.success {
            background: #28a745;
        }

        .management-btn.success:hover {
            background: #218838;
        }

        .management-btn.primary {
            background: #007bff;
        }

        .management-btn.primary:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 持仓系统 V12 股票专版</h1>
        <div class="subtitle">A股股票数据监控 | 智能交易时间检测 | 扫雷风险评估 | 自动过滤非股票证券</div>
        <div class="update-info">
            <span id="updateTime">等待数据加载...</span> |
            <span id="stockCount">0</span> 只A股股票 |
            <span id="tradingStatus">检查交易状态中...</span>
        </div>
    </div>

    <!-- 搜索和统计 -->
    <div class="controls">
        <div class="search-box">
            <input type="text" id="searchInput" placeholder="搜索股票代码或名称...">
        </div>
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number" id="totalValue">¥0</div>
                <div class="stat-label">总市值</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="stockCountStat">0</div>
                <div class="stat-label">股票数量</div>
            </div>
        </div>
    </div>

    <!-- 股票筛选说明 -->
    <div class="filter-buttons">
        <div style="text-align: center; color: #666; font-size: 14px; padding: 10px;">
            📊 当前显示：仅A股股票（已自动过滤ETF、基金、可转债等其他证券）
        </div>
    </div>

    <!-- 统计面板 -->
    <div class="stats-panel">
        <div class="stats-card total-assets">
            <h3>💰 股票资产统计</h3>
            <div class="stats-item">
                <span>总市值</span>
                <span class="stats-value" id="totalMarketValue">¥0</span>
            </div>
            <div class="stats-item">
                <span>股票数量</span>
                <span class="stats-value" id="stockCountDisplay">0只</span>
            </div>
        </div>


    </div>

    <!-- 数据管理按钮 -->
    <div class="management-buttons">
        <button class="management-btn danger" onclick="clearData()">🗑️ 清空数据</button>
        <input type="file" id="fileInput" accept=".xlsx,.xls" style="display: none;" onchange="uploadFile()">
        <button class="management-btn primary" onclick="document.getElementById('fileInput').click()">📁 导入持仓表格</button>
        <button class="management-btn success" onclick="refreshData()">🔄 重新获取数据</button>
        <button class="management-btn" onclick="checkCacheStatus()">📊 缓存状态</button>
    </div>

    <!-- 表格容器 -->
    <div class="table-container">
        <table id="stockTable">
            <thead>
                <tr>
                    <th onclick="sortTable('code')">代码 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('name')">名称 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('price')">最新价 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('change_pct')">涨跌幅 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('yearly_low')" class="mobile-hidden">年内最低价 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('low_date')" class="mobile-hidden">最低价日期 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('distance_from_low_pct')" class="mobile-hidden">距最低点涨幅 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('pb_ratio')" class="mobile-hidden">市净率 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('pe_ratio')" class="mobile-hidden">TTM市盈率 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('dividend_yield')" class="mobile-hidden">股息率 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('industry')" class="extra-mobile-hidden">行业 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('scan_score')">扫雷 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('holdings')">持仓 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('unit_cost')" class="mobile-hidden">单位成本 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('market_value')">市值 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('update_time')" class="mobile-hidden">更新时间 <span class="sort-indicator"></span></th>
                </tr>
            </thead>
            <tbody id="stockTableBody">
                <tr>
                    <td colspan="16" style="text-align: center; padding: 40px;">
                        <div style="color: #666;">
                            <div style="font-size: 48px; margin-bottom: 20px;">📊</div>
                            <div style="font-size: 18px; margin-bottom: 10px;">暂无股票数据</div>
                            <div style="font-size: 14px;">请导入持仓表格或等待数据加载</div>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <style>
        /* 表格样式 */
        .table-container {
            background: white;
            margin: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        th, td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #eee;
            white-space: nowrap;
        }

        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #495057;
            cursor: pointer;
            user-select: none;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        th:hover {
            background: #e9ecef;
        }

        .sort-indicator {
            margin-left: 5px;
            opacity: 0.5;
        }

        .sort-indicator.asc::after {
            content: '↑';
            opacity: 1;
        }

        .sort-indicator.desc::after {
            content: '↓';
            opacity: 1;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .positive {
            color: #dc3545;
        }

        .negative {
            color: #28a745;
        }

        .scan-score {
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                gap: 15px;
            }

            .search-box {
                max-width: none;
                margin-right: 0;
            }

            .stats {
                justify-content: center;
            }

            .stats-panel {
                grid-template-columns: 1fr;
                margin: 0 10px 20px 10px;
            }

            .filter-buttons, .management-buttons {
                margin: 0 10px 20px 10px;
            }

            .table-container {
                margin: 10px;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }

            table {
                min-width: 800px; /* 减少最小宽度适配手机 */
                font-size: 13px;
            }

            th, td {
                padding: 6px 3px;
                font-size: 12px;
                white-space: nowrap;
            }

            /* 移动端隐藏次要列 */
            .mobile-hidden {
                display: none;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.6em;
                padding: 10px;
            }

            .header .subtitle {
                font-size: 0.85em;
                padding: 0 10px;
            }

            .filter-btn, .management-btn {
                padding: 6px 10px;
                font-size: 11px;
                margin: 2px;
            }

            table {
                min-width: 600px; /* 超小屏幕进一步减少 */
                font-size: 11px;
            }

            th, td {
                padding: 4px 2px;
                font-size: 10px;
            }

            /* 超小屏幕隐藏更多列 */
            .extra-mobile-hidden {
                display: none;
            }
        }
    </style>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        let stockData = [];
        let filteredData = [];
        let sortColumn = '';
        let sortDirection = 'asc';

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStockData();
            loadTradingStatus();

            // 设置定时刷新
            setInterval(loadStockData, 30000); // 30秒刷新一次
            setInterval(loadTradingStatus, 60000); // 1分钟刷新一次交易状态

            // 搜索功能
            document.getElementById('searchInput').addEventListener('input', function() {
                filterAndDisplayData();
            });
        });

        // 加载股票数据
        async function loadStockData() {
            try {
                const response = await fetch('/api/stocks');
                const result = await response.json();

                if (result.success) {
                    stockData = result.data;
                    updateHeader(result);
                    updateStatistics(result.stats);
                    filterAndDisplayData();
                } else {
                    console.error('加载数据失败:', result.message);
                }
            } catch (error) {
                console.error('请求失败:', error);
            }
        }

        // 加载交易状态
        async function loadTradingStatus() {
            try {
                const response = await fetch('/api/trading-status');
                const result = await response.json();

                if (result.success) {
                    document.getElementById('tradingStatus').textContent = result.data.message;
                }
            } catch (error) {
                console.error('获取交易状态失败:', error);
            }
        }

        // 更新页面头部信息
        function updateHeader(result) {
            document.getElementById('updateTime').textContent =
                result.last_update ? `最后更新: ${result.last_update}` : '暂未更新';
            document.getElementById('stockCount').textContent = result.count;
        }

        // 更新统计信息
        function updateStatistics(stats) {
            if (!stats) return;

            // 更新总市值和股票数量
            const totalValue = formatCurrency(stats.total_market_value);
            document.getElementById('totalValue').textContent = totalValue;
            document.getElementById('totalMarketValue').textContent = totalValue;
            document.getElementById('stockCountStat').textContent = stockData.length;
            document.getElementById('stockCountDisplay').textContent = stats.stock_count + '只';


        }

        // 筛选和显示数据
        function filterAndDisplayData() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();

            // 应用筛选条件（只有搜索筛选，因为现在只显示股票）
            filteredData = stockData.filter(stock => {
                // 搜索筛选
                if (searchTerm &&
                    !stock.code.toLowerCase().includes(searchTerm) &&
                    !stock.name.toLowerCase().includes(searchTerm)) {
                    return false;
                }

                return true;
            });

            // 应用排序
            if (sortColumn) {
                filteredData.sort((a, b) => {
                    let aVal = a[sortColumn];
                    let bVal = b[sortColumn];

                    // 处理数值类型
                    if (typeof aVal === 'number' && typeof bVal === 'number') {
                        return sortDirection === 'asc' ? aVal - bVal : bVal - aVal;
                    }

                    // 处理字符串类型
                    aVal = String(aVal || '').toLowerCase();
                    bVal = String(bVal || '').toLowerCase();

                    if (sortDirection === 'asc') {
                        return aVal.localeCompare(bVal);
                    } else {
                        return bVal.localeCompare(aVal);
                    }
                });
            }

            displayStockTable();
        }

        // 显示股票表格
        function displayStockTable() {
            const tbody = document.getElementById('stockTableBody');

            if (filteredData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="15" style="text-align: center; padding: 40px;">
                            <div style="color: #666;">
                                <div style="font-size: 48px; margin-bottom: 20px;">📊</div>
                                <div style="font-size: 18px; margin-bottom: 10px;">暂无匹配的股票数据</div>
                                <div style="font-size: 14px;">请调整筛选条件或导入持仓表格</div>
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = filteredData.map(stock => `
                <tr>
                    <td>${stock.code}</td>
                    <td>${stock.name}</td>
                    <td>${formatNumber(stock.price, 2)}</td>
                    <td class="${stock.change_pct >= 0 ? 'positive' : 'negative'}">
                        ${formatPercent(stock.change_pct)}
                    </td>
                    <td class="mobile-hidden">${formatNumber(stock.yearly_low, 2)}</td>
                    <td class="mobile-hidden">${stock.low_date || '-'}</td>
                    <td class="mobile-hidden ${stock.distance_from_low_pct >= 0 ? 'positive' : 'negative'}">
                        ${stock.distance_from_low_pct ? formatPercent(stock.distance_from_low_pct) : '-'}
                    </td>
                    <td class="mobile-hidden">${formatNumber(stock.pb_ratio, 2)}</td>
                    <td class="mobile-hidden">${formatNumber(stock.pe_ratio, 2)}</td>
                    <td class="mobile-hidden">${formatPercent(stock.dividend_yield)}</td>
                    <td class="extra-mobile-hidden">${stock.industry || '-'}</td>
                    <td>
                        <span class="scan-score" style="background-color: ${stock.scan_risk_color || '#cccccc'}">
                            ${stock.scan_display_text || '-'}
                        </span>
                    </td>
                    <td>${formatNumber(stock.holdings || stock.quantity || 0, 0)}</td>
                    <td class="mobile-hidden">${stock.unit_cost > 0 ? formatNumber(stock.unit_cost, 3) : '-'}</td>
                    <td>${formatCurrency(stock.market_value)}</td>
                    <td class="mobile-hidden">${stock.update_time || '-'}</td>
                </tr>
            `).join('');
        }

        // 表格排序
        function sortTable(column) {
            if (sortColumn === column) {
                sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                sortColumn = column;
                sortDirection = 'asc';
            }

            // 更新排序指示器
            document.querySelectorAll('.sort-indicator').forEach(indicator => {
                indicator.className = 'sort-indicator';
            });

            const currentIndicator = document.querySelector(`th[onclick="sortTable('${column}')"] .sort-indicator`);
            if (currentIndicator) {
                currentIndicator.className = `sort-indicator ${sortDirection}`;
            }

            filterAndDisplayData();
        }

        // 数据管理功能
        async function clearData() {
            if (!confirm('确定要清空所有数据吗？此操作不可恢复。')) {
                return;
            }

            try {
                const response = await fetch('/api/clear-data', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    alert('数据已清空');
                    stockData = [];
                    filterAndDisplayData();
                    updateStatistics({});
                } else {
                    alert('清空失败: ' + result.message);
                }
            } catch (error) {
                alert('清空失败: ' + error.message);
            }
        }

        async function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];

            if (!file) {
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            try {
                const response = await fetch('/api/upload-holdings', {
                    method: 'POST',
                    body: formData
                });
                const result = await response.json();

                if (result.success) {
                    alert(`导入成功: ${result.message}`);
                } else {
                    alert('导入失败: ' + result.message);
                }
            } catch (error) {
                alert('导入失败: ' + error.message);
            }

            fileInput.value = '';
        }

        async function refreshData() {
            if (!confirm('确定要重新获取所有股票数据吗？这可能需要几分钟时间。')) {
                return;
            }

            try {
                const response = await fetch('/api/refresh-data', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    alert(result.message);
                } else {
                    alert('更新失败: ' + result.message);
                }
            } catch (error) {
                alert('更新失败: ' + error.message);
            }
        }

        async function checkCacheStatus() {
            try {
                const [cacheResponse, scanResponse] = await Promise.all([
                    fetch('/api/cache-status'),
                    fetch('/api/scan-cache-info')
                ]);

                const cacheResult = await cacheResponse.json();
                const scanResult = await scanResponse.json();

                let message = '缓存状态信息:\\n\\n';

                if (cacheResult.success) {
                    const cache = cacheResult.data;
                    message += `年内最低价缓存:\\n`;
                    message += `- 状态: ${cache.exists ? '存在' : '不存在'}\\n`;
                    message += `- 有效: ${cache.valid ? '是' : '否'}\\n`;
                    if (cache.success_rate) {
                        message += `- 成功率: ${cache.success_rate.toFixed(1)}%\\n`;
                    }
                } else {
                    message += `年内最低价缓存: 获取失败\\n`;
                }

                message += '\\n';

                if (scanResult.success) {
                    const scan = scanResult.data;
                    message += `扫雷数据缓存:\\n`;
                    message += `- 缓存股票数: ${scan.cached_stocks}\\n`;
                    message += `- 缓存时长: ${scan.cache_duration}秒\\n`;
                } else {
                    message += `扫雷数据缓存: 获取失败\\n`;
                }

                alert(message);
            } catch (error) {
                alert('获取缓存状态失败: ' + error.message);
            }
        }

        // 工具函数
        function formatNumber(value, decimals = 2) {
            if (value === null || value === undefined || value === '') return '-';
            return Number(value).toFixed(decimals);
        }

        function formatPercent(value) {
            if (value === null || value === undefined || value === '') return '-';
            return Number(value).toFixed(2) + '%';
        }

        function formatCurrency(value) {
            if (value === null || value === undefined || value === '' || value === 0) return '¥0';
            return '¥' + Number(value).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
        }
    </script>
</body>
</html>
'''

if __name__ == '__main__':
    print("=== 🚀 启动持仓系统 V12 股票专版 ===")
    print("📊 功能特性:")
    print("   • 只显示A股股票（自动过滤ETF、基金、可转债等）")
    print("   • 实时股价、年内最低价、股息率、行业信息")
    print("   • 智能交易时间监测")
    print("   • 持仓数量和市值计算")
    print("   • Excel数据管理（上传、清空、刷新）")
    print("   • 行业分布统计分析")
    print("   • 扫雷风险评估")
    print("   • 移动端响应式设计")
    print("   • 年内最低价缓存系统")
    print()
    print("⚠️  注意：本版本只处理A股股票，会自动跳过其他证券类型")

    # 检查缓存状态
    try:
        cache_status = check_cache_status()
        if cache_status['exists'] and cache_status['valid']:
            print("✅ 年内最低价缓存可用")
            if 'success_rate' in cache_status:
                print(f"   📊 缓存统计: {cache_status['success_count']}/{cache_status['total_count']} ({cache_status['success_rate']:.1f}%)")
        else:
            print("⚠️ 年内最低价缓存不可用")
            print("   建议运行: python yearly_low_cache_system.py")
    except Exception as e:
        print(f"⚠️ 缓存检查失败: {e}")

    # 显示交易状态
    try:
        status = trading_monitor.get_trading_status()
        print(f"📈 当前交易状态: {status['message']}")
        print(f"⏰ 更新间隔: {trading_monitor.get_update_interval()}秒")
    except Exception as e:
        print(f"⚠️ 获取交易状态失败: {e}")

    print(f"\n🌐 访问地址: http://localhost:5000")
    print("📱 支持移动端访问，响应式设计")
    print("🔄 智能更新：只在交易时间更新数据")
    print("📊 完整功能：数据管理、统计分析、风险评估")

    # 启动后台更新线程
    update_thread = threading.Thread(target=background_update)
    update_thread.daemon = True
    update_thread.start()

    # 启动Flask应用
    app.run(host='0.0.0.0', port=5000, debug=False)
