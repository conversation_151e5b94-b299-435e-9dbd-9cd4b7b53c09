#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持仓系统 V10 修复版本测试
验证基于V9增量升级的修复效果
"""

import requests
import time
import json
from datetime import datetime

def test_v10_fix():
    """测试V10修复版本"""
    base_url = 'http://localhost:5000'
    
    print("=== 📊 持仓系统 V10 修复版本测试 ===\n")
    
    # 等待服务器启动
    print("⏱️ 等待服务器启动...")
    time.sleep(3)
    
    tests = []
    
    # 1. 测试服务器连接
    print("1️⃣ 测试服务器连接...")
    try:
        response = requests.get(f"{base_url}/", timeout=10)
        if response.status_code == 200:
            if 'V10 表格增强版' in response.text:
                tests.append(("服务器连接", True, "V10增强版页面加载成功"))
            else:
                tests.append(("服务器连接", False, "页面加载但未检测到V10标识"))
        else:
            tests.append(("服务器连接", False, f"HTTP状态码: {response.status_code}"))
    except Exception as e:
        tests.append(("服务器连接", False, f"连接失败: {e}"))
    
    # 2. 测试API兼容性
    print("2️⃣ 测试API兼容性...")
    api_endpoints = [
        '/api/stocks',
        '/api/trading-status',
        '/api/cache/status',
        '/api/cache/check'
    ]
    
    api_success = 0
    for endpoint in api_endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            if response.status_code == 200:
                api_success += 1
                print(f"   ✅ {endpoint} - 正常")
            else:
                print(f"   ❌ {endpoint} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {endpoint} - 错误: {e}")
    
    tests.append(("API兼容性", api_success == len(api_endpoints), f"{api_success}/{len(api_endpoints)} 个API正常"))
    
    # 3. 测试数据结构
    print("3️⃣ 测试数据结构...")
    try:
        response = requests.get(f"{base_url}/api/stocks", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if 'stocks' in data and 'last_update' in data:
                stocks = data['stocks']
                if stocks:
                    # 检查第一只股票的数据结构
                    first_stock = stocks[0]
                    required_fields = ['code', 'name', 'price', 'change_pct', 'yearly_low', 'distance_from_low_pct']
                    missing_fields = [field for field in required_fields if field not in first_stock]
                    
                    if not missing_fields:
                        tests.append(("数据结构", True, f"数据结构完整 ({len(stocks)}只股票)"))
                    else:
                        tests.append(("数据结构", False, f"缺少字段: {missing_fields}"))
                else:
                    tests.append(("数据结构", True, "暂无股票数据（正常状态）"))
            else:
                tests.append(("数据结构", False, "API响应格式错误"))
        else:
            tests.append(("数据结构", False, f"API调用失败: {response.status_code}"))
    except Exception as e:
        tests.append(("数据结构", False, f"测试异常: {e}"))
    
    # 4. 测试V10增强功能
    print("4️⃣ 测试V10增强功能...")
    try:
        response = requests.get(f"{base_url}/", timeout=10)
        if response.status_code == 200:
            html_content = response.text
            
            # 检查V10特有功能
            v10_features = [
                ('排序功能', 'sortable'),
                ('列设置', 'columnModal'),
                ('拖拽库', 'SortableJS'),
                ('本地存储', 'localStorage'),
                ('搜索过滤', 'searchBox'),
                ('数据导出', 'exportData')
            ]
            
            feature_count = 0
            for feature_name, feature_key in v10_features:
                if feature_key in html_content:
                    feature_count += 1
                    print(f"   ✅ {feature_name} - 已集成")
                else:
                    print(f"   ❌ {feature_name} - 未找到")
            
            tests.append(("V10增强功能", feature_count == len(v10_features), f"{feature_count}/{len(v10_features)} 个功能已集成"))
        else:
            tests.append(("V10增强功能", False, "页面加载失败"))
    except Exception as e:
        tests.append(("V10增强功能", False, f"测试异常: {e}"))
    
    # 5. 测试缓存系统集成
    print("5️⃣ 测试缓存系统集成...")
    try:
        response = requests.get(f"{base_url}/api/cache/status", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                cache_status = data.get('cache_status', {})
                tests.append(("缓存系统", True, f"缓存状态: {'可用' if cache_status.get('exists') else '不可用'}"))
            else:
                tests.append(("缓存系统", False, "缓存状态检查失败"))
        else:
            tests.append(("缓存系统", False, f"API调用失败: {response.status_code}"))
    except Exception as e:
        tests.append(("缓存系统", False, f"测试异常: {e}"))
    
    # 生成测试报告
    print("\n" + "="*60)
    print("📋 测试报告")
    print("="*60)
    
    success_count = sum(1 for _, success, _ in tests if success)
    total_count = len(tests)
    success_rate = (success_count / total_count) * 100
    
    print(f"✅ 成功: {success_count}/{total_count} ({success_rate:.1f}%)")
    print(f"❌ 失败: {total_count - success_count}")
    print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print("\n📊 详细结果:")
    for test_name, success, message in tests:
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {message}")
    
    if success_rate >= 80:
        print("\n🎉 测试通过！V10修复版本运行正常。")
        print("\n💡 修复要点:")
        print("   • 保持V9版本的所有数据获取逻辑")
        print("   • 保持原有的延时控制和更新机制")
        print("   • 在V9基础上增量添加表格增强功能")
        print("   • 完全兼容现有API和缓存系统")
    else:
        print("\n⚠️ 测试未完全通过，请检查失败的测试项。")
    
    return success_rate >= 80

def main():
    """主函数"""
    try:
        success = test_v10_fix()
        if success:
            print("\n🚀 V10修复版本测试完成，系统运行正常！")
        else:
            print("\n❌ V10修复版本存在问题，需要进一步检查。")
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")

if __name__ == '__main__':
    main()
