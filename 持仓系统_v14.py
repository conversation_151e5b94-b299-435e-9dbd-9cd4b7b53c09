#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持仓系统 V14 显示逻辑优化版
==========================

基于V13版本的改进：
- V1-V1.2: 基础功能（股价、年内最低价、股息率、行业信息）
- V2: 智能交易时间监测
- V3: 持仓数量和市值计算
- V4: 数据管理功能（Excel上传、清空数据）
- V5: 分类筛选功能
- V6: 统计分析功能
- V7: 界面优化
- V8: 饼图可视化
- V9: 移动端适配
- V10-V11: 扫雷功能和年内最低价缓存
- V12: 完整整合版
- V13: 策略优化版
- V14: 显示逻辑优化版
  * 修改减半后的显示逻辑：不显示"已减半"标签
  * 将原来的"卖出"显示改为"已减半"
  * 实现持仓数量的直接点击编辑功能

作者: AI Assistant
版本: 14.0
日期: 2025-07-23
"""

from flask import Flask, render_template_string, jsonify, request
import requests
import time
import json
import pandas as pd
import os
from datetime import datetime, timedelta
import threading
from urllib.parse import urlencode
from werkzeug.utils import secure_filename
import pickle
import sys
# 注：hashlib, atexit, signal 等模块在当前版本中暂未使用，已移除导入
# from wechat_alert import WeChatAlert  # 暂时注释，我们将在代码中实现
from A股交易时间监测_简化版 import AStockTradingTimeMonitor
from yearly_low_cache_reader import get_yearly_low_from_cache, check_cache_status
from tdx_scan_module import TdxScanClient

app = Flask(__name__)

# 配置文件上传
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
ALLOWED_EXTENSIONS = {'xlsx', 'xls'}

# 确保上传目录存在
if not os.path.exists(app.config['UPLOAD_FOLDER']):
    os.makedirs(app.config['UPLOAD_FOLDER'])

# 企业微信提醒类
class WeChatAlert:
    def __init__(self, webhook_url):
        self.webhook_url = webhook_url
        self.sent_alerts = {}  # 存储已发送的提醒，避免重复发送
        self.alert_cache_file = 'wechat_alert_cache.pkl'
        self.load_alert_cache()

    def load_alert_cache(self):
        """加载提醒缓存"""
        try:
            if os.path.exists(self.alert_cache_file):
                with open(self.alert_cache_file, 'rb') as f:
                    self.sent_alerts = pickle.load(f)
                print(f"✅ 已加载企业微信提醒缓存: {len(self.sent_alerts)} 条记录")
        except Exception as e:
            print(f"⚠️ 加载企业微信提醒缓存失败: {e}")
            self.sent_alerts = {}

    def save_alert_cache(self):
        """保存提醒缓存"""
        try:
            with open(self.alert_cache_file, 'wb') as f:
                pickle.dump(self.sent_alerts, f)
        except Exception as e:
            print(f"⚠️ 保存企业微信提醒缓存失败: {e}")

    def generate_alert_key(self, stock_code, alert_type, date_str):
        """生成提醒唯一标识"""
        return f"{stock_code}_{alert_type}_{date_str}"

    def should_send_alert(self, stock_code, alert_type):
        """检查是否应该发送提醒（每只股票每种类型每天最多1次）"""
        today = datetime.now().strftime('%Y-%m-%d')
        alert_key = self.generate_alert_key(stock_code, alert_type, today)

        # 检查是否已标记为已处理（永久停止提醒）
        handled_key = f"{stock_code}_handled"
        if handled_key in self.sent_alerts:
            print(f"📱 跳过已处理股票的提醒: {stock_code} - {alert_type}")
            return False

        # 检查今天是否已经发送过相同类型的提醒
        if alert_key in self.sent_alerts:
            return False

        return True

    def mark_alert_sent(self, stock_code, alert_type):
        """标记提醒已发送"""
        today = datetime.now().strftime('%Y-%m-%d')
        alert_key = self.generate_alert_key(stock_code, alert_type, today)
        self.sent_alerts[alert_key] = {
            'timestamp': datetime.now().isoformat(),
            'stock_code': stock_code,
            'alert_type': alert_type
        }
        self.save_alert_cache()

    def mark_as_handled(self, stock_code, action_type):
        """标记股票为已处理，永久停止提醒"""
        handled_key = f"{stock_code}_handled"
        self.sent_alerts[handled_key] = {
            'timestamp': datetime.now().isoformat(),
            'stock_code': stock_code,
            'action_type': action_type,
            'handled': True
        }
        self.save_alert_cache()
        print(f"✅ 标记 {stock_code} 为已处理，将停止所有提醒 (操作: {action_type})")

    def remove_handled_status(self, stock_code):
        """移除股票的已处理状态，恢复企业微信提醒"""
        handled_key = f"{stock_code}_handled"
        if handled_key in self.sent_alerts:
            del self.sent_alerts[handled_key]
            self.save_alert_cache()
            print(f"📱 企业微信提醒已恢复: {stock_code}")
            return True
        return False

    def clean_old_alerts(self):
        """清理7天前的提醒记录"""
        try:
            cutoff_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
            keys_to_remove = []

            for key in self.sent_alerts:
                if '_' in key:
                    date_part = key.split('_')[-1]
                    if date_part < cutoff_date:
                        keys_to_remove.append(key)

            for key in keys_to_remove:
                del self.sent_alerts[key]

            if keys_to_remove:
                self.save_alert_cache()
                print(f"🧹 已清理 {len(keys_to_remove)} 条过期提醒记录")
        except Exception as e:
            print(f"⚠️ 清理过期提醒记录失败: {e}")

    def send_message(self, message):
        """发送企业微信消息"""
        try:
            data = {
                "msgtype": "text",
                "text": {
                    "content": message
                }
            }

            response = requests.post(
                self.webhook_url,
                json=data,
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    return True
                else:
                    print(f"企业微信发送失败: {result}")
                    return False
            else:
                print(f"企业微信请求失败: HTTP {response.status_code}")
                return False

        except Exception as e:
            print(f"发送企业微信消息异常: {e}")
            return False

    def send_sell_alert(self, stock_code, stock_name, alert_type, reason, current_price, distance_from_low=None, profit_status=None, profit_margin=None, strategy_name=None):
        """发送卖出提醒 - 重构版本支持新策略"""
        # 检查是否应该发送提醒
        if not self.should_send_alert(stock_code, alert_type):
            print(f"📱 跳过重复提醒: {stock_name} ({stock_code}) - {alert_type}")
            return False

        # 构建消息内容 - 支持新的信号类型
        if alert_type == 'clearance':
            emoji = "🔥"
            title = "清仓信号"
        elif alert_type == 'reduce':
            emoji = "📉"
            title = "减半信号"
        elif alert_type == 'sell':
            emoji = "🚨"
            title = "卖出信号"
        elif alert_type == 'warning':
            emoji = "⚠️"
            title = "策略预警"
        else:
            emoji = "📊"
            title = "股票提醒"

        message = f"{emoji} {title}\n"
        message += f"股票: {stock_name} ({stock_code})\n"
        message += f"当前价格: {current_price:.2f}元\n"

        if strategy_name:
            message += f"触发策略: {strategy_name}\n"

        message += f"操作建议: {reason}\n"

        if distance_from_low is not None:
            message += f"距最低点涨幅: {distance_from_low:.1f}%\n"

        if profit_status == 'profit' and profit_margin is not None:
            message += f"盈利状况: 已盈利 +{profit_margin:.1f}%\n"

        # 添加操作提示
        if alert_type == 'clearance':
            message += f"💡 建议: 立即清仓，风险较高\n"
        elif alert_type == 'reduce':
            message += f"💡 建议: 减半持仓，锁定部分收益\n"
        elif alert_type == 'warning':
            message += f"💡 建议: 提前挂单，准备操作\n"

        message += f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        # 发送消息
        if self.send_message(message):
            self.mark_alert_sent(stock_code, alert_type)
            print(f"📱 已发送{title}: {stock_name} - {reason}")
            return True
        else:
            print(f"📱 发送{title}失败: {stock_name}")
            return False

# 全局变量存储股票数据
stock_data = {}
last_update_time = None
imported_stock_list = []  # 存储导入的股票列表

# 自动更新控制变量
auto_update_enabled = True  # 自动更新开关
update_thread_running = False  # 更新线程运行状态

# 数据持久化文件路径
STOCK_DATA_FILE = 'stock_data_cache.json'
IMPORTED_LIST_FILE = 'imported_stock_list.json'
AUTO_UPDATE_CONFIG_FILE = 'auto_update_config.json'

def save_stock_data():
    """保存股票数据到文件"""
    try:
        data_to_save = {
            'stock_data': stock_data,
            'last_update_time': last_update_time,
            'save_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        with open(STOCK_DATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(data_to_save, f, ensure_ascii=False, indent=2)
        print(f"✅ 股票数据已保存到 {STOCK_DATA_FILE}")
    except Exception as e:
        print(f"❌ 保存股票数据失败: {e}")

def load_stock_data():
    """
    从文件加载股票数据并重新计算卖出信号

    从STOCK_DATA_FILE文件中加载股票数据，包括股票信息和最后更新时间。
    加载后会重新计算所有股票的卖出信号，确保信号的准确性。

    Returns:
        bool: 加载成功返回True，失败返回False

    Global Variables:
        stock_data (dict): 全局股票数据字典
        last_update_time (str): 全局最后更新时间
    """
    global stock_data, last_update_time

    try:
        if os.path.exists(STOCK_DATA_FILE):
            with open(STOCK_DATA_FILE, 'r', encoding='utf-8') as file:
                data = json.load(file)

            # 加载基础数据
            stock_data = data.get('stock_data', {})
            last_update_time = data.get('last_update_time')
            save_time = data.get('save_time', '未知')

            print(f"✅ 已加载股票数据缓存 (保存时间: {save_time})")
            print(f"   📊 包含 {len(stock_data)} 只股票数据")

            # 重新计算卖出信号和减半监控状态（确保信号准确性）
            print("🔄 重新计算卖出信号和减半监控状态...")
            try:
                updated_count = 0
                reduction_updated_count = 0
                for stock_code, stock_info in stock_data.items():
                    # 更新减半监控策略模式
                    if stock_info.get('is_reduced', False):
                        try:
                            reduction_monitor.update_strategy_mode(stock_code, stock_info)
                            reduction_updated_count += 1
                        except Exception as e:
                            print(f"⚠️ 更新减半监控状态失败 {stock_code}: {e}")

                    # 重新计算卖出信号
                    sell_signal = calculate_sell_signal(stock_info)
                    stock_info['sell_signal'] = sell_signal['signal']
                    stock_info['sell_reason'] = sell_signal['reason']
                    stock_info['sell_color'] = sell_signal['color']
                    stock_info['sell_priority'] = sell_signal['priority']
                    updated_count += 1

                print(f"✅ 已更新 {updated_count} 只股票的卖出信号")
                if reduction_updated_count > 0:
                    print(f"🧊 已更新 {reduction_updated_count} 只已减半股票的监控状态")

            except Exception as signal_error:
                print(f"⚠️ 计算卖出信号时出错: {signal_error}")
                # 如果计算失败，给所有股票设置默认值
                for _, stock_info in stock_data.items():  # 使用_忽略股票代码
                    stock_info['sell_signal'] = 'hold'
                    stock_info['sell_reason'] = '持有'
                    stock_info['sell_color'] = '#2ed573'
                    stock_info['sell_priority'] = 3

            return True

    except Exception as e:
        print(f"❌ 加载股票数据失败: {e}")

    return False

def save_imported_list():
    """保存导入的股票列表"""
    try:
        data_to_save = {
            'imported_stock_list': imported_stock_list,
            'save_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        with open(IMPORTED_LIST_FILE, 'w', encoding='utf-8') as f:
            json.dump(data_to_save, f, ensure_ascii=False, indent=2)
        print(f"✅ 导入列表已保存到 {IMPORTED_LIST_FILE}")
    except Exception as e:
        print(f"❌ 保存导入列表失败: {e}")

def load_imported_list():
    """从文件加载导入的股票列表"""
    global imported_stock_list
    try:
        if os.path.exists(IMPORTED_LIST_FILE):
            with open(IMPORTED_LIST_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
            imported_stock_list = data.get('imported_stock_list', [])
            save_time = data.get('save_time', '未知')
            print(f"✅ 已加载导入列表缓存 (保存时间: {save_time})")
            print(f"   📋 包含 {len(imported_stock_list)} 只股票")
            return True
    except Exception as e:
        print(f"❌ 加载导入列表失败: {e}")
    return False

def save_auto_update_config():
    """保存自动更新配置"""
    try:
        config_data = {
            'auto_update_enabled': auto_update_enabled,
            'save_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        with open(AUTO_UPDATE_CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
        print(f"✅ 自动更新配置已保存: {'启用' if auto_update_enabled else '禁用'}")
    except Exception as e:
        print(f"❌ 保存自动更新配置失败: {e}")

def load_auto_update_config():
    """从文件加载自动更新配置"""
    global auto_update_enabled
    try:
        if os.path.exists(AUTO_UPDATE_CONFIG_FILE):
            with open(AUTO_UPDATE_CONFIG_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
            auto_update_enabled = data.get('auto_update_enabled', True)
            save_time = data.get('save_time', '未知')
            print(f"✅ 已加载自动更新配置 (保存时间: {save_time})")
            print(f"   🔄 自动更新状态: {'启用' if auto_update_enabled else '禁用'}")
            return True
    except Exception as e:
        print(f"❌ 加载自动更新配置失败: {e}")
    return False

# 配置
CONFIG = {
    'stock_interval': 0.2,      # 每只股票间隔时间（秒）
    'round_interval': 600,      # 每轮更新间隔时间（秒）
    'request_timeout': 15,      # 请求超时时间（秒）
    'alert_threshold': 70.0,    # 企业微信提醒阈值（距离最低点涨幅%）
}

# 减半监控配置
REDUCTION_MONITORING_CONFIG = {
    'default_cooling_period_days': 365,    # 默认冷却期（天）
    'default_ttm_threshold': 30.0,         # 默认TTM阈值
    'fundamentals_check_interval': 1,      # 基本面检查间隔（天）
    'config_file': 'reduction_monitoring_config.json',
    'version': '1.0'
}

# 卖出信号优先级控制配置
SELL_SIGNAL_PRIORITY_CONFIG = {
    'manual_priority_over_auto': True,  # 手动标记的卖出信号是否优先于自动生成的卖出信号
    'config_file': 'sell_signal_priority_config.json',  # 配置文件路径
    'last_updated': None,  # 最后更新时间
    'version': '1.0'  # 配置版本
}

# 企业微信提醒配置
WECHAT_WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7bddd706-ef0f-4d6c-9e1f-3cc8a0a18d9e"

# 初始化企业微信提醒
wechat_alert = WeChatAlert(WECHAT_WEBHOOK_URL)

# 初始化A股交易时间监测器
trading_monitor = AStockTradingTimeMonitor()

# ==================== 缓存管理模块优化 ====================

class CacheManager:
    """统一缓存管理器 - 管理所有类型的缓存"""

    def __init__(self):
        self.cache_configs = {
            'stock_data': {'timeout': 300, 'max_size': 1000},      # 股票数据缓存5分钟
            'yearly_low': {'timeout': 3600, 'max_size': 500},     # 年内最低价缓存1小时
            'scan_data': {'timeout': 1800, 'max_size': 200},      # 扫雷数据缓存30分钟
            'industry_info': {'timeout': 7200, 'max_size': 100},  # 行业信息缓存2小时
            'trading_status': {'timeout': 60, 'max_size': 1}      # 交易状态缓存1分钟
        }
        self.caches = {}
        self._initialize_caches()

    def _initialize_caches(self):
        """初始化各类缓存"""
        for cache_type, config in self.cache_configs.items():
            self.caches[cache_type] = {
                'data': {},
                'timestamps': {},
                'config': config
            }

    def get(self, cache_type, key):
        """获取缓存数据"""
        if cache_type not in self.caches:
            return None

        cache = self.caches[cache_type]
        current_time = time.time()

        # 检查是否存在且未过期
        if key in cache['data'] and key in cache['timestamps']:
            if current_time - cache['timestamps'][key] < cache['config']['timeout']:
                return cache['data'][key]
            else:
                # 过期则删除
                self._remove_from_cache(cache_type, key)

        return None

    def set(self, cache_type, key, value):
        """设置缓存数据"""
        if cache_type not in self.caches:
            return False

        cache = self.caches[cache_type]
        current_time = time.time()

        # 检查缓存大小限制
        if len(cache['data']) >= cache['config']['max_size']:
            self._cleanup_cache(cache_type)

        cache['data'][key] = value
        cache['timestamps'][key] = current_time
        return True

    def _remove_from_cache(self, cache_type, key):
        """从缓存中移除指定项"""
        cache = self.caches[cache_type]
        cache['data'].pop(key, None)
        cache['timestamps'].pop(key, None)

    def _cleanup_cache(self, cache_type):
        """清理过期缓存项"""
        cache = self.caches[cache_type]
        current_time = time.time()
        timeout = cache['config']['timeout']

        # 找出过期的键
        expired_keys = [
            key for key, timestamp in cache['timestamps'].items()
            if current_time - timestamp >= timeout
        ]

        # 删除过期项
        for key in expired_keys:
            self._remove_from_cache(cache_type, key)

        # 如果还是太多，删除最旧的项
        if len(cache['data']) >= cache['config']['max_size']:
            sorted_items = sorted(cache['timestamps'].items(), key=lambda x: x[1])
            items_to_remove = len(sorted_items) - cache['config']['max_size'] + 10

            for key, _ in sorted_items[:items_to_remove]:
                self._remove_from_cache(cache_type, key)

    def clear_cache(self, cache_type=None):
        """清空指定类型或所有缓存"""
        if cache_type:
            if cache_type in self.caches:
                self.caches[cache_type]['data'].clear()
                self.caches[cache_type]['timestamps'].clear()
        else:
            for cache in self.caches.values():
                cache['data'].clear()
                cache['timestamps'].clear()

    def get_cache_stats(self):
        """获取缓存统计信息"""
        stats = {}
        current_time = time.time()

        for cache_type, cache in self.caches.items():
            valid_count = sum(
                1 for timestamp in cache['timestamps'].values()
                if current_time - timestamp < cache['config']['timeout']
            )

            stats[cache_type] = {
                'total_items': len(cache['data']),
                'valid_items': valid_count,
                'expired_items': len(cache['data']) - valid_count,
                'max_size': cache['config']['max_size'],
                'timeout': cache['config']['timeout']
            }

        return stats

# 创建全局缓存管理器
cache_manager = CacheManager()

# 初始化扫雷客户端
scan_client = TdxScanClient(cache_duration=1800)  # 缓存30分钟

# ==================== 减半监控管理器 ====================

class ReductionMonitoringManager:
    """减半股票监控管理器"""

    def __init__(self):
        self.config_file = REDUCTION_MONITORING_CONFIG['config_file']
        self.config = self._load_config()
        print("🔧 初始化减半监控管理器...")

    def _load_config(self):
        """加载监控配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print(f"✅ 已加载减半监控配置")
                return config
        except Exception as e:
            print(f"⚠️ 加载减半监控配置失败: {e}")

        # 返回默认配置
        return {
            'cooling_period_days': REDUCTION_MONITORING_CONFIG['default_cooling_period_days'],
            'ttm_threshold': REDUCTION_MONITORING_CONFIG['default_ttm_threshold'],
            'fundamentals_check_interval': REDUCTION_MONITORING_CONFIG['fundamentals_check_interval'],
            'auto_strategy_switch': True,
            'enable_cooling_period': True
        }

    def save_config(self):
        """保存监控配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            print("💾 减半监控配置已保存")
        except Exception as e:
            print(f"❌ 保存减半监控配置失败: {e}")

    def update_config(self, new_config):
        """更新配置"""
        self.config.update(new_config)
        self.save_config()

    def add_reduction_record(self, stock_code, stock_data):
        """添加减半记录"""
        current_time = datetime.now()

        # 初始化减半历史记录
        if 'reduction_history' not in stock_data:
            stock_data['reduction_history'] = []

        # 添加新的减半记录
        reduction_record = {
            'reduction_date': current_time.strftime('%Y-%m-%d'),
            'reduction_time': current_time.strftime('%Y-%m-%d %H:%M:%S'),
            'reduction_price': stock_data.get('price', 0),
            'ttm_at_reduction': stock_data.get('pe_ratio', 0),
            'distance_from_low_pct': stock_data.get('distance_from_low_pct', 0),
            'reason': stock_data.get('sell_reason', '手动减半'),
            'strategy_at_reduction': stock_data.get('sell_signal', 'unknown')
        }

        stock_data['reduction_history'].append(reduction_record)

        # 设置监控状态
        cooling_until = current_time + timedelta(days=self.config['cooling_period_days'])
        stock_data['monitoring_status'] = 'cooling_down'
        stock_data['cooling_down_until'] = cooling_until.strftime('%Y-%m-%d')
        stock_data['strategy_mode'] = 'cooling_down'
        stock_data['last_fundamentals_check'] = current_time.strftime('%Y-%m-%d')

        print(f"📝 添加减半记录: {stock_code}, 冷却期至: {stock_data['cooling_down_until']}")

        return reduction_record

    def is_in_cooling_period(self, stock_data):
        """检查是否在冷却期内"""
        if not self.config.get('enable_cooling_period', True):
            return False

        cooling_until = stock_data.get('cooling_down_until')
        if not cooling_until:
            return False

        try:
            cooling_date = datetime.strptime(cooling_until, '%Y-%m-%d')
            return datetime.now() < cooling_date
        except:
            return False

    def get_cooling_period_info(self, stock_data):
        """获取冷却期信息"""
        if not self.is_in_cooling_period(stock_data):
            return None

        cooling_until = stock_data.get('cooling_down_until')
        try:
            cooling_date = datetime.strptime(cooling_until, '%Y-%m-%d')
            days_remaining = (cooling_date - datetime.now()).days
            return {
                'cooling_until': cooling_until,
                'days_remaining': max(0, days_remaining),
                'is_active': days_remaining > 0
            }
        except:
            return None

    def check_fundamentals_deterioration(self, stock_data):
        """检查基本面是否恶化"""
        current_ttm = stock_data.get('pe_ratio', 0)
        ttm_threshold = self.config.get('ttm_threshold', 30.0)

        # TTM变负或超过阈值
        if current_ttm <= 0:
            return True, 'negative_ttm', f'TTM变负({current_ttm})'
        elif current_ttm >= ttm_threshold:
            return True, 'high_ttm', f'TTM超过阈值({current_ttm} >= {ttm_threshold})'

        return False, None, None

    def update_strategy_mode(self, stock_code, stock_data):
        """更新策略模式"""
        # 优先检查冷却期状态 - 修复冷却器显示问题
        if self.is_in_cooling_period(stock_data):
            old_mode = stock_data.get('strategy_mode', 'normal')
            stock_data['strategy_mode'] = 'cooling_down'
            if old_mode != 'cooling_down':
                print(f"🧊 进入冷却期模式: {stock_code}")
            return 'cooling_down', '冷却期模式'

        # 检查基本面恶化（仅在非冷却期时生效）
        is_deteriorated, deterioration_type, reason = self.check_fundamentals_deterioration(stock_data)

        if is_deteriorated and self.config.get('auto_strategy_switch', True):
            # 基本面恶化，切换策略
            old_mode = stock_data.get('strategy_mode', 'normal')
            stock_data['strategy_mode'] = deterioration_type
            stock_data['strategy_switch_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            stock_data['strategy_switch_reason'] = reason

            if old_mode != deterioration_type:
                print(f"📊 策略切换: {stock_code} {old_mode} → {deterioration_type} ({reason})")

            return deterioration_type, reason

        else:
            # 恢复正常模式
            old_mode = stock_data.get('strategy_mode', 'normal')
            stock_data['strategy_mode'] = 'normal'
            if old_mode != 'normal':
                print(f"✅ 恢复正常模式: {stock_code}")
            return 'normal', '正常模式'

    def should_send_signal(self, stock_code, stock_data, signal_type):
        """判断是否应该发送信号"""
        # 更新策略模式
        strategy_mode, reason = self.update_strategy_mode(stock_code, stock_data)

        # 如果在冷却期且是减半信号，则不发送
        if strategy_mode == 'cooling_down' and signal_type in ['reduce', 'prepare_reduce']:
            print(f"🔇 冷却期内屏蔽减半信号: {stock_code} - {signal_type}")
            return False

        # 其他情况正常发送
        return True

    def get_monitoring_summary(self, stock_data_dict):
        """获取监控摘要"""
        summary = {
            'total_stocks': len(stock_data_dict),
            'reduced_stocks': 0,
            'cooling_down_stocks': 0,
            'strategy_switched_stocks': 0,
            'normal_monitoring_stocks': 0
        }

        for stock_code, stock_data in stock_data_dict.items():
            if stock_data.get('is_reduced', False):
                summary['reduced_stocks'] += 1

                if self.is_in_cooling_period(stock_data):
                    summary['cooling_down_stocks'] += 1

                strategy_mode = stock_data.get('strategy_mode', 'normal')
                if strategy_mode in ['high_ttm', 'negative_ttm']:
                    summary['strategy_switched_stocks'] += 1
                elif strategy_mode == 'normal':
                    summary['normal_monitoring_stocks'] += 1

        return summary

# 创建全局减半监控管理器
reduction_monitor = ReductionMonitoringManager()

def get_security_type(code):
    """根据代码判断证券类型"""
    code_str = str(code)

    # 只保留股票，过滤其他类型
    if code_str.startswith('1'):  # ETF
        return 'ETF'
    elif code_str.startswith('5'):  # 基金
        return '基金'
    elif code_str.startswith('11') or code_str.startswith('12') or code_str.startswith('13'):  # 可转债
        return '可转债'
    elif code_str.startswith('4'):  # 新三板等
        return '其他'
    else:
        return '股票'

def is_stock_only(code):
    """判断是否为股票（用于筛选）"""
    code_str = str(code).zfill(6)

    # 只保留6位数字的A股股票
    if len(code_str) != 6 or not code_str.isdigit():
        return False

    # 特殊排除列表（港股、三板等）
    excluded_codes = {
        '700000',  # 腾讯控股（港股）
        '000700',  # 如果是这个代码的腾讯
        # 添加其他需要排除的特定代码
    }

    if code_str in excluded_codes:
        return False

    # 排除ETF、基金、可转债等
    if (code_str.startswith('1') or  # ETF和基金
        code_str.startswith('5') or  # 基金
        code_str.startswith('11') or  # 可转债
        code_str.startswith('12') or  # 可转债
        code_str.startswith('13') or  # 可转债
        code_str.startswith('4') or   # 新三板等
        code_str.startswith('8') or   # 三板股票
        code_str.startswith('9')):    # 其他非A股
        return False

    # 排除港股通等（通常以特定数字开头）
    if (code_str.startswith('02') or  # 港股
        code_str.startswith('03') and len(code_str) > 6):  # 港股（如果超过6位）
        return False

    # 只保留沪深A股
    if code_str.startswith('6'):  # 沪市A股
        return True
    elif code_str.startswith('0') or code_str.startswith('3'):  # 深市A股和创业板
        return True

    return False

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def gen_secid(rawcode: str) -> str:
    """生成东方财富专用的secid"""
    if rawcode[0] == '6':
        return f'1.{rawcode}'  # 沪市
    return f'0.{rawcode}'      # 深市

def get_yearly_low_price_original(code: str) -> dict:
    """获取股票一年内的最低价（前复权）- 原始实现"""
    
    # 计算一年前的日期
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365)
    
    start_date_str = start_date.strftime('%Y%m%d')
    end_date_str = end_date.strftime('%Y%m%d')
    
    secid = gen_secid(code)
    
    params = {
        'fields1': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13',
        'fields2': 'f51,f52,f53,f54,f55',
        'beg': start_date_str,
        'end': end_date_str,
        'rtntype': '6',
        'secid': secid,
        'klt': '101',  # 日K
        'fqt': '1',    # 前复权
    }
    
    base_url = 'https://push2his.eastmoney.com/api/qt/stock/kline/get'
    url = base_url + '?' + urlencode(params)
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; WOW64; Trident/7.0; Touch; rv:11.0) like Gecko',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.8',
        'Referer': 'http://quote.eastmoney.com/',
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=CONFIG['request_timeout'])
        
        if response.status_code == 200:
            json_data = response.json()
            
            if 'data' in json_data and json_data['data'] and 'klines' in json_data['data']:
                klines = json_data['data']['klines']
                
                if klines:
                    # 找到最低价
                    min_low = float('inf')
                    low_date = None
                    
                    for kline in klines:
                        kline_data = kline.split(',')
                        if len(kline_data) >= 5:
                            low_price = float(kline_data[4])  # 最低价
                            if low_price < min_low:
                                min_low = low_price
                                low_date = kline_data[0]  # 日期
                    
                    return {
                        'yearly_low': min_low,
                        'low_date': low_date,
                        'data_points': len(klines)
                    }
        
        return {'yearly_low': None, 'low_date': None, 'data_points': 0}
        
    except Exception as e:
        print(f"获取股票 {code} 历史数据失败: {e}")
        return {'yearly_low': None, 'low_date': None, 'data_points': 0}

def get_yearly_low_price(code: str) -> dict:
    """获取股票一年内的最低价（前复权）- 优先使用缓存"""
    
    # 首先尝试从缓存获取
    try:
        cache_result = get_yearly_low_from_cache(code)
        if cache_result['success']:
            return {
                'yearly_low': cache_result['yearly_low'],
                'low_date': cache_result['low_date'],
                'data_points': cache_result['data_points']
            }
    except Exception as e:
        print(f"从缓存获取 {code} 年内最低价失败: {e}")
    
    # 缓存获取失败，使用原始方法
    print(f"⚠️ 缓存获取失败，使用实时API获取 {code} 年内最低价...")
    return get_yearly_low_price_original(code)

def get_xueqiu_dividend_yield_ttm(stock_code):
    """
    从雪球网页爬取股息率TTM数据 - 带防限制措施
    """
    try:
        import random
        import time

        # 构造雪球股票代码格式
        if stock_code.startswith('6'):
            symbol = f'SH{stock_code}'
        else:
            symbol = f'SZ{stock_code}'

        # 雪球股票详情页面URL
        url = f'https://xueqiu.com/S/{symbol}'

        # 更完整的请求头，模拟真实浏览器
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Referer': 'https://xueqiu.com/',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Cache-Control': 'max-age=0',
        }

        # 添加随机延迟，避免被限制
        delay = random.uniform(1, 3)
        time.sleep(delay)

        # 获取网页内容
        response = requests.get(url, headers=headers, timeout=20)

        if response.status_code == 200:
            html_content = response.text

            # 检查是否被限制
            if '验证码' in html_content or 'captcha' in html_content.lower():
                print(f"⚠️ 雪球访问受限 {stock_code}，跳过股息率获取")
                return 0

            # 使用正则表达式提取股息率TTM
            import re

            # 查找股息率TTM的多种可能模式
            patterns = [
                r'股息率\(TTM\)：(\d+\.?\d*)%',
                r'股息率\(TTM\).*?(\d+\.?\d*)%',
                r'股息率.*?TTM.*?(\d+\.?\d*)%',
            ]

            dividend_yield = 0
            for pattern in patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                if matches:
                    try:
                        dividend_yield = float(matches[0])
                        if dividend_yield > 0:
                            print(f"✅ 从雪球获取股息率TTM {stock_code}: {dividend_yield}%")
                            break
                    except:
                        continue

            if dividend_yield == 0:
                print(f"⚠️ 雪球未找到股息率TTM数据 {stock_code}")

            return dividend_yield

        print(f"❌ 雪球页面请求失败 {stock_code}: {response.status_code}")
        return 0

    except Exception as e:
        print(f'❌ 从雪球爬取股息率TTM失败 {stock_code}: {e}')
        return 0

def calculate_dividend_yield_ttm(stock_code, current_price=None):
    """
    计算股息率TTM - 从雪球获取

    Args:
        stock_code (str): 股票代码
        current_price (float, optional): 当前价格，保留兼容性但暂不使用

    Returns:
        float: 股息率TTM，保留2位小数
    """
    # current_price参数保留兼容性，但当前实现中暂不使用
    _ = current_price  # 显式标记为未使用，避免警告

    try:
        # 从雪球获取股息率TTM数据
        dividend_yield = get_xueqiu_dividend_yield_ttm(stock_code)

        if dividend_yield > 0:
            return round(dividend_yield, 2)

        return 0

    except Exception as e:
        print(f'计算股息率TTM失败 {stock_code}: {e}')
        return 0

# ==================== 可扩展字段配置系统 ====================

class StrategyFieldConfig:
    """策略字段配置管理器 - 支持动态字段扩展"""

    def __init__(self):
        self.field_definitions = {
            'distance_from_low_pct': {
                'name': '距最低点涨幅',
                'type': 'number',
                'unit': '%',
                'description': '股价相对于年内最低价的涨幅百分比',
                'category': 'technical'
            },
            'pe_ratio': {
                'name': 'PE比率',
                'type': 'number',
                'unit': '',
                'description': 'TTM市盈率，反映股票估值水平',
                'category': 'fundamental'
            },
            'pb_ratio': {
                'name': 'PB比率',
                'type': 'number',
                'unit': '',
                'description': '市净率，股价与每股净资产的比值',
                'category': 'fundamental'
            },
            'profit_margin': {
                'name': '回本盈利涨幅',
                'type': 'number',
                'unit': '%',
                'description': '相对于持仓成本的盈利涨幅百分比',
                'category': 'profit'
            },
            'dividend_yield': {
                'name': '股息率',
                'type': 'number',
                'unit': '%',
                'description': '年化股息收益率',
                'category': 'fundamental'
            },
            'scan_score': {
                'name': '扫雷评分',
                'type': 'number',
                'unit': '分',
                'description': '风险评估评分，分数越高风险越低',
                'category': 'risk'
            },
            'change_pct': {
                'name': '当日涨跌幅',
                'type': 'number',
                'unit': '%',
                'description': '当日股价涨跌幅度',
                'category': 'technical'
            },
            'market_value': {
                'name': '持仓市值',
                'type': 'number',
                'unit': '元',
                'description': '当前持仓的市场价值',
                'category': 'portfolio'
            }
        }

    def get_field_name(self, field_key):
        """获取字段显示名称"""
        return self.field_definitions.get(field_key, {}).get('name', field_key)

    def get_all_fields(self):
        """获取所有可用字段"""
        return list(self.field_definitions.keys())

    def get_fields_by_category(self, category):
        """按类别获取字段"""
        return [key for key, config in self.field_definitions.items()
                if config.get('category') == category]

    def add_field(self, field_key, config):
        """动态添加新字段"""
        self.field_definitions[field_key] = config
        print(f"✅ 已添加新字段: {field_key} - {config.get('name', field_key)}")

    def get_field_config(self, field_key):
        """获取字段完整配置"""
        return self.field_definitions.get(field_key, {})

# 全局字段配置管理器实例
field_config = StrategyFieldConfig()

# ==================== 多策略卖出决策系统 ====================

# ==================== 策略管理模块优化 ====================

class StrategyConfig:
    """策略配置类 - 管理策略的基本信息和参数"""

    def __init__(self, name, description, enabled=True, params=None, weight=1.0, category='default', action='hold'):
        self.name = name
        self.description = description
        self.enabled = enabled
        self.params = params or {}
        self.weight = weight
        self.category = category
        self.action = action

    def to_dict(self):
        """转换为字典格式"""
        return {
            'name': self.name,
            'description': self.description,
            'enabled': self.enabled,
            'params': self.params,
            'weight': self.weight,
            'category': self.category,
            'action': self.action
        }

    @classmethod
    def from_dict(cls, data):
        """从字典创建策略配置"""
        return cls(
            name=data.get('name', ''),
            description=data.get('description', ''),
            enabled=data.get('enabled', True),
            params=data.get('params', {}),
            weight=data.get('weight', 1.0),
            category=data.get('category', 'default'),
            action=data.get('action', 'hold')
        )

class DefaultStrategyFactory:
    """默认策略工厂 - 创建系统预设策略"""

    @staticmethod
    def create_default_strategies():
        """创建所有默认策略"""
        strategies = {}

        # 1. 基础涨幅策略
        strategies.update(DefaultStrategyFactory._create_basic_gain_strategies())

        # 2. 涨停+PB策略
        strategies.update(DefaultStrategyFactory._create_limit_up_pb_strategies())

        # 3. 高TTM策略
        strategies.update(DefaultStrategyFactory._create_high_ttm_strategies())

        # 4. 高TTM+涨停策略
        strategies.update(DefaultStrategyFactory._create_high_ttm_limit_up_strategies())

        # 5. 负TTM+低股息+低扫雷策略
        strategies.update(DefaultStrategyFactory._create_negative_ttm_risk_strategies())

        # 6. 负TTM回本策略
        strategies.update(DefaultStrategyFactory._create_negative_ttm_profit_strategies())

        return strategies

    @staticmethod
    def _create_basic_gain_strategies():
        """创建基础涨幅策略"""
        return {
            'basic_gain_reduce': StrategyConfig(
                name='基础涨幅减半策略',
                description='涨幅≥70%时减半，≥60%时预警',
                params={
                    'main_threshold': 70.0,
                    'warning_offset': 10.0,
                    'warning_threshold': 60.0
                },
                weight=1.0,
                category='basic_gain',
                action='reduce'
            ).to_dict(),
            'basic_gain_clearance': StrategyConfig(
                name='基础涨幅清仓策略',
                description='涨幅≥140%时清仓，≥125%时预警',
                params={
                    'main_threshold': 140.0,
                    'warning_offset': 15.0,
                    'warning_threshold': 125.0
                },
                weight=2.0,
                category='basic_gain',
                action='clearance'
            ).to_dict()
        }

    @staticmethod
    def _create_limit_up_pb_strategies():
        """创建涨停+PB策略"""
        return {
            'limit_up_high_pb_clearance': StrategyConfig(
                name='涨停高PB清仓策略',
                description='涨停 且 PB≥1.75 且 涨幅≥70%时清仓，≥60%时预警',
                params={
                    'limit_up_threshold': 9.8,
                    'pb_threshold': 1.75,
                    'main_threshold': 70.0,
                    'warning_offset': 10.0,
                    'warning_threshold': 60.0
                },
                weight=2.0,
                category='limit_up_pb',
                action='clearance'
            ).to_dict(),
            'limit_up_low_pb_reduce': StrategyConfig(
                name='涨停低PB减半策略',
                description='涨停 且 PB≤1.75 且 涨幅≥70%时减半，≥60%时预警',
                params={
                    'limit_up_threshold': 9.8,
                    'pb_threshold': 1.75,
                    'main_threshold': 70.0,
                    'warning_offset': 10.0,
                    'warning_threshold': 60.0
                },
                weight=1.5,
                category='limit_up_pb',
                action='reduce'
            ).to_dict()
        }

    @staticmethod
    def _create_high_ttm_strategies():
        """创建高TTM策略"""
        return {
            'high_ttm_reduce': StrategyConfig(
                name='高TTM减半策略',
                description='TTM≥30 且 涨幅≥50%时减半，≥40%时预警',
                params={
                    'ttm_threshold': 30.0,
                    'main_threshold': 50.0,
                    'warning_offset': 10.0,
                    'warning_threshold': 40.0
                },
                weight=1.2,
                category='high_ttm',
                action='reduce'
            ).to_dict(),
            'high_ttm_clearance': StrategyConfig(
                name='高TTM清仓策略',
                description='TTM≥30 且 涨幅≥100%时清仓，≥85%时预警',
                params={
                    'ttm_threshold': 30.0,
                    'main_threshold': 100.0,
                    'warning_offset': 15.0,
                    'warning_threshold': 85.0
                },
                weight=2.0,
                category='high_ttm',
                action='clearance'
            ).to_dict()
        }

    @staticmethod
    def _create_high_ttm_limit_up_strategies():
        """创建高TTM+涨停策略"""
        return {
            'high_ttm_limit_up_high_pb_clearance': StrategyConfig(
                name='高TTM涨停高PB清仓策略',
                description='TTM≥30 且 涨停 且 PB≥1.75 且 涨幅≥50%时清仓，≥40%时预警',
                params={
                    'ttm_threshold': 30.0,
                    'limit_up_threshold': 9.8,
                    'pb_threshold': 1.75,
                    'main_threshold': 50.0,
                    'warning_offset': 10.0,
                    'warning_threshold': 40.0
                },
                weight=2.5,
                category='high_ttm_limit_up',
                action='clearance'
            ).to_dict(),
            'high_ttm_limit_up_low_pb_reduce': StrategyConfig(
                name='高TTM涨停低PB减半策略',
                description='TTM≥30 且 涨停 且 PB≤1.75 且 涨幅≥50%时减半，≥40%时预警',
                params={
                    'ttm_threshold': 30.0,
                    'limit_up_threshold': 9.8,
                    'pb_threshold': 1.75,
                    'main_threshold': 50.0,
                    'warning_offset': 10.0,
                    'warning_threshold': 40.0
                },
                weight=1.8,
                category='high_ttm_limit_up',
                action='reduce'
            ).to_dict()
        }

    @staticmethod
    def _create_negative_ttm_risk_strategies():
        """创建负TTM+低股息+低扫雷策略"""
        return {
            'negative_ttm_low_dividend_low_scan_reduce': StrategyConfig(
                name='负TTM低股息低扫雷减半策略',
                description='TTM≤0 且 股息≤2% 且 扫雷分数≤70 且 涨幅≥30%时减半，≥25%时预警',
                params={
                    'ttm_max': 0.0,
                    'dividend_max': 2.0,
                    'scan_score_max': 70,
                    'main_threshold': 30.0,
                    'warning_offset': 5.0,
                    'warning_threshold': 25.0
                },
                weight=1.3,
                category='negative_ttm_risk',
                action='reduce'
            ).to_dict(),
            'negative_ttm_low_dividend_low_scan_clearance': StrategyConfig(
                name='负TTM低股息低扫雷清仓策略',
                description='TTM≤0 且 股息≤2% 且 扫雷分数≤70 且 涨幅≥50%时清仓，≥45%时预警',
                params={
                    'ttm_max': 0.0,
                    'dividend_max': 2.0,
                    'scan_score_max': 70,
                    'main_threshold': 50.0,
                    'warning_offset': 5.0,
                    'warning_threshold': 45.0
                },
                weight=2.2,
                category='negative_ttm_risk',
                action='clearance'
            ).to_dict()
        }

    @staticmethod
    def _create_negative_ttm_profit_strategies():
        """创建负TTM回本策略"""
        return {
            'negative_ttm_profit_clearance': StrategyConfig(
                name='负TTM回本清仓策略',
                description='TTM≤0 且 股息≤3% 且 接近/已回本时清仓',
                params={
                    'ttm_max': 0.0,
                    'dividend_max': 3.0,
                    'require_profit': True,
                    'profit_threshold': 10.0
                },
                weight=2.0,
                category='negative_ttm_profit',
                action='clearance'
            ).to_dict()
        }

class SellStrategyManager:
    """卖出策略管理器 - 优化版本"""

    def __init__(self):
        self.strategies = {}
        self.config_file = 'strategy_config.json'
        print("🔧 初始化策略管理器...")
        self._initialize_strategies()

    def _initialize_strategies(self):
        """初始化策略配置"""
        self.strategies = DefaultStrategyFactory.create_default_strategies()
        self.load_strategy_config()  # 加载保存的配置（会覆盖默认配置）

    def load_strategy_config(self):
        """从文件加载策略配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    print(f"📂 从文件加载策略配置，包含 {len(saved_config)} 个策略")

                    # 完全替换策略配置（包括自定义策略）
                    self.strategies = saved_config

                    # 打印加载的策略
                    self._print_strategy_status(saved_config)

            else:
                print("⚠️ 策略配置文件不存在，使用默认配置")
                self._print_strategy_status(self.strategies)
        except Exception as e:
            print(f"❌ 加载策略配置失败: {e}")
            print("🔄 将使用默认策略配置")

    def save_strategy_config(self):
        """保存策略配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.strategies, f, ensure_ascii=False, indent=2)
            print(f"💾 策略配置已保存到文件")

            # 打印当前策略状态用于调试
            self._print_strategy_status(self.strategies)
        except Exception as e:
            print(f"❌ 保存策略配置失败: {e}")

    def _print_strategy_status(self, strategies):
        """打印策略状态"""
        for strategy_id, config in strategies.items():
            category = config.get('category', 'unknown')
            enabled = config.get('enabled', False)
            name = config.get('name', strategy_id)
            print(f"  📋 {name} ({category}): {'✅' if enabled else '❌'}")

    def get_enabled_strategies(self):
        """获取所有启用的策略"""
        return {
            strategy_id: strategy
            for strategy_id, strategy in self.strategies.items()
            if strategy.get('enabled', False)
        }

    def get_strategy_by_category(self, category):
        """根据类别获取策略"""
        return {
            strategy_id: strategy
            for strategy_id, strategy in self.strategies.items()
            if strategy.get('category') == category
        }

    def update_strategy(self, strategy_id, updates):
        """更新策略配置"""
        if strategy_id in self.strategies:
            self.strategies[strategy_id].update(updates)
            return True
        return False

    def add_custom_strategy(self, strategy_id, strategy_config):
        """添加自定义策略"""
        self.strategies[strategy_id] = strategy_config
        return True

    def remove_strategy(self, strategy_id):
        """删除策略"""
        if strategy_id in self.strategies:
            del self.strategies[strategy_id]
            return True
        return False

    def evaluate_strategy(self, strategy_id, stock_data):
        """评估单个策略 - 优化版本"""
        if strategy_id not in self.strategies:
            return None

        strategy = self.strategies[strategy_id]
        if not strategy.get('enabled', False):
            return None

        try:
            # 使用策略评估器映射表
            evaluator = self._get_strategy_evaluator(strategy_id)
            if evaluator:
                return evaluator(stock_data, strategy['params'], strategy)
            else:
                print(f"⚠️ 未找到策略 {strategy_id} 的评估器")
                return None
        except Exception as e:
            print(f"❌ 评估策略 {strategy_id} 失败: {e}")
            return None

    def _get_strategy_evaluator(self, strategy_id):
        """获取策略评估器"""
        # 策略评估器映射表
        evaluator_map = {
            # 基础涨幅策略
            'basic_gain_reduce': self._evaluate_basic_gain_reduce_strategy,
            'basic_gain_clearance': self._evaluate_basic_gain_clearance_strategy,

            # 涨停+PB策略
            'limit_up_high_pb_clearance': self._evaluate_limit_up_high_pb_clearance_strategy,
            'limit_up_low_pb_reduce': self._evaluate_limit_up_low_pb_reduce_strategy,

            # 高TTM策略
            'high_ttm_reduce': self._evaluate_high_ttm_reduce_strategy,
            'high_ttm_clearance': self._evaluate_high_ttm_clearance_strategy,

            # 高TTM+涨停策略
            'high_ttm_limit_up_high_pb_clearance': self._evaluate_high_ttm_limit_up_high_pb_clearance_strategy,
            'high_ttm_limit_up_low_pb_reduce': self._evaluate_high_ttm_limit_up_low_pb_reduce_strategy,

            # 负TTM+低股息+低扫雷策略
            'negative_ttm_low_dividend_low_scan_reduce': self._evaluate_negative_ttm_low_dividend_low_scan_reduce_strategy,
            'negative_ttm_low_dividend_low_scan_clearance': self._evaluate_negative_ttm_low_dividend_low_scan_clearance_strategy,

            # 负TTM回本策略
            'negative_ttm_profit_clearance': self._evaluate_negative_ttm_profit_clearance_strategy,
        }

        # 处理自定义策略
        if strategy_id.startswith('custom_'):
            return self._evaluate_custom_strategy

        return evaluator_map.get(strategy_id)

    # ==================== 新策略评估方法 ====================

    def _evaluate_basic_gain_reduce_strategy(self, stock_data, params, strategy):
        """评估基础涨幅减半策略：涨幅≥70%时减半，≥60%时预警准备"""
        distance_pct = stock_data.get('distance_from_low_pct', 0)

        main_threshold = params.get('main_threshold', params.get('gain_threshold', 70.0))
        warning_threshold = params.get('warning_threshold', main_threshold - params.get('warning_offset', 10.0))

        print(f"    📈 基础涨幅减半策略评估: distance_pct={distance_pct:.1f}%")
        print(f"    📊 参数: 执行阈值={main_threshold}%, 预警阈值={warning_threshold}%")

        if distance_pct >= main_threshold:
            print(f"    ✅ 触发减半信号: {distance_pct:.1f}% >= {main_threshold}%")
            return {
                'signal': 'reduce',
                'reason': f"【立即减半】基础涨幅达标({distance_pct:.1f}%)",
                'score': min(100, distance_pct),
                'strategy': strategy['name'],
                'action': 'reduce',
                'action_guidance': '建议立即挂单减半或市价减半',
                'urgency': 'immediate'
            }
        elif distance_pct >= warning_threshold:
            remaining_pct = main_threshold - distance_pct
            print(f"    ⚠️ 触发减半预警: {distance_pct:.1f}% >= {warning_threshold}%")
            return {
                'signal': 'prepare_reduce',
                'reason': f"【准备减半】即将触发减半操作，还需{remaining_pct:.1f}%涨幅",
                'score': distance_pct,
                'strategy': strategy['name'],
                'action': 'prepare_reduce',
                'action_guidance': f'建议提前挂单准备减半，目标价位附近挂单',
                'urgency': 'prepare',
                'remaining_percent': remaining_pct
            }

        print(f"    ❌ 未达到预警阈值: {distance_pct:.1f}% < {warning_threshold:.1f}%")
        return None

    def _evaluate_basic_gain_clearance_strategy(self, stock_data, params, strategy):
        """评估基础涨幅清仓策略：涨幅≥140%时清仓，≥125%时预警准备"""
        distance_pct = stock_data.get('distance_from_low_pct', 0)

        main_threshold = params.get('main_threshold', params.get('gain_threshold', 140.0))
        warning_threshold = params.get('warning_threshold', main_threshold - params.get('warning_offset', 15.0))

        print(f"    🚀 基础涨幅清仓策略评估: distance_pct={distance_pct:.1f}%")
        print(f"    📊 参数: 执行阈值={main_threshold}%, 预警阈值={warning_threshold}%")

        if distance_pct >= main_threshold:
            print(f"    ✅ 触发清仓信号: {distance_pct:.1f}% >= {main_threshold}%")
            return {
                'signal': 'clearance',
                'reason': f"【立即清仓】基础涨幅达标({distance_pct:.1f}%)",
                'score': min(100, distance_pct),
                'strategy': strategy['name'],
                'action': 'clearance',
                'action_guidance': '建议立即清仓，风险较高',
                'urgency': 'immediate'
            }
        elif distance_pct >= warning_threshold:
            remaining_pct = main_threshold - distance_pct
            print(f"    ⚠️ 触发清仓预警: {distance_pct:.1f}% >= {warning_threshold}%")
            return {
                'signal': 'prepare_clearance',
                'reason': f"【准备清仓】即将触发清仓操作，还需{remaining_pct:.1f}%涨幅",
                'score': distance_pct,
                'strategy': strategy['name'],
                'action': 'prepare_clearance',
                'action_guidance': f'建议提前挂单准备清仓，密切关注价格变化',
                'urgency': 'prepare',
                'remaining_percent': remaining_pct
            }

        print(f"    ❌ 未达到预警阈值: {distance_pct:.1f}% < {warning_threshold:.1f}%")
        return None

    def _evaluate_limit_up_high_pb_clearance_strategy(self, stock_data, params, strategy):
        """评估涨停高PB清仓策略：涨停 且 PB≥1.75 且 涨幅≥70%时清仓"""
        change_pct = abs(stock_data.get('change_pct', 0))
        pb_ratio = stock_data.get('pb_ratio', 0)
        distance_pct = stock_data.get('distance_from_low_pct', 0)
        warning_threshold = params['gain_threshold'] - params['warning_offset']

        print(f"    🔥 涨停高PB清仓策略评估: change_pct={change_pct:.1f}%, pb_ratio={pb_ratio:.2f}, distance_pct={distance_pct:.1f}%")

        is_limit_up = change_pct >= params['limit_up_threshold']
        is_high_pb = pb_ratio >= params['pb_threshold']
        is_high_gain = distance_pct >= params['gain_threshold']
        is_warning_gain = distance_pct >= warning_threshold

        if is_limit_up and is_high_pb and is_high_gain:
            print(f"    ✅ 触发清仓信号")
            return {
                'signal': 'clearance',
                'reason': f"【清仓】涨停高PB(涨停={change_pct:.1f}%, PB={pb_ratio:.2f}, 涨幅={distance_pct:.1f}%)",
                'score': 100,
                'strategy': strategy['name'],
                'action': 'clearance'
            }
        elif is_limit_up and is_high_pb and is_warning_gain:
            print(f"    ⚠️ 触发预警信号")
            return {
                'signal': 'warning',
                'reason': f"建议提前挂单，涨停高PB策略距离清仓还有{params['gain_threshold'] - distance_pct:.1f}%涨幅",
                'score': 90,
                'strategy': strategy['name'],
                'action': 'warning'
            }

        print(f"    ❌ 条件不满足: 涨停={is_limit_up}, 高PB={is_high_pb}, 高涨幅={is_high_gain}")
        return None

    def _evaluate_limit_up_low_pb_reduce_strategy(self, stock_data, params, strategy):
        """评估涨停低PB减半策略：涨停 且 PB≤1.75 且 涨幅≥70%时减半"""
        change_pct = abs(stock_data.get('change_pct', 0))
        pb_ratio = stock_data.get('pb_ratio', 0)
        distance_pct = stock_data.get('distance_from_low_pct', 0)
        warning_threshold = params['gain_threshold'] - params['warning_offset']

        print(f"    📊 涨停低PB减半策略评估: change_pct={change_pct:.1f}%, pb_ratio={pb_ratio:.2f}, distance_pct={distance_pct:.1f}%")

        is_limit_up = change_pct >= params['limit_up_threshold']
        is_low_pb = pb_ratio <= params['pb_threshold']
        is_high_gain = distance_pct >= params['gain_threshold']
        is_warning_gain = distance_pct >= warning_threshold

        if is_limit_up and is_low_pb and is_high_gain:
            print(f"    ✅ 触发减半信号")
            return {
                'signal': 'reduce',
                'reason': f"【减半】涨停低PB(涨停={change_pct:.1f}%, PB={pb_ratio:.2f}, 涨幅={distance_pct:.1f}%)",
                'score': 85,
                'strategy': strategy['name'],
                'action': 'reduce'
            }
        elif is_limit_up and is_low_pb and is_warning_gain:
            print(f"    ⚠️ 触发预警信号")
            return {
                'signal': 'warning',
                'reason': f"建议提前挂单，涨停低PB策略距离减半还有{params['gain_threshold'] - distance_pct:.1f}%涨幅",
                'score': 75,
                'strategy': strategy['name'],
                'action': 'warning'
            }

        print(f"    ❌ 条件不满足: 涨停={is_limit_up}, 低PB={is_low_pb}, 高涨幅={is_high_gain}")
        return None

    def _evaluate_high_ttm_reduce_strategy(self, stock_data, params, strategy):
        """评估高TTM减半策略：TTM≥30 且 涨幅≥50%时减半，≥40%时预警准备"""
        ttm_pe = stock_data.get('pe_ratio', 0)
        distance_pct = stock_data.get('distance_from_low_pct', 0)

        main_threshold = params.get('main_threshold', params.get('gain_threshold', 50.0))
        warning_threshold = params.get('warning_threshold', main_threshold - params.get('warning_offset', 10.0))

        print(f"    📈 高TTM减半策略评估: ttm_pe={ttm_pe}, distance_pct={distance_pct:.1f}%")

        if ttm_pe is None or ttm_pe < params['ttm_threshold']:
            print(f"    ❌ TTM不满足条件: {ttm_pe} < {params['ttm_threshold']}")
            return None

        if distance_pct >= main_threshold:
            print(f"    ✅ 触发减半信号: TTM={ttm_pe:.1f} >= {params['ttm_threshold']}, 涨幅={distance_pct:.1f}% >= {main_threshold}%")
            return {
                'signal': 'reduce',
                'reason': f"【立即减半】高TTM中涨幅(TTM={ttm_pe:.1f}, 涨幅={distance_pct:.1f}%)",
                'score': min(100, ttm_pe + distance_pct),
                'strategy': strategy['name'],
                'action': 'reduce',
                'action_guidance': '建议立即减半，高TTM股票风险较高',
                'urgency': 'immediate'
            }
        elif distance_pct >= warning_threshold:
            remaining_pct = main_threshold - distance_pct
            print(f"    ⚠️ 触发减半预警: TTM={ttm_pe:.1f} >= {params['ttm_threshold']}, 涨幅={distance_pct:.1f}% >= {warning_threshold:.1f}%")
            return {
                'signal': 'prepare_reduce',
                'reason': f"【准备减半】高TTM即将触发减半，还需{remaining_pct:.1f}%涨幅",
                'score': ttm_pe + distance_pct,
                'strategy': strategy['name'],
                'action': 'prepare_reduce',
                'action_guidance': f'建议提前挂单准备减半，高TTM股票注意风险控制',
                'urgency': 'prepare',
                'remaining_percent': remaining_pct
            }

        print(f"    ❌ 涨幅不满足预警条件: {distance_pct:.1f}% < {warning_threshold:.1f}%")
        return None

    def _evaluate_high_ttm_clearance_strategy(self, stock_data, params, strategy):
        """评估高TTM清仓策略：TTM≥30 且 涨幅≥100%时清仓，≥85%时预警准备"""
        ttm_pe = stock_data.get('pe_ratio', 0)
        distance_pct = stock_data.get('distance_from_low_pct', 0)

        main_threshold = params.get('main_threshold', params.get('gain_threshold', 100.0))
        warning_threshold = params.get('warning_threshold', main_threshold - params.get('warning_offset', 15.0))

        print(f"    🚀 高TTM清仓策略评估: ttm_pe={ttm_pe}, distance_pct={distance_pct:.1f}%")

        if ttm_pe is None or ttm_pe < params['ttm_threshold']:
            print(f"    ❌ TTM不满足条件: {ttm_pe} < {params['ttm_threshold']}")
            return None

        if distance_pct >= main_threshold:
            print(f"    ✅ 触发清仓信号: TTM={ttm_pe:.1f} >= {params['ttm_threshold']}, 涨幅={distance_pct:.1f}% >= {main_threshold}%")
            return {
                'signal': 'clearance',
                'reason': f"【立即清仓】高TTM高涨幅(TTM={ttm_pe:.1f}, 涨幅={distance_pct:.1f}%)",
                'score': min(100, ttm_pe + distance_pct),
                'strategy': strategy['name'],
                'action': 'clearance',
                'action_guidance': '建议立即清仓，高TTM高涨幅风险极高',
                'urgency': 'immediate'
            }
        elif distance_pct >= warning_threshold:
            remaining_pct = main_threshold - distance_pct
            print(f"    ⚠️ 触发清仓预警: TTM={ttm_pe:.1f} >= {params['ttm_threshold']}, 涨幅={distance_pct:.1f}% >= {warning_threshold:.1f}%")
            return {
                'signal': 'prepare_clearance',
                'reason': f"【准备清仓】高TTM即将触发清仓，还需{remaining_pct:.1f}%涨幅",
                'score': ttm_pe + distance_pct,
                'strategy': strategy['name'],
                'action': 'prepare_clearance',
                'action_guidance': f'建议提前挂单准备清仓，高TTM股票风险控制优先',
                'urgency': 'prepare',
                'remaining_percent': remaining_pct
            }

        print(f"    ❌ 涨幅不满足预警条件: {distance_pct:.1f}% < {warning_threshold:.1f}%")
        return None

    def _evaluate_high_ttm_limit_up_high_pb_clearance_strategy(self, stock_data, params, strategy):
        """评估高TTM涨停高PB清仓策略：TTM≥30 且 涨停 且 PB≥1.75 且 涨幅≥50%时清仓"""
        ttm_pe = stock_data.get('pe_ratio', 0)
        change_pct = abs(stock_data.get('change_pct', 0))
        pb_ratio = stock_data.get('pb_ratio', 0)
        distance_pct = stock_data.get('distance_from_low_pct', 0)
        warning_threshold = params['gain_threshold'] - params['warning_offset']

        print(f"    ⚡ 高TTM涨停高PB清仓策略评估: ttm_pe={ttm_pe}, change_pct={change_pct:.1f}%, pb_ratio={pb_ratio:.2f}, distance_pct={distance_pct:.1f}%")

        is_high_ttm = ttm_pe is not None and ttm_pe >= params['ttm_threshold']
        is_limit_up = change_pct >= params['limit_up_threshold']
        is_high_pb = pb_ratio >= params['pb_threshold']
        is_high_gain = distance_pct >= params['gain_threshold']
        is_warning_gain = distance_pct >= warning_threshold

        if is_high_ttm and is_limit_up and is_high_pb and is_high_gain:
            print(f"    ✅ 触发清仓信号")
            return {
                'signal': 'clearance',
                'reason': f"【清仓】高TTM涨停高PB(TTM={ttm_pe:.1f}, 涨停={change_pct:.1f}%, PB={pb_ratio:.2f}, 涨幅={distance_pct:.1f}%)",
                'score': 100,
                'strategy': strategy['name'],
                'action': 'clearance'
            }
        elif is_high_ttm and is_limit_up and is_high_pb and is_warning_gain:
            print(f"    ⚠️ 触发预警信号")
            return {
                'signal': 'warning',
                'reason': f"建议提前挂单，高TTM涨停高PB策略距离清仓还有{params['gain_threshold'] - distance_pct:.1f}%涨幅",
                'score': 95,
                'strategy': strategy['name'],
                'action': 'warning'
            }

        print(f"    ❌ 条件不满足: 高TTM={is_high_ttm}, 涨停={is_limit_up}, 高PB={is_high_pb}, 高涨幅={is_high_gain}")
        return None

    def _evaluate_high_ttm_limit_up_low_pb_reduce_strategy(self, stock_data, params, strategy):
        """评估高TTM涨停低PB减半策略：TTM≥30 且 涨停 且 PB≤1.75 且 涨幅≥50%时减半"""
        ttm_pe = stock_data.get('pe_ratio', 0)
        change_pct = abs(stock_data.get('change_pct', 0))
        pb_ratio = stock_data.get('pb_ratio', 0)
        distance_pct = stock_data.get('distance_from_low_pct', 0)
        warning_threshold = params['gain_threshold'] - params['warning_offset']

        print(f"    📊 高TTM涨停低PB减半策略评估: ttm_pe={ttm_pe}, change_pct={change_pct:.1f}%, pb_ratio={pb_ratio:.2f}, distance_pct={distance_pct:.1f}%")

        is_high_ttm = ttm_pe is not None and ttm_pe >= params['ttm_threshold']
        is_limit_up = change_pct >= params['limit_up_threshold']
        is_low_pb = pb_ratio <= params['pb_threshold']
        is_high_gain = distance_pct >= params['gain_threshold']
        is_warning_gain = distance_pct >= warning_threshold

        if is_high_ttm and is_limit_up and is_low_pb and is_high_gain:
            print(f"    ✅ 触发减半信号")
            return {
                'signal': 'reduce',
                'reason': f"【减半】高TTM涨停低PB(TTM={ttm_pe:.1f}, 涨停={change_pct:.1f}%, PB={pb_ratio:.2f}, 涨幅={distance_pct:.1f}%)",
                'score': 90,
                'strategy': strategy['name'],
                'action': 'reduce'
            }
        elif is_high_ttm and is_limit_up and is_low_pb and is_warning_gain:
            print(f"    ⚠️ 触发预警信号")
            return {
                'signal': 'warning',
                'reason': f"建议提前挂单，高TTM涨停低PB策略距离减半还有{params['gain_threshold'] - distance_pct:.1f}%涨幅",
                'score': 80,
                'strategy': strategy['name'],
                'action': 'warning'
            }

        print(f"    ❌ 条件不满足: 高TTM={is_high_ttm}, 涨停={is_limit_up}, 低PB={is_low_pb}, 高涨幅={is_high_gain}")
        return None

    def _evaluate_negative_ttm_low_dividend_low_scan_reduce_strategy(self, stock_data, params, strategy):
        """评估负TTM低股息低扫雷减半策略：TTM≤0 且 股息≤2% 且 扫雷分数≤70 且 涨幅≥30%时减半"""
        ttm_pe = stock_data.get('pe_ratio', 0)
        dividend_yield = stock_data.get('dividend_yield', 0)
        scan_score = stock_data.get('scan_score', 100)
        distance_pct = stock_data.get('distance_from_low_pct', 0)
        warning_threshold = params['gain_threshold'] - params['warning_offset']

        # 清仓策略阈值（50%）
        clearance_threshold = 50.0
        clearance_warning_threshold = clearance_threshold - 5.0  # 45%开始清仓预警

        print(f"    ⚠️ 负TTM低股息低扫雷减半策略评估: ttm_pe={ttm_pe}, dividend_yield={dividend_yield:.1f}%, scan_score={scan_score}, distance_pct={distance_pct:.1f}%")

        is_negative_ttm = ttm_pe is not None and ttm_pe <= params['ttm_max']
        is_low_dividend = dividend_yield <= params['dividend_max']
        is_low_scan = scan_score <= params['scan_score_max']
        is_high_gain = distance_pct >= params['gain_threshold']
        is_warning_gain = distance_pct >= warning_threshold
        is_clearance_warning = distance_pct >= clearance_warning_threshold

        # 检查基本条件
        if not (is_negative_ttm and is_low_dividend and is_low_scan):
            print(f"    ❌ 条件不满足: 负TTM={is_negative_ttm}, 低股息={is_low_dividend}, 低扫雷={is_low_scan}")
            return None

        # 策略优先级：清仓预警 > 减半信号 > 减半预警
        if is_clearance_warning:
            # 45-50%：清仓预警（紧急）
            remaining_pct = clearance_threshold - distance_pct
            print(f"    🚨 触发清仓预警信号（距离清仓还有{remaining_pct:.1f}%）")
            return {
                'signal': 'warning',
                'reason': f"紧急预警：距离负TTM清仓策略触发还有{remaining_pct:.1f}%涨幅",
                'score': 90,  # 高优先级
                'strategy': '负TTM清仓预警',
                'action': 'warning'
            }
        elif is_high_gain:
            # 30-45%：已超减半线，提醒清仓预警
            remaining_pct = clearance_threshold - distance_pct
            print(f"    ⚠️ 已超减半线，提醒清仓预警（距离清仓还有{remaining_pct:.1f}%）")
            return {
                'signal': 'warning',
                'reason': f"已超减半线，距离负TTM清仓策略触发还有{remaining_pct:.1f}%涨幅",
                'score': 80,
                'strategy': '负TTM清仓预警',
                'action': 'warning'
            }
        elif is_warning_gain:
            # 25-30%：减半预警
            remaining_pct = params['gain_threshold'] - distance_pct
            print(f"    ⚠️ 触发减半预警信号（距离减半还有{remaining_pct:.1f}%）")
            return {
                'signal': 'warning',
                'reason': f"建议提前挂单，距离负TTM减半策略触发还有{remaining_pct:.1f}%涨幅",
                'score': 75,
                'strategy': strategy['name'],
                'action': 'warning'
            }

        print(f"    ❌ 涨幅不足: {distance_pct:.1f}% < {warning_threshold:.1f}%")
        return None

    def _evaluate_negative_ttm_low_dividend_low_scan_clearance_strategy(self, stock_data, params, strategy):
        """评估负TTM低股息低扫雷清仓策略：TTM≤0 且 股息≤2% 且 扫雷分数≤70 且 涨幅≥50%时清仓"""
        ttm_pe = stock_data.get('pe_ratio', 0)
        dividend_yield = stock_data.get('dividend_yield', 0)
        scan_score = stock_data.get('scan_score', 100)
        distance_pct = stock_data.get('distance_from_low_pct', 0)
        warning_threshold = params['gain_threshold'] - params['warning_offset']

        print(f"    🚨 负TTM低股息低扫雷清仓策略评估: ttm_pe={ttm_pe}, dividend_yield={dividend_yield:.1f}%, scan_score={scan_score}, distance_pct={distance_pct:.1f}%")

        is_negative_ttm = ttm_pe is not None and ttm_pe <= params['ttm_max']
        is_low_dividend = dividend_yield <= params['dividend_max']
        is_low_scan = scan_score <= params['scan_score_max']
        is_high_gain = distance_pct >= params['gain_threshold']
        is_warning_gain = distance_pct >= warning_threshold

        if is_negative_ttm and is_low_dividend and is_low_scan and is_high_gain:
            print(f"    ✅ 触发清仓信号")
            return {
                'signal': 'clearance',
                'reason': f"【清仓】负TTM低股息低扫雷(TTM={ttm_pe:.1f}, 股息={dividend_yield:.1f}%, 扫雷={scan_score}, 涨幅={distance_pct:.1f}%)",
                'score': 95,
                'strategy': strategy['name'],
                'action': 'clearance'
            }
        elif is_negative_ttm and is_low_dividend and is_low_scan and is_warning_gain:
            print(f"    ⚠️ 触发清仓预警信号")
            return {
                'signal': 'warning',
                'reason': f"建议提前挂单，距离负TTM清仓策略触发还有{params['gain_threshold'] - distance_pct:.1f}%涨幅",
                'score': 85,
                'strategy': strategy['name'],
                'action': 'warning'
            }

        print(f"    ❌ 条件不满足: 负TTM={is_negative_ttm}, 低股息={is_low_dividend}, 低扫雷={is_low_scan}, 高涨幅={is_high_gain}")
        return None

    def _evaluate_negative_ttm_profit_clearance_strategy(self, stock_data, params, strategy):
        """评估负TTM回本清仓策略：TTM≤0 且 股息≤3% 且 接近/已回本时清仓"""
        ttm_pe = stock_data.get('pe_ratio', 0)
        dividend_yield = stock_data.get('dividend_yield', 0)
        current_price = stock_data.get('price', 0)
        cost_price = stock_data.get('unit_cost', 0)

        # 计算距离回本的百分比
        if cost_price > 0:
            distance_to_profit_pct = ((cost_price - current_price) / current_price) * 100
        else:
            distance_to_profit_pct = 100  # 无成本价信息，设为很大值

        profit_threshold = params.get('profit_threshold', 10.0)  # 回本阈值

        print(f"    💰 负TTM回本清仓策略评估: ttm_pe={ttm_pe}, dividend_yield={dividend_yield:.1f}%, current_price={current_price}, cost_price={cost_price}, distance_to_profit={distance_to_profit_pct:.1f}%")

        is_negative_ttm = ttm_pe is not None and ttm_pe <= params['ttm_max']
        is_low_dividend = dividend_yield <= params['dividend_max']
        is_profit = current_price >= cost_price  # 已回本
        is_near_profit = 0 < distance_to_profit_pct <= profit_threshold  # 接近回本

        # 优先级：已回本清仓 > 接近回本预警
        if is_negative_ttm and is_low_dividend and is_profit:
            print(f"    ✅ 触发清仓信号（已回本）")
            return {
                'signal': 'clearance',
                'reason': f"【清仓】负TTM回本(TTM={ttm_pe:.1f}, 股息={dividend_yield:.1f}%, 已回本)",
                'score': 100,
                'strategy': strategy['name'],
                'action': 'clearance'
            }
        elif is_negative_ttm and is_low_dividend and is_near_profit:
            # 接近回本预警
            print(f"    ⚠️ 触发回本预警信号（距离回本{distance_to_profit_pct:.1f}%）")
            return {
                'signal': 'warning',
                'reason': f"负TTM回本预警：距离回本还需{distance_to_profit_pct:.1f}%涨幅",
                'score': 90,
                'strategy': '负TTM回本预警',
                'action': 'warning'
            }

        print(f"    ❌ 条件不满足: 负TTM={is_negative_ttm}, 低股息={is_low_dividend}, 已回本={is_profit}, 接近回本={is_near_profit}")
        print(f"    🔍 详细调试: distance_to_profit_pct={distance_to_profit_pct:.1f}%, profit_threshold={profit_threshold:.1f}%")
        print(f"    🔍 接近回本条件: 0 < {distance_to_profit_pct:.1f} <= {profit_threshold:.1f} = {0 < distance_to_profit_pct <= profit_threshold}")
        return None

    def _evaluate_custom_strategy(self, stock_data, strategy):
        """评估自定义策略"""
        conditions = strategy.get('conditions', [])
        if not conditions:
            return None

        # 评估所有条件
        all_conditions_met = True
        triggered_reasons = []
        total_score = 0

        for condition in conditions:
            field = condition.get('field')
            operator = condition.get('operator')
            value = condition.get('value')

            if not all([field, operator, value is not None]):
                continue

            # 特殊处理 profit_margin 字段
            if field == 'profit_margin':
                stock_value = self._get_actual_profit_margin(stock_data)
            else:
                stock_value = stock_data.get(field, 0)
                if stock_value is None:
                    stock_value = 0

            condition_met = False
            if operator == '>':
                condition_met = stock_value > value
            elif operator == '>=':
                condition_met = stock_value >= value
            elif operator == '<':
                condition_met = stock_value < value
            elif operator == '<=':
                condition_met = stock_value <= value
            elif operator == '==':
                condition_met = stock_value == value

            if condition_met:
                # 使用字段配置管理器获取字段名称
                field_name = field_config.get_field_name(field)
                triggered_reasons.append(f"{field_name}{operator}{value}")
                total_score += abs(stock_value - value)
            else:
                all_conditions_met = False

        if all_conditions_met and triggered_reasons:
            return {
                'signal': 'sell',
                'reason': f"自定义策略: {' & '.join(triggered_reasons)}",
                'score': min(100, total_score),
                'strategy': strategy.get('name', '自定义策略')
            }

        return None

    def _get_actual_profit_margin(self, stock_data):
        """获取实际的盈利涨幅（负数表示亏损，正数表示盈利）"""
        profit_status = stock_data.get('profit_status', 'unknown')
        profit_margin = stock_data.get('profit_margin', 0)

        if profit_status == 'need_gain':
            # 还没回本，返回负值表示亏损
            return -profit_margin
        elif profit_status == 'profit':
            # 已经盈利，返回正值
            return profit_margin
        else:
            # 未知状态，返回0
            return 0

# ==================== 卖出信号优先级控制系统 ====================

class SellSignalPriorityManager:
    """卖出信号优先级控制管理器"""

    def __init__(self):
        self.config = SELL_SIGNAL_PRIORITY_CONFIG.copy()
        self.load_config()

    def load_config(self):
        """从文件加载优先级配置"""
        try:
            config_file = self.config['config_file']
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    self.config.update(saved_config)
                print(f"✅ 已加载卖出信号优先级配置: 手动优先={self.config['manual_priority_over_auto']}")
            else:
                # 首次运行，创建默认配置文件
                self.save_config()
                print(f"📝 已创建默认卖出信号优先级配置文件")
        except Exception as e:
            print(f"❌ 加载卖出信号优先级配置失败: {e}")
            # 使用默认配置
            self.config = SELL_SIGNAL_PRIORITY_CONFIG.copy()

    def save_config(self):
        """保存优先级配置到文件"""
        try:
            config_file = self.config['config_file']
            self.config['last_updated'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            print(f"💾 卖出信号优先级配置已保存")
        except Exception as e:
            print(f"❌ 保存卖出信号优先级配置失败: {e}")

    def set_manual_priority(self, enabled):
        """设置手动标记优先级"""
        self.config['manual_priority_over_auto'] = bool(enabled)
        self.save_config()
        print(f"🔄 手动标记优先级已设置为: {'启用' if enabled else '禁用'}")

    def is_manual_priority_enabled(self):
        """检查是否启用手动标记优先级"""
        return self.config.get('manual_priority_over_auto', True)

    def get_config(self):
        """获取当前配置"""
        return self.config.copy()

# 全局策略管理器实例
strategy_manager = SellStrategyManager()

# 全局优先级管理器实例
priority_manager = SellSignalPriorityManager()

def recalculate_all_sell_signals():
    """重新计算所有股票的卖出信号"""
    global stock_data
    try:
        updated_count = 0
        for _, stock_info in stock_data.items():  # 使用_忽略股票代码
            # 重新计算卖出信号
            sell_signal = calculate_sell_signal(stock_info)
            stock_info['sell_signal'] = sell_signal['signal']
            stock_info['sell_reason'] = sell_signal['reason']
            stock_info['sell_color'] = sell_signal['color']
            stock_info['sell_priority'] = sell_signal['priority']
            updated_count += 1

        print(f"✅ 已重新计算 {updated_count} 只股票的卖出信号")

        # 保存更新后的数据
        save_stock_data()

    except Exception as e:
        print(f"❌ 重新计算卖出信号失败: {e}")

def calculate_sell_signal(stock_data):
    """
    多策略卖出信号计算 - 支持优先级控制
    返回: {
        'signal': 'sell'|'warning'|'hold',
        'reason': '卖出原因',
        'color': '显示颜色',
        'priority': 优先级数字,
        'strategies': [触发的策略列表],
        'details': '详细信息',
        'source': 'manual'|'auto'  # 信号来源
    }
    """
    try:
        # 检查是否有手动标记的状态，且优先级控制启用
        if priority_manager.is_manual_priority_enabled():
            # 检查各种手动状态标记
            # 🛑 已清仓 - 最低优先级，排在最后
            if stock_data.get('is_cleared', False):
                print(f"🔒 检测到已清仓状态，设置为最低优先级")
                return {
                    'signal': 'sell',
                    'reason': '已清仓',
                    'color': '#dc3545',  # 红色表示已清仓
                    'priority': 999,  # 最低优先级，排在最后
                    'strategies': [],
                    'details': f"已于 {stock_data.get('cleared_time', '未知时间')} 标记为清仓",
                    'source': 'manual'
                }

            # ✅ 已减半 - 根据监控状态设置优先级
            if stock_data.get('is_reduced', False):
                strategy_mode = stock_data.get('strategy_mode', 'normal')

                # 根据减半监控状态设置不同优先级
                if strategy_mode == 'negative_ttm':
                    priority = 15  # ⚠️ 负TTM策略 - 需要关注
                    reason = '已减半(负TTM)'
                    color = '#dc3545'  # 红色警告
                elif strategy_mode == 'high_ttm':
                    priority = 16  # 📊 高TTM策略 - 需要关注
                    reason = '已减半(高TTM)'
                    color = '#fd7e14'  # 橙色警告
                elif strategy_mode == 'cooling_down':
                    priority = 17  # 🧊 冷却期 - 正常监控
                    reason = '已减半(冷却期)'
                    color = '#17a2b8'  # 蓝色信息
                else:
                    priority = 18  # ✅ 正常监控
                    reason = '已减半(正常)'
                    color = '#28a745'  # 绿色正常

                print(f"🔒 检测到已减半状态({strategy_mode})，优先级: {priority}")
                return {
                    'signal': 'sell',
                    'reason': reason,
                    'color': color,
                    'priority': priority,
                    'strategies': [],
                    'details': f"已于 {stock_data.get('reduced_time', '未知时间')} 标记为减半",
                    'source': 'manual'
                }

            # 📋 已挂单等自定义状态
            if stock_data.get('custom_status', False):
                custom_status_text = stock_data.get('custom_status_text', '自定义状态')
                print(f"🔒 检测到自定义状态: {custom_status_text}")

                # 特殊处理"已挂单"状态
                if custom_status_text and ('挂单' in custom_status_text or '待成交' in custom_status_text):
                    priority = 10  # ⏳ 已挂单 - 等待成交
                    color = '#ffc107'  # 黄色表示等待
                    print(f"📋 挂单状态，设置优先级: {priority}")
                else:
                    priority = 20  # 其他自定义状态
                    color = '#6c757d'  # 灰色表示手动设置

                return {
                    'signal': stock_data.get('custom_status_type', 'hold'),
                    'reason': custom_status_text,
                    'color': color,
                    'priority': priority,
                    'strategies': [],
                    'details': f"手动设置于 {stock_data.get('custom_status_time', '未知时间')}",
                    'source': 'manual'
                }

        # 如果没有手动状态或手动优先级未启用，进行自动策略计算
        triggered_strategies = []

        print(f"🔍 开始评估股票 {stock_data.get('name', 'Unknown')} 的自动卖出信号...")

        # 评估所有启用的策略
        for strategy_id, strategy_config in strategy_manager.strategies.items():
            if strategy_config.get('enabled', False):
                print(f"  📊 评估策略: {strategy_config['name']} (enabled: {strategy_config['enabled']})")
                result = strategy_manager.evaluate_strategy(strategy_id, stock_data)
                if result:
                    # 检查减半监控是否允许发送此信号
                    stock_code = stock_data.get('code', 'Unknown')
                    signal_type = result.get('signal', 'unknown')

                    if reduction_monitor.should_send_signal(stock_code, stock_data, signal_type):
                        print(f"    ✅ 策略触发: {result['reason']}")
                        triggered_strategies.append(result)
                    else:
                        print(f"    🔇 策略触发但被减半监控过滤: {result['reason']}")
                else:
                    print(f"    ❌ 策略未触发")
            else:
                print(f"  ⏸️ 跳过禁用策略: {strategy_config['name']}")

        print(f"📋 触发的自动策略数量: {len(triggered_strategies)}")

        if not triggered_strategies:
            # 无触发策略时返回持有信号
            # 注：可以在这里添加距离最近策略的涨幅差距计算逻辑

            return {
                'signal': 'hold',
                'reason': '继续持有',
                'color': '#2ed573',  # 绿色
                'priority': 50,  # 💚 持有 - 正常状态
                'strategies': [],
                'details': '无触发策略',
                'source': 'auto'
            }

        # 按信号类型和评分排序 - 支持新的预警信号类型
        immediate_clearance = [s for s in triggered_strategies if s['signal'] == 'clearance']
        immediate_reduce = [s for s in triggered_strategies if s['signal'] == 'reduce']
        prepare_clearance = [s for s in triggered_strategies if s['signal'] == 'prepare_clearance']
        prepare_reduce = [s for s in triggered_strategies if s['signal'] == 'prepare_reduce']
        sell_strategies = [s for s in triggered_strategies if s['signal'] == 'sell']
        warning_strategies = [s for s in triggered_strategies if s['signal'] == 'warning']

        # 优先级：立即清仓 > 立即减半 > 准备清仓 > 准备减半 > 卖出 > 预警
        if immediate_clearance:
            best_strategy = max(immediate_clearance, key=lambda x: x['score'])
            reasons = [s['reason'] for s in immediate_clearance]
            return {
                'signal': 'clearance',
                'reason': ' | '.join(reasons),
                'color': '#8b0000',  # 深红色表示立即清仓
                'priority': 1,
                'strategies': immediate_clearance,
                'details': f"主要策略: {best_strategy.get('strategy', '未知策略')}",
                'action_guidance': best_strategy.get('action_guidance', '建议立即清仓'),
                'urgency': 'immediate',
                'source': 'auto'
            }

        elif immediate_reduce:
            best_strategy = max(immediate_reduce, key=lambda x: x['score'])
            reasons = [s['reason'] for s in immediate_reduce]
            return {
                'signal': 'reduce',
                'reason': ' | '.join(reasons),
                'color': '#ff6b35',  # 橙红色表示立即减半
                'priority': 2,
                'strategies': immediate_reduce,
                'details': f"主要策略: {best_strategy.get('strategy', '未知策略')}",
                'action_guidance': best_strategy.get('action_guidance', '建议立即减半'),
                'urgency': 'immediate',
                'source': 'auto'
            }

        elif prepare_clearance:
            best_strategy = max(prepare_clearance, key=lambda x: x['score'])
            reasons = [s['reason'] for s in prepare_clearance]
            return {
                'signal': 'prepare_clearance',
                'reason': ' | '.join(reasons),
                'color': '#ff8c00',  # 橙红色表示准备清仓
                'priority': 3,
                'strategies': prepare_clearance,
                'details': f"主要策略: {best_strategy.get('strategy', '未知策略')}",
                'action_guidance': best_strategy.get('action_guidance', '建议准备清仓操作'),
                'urgency': 'prepare',
                'remaining_percent': best_strategy.get('remaining_percent'),
                'source': 'auto'
            }

        elif prepare_reduce:
            best_strategy = max(prepare_reduce, key=lambda x: x['score'])
            reasons = [s['reason'] for s in prepare_reduce]
            return {
                'signal': 'prepare_reduce',
                'reason': ' | '.join(reasons),
                'color': '#ffa500',  # 橙色表示准备减半
                'priority': 4,
                'strategies': prepare_reduce,
                'details': f"主要策略: {best_strategy.get('strategy', '未知策略')}",
                'action_guidance': best_strategy.get('action_guidance', '建议准备减半操作'),
                'urgency': 'prepare',
                'remaining_percent': best_strategy.get('remaining_percent'),
                'source': 'auto'
            }

        elif sell_strategies:
            # 有卖出信号，选择评分最高的
            best_strategy = max(sell_strategies, key=lambda x: x['score'])
            reasons = [s['reason'] for s in sell_strategies]

            return {
                'signal': 'sell',
                'reason': ' | '.join(reasons),
                'color': '#ff4757',  # 红色表示卖出
                'priority': 5,  # 卖出第五优先级
                'strategies': sell_strategies,
                'details': f"主要策略: {best_strategy.get('strategy', '未知策略')}",
                'source': 'auto'
            }

        elif warning_strategies:
            # 只有预警信号（兼容旧版本）
            best_strategy = max(warning_strategies, key=lambda x: x['score'])
            reasons = [s['reason'] for s in warning_strategies]

            return {
                'signal': 'warning',
                'reason': ' | '.join(reasons),
                'color': '#ffa502',  # 橙色
                'priority': 6,  # 自动预警优先级
                'strategies': warning_strategies,
                'details': f"主要策略: {best_strategy.get('strategy', '未知策略')}",
                'source': 'auto'
            }

    except Exception as e:
        print(f"❌ 计算卖出信号失败: {e}")
        import traceback
        traceback.print_exc()
        return {
            'signal': 'hold',
            'reason': '数据异常',
            'color': '#747d8c',  # 灰色
            'priority': 60,  # 异常状态
            'strategies': [],
            'details': f'计算错误: {str(e)}',
            'source': 'auto'
        }

# ==================== 数据获取模块优化 ====================

class StockDataFetcher:
    """股票数据获取器 - 统一管理所有数据获取逻辑"""

    def __init__(self):
        self.base_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Referer': 'http://quote.eastmoney.com/',
        }
        self.timeout = CONFIG.get('request_timeout', 10)

    def get_market_secid(self, stock_code):
        """根据股票代码确定市场标识"""
        return f'1.{stock_code}' if stock_code.startswith('6') else f'0.{stock_code}'

    def safe_get_value(self, data, key, default=None):
        """安全获取数据值，处理'-'等无效值"""
        value = data.get(key, default)
        return None if value == '-' else value

    def get_realtime_data(self, stock_code):
        """获取股票实时数据 - 东方财富API"""
        secid = self.get_market_secid(stock_code)

        url = 'http://push2.eastmoney.com/api/qt/ulist.np/get'
        params = {
            'fltt': '2',
            'invt': '2',
            'fields': 'f12,f14,f2,f3,f4,f23,f115,f114,f9,f133,f162,f173,f116,f127,f128,f129,f47,f48',
            'secids': secid,
            '_': str(int(time.time() * 1000))
        }

        try:
            response = requests.get(url, params=params, headers=self.base_headers, timeout=self.timeout)

            if response.status_code == 200:
                data = json.loads(response.text)

                if 'data' in data and 'diff' in data['data'] and len(data['data']['diff']) > 0:
                    stock_info = data['data']['diff'][0]

                    # 计算股息率TTM
                    current_price = stock_info.get('f2', 0)
                    dividend_yield = calculate_dividend_yield_ttm(stock_code, current_price)

                    return {
                        'code': stock_info.get('f12', ''),
                        'name': stock_info.get('f14', ''),
                        'price': stock_info.get('f2', 0),
                        'change': stock_info.get('f4', 0),
                        'change_pct': stock_info.get('f3', 0),
                        'dividend_yield': dividend_yield,
                        'pb_ratio': self.safe_get_value(stock_info, 'f23'),
                        'pe_ttm': self.safe_get_value(stock_info, 'f115'),
                        'industry': '',  # 后续单独获取
                        'volume': stock_info.get('f47', 0),
                        'turnover': stock_info.get('f48', 0),
                        'market_cap': self.safe_get_value(stock_info, 'f116', 0),
                    }

        except Exception as e:
            print(f"获取股票 {stock_code} 实时数据失败: {e}")

        return None

    def get_industry_info(self, stock_code):
        """获取股票行业信息"""
        secid = self.get_market_secid(stock_code)

        url = 'http://push2.eastmoney.com/api/qt/stock/get'
        params = {
            'fltt': '2',
            'invt': '2',
            'fields': 'f127,f128,f129',  # 行业、板块、概念
            'secid': secid,
            '_': str(int(time.time() * 1000))
        }

        try:
            response = requests.get(url, params=params, headers=self.base_headers, timeout=self.timeout)

            if response.status_code == 200:
                data = json.loads(response.text).get('data', {})

                if data:
                    return {
                        'industry': data.get('f127', ''),
                        'sector': data.get('f128', ''),
                        'concept': data.get('f129', ''),
                    }

        except Exception as e:
            print(f"获取股票 {stock_code} 行业信息失败: {e}")

        return {'industry': '', 'sector': '', 'concept': ''}

# 创建全局数据获取器实例
stock_data_fetcher = StockDataFetcher()

def get_eastmoney_stock_data_v2(stock_code):
    """获取股票实时数据 - 保持向后兼容"""
    return stock_data_fetcher.get_realtime_data(stock_code)

def get_stock_industry_info(stock_code):
    """获取股票行业信息 - 保持向后兼容"""
    return stock_data_fetcher.get_industry_info(stock_code)

class StockInfoProcessor:
    """股票信息处理器 - 整合各种数据源"""

    def __init__(self, data_fetcher):
        self.data_fetcher = data_fetcher

    def get_complete_stock_info(self, code: str) -> dict:
        """获取完整的股票信息"""
        # 获取实时数据
        real_data = self.data_fetcher.get_realtime_data(code)
        if not real_data:
            return None

        # 获取年内最低价信息
        yearly_low_info = get_yearly_low_price(code)

        # 获取行业信息（如果主接口没有获取到）
        if not real_data.get('industry'):
            industry_data = self.data_fetcher.get_industry_info(code)
            real_data['industry'] = industry_data.get('industry', '')

        # 计算距离最低点的涨幅
        distance_from_low_pct = self._calculate_distance_from_low(
            real_data['price'],
            yearly_low_info.get('yearly_low')
        )

        return {
            'code': code,
            'name': real_data['name'],
            'price': real_data['price'],
            'change': real_data['change'],
            'change_pct': real_data['change_pct'],
            'volume': real_data.get('volume', 0),
            'turnover': real_data.get('turnover', 0),
            'pe_ratio': real_data['pe_ttm'],
            'pb_ratio': real_data['pb_ratio'],
            'dividend_yield': real_data['dividend_yield'],
            'market_cap': real_data.get('market_cap', 0),
            'industry': real_data['industry'],
            'yearly_low': yearly_low_info.get('yearly_low'),
            'low_date': yearly_low_info.get('low_date'),
            'distance_from_low_pct': distance_from_low_pct,
            'security_type': get_security_type(code),
            'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

    def _calculate_distance_from_low(self, current_price, yearly_low):
        """计算距离年内最低点的涨幅"""
        if yearly_low and yearly_low > 0 and current_price > 0:
            return ((current_price - yearly_low) / yearly_low) * 100
        return None

# 创建全局股票信息处理器
stock_info_processor = StockInfoProcessor(stock_data_fetcher)

def get_stock_info(code: str) -> dict:
    """获取股票基本信息 - 保持向后兼容"""
    return stock_info_processor.get_complete_stock_info(code)

def load_stock_list():
    """加载股票列表 - 只加载股票，过滤其他证券类型"""
    global imported_stock_list

    # 优先使用导入的股票列表
    if imported_stock_list:
        # 过滤只保留股票
        filtered_list = []
        for stock in imported_stock_list:
            code = str(stock['代码']).zfill(6)
            if is_stock_only(code):
                filtered_list.append(stock)
            else:
                print(f"⚠️ 跳过非股票证券: {code} {stock.get('名称', '')}")
        print(f"📊 过滤后保留 {len(filtered_list)} 只股票")
        return filtered_list

    # 否则尝试从CSV文件加载
    csv_file = 'stocks_list.csv'
    if os.path.exists(csv_file):
        try:
            df = pd.read_csv(csv_file, encoding='utf-8-sig')
            print(f"📊 CSV文件列名: {list(df.columns)}")
            stock_list = []
            for _, row in df.iterrows():
                code = str(row['代码']).zfill(6)
                if is_stock_only(code):
                    # 支持多种持仓数量列名
                    holdings = 0
                    found_col = None
                    for col in ['持有数量', '持仓数量', '持仓', '数量', 'quantity']:
                        if col in row and pd.notna(row[col]):
                            holdings = int(float(row[col]))  # 先转float再转int，处理小数
                            found_col = col
                            break

                    if found_col and holdings > 0:
                        print(f"   📈 {code}: 从列'{found_col}'读取持仓 {holdings}")

                    stock_list.append({
                        '代码': code,
                        '名称': row.get('名称', ''),
                        '持仓数量': holdings
                    })
                else:
                    print(f"⚠️ 跳过非股票证券: {code} {row.get('名称', '')}")
            return stock_list
        except Exception as e:
            print(f"加载股票列表失败: {e}")

    return []

def update_single_stock(stock_info):
    """更新单只股票数据"""
    global stock_data

    code = stock_info['代码']
    name = stock_info.get('名称', '')
    holdings = stock_info.get('持仓数量', 0)
    unit_cost = stock_info.get('单位成本', 0)  # 获取单位成本

    print(f"正在处理 {code} {name}，持仓数量: {holdings}，单位成本: {unit_cost}...")

    # 获取股票信息
    info = get_stock_info(code)

    if info:
        # 添加持仓数量和成本信息
        info['holdings'] = int(holdings)  # 使用holdings作为键名
        info['quantity'] = int(holdings)  # 同时保留quantity以兼容前端
        info['unit_cost'] = float(unit_cost) if unit_cost != 0 else 0  # 修复：允许负成本显示
        info['name'] = name if name else info.get('name', '')

        # 计算市值
        current_price = info['price']
        if current_price > 0 and holdings != 0:
            info['market_value'] = round(current_price * holdings, 2)
            print(f"   💰 市值计算: {current_price} × {holdings} = {info['market_value']}")
        else:
            info['market_value'] = 0.0
            print(f"   ⚠️ 市值为0: 价格={current_price}, 持仓={holdings}")

        # 计算回本涨幅（更直观的显示方式）
        if unit_cost != 0 and current_price > 0:  # 修改：允许负成本
            if unit_cost > 0 and current_price < unit_cost:
                # 亏损：显示还需要涨多少才能回本
                info['profit_margin'] = round(((unit_cost - current_price) / current_price) * 100, 2)
                info['profit_status'] = 'need_gain'  # 标记为需要涨幅
                print(f"   📈 还需涨幅: ({unit_cost} - {current_price}) / {current_price} × 100% = +{info['profit_margin']}% 才能回本")
            elif unit_cost > 0:
                # 正成本盈利：显示已经盈利多少
                info['profit_margin'] = round(((current_price - unit_cost) / unit_cost) * 100, 2)
                info['profit_status'] = 'profit'  # 标记为已盈利
                print(f"   � 已盈利: ({current_price} - {unit_cost}) / {unit_cost} × 100% = +{info['profit_margin']}%")
            else:
                # 负成本的情况：显示为已盈利
                info['profit_margin'] = round(((current_price - abs(unit_cost)) / abs(unit_cost)) * 100, 2)
                info['profit_status'] = 'profit'  # 负成本标记为已盈利
                print(f"   💰 负成本盈利: ({current_price} - {abs(unit_cost)}) / {abs(unit_cost)} × 100% = +{info['profit_margin']}%")
        else:
            info['profit_margin'] = None
            info['profit_status'] = 'unknown'
            print(f"   ⚠️ 无法计算回本涨幅: 成本={unit_cost}, 价格={current_price}")

        # 获取扫雷数据
        try:
            scan_result = scan_client.get_stock_score(code)
            if scan_result:
                info['scan_score'] = scan_result['score']
                info['scan_risk_level'] = scan_result['risk_level']
                info['scan_risk_color'] = scan_result['risk_color']
                info['scan_triggered_risks'] = scan_result['triggered_risks']
                info['scan_display_text'] = f"{scan_result['score']}分"
                print(f"🔍 扫雷: {scan_result['score']}分 ({scan_result['risk_level']})")
            else:
                # 添加默认扫雷数据
                info['scan_score'] = 0
                info['scan_risk_level'] = '数据异常'
                info['scan_risk_color'] = '#cccccc'
                info['scan_triggered_risks'] = 0
                info['scan_display_text'] = '获取失败'
        except Exception as e:
            print(f"获取扫雷数据失败: {e}")
            # 添加默认扫雷数据
            info['scan_score'] = 0
            info['scan_risk_level'] = '数据异常'
            info['scan_risk_color'] = '#cccccc'
            info['scan_triggered_risks'] = 0
            info['scan_display_text'] = '获取失败'

        # 状态保护机制：检查是否存在手动状态设置
        existing_data = stock_data.get(code, {})
        has_manual_status = (
            existing_data.get('custom_status', False) or
            existing_data.get('is_cleared', False) or
            existing_data.get('is_reduced', False)
        )

        # 如果启用手动优先级且存在手动状态，保护手动设置的状态
        if priority_manager.is_manual_priority_enabled() and has_manual_status:
            print(f"🔒 检测到手动状态设置，保护现有状态不被覆盖")

            # 保护的字段列表
            protected_fields = [
                'sell_signal', 'sell_reason', 'sell_color', 'sell_priority',
                'custom_status', 'custom_status_type', 'custom_status_text',
                'custom_status_emoji', 'custom_status_time',
                'is_cleared', 'cleared_time', 'is_reduced', 'reduced_time'
            ]

            # 保护手动设置的字段
            for field in protected_fields:
                if field in existing_data:
                    info[field] = existing_data[field]

            print(f"   🛡️ 已保护手动状态: {info.get('sell_reason', '未知状态')}")
        else:
            # 没有手动状态设置或手动优先级未启用，正常计算卖出信号
            sell_signal = calculate_sell_signal(info)
            info['sell_signal'] = sell_signal['signal']
            info['sell_reason'] = sell_signal['reason']
            info['sell_color'] = sell_signal['color']
            info['sell_priority'] = sell_signal['priority']

            print(f"   🤖 自动计算卖出信号: {sell_signal['signal']} - {sell_signal['reason']}")

        # 更新减半监控策略模式（修复冷却器显示问题）
        if info.get('is_reduced', False):
            try:
                reduction_monitor.update_strategy_mode(code, info)
                print(f"   🧊 已更新减半监控状态: {info.get('strategy_mode', 'unknown')}")
            except Exception as e:
                print(f"   ⚠️ 更新减半监控状态失败: {e}")

        # 存储到全局数据
        stock_data[code] = info

        print(f"✅ {code} {info['name']} 价格: {info['price']:.2f} 涨跌: {info['change_pct']:.2f}% 卖出信号: {info.get('sell_signal', 'unknown')}")

        # 检查是否需要发送策略提醒 - 支持新的信号类型
        if info.get('sell_signal') in ['clearance', 'reduce', 'sell', 'warning']:
            try:
                # 获取策略名称
                strategy_name = None
                if 'strategies' in sell_signal and sell_signal['strategies']:
                    strategy_name = sell_signal['strategies'][0].get('strategy', '未知策略')

                wechat_alert.send_sell_alert(
                    stock_code=code,
                    stock_name=info['name'],
                    alert_type=info['sell_signal'],
                    reason=info.get('sell_reason', '未知原因'),
                    current_price=info['price'],
                    distance_from_low=info.get('distance_from_low_pct'),
                    profit_status=info.get('profit_status'),
                    profit_margin=info.get('profit_margin'),
                    strategy_name=strategy_name
                )
            except Exception as e:
                print(f"发送策略提醒失败: {e}")

        # 检查是否需要发送高涨幅提醒（保留原有功能，但也要避免重复）
        elif info.get('distance_from_low_pct') and info['distance_from_low_pct'] >= CONFIG['alert_threshold']:
            try:
                if wechat_alert.should_send_alert(code, 'high_gain'):
                    message = f"📈 高涨幅提醒\n股票: {info['name']} ({code})\n当前价格: {info['price']:.2f}元\n距最低点涨幅: {info['distance_from_low_pct']:.1f}%\n年内最低价: {info['yearly_low']:.2f}元 ({info['low_date']})\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                    if wechat_alert.send_message(message):
                        wechat_alert.mark_alert_sent(code, 'high_gain')
                        print(f"📱 已发送高涨幅提醒: {info['name']} 涨幅 {info['distance_from_low_pct']:.1f}%")
                else:
                    print(f"📱 跳过重复高涨幅提醒: {info['name']}")
            except Exception as e:
                print(f"发送高涨幅提醒失败: {e}")
    else:
        print(f"❌ {code} {name} 获取数据失败")

def update_all_stocks():
    """更新所有股票数据"""
    global last_update_time

    stock_list = load_stock_list()
    if not stock_list:
        print("❌ 没有找到股票列表")
        print(f"   导入列表状态: {len(imported_stock_list)} 只股票")
        return

    print(f"\n🚀 开始更新 {len(stock_list)} 只股票数据...")
    print(f"📋 股票列表预览:")
    for i, stock in enumerate(stock_list[:3]):  # 显示前3只股票
        print(f"   {i+1}. {stock['代码']} {stock.get('名称', '')} 持仓:{stock.get('持仓数量', 0)}股")
    start_time = time.time()
    success_count = 0

    for i, stock in enumerate(stock_list, 1):
        print(f"\n[{i}/{len(stock_list)}] ", end="")

        try:
            update_single_stock(stock)
            success_count += 1
        except Exception as e:
            print(f"❌ 更新失败: {e}")

        # 间隔时间
        if i < len(stock_list):
            time.sleep(CONFIG['stock_interval'])

    end_time = time.time()
    elapsed_time = end_time - start_time
    last_update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    success_rate = success_count / len(stock_list) * 100

    print(f"\n✅ 更新完成！")
    print(f"   📈 成功率: {success_rate:.1f}% ({success_count}/{len(stock_list)})")
    print(f"   ⏱️ 耗时: {elapsed_time:.1f}秒")
    print(f"   🕐 更新时间: {last_update_time}")

    # 保存更新后的数据
    save_stock_data()

def background_update():
    """后台智能更新股票数据 - 只在交易时间更新，支持启停控制"""
    global update_thread_running
    update_thread_running = True
    round_count = 0

    print(f"🚀 后台自动更新线程已启动")

    while update_thread_running:
        # 检查自动更新是否被禁用
        if not auto_update_enabled:
            print(f"⏸️  自动更新已禁用，线程暂停...")
            while not auto_update_enabled and update_thread_running:
                time.sleep(10)  # 每10秒检查一次是否重新启用
            if not update_thread_running:
                break
            print(f"▶️  自动更新已重新启用，继续运行...")

        round_count += 1
        current_time = datetime.now()

        # 获取交易状态
        trading_status = trading_monitor.get_trading_status()
        should_update = trading_monitor.should_update_data(include_auction=True)  # 包含集合竞价
        update_interval = trading_monitor.get_update_interval(CONFIG['round_interval'])

        print(f"\n{'='*60}")
        print(f"🔄 第 {round_count} 轮检查 - {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📊 交易状态: {trading_status['message']}")
        print(f"🔄 自动更新: {'启用' if auto_update_enabled else '禁用'}")
        print(f"{'='*60}")

        if should_update and auto_update_enabled:
            print(f"✅ 当前为交易时间且自动更新已启用，开始更新股票数据...")
            update_all_stocks()
            print(f"✅ 股票数据更新完成")
        else:
            if not should_update:
                print(f"⏸️  当前为休市时间，跳过数据更新")
                print(f"📅 {trading_status['message']}")
            elif not auto_update_enabled:
                print(f"⏸️  自动更新已禁用，跳过数据更新")

        print(f"\n⏰ 下一轮检查将在 {update_interval} 秒后开始")
        if update_interval >= 60:
            print(f"   (约 {update_interval/60:.1f} 分钟后)")

        # 分段睡眠，以便能够及时响应停止信号
        sleep_time = 0
        while sleep_time < update_interval and update_thread_running:
            time.sleep(min(5, update_interval - sleep_time))  # 每5秒检查一次
            sleep_time += 5

    update_thread_running = False
    print(f"🛑 后台自动更新线程已停止")

# ==================== API管理模块优化 ====================

class APIResponseHandler:
    """API响应处理器 - 统一处理API响应格式和错误处理"""

    @staticmethod
    def success(data=None, message="操作成功"):
        """成功响应"""
        response = {
            'success': True,
            'message': message
        }
        if data is not None:
            response['data'] = data
        return jsonify(response)

    @staticmethod
    def error(message="操作失败", status_code=500, error_code=None):
        """错误响应"""
        response = {
            'success': False,
            'message': message
        }
        if error_code:
            response['error_code'] = error_code
        return jsonify(response), status_code

    @staticmethod
    def handle_exception(func):
        """异常处理装饰器"""
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                print(f"API异常 {func.__name__}: {e}")
                return APIResponseHandler.error(f"操作失败: {str(e)}")
        wrapper.__name__ = func.__name__
        return wrapper

class DataCleaner:
    """数据清理器 - 确保数据可以JSON序列化"""

    @staticmethod
    def clean_stock_data(stock_data_dict):
        """清理股票数据，确保JSON可序列化"""
        clean_data = []
        for stock in stock_data_dict.values():
            clean_stock = {}
            for key, value in stock.items():
                # 处理可能的NaN、None或无穷大值
                if value is None:
                    clean_stock[key] = None
                elif isinstance(value, (int, float)):
                    if str(value).lower() in ['nan', 'inf', '-inf']:
                        clean_stock[key] = 0
                    else:
                        clean_stock[key] = value
                else:
                    clean_stock[key] = str(value) if value is not None else ''
            clean_data.append(clean_stock)
        return clean_data

# Flask路由 - 优化版本
@app.route('/')
def index():
    """主页面"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/stocks')
@APIResponseHandler.handle_exception
def get_stocks():
    """获取股票数据API - 优化版本"""
    # 更新减半监控状态（确保显示正确的策略模式）
    reduction_updated_count = 0
    for stock_code, stock_info in stock_data.items():
        if stock_info.get('is_reduced', False):
            try:
                reduction_monitor.update_strategy_mode(stock_code, stock_info)
                reduction_updated_count += 1
            except Exception as e:
                print(f"⚠️ 更新减半监控状态失败 {stock_code}: {e}")

    if reduction_updated_count > 0:
        print(f"🧊 API调用时更新了 {reduction_updated_count} 只已减半股票的监控状态")

    # 计算统计数据
    stats = calculate_statistics()

    # 清理股票数据，确保JSON可序列化
    clean_data = DataCleaner.clean_stock_data(stock_data)

    return jsonify({
        'success': True,
        'data': clean_data,
        'last_update': last_update_time,
        'count': len(clean_data),
        'stats': stats
    })

@app.route('/api/trading-status')
@APIResponseHandler.handle_exception
def get_trading_status():
    """获取交易状态API - 优化版本"""
    status = trading_monitor.get_trading_status()
    return APIResponseHandler.success(status)

@app.route('/api/refresh-holidays', methods=['POST'])
@APIResponseHandler.handle_exception
def refresh_holidays():
    """刷新节假日数据API - 优化版本"""
    trading_monitor.refresh_holidays()
    return APIResponseHandler.success(message='节假日数据已刷新')

@app.route('/api/clear-data', methods=['POST'])
@APIResponseHandler.handle_exception
def clear_data():
    """清空数据API - 优化版本"""
    global stock_data, last_update_time
    stock_data = {}
    last_update_time = None
    save_stock_data()
    return APIResponseHandler.success(message='数据已清空')

@app.route('/api/upload-holdings', methods=['POST'])
def upload_holdings():
    """上传持仓表格API"""
    try:
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'message': '没有选择文件'
            }), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'success': False,
                'message': '没有选择文件'
            }), 400

        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{timestamp}_{filename}"
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)

            # 读取Excel文件
            try:
                df = pd.read_excel(filepath)

                # 验证必要的列 - 支持多种列名
                code_column = None
                quantity_column = None

                # 查找代码列
                for col in ['代码', 'code', '股票代码']:
                    if col in df.columns:
                        code_column = col
                        break

                # 查找数量列
                for col in ['持有数量', '持仓数量', '数量', 'quantity', '股数']:
                    if col in df.columns:
                        quantity_column = col
                        break

                if not code_column or not quantity_column:
                    return jsonify({
                        'success': False,
                        'message': f'缺少必要的列。需要包含代码列（代码/code/股票代码）和数量列（持有数量/持仓数量/数量/quantity/股数）'
                    }), 400

                # 处理数据 - 只处理股票，使用正确的列名
                global imported_stock_list
                imported_stock_list = []
                skipped_count = 0

                # 首先过滤掉代码列为空的行
                df = df.dropna(subset=[code_column])

                # 确保代码列转换为字符串
                df[code_column] = df[code_column].astype(str)

                # 处理持有数量，确保是数值类型
                df[quantity_column] = pd.to_numeric(df[quantity_column], errors='coerce').fillna(0).astype(int)

                # 过滤掉持有数量为0或负数的股票
                df = df[df[quantity_column] > 0]

                for _, row in df.iterrows():
                    code = str(row[code_column]).zfill(6)
                    quantity = int(row[quantity_column])
                    name = str(row.get('名称', '')) if pd.notna(row.get('名称', '')) else ''

                    # 读取单位成本
                    unit_cost = 0
                    for cost_col in ['单位成本', '成本价', '成本', 'unit_cost']:
                        if cost_col in row and pd.notna(row[cost_col]):
                            unit_cost = float(row[cost_col])
                            break

                    if len(code) == 6 and code.isdigit() and quantity > 0:
                        if is_stock_only(code):
                            imported_stock_list.append({
                                '代码': code,
                                '名称': name,
                                '持仓数量': quantity,
                                '单位成本': unit_cost
                            })
                        else:
                            skipped_count += 1
                            # 详细显示跳过的原因
                            skip_reason = "未知原因"
                            if code.startswith('1') or code.startswith('5'):
                                skip_reason = "ETF/基金"
                            elif code.startswith('11') or code.startswith('12') or code.startswith('13'):
                                skip_reason = "可转债"
                            elif code.startswith('4') or code.startswith('8'):
                                skip_reason = "三板股票"
                            elif code in ['700000', '000700']:
                                skip_reason = "港股"
                            elif "腾讯" in name or "艾" in name:
                                skip_reason = "港股/三板"

                            print(f"⚠️ 跳过非A股证券: {code} {name} ({skip_reason})")

                message = f'成功导入 {len(imported_stock_list)} 只股票'
                if skipped_count > 0:
                    message += f'，跳过 {skipped_count} 只非股票证券'

                # 保存导入的股票列表
                save_imported_list()

                # 上传成功后立即更新股票数据
                print(f"📈 开始更新股票数据...")
                update_all_stocks()

                # 自动恢复股票状态
                restore_result = auto_restore_stock_states()
                if restore_result['success'] and restore_result['restored_count'] > 0:
                    message += f"，自动恢复 {restore_result['restored_count']} 只股票的状态"

                return jsonify({
                    'success': True,
                    'message': message,
                    'count': len(imported_stock_list),
                    'restore_info': restore_result
                })

            except Exception as e:
                return jsonify({
                    'success': False,
                    'message': f'读取文件失败: {str(e)}'
                }), 400

        return jsonify({
            'success': False,
            'message': '不支持的文件格式'
        }), 400

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'上传失败: {str(e)}'
        }), 500

@app.route('/api/refresh-data', methods=['POST'])
def refresh_data():
    """重新获取数据API"""
    try:
        if not imported_stock_list:
            return jsonify({
                'success': False,
                'message': '没有找到导入的股票列表，请先上传Excel文件'
            }), 400

        # 在后台线程中更新数据
        def update_in_background():
            update_all_stocks()

        thread = threading.Thread(target=update_in_background)
        thread.daemon = True
        thread.start()

        return jsonify({
            'success': True,
            'message': f'数据更新已开始，正在更新 {len(imported_stock_list)} 只股票，请稍后刷新页面查看结果'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'更新失败: {str(e)}'
        }), 500

def calculate_statistics():
    """计算统计数据 - 只统计股票"""
    if not stock_data:
        return {
            'total_market_value': 0,
            'stock_count': 0,
            'industry_distribution': {}
        }

    # 总市值（现在只有股票）
    total_market_value = sum(stock.get('market_value', 0) for stock in stock_data.values())
    stock_count = len(stock_data)

    # 行业分布统计
    industry_distribution = {}

    for stock in stock_data.values():
        industry = stock.get('industry', '未知行业')
        if not industry:
            industry = '未知行业'
        market_value = stock.get('market_value', 0)

        if industry not in industry_distribution:
            industry_distribution[industry] = {'market_value': 0, 'count': 0}

        industry_distribution[industry]['market_value'] += market_value
        industry_distribution[industry]['count'] += 1

    # 计算行业分布百分比
    for industry in industry_distribution:
        if total_market_value > 0:
            industry_distribution[industry]['percentage'] = (
                industry_distribution[industry]['market_value'] / total_market_value * 100
            )
        else:
            industry_distribution[industry]['percentage'] = 0

    # 按市值排序
    industry_distribution = dict(
        sorted(industry_distribution.items(),
               key=lambda x: x[1]['market_value'],
               reverse=True)
    )

    return {
        'total_market_value': total_market_value,
        'stock_count': stock_count,
        'industry_distribution': industry_distribution
    }

@app.route('/api/cache-status')
def get_cache_status():
    """获取缓存状态API"""
    try:
        cache_status = check_cache_status()
        return jsonify({
            'success': True,
            'data': cache_status
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取缓存状态失败: {str(e)}'
        }), 500

@app.route('/api/scan-cache-info')
def get_scan_cache_info():
    """获取扫雷缓存信息API"""
    try:
        cache_info = scan_client.get_cache_info()
        return jsonify({
            'success': True,
            'data': cache_info
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取扫雷缓存信息失败: {str(e)}'
        }), 500

@app.route('/api/stock-list-status')
def get_stock_list_status():
    """获取股票列表状态API"""
    try:
        stock_list = load_stock_list()
        return jsonify({
            'success': True,
            'data': {
                'imported_count': len(imported_stock_list),
                'filtered_count': len(stock_list),
                'sample_stocks': stock_list[:5] if stock_list else []
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取股票列表状态失败: {str(e)}'
        }), 500

@app.route('/api/wechat-alert-stats')
def get_wechat_alert_stats():
    """获取企业微信提醒统计API"""
    try:
        today = datetime.now().strftime('%Y-%m-%d')
        today_alerts = {}
        total_alerts = len(wechat_alert.sent_alerts)

        # 统计今天的提醒
        for key, alert_info in wechat_alert.sent_alerts.items():
            if key.endswith(today):
                alert_type = alert_info.get('alert_type', 'unknown')
                if alert_type not in today_alerts:
                    today_alerts[alert_type] = 0
                today_alerts[alert_type] += 1

        return jsonify({
            'success': True,
            'data': {
                'today_alerts': today_alerts,
                'total_alerts': total_alerts,
                'today_date': today
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取企业微信提醒统计失败: {str(e)}'
        }), 500

@app.route('/api/update-single-stock/<stock_code>', methods=['POST'])
def update_single_stock_api(stock_code):
    """更新单只股票数据API"""
    try:
        global stock_data

        # 验证股票代码
        if not stock_code or len(stock_code) != 6:
            return jsonify({
                'success': False,
                'message': '无效的股票代码'
            }), 400

        # 检查股票是否在持仓列表中
        if stock_code not in stock_data:
            return jsonify({
                'success': False,
                'message': f'股票 {stock_code} 不在持仓列表中'
            }), 404

        old_data = stock_data[stock_code].copy()

        # 获取持仓数量（保持不变）
        holdings = old_data.get('holdings', 0)

        print(f"🔄 开始更新单只股票: {stock_code} ({old_data.get('name', '未知')})")

        # 获取最新股票数据
        new_data = get_stock_info(stock_code)

        if new_data:
            # 保持持仓数量不变
            new_data['holdings'] = holdings

            # 重新计算市值
            if new_data['price'] > 0 and holdings != 0:
                new_data['market_value'] = round(new_data['price'] * holdings, 2)
            else:
                new_data['market_value'] = 0.0

            # 获取成本价（如果有的话）
            unit_cost = old_data.get('unit_cost', 0)
            new_data['unit_cost'] = unit_cost

            # 计算回本涨幅
            if unit_cost > 0 and new_data['price'] > 0:
                profit_margin = ((new_data['price'] - unit_cost) / unit_cost) * 100
                new_data['profit_margin'] = profit_margin
                new_data['profit_status'] = 'profit' if profit_margin > 0 else 'loss'
            else:
                new_data['profit_margin'] = None
                new_data['profit_status'] = 'unknown'

            # 状态保护机制：检查是否存在手动状态设置
            has_manual_status = (
                old_data.get('custom_status', False) or
                old_data.get('is_cleared', False) or
                old_data.get('is_reduced', False)
            )

            # 如果启用手动优先级且存在手动状态，保护手动设置的状态
            if priority_manager.is_manual_priority_enabled() and has_manual_status:
                print(f"🔒 检测到手动状态设置，保护现有状态不被单个更新覆盖")

                # 保护的字段列表
                protected_fields = [
                    'sell_signal', 'sell_reason', 'sell_color', 'sell_priority',
                    'custom_status', 'custom_status_type', 'custom_status_text',
                    'custom_status_emoji', 'custom_status_time',
                    'is_cleared', 'cleared_time', 'is_reduced', 'reduced_time'
                ]

                # 保护手动设置的字段
                for field in protected_fields:
                    if field in old_data:
                        new_data[field] = old_data[field]

                print(f"   🛡️ 已保护手动状态: {new_data.get('sell_reason', '未知状态')}")
            else:
                # 没有手动状态设置或手动优先级未启用，正常计算卖出信号
                try:
                    sell_signal_data = {
                        'distance_from_low_pct': new_data.get('distance_from_low_pct', 0),
                        'pe_ratio': new_data.get('pe_ratio'),
                        'profit_status': new_data.get('profit_status', 'unknown'),
                        'profit_margin': new_data.get('profit_margin', 0)
                    }
                    sell_signal = calculate_sell_signal(sell_signal_data)
                    new_data['sell_signal'] = sell_signal['signal']
                    new_data['sell_reason'] = sell_signal['reason']
                    new_data['sell_color'] = sell_signal['color']
                    new_data['sell_priority'] = sell_signal['priority']

                    print(f"   🤖 自动计算卖出信号: {sell_signal['signal']} - {sell_signal['reason']}")
                except Exception as e:
                    print(f"⚠️ 计算卖出信号失败: {e}")
                    new_data['sell_signal'] = 'hold'
                    new_data['sell_reason'] = '持有'
                    new_data['sell_color'] = '#2ed573'
                    new_data['sell_priority'] = 3

            # 获取扫雷评分
            try:
                print(f"🔍 获取 {stock_code} 的扫雷评分...")
                scan_client = TdxScanClient()
                scan_result = scan_client.get_scan_score(stock_code)

                if scan_result['success']:
                    new_data['scan_score'] = scan_result['score']
                    new_data['scan_risk_level'] = scan_result['risk_level']
                    new_data['scan_risk_color'] = scan_result['risk_color']
                    new_data['scan_triggered_risks'] = scan_result['triggered_risks']
                    new_data['scan_display_text'] = scan_result['display_text']
                    print(f"✅ 扫雷评分获取成功: {scan_result['score']}分")
                else:
                    # 使用旧数据或默认值
                    new_data['scan_score'] = old_data.get('scan_score', 85)
                    new_data['scan_risk_level'] = old_data.get('scan_risk_level', '低风险')
                    new_data['scan_risk_color'] = old_data.get('scan_risk_color', '#2ed573')
                    new_data['scan_triggered_risks'] = old_data.get('scan_triggered_risks', 0)
                    new_data['scan_display_text'] = old_data.get('scan_display_text', '85分')
                    print(f"⚠️ 扫雷评分获取失败，使用缓存数据: {scan_result.get('message', '未知错误')}")
            except Exception as e:
                print(f"❌ 扫雷评分获取异常: {e}")
                # 使用旧数据或默认值
                new_data['scan_score'] = old_data.get('scan_score', 85)
                new_data['scan_risk_level'] = old_data.get('scan_risk_level', '低风险')
                new_data['scan_risk_color'] = old_data.get('scan_risk_color', '#2ed573')
                new_data['scan_triggered_risks'] = old_data.get('scan_triggered_risks', 0)
                new_data['scan_display_text'] = old_data.get('scan_display_text', '85分')

            # 更新减半监控策略模式（修复冷却器显示问题）
            if new_data.get('is_reduced', False):
                try:
                    reduction_monitor.update_strategy_mode(stock_code, new_data)
                    print(f"   🧊 已更新减半监控状态: {new_data.get('strategy_mode', 'unknown')}")
                except Exception as e:
                    print(f"   ⚠️ 更新减半监控状态失败: {e}")

            # 更新股票数据
            stock_data[stock_code] = new_data

            # 保存数据
            save_stock_data()

            # 计算变化
            price_change = new_data['price'] - old_data.get('price', 0)
            price_change_pct = (price_change / old_data.get('price', 1)) * 100 if old_data.get('price', 0) > 0 else 0

            print(f"✅ 股票 {stock_code} 更新成功")
            print(f"   价格: {old_data.get('price', 0):.2f} → {new_data['price']:.2f} ({price_change:+.2f}, {price_change_pct:+.1f}%)")

            return jsonify({
                'success': True,
                'message': f'股票 {new_data["name"]} 数据更新成功',
                'data': {
                    'code': stock_code,
                    'name': new_data['name'],
                    'old_price': old_data.get('price', 0),
                    'new_price': new_data['price'],
                    'price_change': price_change,
                    'price_change_pct': price_change_pct,
                    'update_time': new_data['update_time']
                }
            })
        else:
            print(f"❌ 股票 {stock_code} 数据获取失败")
            return jsonify({
                'success': False,
                'message': f'获取股票 {stock_code} 数据失败，请稍后重试'
            }), 500

    except Exception as e:
        print(f"❌ 更新单只股票失败: {e}")
        return jsonify({
            'success': False,
            'message': f'更新失败: {str(e)}'
        }), 500

@app.route('/api/recalculate-sell-signals', methods=['POST'])
def api_recalculate_sell_signals():
    """手动重新计算所有股票的卖出信号"""
    try:
        print(f"📱 收到手动重新计算卖出信号请求")

        # 重新计算所有股票的卖出信号
        recalculate_all_sell_signals()

        return jsonify({
            'success': True,
            'message': f'已重新计算所有股票的卖出信号，共处理 {len(stock_data)} 只股票'
        })

    except Exception as e:
        print(f"❌ 手动重新计算卖出信号失败: {e}")
        return jsonify({
            'success': False,
            'message': f'重新计算卖出信号失败: {str(e)}'
        }), 500

@app.route('/api/test-wechat-alerts', methods=['POST'])
def test_wechat_alerts():
    """手动测试企业微信提醒API"""
    try:
        import random

        # 18支随机测试股票数据
        test_stocks = [
            {'code': '000001', 'name': '平安银行', 'price': 12.85, 'signal': 'sell', 'reason': '距最低点已涨72.3%', 'distance': 72.3},
            {'code': '000002', 'name': '万科A', 'price': 8.92, 'signal': 'warning', 'reason': '距卖出条件还差6.8%', 'distance': 63.2},
            {'code': '000858', 'name': '五粮液', 'price': 128.50, 'signal': 'sell', 'reason': 'TTM市盈率-15.2且已回本', 'distance': 45.6},
            {'code': '000876', 'name': '新希望', 'price': 15.67, 'signal': 'warning', 'reason': '距卖出条件还差8.2%', 'distance': 61.8},
            {'code': '002415', 'name': '海康威视', 'price': 32.45, 'signal': 'sell', 'reason': '距最低点已涨75.8%', 'distance': 75.8},
            {'code': '002594', 'name': '比亚迪', 'price': 245.60, 'signal': 'warning', 'reason': '距卖出条件还差5.5%', 'distance': 64.5},
            {'code': '600036', 'name': '招商银行', 'price': 35.20, 'signal': 'sell', 'reason': 'TTM市盈率28.5且已回本', 'distance': 52.3},
            {'code': '600519', 'name': '贵州茅台', 'price': 1680.00, 'signal': 'warning', 'reason': '距卖出条件还差7.3%', 'distance': 62.7},
            {'code': '600887', 'name': '伊利股份', 'price': 28.90, 'signal': 'sell', 'reason': '距最低点已涨71.2%', 'distance': 71.2},
            {'code': '000568', 'name': '泸州老窖', 'price': 142.30, 'signal': 'warning', 'reason': '距卖出条件还差9.1%', 'distance': 60.9},
            {'code': '002304', 'name': '洋河股份', 'price': 98.75, 'signal': 'sell', 'reason': 'TTM市盈率-8.7且已回本', 'distance': 48.2},
            {'code': '000895', 'name': '双汇发展', 'price': 25.80, 'signal': 'warning', 'reason': '距卖出条件还差6.2%', 'distance': 63.8},
            {'code': '600276', 'name': '恒瑞医药', 'price': 45.60, 'signal': 'sell', 'reason': '距最低点已涨73.5%', 'distance': 73.5},
            {'code': '000661', 'name': '长春高新', 'price': 156.40, 'signal': 'warning', 'reason': '距卖出条件还差8.7%', 'distance': 61.3},
            {'code': '002142', 'name': '宁波银行', 'price': 22.15, 'signal': 'sell', 'reason': 'TTM市盈率26.8且已回本', 'distance': 55.9},
            {'code': '600309', 'name': '万华化学', 'price': 78.90, 'signal': 'warning', 'reason': '距卖出条件还差5.8%', 'distance': 64.2},
            {'code': '000338', 'name': '潍柴动力', 'price': 13.45, 'signal': 'sell', 'reason': '距最低点已涨74.1%', 'distance': 74.1},
            {'code': '002475', 'name': '立讯精密', 'price': 28.30, 'signal': 'warning', 'reason': '距卖出条件还差7.5%', 'distance': 62.5}
        ]

        sent_count = 0
        skipped_count = 0
        results = []

        for stock in test_stocks:
            try:
                # 随机生成一些额外数据
                profit_margin = random.uniform(5.0, 25.0) if random.choice([True, False]) else None
                profit_status = 'profit' if profit_margin else 'loss'

                # 发送提醒
                success = wechat_alert.send_sell_alert(
                    stock_code=stock['code'],
                    stock_name=stock['name'],
                    alert_type=stock['signal'],
                    reason=stock['reason'],
                    current_price=stock['price'],
                    distance_from_low=stock['distance'],
                    profit_status=profit_status,
                    profit_margin=profit_margin
                )

                if success:
                    sent_count += 1
                    status = '✅ 已发送'
                else:
                    skipped_count += 1
                    status = '⏭️ 已跳过(重复)'

                results.append({
                    'code': stock['code'],
                    'name': stock['name'],
                    'signal': stock['signal'],
                    'status': status
                })

                # 添加小延迟避免频率限制
                time.sleep(0.1)

            except Exception as e:
                results.append({
                    'code': stock['code'],
                    'name': stock['name'],
                    'signal': stock['signal'],
                    'status': f'❌ 失败: {str(e)}'
                })

        return jsonify({
            'success': True,
            'message': f'测试完成！发送 {sent_count} 条，跳过 {skipped_count} 条',
            'data': {
                'sent_count': sent_count,
                'skipped_count': skipped_count,
                'total_count': len(test_stocks),
                'results': results
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'测试失败: {str(e)}'
        }), 500

@app.route('/api/strategies', methods=['GET'])
def get_strategies():
    """获取所有策略配置"""
    try:
        return jsonify({
            'success': True,
            'data': strategy_manager.strategies
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取策略配置失败: {str(e)}'
        }), 500

@app.route('/api/strategies', methods=['POST'])
def update_strategies():
    """更新策略配置"""
    try:
        data = request.get_json()

        print(f"📝 收到策略配置更新: {data}")

        # 更新策略配置
        for strategy_id, config in data.items():
            if strategy_id in strategy_manager.strategies:
                strategy_manager.strategies[strategy_id].update(config)
                print(f"✅ 更新策略 {strategy_id}: enabled={config.get('enabled', 'N/A')}")

        # 保存配置
        strategy_manager.save_strategy_config()

        # 强制重新加载配置以确保生效
        strategy_manager.load_strategy_config()

        # 重新计算所有股票的卖出信号
        print(f"🔄 重新计算所有股票的卖出信号...")
        recalculate_all_sell_signals()

        print(f"💾 策略配置已保存并重新加载，卖出信号已更新")

        return jsonify({
            'success': True,
            'message': '策略配置已更新，卖出信号已重新计算'
        })
    except Exception as e:
        print(f"❌ 更新策略配置失败: {e}")
        return jsonify({
            'success': False,
            'message': f'更新策略配置失败: {str(e)}'
        }), 500

@app.route('/api/strategies/presets/<preset_name>', methods=['POST'])
def apply_strategy_preset(preset_name):
    """应用策略预设"""
    try:
        presets = {
            'conservative': {  # 保守型
                'distance_from_low': {'enabled': True, 'params': {'sell_threshold': 50.0, 'warning_threshold': 40.0}},
                'pe_abnormal': {'enabled': True, 'params': {'pe_max': 20.0, 'require_profit': True}},
                'profit_target': {'enabled': True, 'params': {'profit_threshold': 20.0, 'warning_threshold': 15.0}},
                'scan_risk_high': {'enabled': True, 'params': {'score_threshold': 80, 'require_profit': False}},
                'high_gain_sell': {'enabled': True, 'params': {'gain_threshold': 60.0}},
                'high_ttm_medium_gain': {'enabled': True, 'params': {'ttm_threshold': 25.0, 'gain_threshold': 40.0}},
                'limit_up_high_pb_clearance': {'enabled': True, 'params': {'limit_up_threshold': 9.8, 'pb_threshold': 1.5, 'gain_threshold': 60.0}},
                'extreme_ttm_profit_clearance': {'enabled': True, 'params': {'high_ttm_threshold': 35.0, 'require_profit': True}},
            },
            'aggressive': {  # 激进型
                'distance_from_low': {'enabled': True, 'params': {'sell_threshold': 80.0, 'warning_threshold': 70.0}},
                'pe_abnormal': {'enabled': True, 'params': {'pe_max': 30.0, 'require_profit': False}},
                'pb_overvalued': {'enabled': True, 'params': {'pb_threshold': 6.0, 'require_profit': False}},
                'profit_target': {'enabled': True, 'params': {'profit_threshold': 50.0, 'warning_threshold': 40.0}},
                'high_gain_sell': {'enabled': True, 'params': {'gain_threshold': 80.0}},
                'high_ttm_medium_gain': {'enabled': True, 'params': {'ttm_threshold': 35.0, 'gain_threshold': 60.0}},
                'limit_up_high_pb_clearance': {'enabled': True, 'params': {'limit_up_threshold': 9.8, 'pb_threshold': 2.0, 'gain_threshold': 80.0}},
                'extreme_ttm_profit_clearance': {'enabled': True, 'params': {'high_ttm_threshold': 50.0, 'require_profit': True}},
            },
            'balanced': {  # 平衡型
                'distance_from_low': {'enabled': True, 'params': {'sell_threshold': 70.0, 'warning_threshold': 60.0}},
                'pe_abnormal': {'enabled': True, 'params': {'pe_max': 25.0, 'require_profit': True}},
                'pb_overvalued': {'enabled': True, 'params': {'pb_threshold': 5.0, 'require_profit': True}},
                'profit_target': {'enabled': True, 'params': {'profit_threshold': 30.0, 'warning_threshold': 25.0}},
                'scan_risk_high': {'enabled': True, 'params': {'score_threshold': 70, 'require_profit': False}},
                'high_gain_sell': {'enabled': True, 'params': {'gain_threshold': 70.0}},
                'high_ttm_medium_gain': {'enabled': True, 'params': {'ttm_threshold': 30.0, 'gain_threshold': 50.0}},
                'limit_up_high_pb_clearance': {'enabled': True, 'params': {'limit_up_threshold': 9.8, 'pb_threshold': 1.75, 'gain_threshold': 70.0}},
                'extreme_ttm_profit_clearance': {'enabled': True, 'params': {'high_ttm_threshold': 40.0, 'require_profit': True}},
            }
        }

        if preset_name not in presets:
            return jsonify({
                'success': False,
                'message': f'未知的预设类型: {preset_name}'
            }), 400

        preset_config = presets[preset_name]

        # 先禁用所有策略
        for strategy_id in strategy_manager.strategies:
            strategy_manager.strategies[strategy_id]['enabled'] = False

        # 应用预设配置
        for strategy_id, config in preset_config.items():
            if strategy_id in strategy_manager.strategies:
                strategy_manager.strategies[strategy_id].update(config)

        # 保存配置
        strategy_manager.save_strategy_config()

        # 重新计算所有股票的卖出信号
        print(f"🔄 应用预设后重新计算卖出信号...")
        recalculate_all_sell_signals()

        return jsonify({
            'success': True,
            'message': f'{preset_name}型策略预设已应用，卖出信号已更新'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'应用策略预设失败: {str(e)}'
        }), 500

@app.route('/api/strategies/custom', methods=['POST'])
def create_custom_strategy():
    """创建自定义策略"""
    try:
        data = request.get_json()
        strategy_name = data.get('name', '')
        strategy_config = data.get('config', {})

        if not strategy_name:
            return jsonify({
                'success': False,
                'message': '策略名称不能为空'
            }), 400

        # 生成策略ID
        strategy_id = f"custom_{strategy_name.lower().replace(' ', '_')}"

        # 添加自定义策略
        strategy_manager.strategies[strategy_id] = {
            'name': strategy_name,
            'description': data.get('description', '用户自定义策略'),
            'enabled': True,
            'params': strategy_config.get('params', {}),
            'weight': strategy_config.get('weight', 1.0),
            'category': 'custom',
            'conditions': strategy_config.get('conditions', [])
        }

        # 保存配置
        strategy_manager.save_strategy_config()

        # 重新计算所有股票的卖出信号
        print(f"🔄 创建自定义策略后重新计算卖出信号...")
        recalculate_all_sell_signals()

        return jsonify({
            'success': True,
            'message': f'自定义策略 "{strategy_name}" 已创建，卖出信号已更新',
            'strategy_id': strategy_id
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'创建自定义策略失败: {str(e)}'
        }), 500

@app.route('/api/strategies/custom/<strategy_id>', methods=['PUT'])
def update_custom_strategy(strategy_id):
    """更新自定义策略"""
    try:
        # 检查策略是否存在
        if strategy_id not in strategy_manager.strategies:
            return jsonify({
                'success': False,
                'message': '策略不存在'
            }), 404

        # 检查是否为自定义策略
        strategy = strategy_manager.strategies[strategy_id]
        if strategy.get('category') != 'custom':
            return jsonify({
                'success': False,
                'message': '只能更新自定义策略'
            }), 400

        data = request.get_json()
        strategy_name = data.get('name', '')
        strategy_config = data.get('config', {})

        if not strategy_name:
            return jsonify({
                'success': False,
                'message': '策略名称不能为空'
            }), 400

        # 更新策略
        strategy_manager.strategies[strategy_id].update({
            'name': strategy_name,
            'description': data.get('description', '用户自定义策略'),
            'params': strategy_config.get('params', {}),
            'weight': strategy_config.get('weight', 1.0),
            'conditions': strategy_config.get('conditions', [])
        })

        # 保存配置
        strategy_manager.save_strategy_config()

        # 重新计算所有股票的卖出信号
        print(f"🔄 更新自定义策略后重新计算卖出信号...")
        recalculate_all_sell_signals()

        print(f"✅ 已更新自定义策略: {strategy_name} ({strategy_id})")

        return jsonify({
            'success': True,
            'message': f'自定义策略 "{strategy_name}" 更新成功，卖出信号已更新',
            'strategy_id': strategy_id
        })
    except Exception as e:
        print(f"❌ 更新自定义策略失败: {e}")
        return jsonify({
            'success': False,
            'message': f'更新失败: {str(e)}'
        }), 500

@app.route('/api/strategies/custom/<strategy_id>', methods=['DELETE'])
def delete_custom_strategy(strategy_id):
    """删除自定义策略"""
    try:
        # 检查策略是否存在
        if strategy_id not in strategy_manager.strategies:
            return jsonify({
                'success': False,
                'message': '策略不存在'
            }), 404

        # 检查是否为自定义策略
        strategy = strategy_manager.strategies[strategy_id]
        if strategy.get('category') != 'custom':
            return jsonify({
                'success': False,
                'message': '只能删除自定义策略'
            }), 400

        strategy_name = strategy.get('name', strategy_id)

        # 删除策略
        del strategy_manager.strategies[strategy_id]

        # 保存策略配置
        strategy_manager.save_strategy_config()

        # 重新计算所有股票的卖出信号
        print(f"🔄 删除自定义策略后重新计算卖出信号...")
        recalculate_all_sell_signals()

        print(f"✅ 已删除自定义策略: {strategy_name} ({strategy_id})")

        return jsonify({
            'success': True,
            'message': f'自定义策略 "{strategy_name}" 删除成功，卖出信号已更新'
        })

    except Exception as e:
        print(f"❌ 删除自定义策略失败: {e}")
        return jsonify({
            'success': False,
            'message': f'删除失败: {str(e)}'
        }), 500

@app.route('/api/strategy-fields', methods=['GET'])
def get_strategy_fields():
    """获取可用的策略字段配置"""
    try:
        return jsonify({
            'success': True,
            'data': {
                'fields': field_config.field_definitions,
                'categories': {
                    'technical': '技术指标',
                    'fundamental': '基本面',
                    'profit': '盈利指标',
                    'risk': '风险指标',
                    'portfolio': '持仓指标'
                }
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取字段配置失败: {str(e)}'
        }), 500

@app.route('/api/refresh-signals', methods=['POST'])
def refresh_signals():
    """强制刷新所有股票的卖出信号"""
    try:
        global stock_data

        print("🔄 开始强制刷新所有股票的卖出信号...")

        # 重新计算所有股票的卖出信号
        for stock_code, stock_info in stock_data.items():
            try:
                # 重新计算卖出信号
                sell_signal_result = calculate_sell_signal(stock_info)

                # 更新股票信息
                stock_info['sell_signal'] = sell_signal_result['signal']
                stock_info['sell_reason'] = sell_signal_result['reason']
                stock_info['sell_color'] = sell_signal_result['color']
                stock_info['sell_priority'] = sell_signal_result['priority']
                stock_info['sell_details'] = sell_signal_result.get('details', '')

                print(f"✅ 更新 {stock_code} 卖出信号: {sell_signal_result['signal']}")

            except Exception as e:
                print(f"❌ 更新 {stock_code} 卖出信号失败: {e}")

        # 保存更新后的数据
        save_stock_data()

        print("✅ 所有股票卖出信号刷新完成")

        return jsonify({
            'success': True,
            'message': '卖出信号已刷新'
        })
    except Exception as e:
        print(f"❌ 刷新卖出信号失败: {e}")
        return jsonify({
            'success': False,
            'message': f'刷新失败: {str(e)}'
        }), 500

@app.route('/api/update-holdings', methods=['POST'])
def update_holdings():
    """更新持仓数量"""
    try:
        data = request.get_json()
        stock_code = data.get('stock_code')
        new_holdings = data.get('holdings')

        if not stock_code or new_holdings is None:
            return jsonify({
                'success': False,
                'message': '股票代码和持仓数量不能为空'
            }), 400

        # 允许负数（表示做空或负持仓）
        # if new_holdings < 0:
        #     return jsonify({
        #         'success': False,
        #         'message': '持仓数量不能为负数'
        #     }), 400

        global stock_data

        if stock_code not in stock_data:
            return jsonify({
                'success': False,
                'message': f'未找到股票 {stock_code}'
            }), 404

        old_holdings = stock_data[stock_code].get('holdings', 0)
        stock_data[stock_code]['holdings'] = new_holdings

        # 重新计算市值
        current_price = stock_data[stock_code].get('price', 0)

        # 处理边界情况
        if current_price <= 0:
            new_market_value = 0.0
        else:
            new_market_value = current_price * new_holdings

        # 四舍五入到小数点后2位
        new_market_value = round(new_market_value, 2)
        stock_data[stock_code]['market_value'] = new_market_value

        # 保存数据
        save_stock_data()

        print(f"📝 更新持仓: {stock_data[stock_code]['name']} ({stock_code}) 持仓从 {old_holdings} 更新为 {new_holdings}")
        print(f"💰 市值更新: {current_price} × {new_holdings} = {new_market_value}")

        return jsonify({
            'success': True,
            'message': f'持仓数量已更新',
            'data': {
                'stock_code': stock_code,
                'stock_name': stock_data[stock_code]['name'],
                'old_holdings': old_holdings,
                'new_holdings': new_holdings,
                'current_price': current_price,
                'new_market_value': new_market_value,
                'formatted_market_value': f"¥{new_market_value:,.2f}"
            }
        })
    except Exception as e:
        print(f"❌ 更新持仓失败: {e}")
        return jsonify({
            'success': False,
            'message': f'更新失败: {str(e)}'
        }), 500

@app.route('/api/mark-reduced', methods=['POST'])
def mark_reduced():
    """标记股票已减半，停止提醒"""
    try:
        data = request.get_json()
        stock_code = data.get('stock_code')

        if not stock_code:
            return jsonify({
                'success': False,
                'message': '股票代码不能为空'
            }), 400

        global stock_data

        if stock_code not in stock_data:
            return jsonify({
                'success': False,
                'message': f'未找到股票 {stock_code}'
            }), 404

        # 标记为已减半
        stock_data[stock_code]['is_reduced'] = True
        stock_data[stock_code]['reduced_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 重新计算卖出信号以更新优先级
        sell_signal = calculate_sell_signal(stock_data[stock_code])
        stock_data[stock_code]['sell_signal'] = sell_signal['signal']
        stock_data[stock_code]['sell_reason'] = sell_signal['reason']
        stock_data[stock_code]['sell_color'] = sell_signal['color']
        stock_data[stock_code]['sell_priority'] = sell_signal['priority']
        stock_data[stock_code]['sell_details'] = sell_signal.get('details', '')

        # 在企业微信提醒系统中标记为已处理
        stock_name = stock_data[stock_code]['name']
        wechat_alert.mark_as_handled(stock_code, 'reduced')

        # 保存数据
        save_stock_data()

        print(f"✅ 标记已减半: {stock_name} ({stock_code})")

        return jsonify({
            'success': True,
            'message': f'{stock_name} 已标记为减半，将停止提醒',
            'data': {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'reduced_time': stock_data[stock_code]['reduced_time']
            }
        })
    except Exception as e:
        print(f"❌ 标记减半失败: {e}")
        return jsonify({
            'success': False,
            'message': f'操作失败: {str(e)}'
        }), 500

@app.route('/api/mark-action', methods=['POST'])
def mark_action():
    """标记股票操作（已减半或已清仓），停止提醒"""
    try:
        data = request.get_json()
        stock_code = data.get('stock_code')
        action_type = data.get('action_type')  # 'reduced' 或 'cleared'

        if not stock_code or not action_type:
            return jsonify({
                'success': False,
                'message': '缺少股票代码或操作类型'
            }), 400

        global stock_data

        if stock_code not in stock_data:
            return jsonify({
                'success': False,
                'message': f'未找到股票 {stock_code}'
            }), 404

        if action_type not in ['reduced', 'cleared']:
            return jsonify({
                'success': False,
                'message': '无效的操作类型，只支持 reduced 或 cleared'
            }), 400

        # 标记操作
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        stock_name = stock_data[stock_code]['name']

        if action_type == 'reduced':
            stock_data[stock_code]['is_reduced'] = True
            stock_data[stock_code]['reduced_time'] = current_time
            action_text = '已减半'

            # 添加减半监控记录
            try:
                reduction_record = reduction_monitor.add_reduction_record(stock_code, stock_data[stock_code])
                print(f"📝 已添加减半监控记录: {stock_code}")
                print(f"🧊 冷却期设置至: {stock_data[stock_code].get('cooling_down_until', 'ERROR')}")
                print(f"📊 策略模式: {stock_data[stock_code].get('strategy_mode', 'ERROR')}")
            except Exception as e:
                print(f"❌ 添加减半监控记录失败: {stock_code} - {e}")
                # 手动设置基本的监控状态
                cooling_until = datetime.strptime(current_time, '%Y-%m-%d %H:%M:%S') + timedelta(days=365)
                stock_data[stock_code]['monitoring_status'] = 'cooling_down'
                stock_data[stock_code]['cooling_down_until'] = cooling_until.strftime('%Y-%m-%d')
                stock_data[stock_code]['strategy_mode'] = 'cooling_down'
                stock_data[stock_code]['reduction_history'] = stock_data[stock_code].get('reduction_history', [])
                print(f"🔧 手动设置冷却期: {stock_data[stock_code]['cooling_down_until']}")

        else:  # cleared
            stock_data[stock_code]['is_cleared'] = True
            stock_data[stock_code]['cleared_time'] = current_time
            action_text = '已清仓'

            # 清仓后停止减半监控
            if 'monitoring_status' in stock_data[stock_code]:
                stock_data[stock_code]['monitoring_status'] = 'cleared'
                stock_data[stock_code]['strategy_mode'] = 'cleared'
                print(f"🛑 已清仓，停止减半监控: {stock_code}")

        # 重新计算卖出信号以更新优先级
        sell_signal = calculate_sell_signal(stock_data[stock_code])
        stock_data[stock_code]['sell_signal'] = sell_signal['signal']
        stock_data[stock_code]['sell_reason'] = sell_signal['reason']
        stock_data[stock_code]['sell_color'] = sell_signal['color']
        stock_data[stock_code]['sell_priority'] = sell_signal['priority']
        stock_data[stock_code]['sell_details'] = sell_signal.get('details', '')

        # 在企业微信提醒系统中标记为已处理
        wechat_alert.mark_as_handled(stock_code, action_type)

        # 保存数据
        save_stock_data()

        print(f"✅ 标记{action_text}: {stock_name} ({stock_code})")

        return jsonify({
            'success': True,
            'message': f'{stock_name} 已标记为{action_text}，将停止提醒',
            'data': {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'action_type': action_type,
                'action_text': action_text,
                'action_time': current_time
            }
        })

    except Exception as e:
        print(f"❌ 标记操作失败: {e}")
        return jsonify({
            'success': False,
            'message': f'操作失败: {str(e)}'
        }), 500

@app.route('/api/set-custom-status', methods=['POST'])
def set_custom_status():
    """设置股票自定义状态"""
    try:
        data = request.get_json()
        stock_code = data.get('stock_code')
        status_type = data.get('status_type')  # 状态类型
        status_text = data.get('status_text')  # 显示文本
        status_emoji = data.get('status_emoji')  # 表情符号

        if not stock_code or not status_type or not status_text:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400

        global stock_data

        if stock_code not in stock_data:
            return jsonify({
                'success': False,
                'message': f'未找到股票 {stock_code}'
            }), 404

        # 设置自定义状态
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        stock_name = stock_data[stock_code]['name']

        # 覆盖原有的卖出信号
        stock_data[stock_code]['custom_status'] = True
        stock_data[stock_code]['custom_status_type'] = status_type
        stock_data[stock_code]['custom_status_text'] = status_text
        stock_data[stock_code]['custom_status_emoji'] = status_emoji or ''
        stock_data[stock_code]['custom_status_time'] = current_time

        # 根据状态类型设置相应的标记
        if status_type == 'reduced':
            stock_data[stock_code]['is_reduced'] = True
            stock_data[stock_code]['reduced_time'] = current_time
        elif status_type == 'cleared':
            stock_data[stock_code]['is_cleared'] = True
            stock_data[stock_code]['cleared_time'] = current_time

        # 重新计算卖出信号以更新优先级
        sell_signal = calculate_sell_signal(stock_data[stock_code])
        stock_data[stock_code]['sell_signal'] = sell_signal['signal']
        stock_data[stock_code]['sell_reason'] = sell_signal['reason']
        stock_data[stock_code]['sell_color'] = sell_signal['color']
        stock_data[stock_code]['sell_priority'] = sell_signal['priority']
        stock_data[stock_code]['sell_details'] = sell_signal.get('details', '')

        # 在企业微信提醒系统中标记为已处理
        wechat_alert.mark_as_handled(stock_code, status_type)

        # 保存数据
        save_stock_data()

        print(f"✅ 设置自定义状态: {stock_name} ({stock_code}) -> {status_emoji}{status_text}")

        return jsonify({
            'success': True,
            'message': f'{stock_name} 已设置为{status_emoji}{status_text}，将停止提醒',
            'data': {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'status_type': status_type,
                'status_text': status_text,
                'status_emoji': status_emoji,
                'status_time': current_time
            }
        })

    except Exception as e:
        print(f"❌ 设置自定义状态失败: {e}")
        return jsonify({
            'success': False,
            'message': f'操作失败: {str(e)}'
        }), 500

@app.route('/api/sell-signal-priority', methods=['GET'])
def get_sell_signal_priority():
    """获取卖出信号优先级配置"""
    try:
        config = priority_manager.get_config()
        return jsonify({
            'success': True,
            'data': config
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取配置失败: {str(e)}'
        }), 500

@app.route('/api/sell-signal-priority', methods=['POST'])
def set_sell_signal_priority():
    """设置卖出信号优先级配置"""
    try:
        data = request.get_json()
        manual_priority = data.get('manual_priority_over_auto')

        if manual_priority is None:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400

        priority_manager.set_manual_priority(manual_priority)

        return jsonify({
            'success': True,
            'message': f'优先级配置已更新: 手动标记{"优先于" if manual_priority else "不优先于"}自动生成',
            'data': {
                'manual_priority_over_auto': manual_priority,
                'updated_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'设置配置失败: {str(e)}'
        }), 500

@app.route('/api/auto-update', methods=['GET'])
def get_auto_update_status():
    """获取自动更新状态"""
    try:
        return jsonify({
            'success': True,
            'data': {
                'auto_update_enabled': auto_update_enabled,
                'update_thread_running': update_thread_running,
                'status_text': '运行中' if auto_update_enabled and update_thread_running else '已停止'
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取自动更新状态失败: {str(e)}'
        }), 500

@app.route('/api/auto-update', methods=['POST'])
def set_auto_update_status():
    """设置自动更新状态"""
    try:
        global auto_update_enabled

        data = request.get_json()
        new_status = data.get('enabled')

        if new_status is None:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400

        auto_update_enabled = bool(new_status)
        save_auto_update_config()

        status_text = '启用' if auto_update_enabled else '禁用'

        return jsonify({
            'success': True,
            'message': f"自动更新已{status_text}",
            'data': {
                'auto_update_enabled': auto_update_enabled,
                'update_thread_running': update_thread_running,
                'status_text': '运行中' if auto_update_enabled and update_thread_running else '已停止'
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'设置自动更新状态失败: {str(e)}'
        }), 500

@app.route('/api/restore-original-status', methods=['POST'])
def restore_original_status():
    """恢复股票的原始系统状态，重新启用企业微信提醒"""
    try:
        data = request.get_json()
        stock_code = data.get('stock_code')
        status_type = data.get('status_type')  # 'clearance', 'sell', 'warning', 'hold'

        if not stock_code or not status_type:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400

        global stock_data

        if stock_code not in stock_data:
            return jsonify({
                'success': False,
                'message': f'未找到股票 {stock_code}'
            }), 404

        valid_statuses = ['clearance', 'sell', 'warning', 'hold']
        if status_type not in valid_statuses:
            return jsonify({
                'success': False,
                'message': '无效的状态类型'
            }), 400

        # 清除自定义状态
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        stock_name = stock_data[stock_code]['name']

        # 移除自定义状态标记
        stock_data[stock_code]['custom_status'] = False
        stock_data[stock_code]['custom_status_type'] = None
        stock_data[stock_code]['custom_status_text'] = None
        stock_data[stock_code]['custom_status_emoji'] = None
        stock_data[stock_code]['custom_status_time'] = None

        # 移除已减半/已清仓标记
        stock_data[stock_code]['is_reduced'] = False
        stock_data[stock_code]['is_cleared'] = False
        stock_data[stock_code]['reduced_time'] = None
        stock_data[stock_code]['cleared_time'] = None

        # 重新计算卖出信号，恢复系统自动生成的信号
        sell_signal = calculate_sell_signal(stock_data[stock_code])
        stock_data[stock_code]['sell_signal'] = sell_signal['signal']
        stock_data[stock_code]['sell_reason'] = sell_signal['reason']
        stock_data[stock_code]['sell_color'] = sell_signal['color']
        stock_data[stock_code]['sell_priority'] = sell_signal['priority']
        stock_data[stock_code]['sell_details'] = sell_signal.get('details', '')

        # 在企业微信提醒系统中移除已处理标记，恢复提醒
        wechat_alert.remove_handled_status(stock_code)

        # 保存数据
        save_stock_data()

        status_map = {
            'clearance': '清仓',
            'sell': '卖出',
            'warning': '预警',
            'hold': '持有'
        }

        status_text = status_map.get(status_type, '未知')

        print(f"🔄 恢复原始状态: {stock_name} ({stock_code}) -> {status_text}")
        print(f"📊 新的卖出信号: {sell_signal['signal']} - {sell_signal['reason']}")

        return jsonify({
            'success': True,
            'message': f'{stock_name} 已恢复为{status_text}状态，企业微信提醒已重新启用',
            'data': {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'status_type': status_type,
                'status_text': status_text,
                'restore_time': current_time,
                'new_sell_signal': sell_signal['signal'],
                'new_sell_reason': sell_signal['reason']
            }
        })

    except Exception as e:
        print(f"❌ 恢复原始状态失败: {e}")
        return jsonify({
            'success': False,
            'message': f'操作失败: {str(e)}'
        }), 500

@app.route('/api/cancel-custom-sell-signal', methods=['POST'])
def cancel_custom_sell_signal():
    """取消自定义卖出信号，恢复系统自动生成的信号"""
    try:
        data = request.get_json()
        stock_code = data.get('stock_code')

        if not stock_code:
            return jsonify({
                'success': False,
                'message': '缺少股票代码'
            }), 400

        global stock_data

        if stock_code not in stock_data:
            return jsonify({
                'success': False,
                'message': f'未找到股票 {stock_code}'
            }), 404

        stock_name = stock_data[stock_code]['name']
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 检查是否有自定义状态需要取消
        has_custom_status = stock_data[stock_code].get('custom_status', False)
        has_reduced_status = stock_data[stock_code].get('is_reduced', False)
        has_cleared_status = stock_data[stock_code].get('is_cleared', False)

        if not (has_custom_status or has_reduced_status or has_cleared_status):
            return jsonify({
                'success': False,
                'message': f'{stock_name} 当前没有自定义卖出信号需要取消'
            }), 400

        # 记录取消前的状态
        old_status = {
            'custom_status_text': stock_data[stock_code].get('custom_status_text', ''),
            'is_reduced': has_reduced_status,
            'is_cleared': has_cleared_status
        }

        # 清除所有自定义状态标记
        stock_data[stock_code]['custom_status'] = False
        stock_data[stock_code]['custom_status_type'] = None
        stock_data[stock_code]['custom_status_text'] = None
        stock_data[stock_code]['custom_status_emoji'] = None
        stock_data[stock_code]['custom_status_time'] = None
        stock_data[stock_code]['is_reduced'] = False
        stock_data[stock_code]['is_cleared'] = False
        stock_data[stock_code]['reduced_time'] = None
        stock_data[stock_code]['cleared_time'] = None

        # 重新计算卖出信号，恢复系统自动生成的信号
        sell_signal = calculate_sell_signal(stock_data[stock_code])
        stock_data[stock_code]['sell_signal'] = sell_signal['signal']
        stock_data[stock_code]['sell_reason'] = sell_signal['reason']
        stock_data[stock_code]['sell_color'] = sell_signal['color']
        stock_data[stock_code]['sell_priority'] = sell_signal['priority']
        stock_data[stock_code]['sell_details'] = sell_signal.get('details', '')

        # 在企业微信提醒系统中移除已处理标记，恢复提醒
        wechat_alert.remove_handled_status(stock_code)

        # 保存数据
        save_stock_data()

        # 构建取消状态的描述
        canceled_statuses = []
        if old_status['custom_status_text']:
            canceled_statuses.append(old_status['custom_status_text'])
        if old_status['is_reduced']:
            canceled_statuses.append('已减半')
        if old_status['is_cleared']:
            canceled_statuses.append('已清仓')

        canceled_text = '、'.join(canceled_statuses)

        print(f"🔄 取消自定义卖出信号: {stock_name} ({stock_code})")
        print(f"📋 取消的状态: {canceled_text}")
        print(f"📊 新的卖出信号: {sell_signal['signal']} - {sell_signal['reason']}")

        return jsonify({
            'success': True,
            'message': f'{stock_name} 的自定义卖出信号已取消，已恢复系统自动生成的信号',
            'data': {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'canceled_statuses': canceled_text,
                'cancel_time': current_time,
                'new_sell_signal': sell_signal['signal'],
                'new_sell_reason': sell_signal['reason'],
                'new_sell_color': sell_signal['color']
            }
        })

    except Exception as e:
        print(f"❌ 取消自定义卖出信号失败: {e}")
        return jsonify({
            'success': False,
            'message': f'操作失败: {str(e)}'
        }), 500

@app.route('/api/batch-cancel-custom-signals', methods=['POST'])
def batch_cancel_custom_signals():
    """批量取消自定义卖出信号"""
    try:
        data = request.get_json()
        stock_codes = data.get('stock_codes', [])

        if not stock_codes or not isinstance(stock_codes, list):
            return jsonify({
                'success': False,
                'message': '缺少股票代码列表'
            }), 400

        global stock_data

        results = {
            'success_count': 0,
            'failed_count': 0,
            'success_stocks': [],
            'failed_stocks': [],
            'details': []
        }

        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        for stock_code in stock_codes:
            try:
                if stock_code not in stock_data:
                    results['failed_count'] += 1
                    results['failed_stocks'].append({
                        'stock_code': stock_code,
                        'reason': '股票不存在'
                    })
                    continue

                stock_name = stock_data[stock_code]['name']

                # 检查是否有自定义状态需要取消
                has_custom_status = stock_data[stock_code].get('custom_status', False)
                has_reduced_status = stock_data[stock_code].get('is_reduced', False)
                has_cleared_status = stock_data[stock_code].get('is_cleared', False)

                if not (has_custom_status or has_reduced_status or has_cleared_status):
                    results['failed_count'] += 1
                    results['failed_stocks'].append({
                        'stock_code': stock_code,
                        'stock_name': stock_name,
                        'reason': '没有自定义状态需要取消'
                    })
                    continue

                # 记录取消前的状态
                old_status = {
                    'custom_status_text': stock_data[stock_code].get('custom_status_text', ''),
                    'is_reduced': has_reduced_status,
                    'is_cleared': has_cleared_status
                }

                # 清除所有自定义状态标记
                stock_data[stock_code]['custom_status'] = False
                stock_data[stock_code]['custom_status_type'] = None
                stock_data[stock_code]['custom_status_text'] = None
                stock_data[stock_code]['custom_status_emoji'] = None
                stock_data[stock_code]['custom_status_time'] = None
                stock_data[stock_code]['is_reduced'] = False
                stock_data[stock_code]['is_cleared'] = False
                stock_data[stock_code]['reduced_time'] = None
                stock_data[stock_code]['cleared_time'] = None

                # 重新计算卖出信号
                sell_signal = calculate_sell_signal(stock_data[stock_code])
                stock_data[stock_code]['sell_signal'] = sell_signal['signal']
                stock_data[stock_code]['sell_reason'] = sell_signal['reason']
                stock_data[stock_code]['sell_color'] = sell_signal['color']
                stock_data[stock_code]['sell_priority'] = sell_signal['priority']
                stock_data[stock_code]['sell_details'] = sell_signal.get('details', '')

                # 恢复企业微信提醒
                wechat_alert.remove_handled_status(stock_code)

                # 构建取消状态的描述
                canceled_statuses = []
                if old_status['custom_status_text']:
                    canceled_statuses.append(old_status['custom_status_text'])
                if old_status['is_reduced']:
                    canceled_statuses.append('已减半')
                if old_status['is_cleared']:
                    canceled_statuses.append('已清仓')

                canceled_text = '、'.join(canceled_statuses)

                results['success_count'] += 1
                results['success_stocks'].append({
                    'stock_code': stock_code,
                    'stock_name': stock_name,
                    'canceled_statuses': canceled_text,
                    'new_sell_signal': sell_signal['signal'],
                    'new_sell_reason': sell_signal['reason']
                })

                print(f"✅ 批量取消成功: {stock_name} ({stock_code}) - {canceled_text}")

            except Exception as e:
                results['failed_count'] += 1
                results['failed_stocks'].append({
                    'stock_code': stock_code,
                    'stock_name': stock_data.get(stock_code, {}).get('name', '未知'),
                    'reason': str(e)
                })
                print(f"❌ 批量取消失败: {stock_code} - {e}")

        # 保存数据
        if results['success_count'] > 0:
            save_stock_data()

        print(f"📊 批量取消完成: 成功 {results['success_count']} 只，失败 {results['failed_count']} 只")

        return jsonify({
            'success': True,
            'message': f'批量取消完成：成功 {results["success_count"]} 只，失败 {results["failed_count"]} 只',
            'data': {
                'total_processed': len(stock_codes),
                'success_count': results['success_count'],
                'failed_count': results['failed_count'],
                'success_stocks': results['success_stocks'],
                'failed_stocks': results['failed_stocks'],
                'process_time': current_time
            }
        })

    except Exception as e:
        print(f"❌ 批量取消自定义信号失败: {e}")
        return jsonify({
            'success': False,
            'message': f'批量操作失败: {str(e)}'
        }), 500

@app.route('/api/reduction-monitoring-config', methods=['GET'])
def get_reduction_monitoring_config():
    """获取减半监控配置"""
    try:
        config = reduction_monitor.config.copy()

        # 添加统计信息
        summary = reduction_monitor.get_monitoring_summary(stock_data)
        config['summary'] = summary

        return jsonify({
            'success': True,
            'data': config
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取配置失败: {str(e)}'
        }), 500

@app.route('/api/reduction-monitoring-config', methods=['POST'])
def update_reduction_monitoring_config():
    """更新减半监控配置"""
    try:
        data = request.get_json()

        # 验证配置参数
        valid_keys = ['cooling_period_days', 'ttm_threshold', 'fundamentals_check_interval',
                     'auto_strategy_switch', 'enable_cooling_period']

        new_config = {}
        for key in valid_keys:
            if key in data:
                new_config[key] = data[key]

        if not new_config:
            return jsonify({
                'success': False,
                'message': '没有有效的配置参数'
            }), 400

        # 更新配置
        reduction_monitor.update_config(new_config)

        # 重新计算所有已减半股票的状态
        updated_count = 0
        for stock_code, stock_info in stock_data.items():
            if stock_info.get('is_reduced', False):
                reduction_monitor.update_strategy_mode(stock_code, stock_info)
                updated_count += 1

        print(f"🔄 减半监控配置已更新，影响 {updated_count} 只已减半股票")

        return jsonify({
            'success': True,
            'message': f'减半监控配置已更新，影响 {updated_count} 只股票',
            'data': {
                'updated_config': new_config,
                'affected_stocks': updated_count,
                'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        })

    except Exception as e:
        print(f"❌ 更新减半监控配置失败: {e}")
        return jsonify({
            'success': False,
            'message': f'更新配置失败: {str(e)}'
        }), 500

@app.route('/api/reduction-monitoring-status/<stock_code>', methods=['GET'])
def get_reduction_monitoring_status(stock_code):
    """获取单只股票的减半监控状态"""
    try:
        if stock_code not in stock_data:
            return jsonify({
                'success': False,
                'message': f'未找到股票 {stock_code}'
            }), 404

        stock_info = stock_data[stock_code]

        # 获取监控状态
        status = {
            'stock_code': stock_code,
            'stock_name': stock_info.get('name', ''),
            'is_reduced': stock_info.get('is_reduced', False),
            'monitoring_status': stock_info.get('monitoring_status', 'normal'),
            'strategy_mode': stock_info.get('strategy_mode', 'normal'),
            'reduction_history': stock_info.get('reduction_history', []),
            'cooling_period_info': None,
            'fundamentals_status': None
        }

        # 获取冷却期信息
        if status['is_reduced']:
            cooling_info = reduction_monitor.get_cooling_period_info(stock_info)
            status['cooling_period_info'] = cooling_info

            # 获取基本面状态
            is_deteriorated, deterioration_type, reason = reduction_monitor.check_fundamentals_deterioration(stock_info)
            status['fundamentals_status'] = {
                'is_deteriorated': is_deteriorated,
                'deterioration_type': deterioration_type,
                'reason': reason,
                'current_ttm': stock_info.get('pe_ratio', 0),
                'ttm_threshold': reduction_monitor.config.get('ttm_threshold', 30.0)
            }

        return jsonify({
            'success': True,
            'data': status
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取监控状态失败: {str(e)}'
        }), 500

@app.route('/api/debug-reduction-status/<stock_code>', methods=['GET'])
def debug_reduction_status(stock_code):
    """调试减半监控状态"""
    try:
        if stock_code not in stock_data:
            return jsonify({
                'success': False,
                'message': f'未找到股票 {stock_code}'
            }), 404

        stock_info = stock_data[stock_code]

        # 获取详细的调试信息
        debug_info = {
            'stock_code': stock_code,
            'stock_name': stock_info.get('name', ''),
            'is_reduced': stock_info.get('is_reduced', False),
            'reduced_time': stock_info.get('reduced_time', ''),
            'monitoring_status': stock_info.get('monitoring_status', ''),
            'strategy_mode': stock_info.get('strategy_mode', ''),
            'cooling_down_until': stock_info.get('cooling_down_until', ''),
            'reduction_history': stock_info.get('reduction_history', []),
            'current_ttm': stock_info.get('pe_ratio', 0),
            'config': reduction_monitor.config,
            'is_in_cooling_period': reduction_monitor.is_in_cooling_period(stock_info),
            'cooling_period_info': reduction_monitor.get_cooling_period_info(stock_info),
            'fundamentals_check': reduction_monitor.check_fundamentals_deterioration(stock_info),
            'current_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        return jsonify({
            'success': True,
            'data': debug_info
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'调试失败: {str(e)}'
        }), 500

@app.route('/api/fix-reduction-monitoring/<stock_code>', methods=['POST'])
def fix_reduction_monitoring(stock_code):
    """修复减半监控状态"""
    try:
        if stock_code not in stock_data:
            return jsonify({
                'success': False,
                'message': f'未找到股票 {stock_code}'
            }), 404

        stock_info = stock_data[stock_code]

        if not stock_info.get('is_reduced', False):
            return jsonify({
                'success': False,
                'message': f'股票 {stock_code} 未标记为已减半'
            }), 400

        # 手动修复监控状态
        current_time = datetime.now()
        stock_name = stock_info.get('name', '')

        # 设置冷却期
        cooling_until = current_time + timedelta(days=reduction_monitor.config.get('cooling_period_days', 365))
        stock_info['monitoring_status'] = 'cooling_down'
        stock_info['cooling_down_until'] = cooling_until.strftime('%Y-%m-%d')
        stock_info['strategy_mode'] = 'cooling_down'
        stock_info['last_fundamentals_check'] = current_time.strftime('%Y-%m-%d')

        # 初始化减半历史记录（如果没有的话）
        if 'reduction_history' not in stock_info:
            stock_info['reduction_history'] = []

        # 如果没有减半记录，添加一个基于现有信息的记录
        if not stock_info['reduction_history']:
            reduction_record = {
                'reduction_date': stock_info.get('reduced_time', current_time.strftime('%Y-%m-%d %H:%M:%S'))[:10],
                'reduction_time': stock_info.get('reduced_time', current_time.strftime('%Y-%m-%d %H:%M:%S')),
                'reduction_price': stock_info.get('price', 0),
                'ttm_at_reduction': stock_info.get('pe_ratio', 0),
                'distance_from_low_pct': stock_info.get('distance_from_low_pct', 0),
                'reason': '手动减半（修复）',
                'strategy_at_reduction': stock_info.get('sell_signal', 'unknown')
            }
            stock_info['reduction_history'].append(reduction_record)

        # 重新计算卖出信号
        sell_signal = calculate_sell_signal(stock_info)
        stock_info['sell_signal'] = sell_signal['signal']
        stock_info['sell_reason'] = sell_signal['reason']
        stock_info['sell_color'] = sell_signal['color']
        stock_info['sell_priority'] = sell_signal['priority']
        stock_info['sell_details'] = sell_signal.get('details', '')

        # 保存数据
        save_stock_data()

        print(f"🔧 修复减半监控状态: {stock_name} ({stock_code})")
        print(f"🧊 冷却期至: {stock_info['cooling_down_until']}")
        print(f"📊 策略模式: {stock_info['strategy_mode']}")

        return jsonify({
            'success': True,
            'message': f'{stock_name} 的减半监控状态已修复',
            'data': {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'cooling_down_until': stock_info['cooling_down_until'],
                'strategy_mode': stock_info['strategy_mode'],
                'monitoring_status': stock_info['monitoring_status'],
                'fix_time': current_time.strftime('%Y-%m-%d %H:%M:%S')
            }
        })

    except Exception as e:
        print(f"❌ 修复减半监控状态失败: {e}")
        return jsonify({
            'success': False,
            'message': f'修复失败: {str(e)}'
        }), 500

@app.route('/api/clean-reduction-history/<stock_code>', methods=['POST'])
def clean_reduction_history(stock_code):
    """清理减半历史记录中的重复数据"""
    try:
        if stock_code not in stock_data:
            return jsonify({
                'success': False,
                'message': f'未找到股票 {stock_code}'
            }), 404

        stock_info = stock_data[stock_code]
        stock_name = stock_info.get('name', '')

        if not stock_info.get('is_reduced', False):
            return jsonify({
                'success': False,
                'message': f'股票 {stock_code} 未标记为已减半'
            }), 400

        # 获取原始历史记录
        original_history = stock_info.get('reduction_history', [])
        original_count = len(original_history)

        if original_count <= 1:
            return jsonify({
                'success': True,
                'message': f'{stock_name} 的减半历史记录正常，无需清理',
                'data': {
                    'original_count': original_count,
                    'cleaned_count': original_count,
                    'removed_count': 0
                }
            })

        # 清理重复记录 - 按日期去重，保留最新的记录
        seen_dates = set()
        cleaned_history = []

        # 按时间倒序排序，保留每个日期的最新记录
        sorted_history = sorted(original_history,
                               key=lambda x: x.get('reduction_time', x.get('reduction_date', '')),
                               reverse=True)

        for record in sorted_history:
            record_date = record.get('reduction_date', '')
            if record_date and record_date not in seen_dates:
                seen_dates.add(record_date)
                cleaned_history.append(record)

        # 按时间正序重新排序
        cleaned_history.sort(key=lambda x: x.get('reduction_time', x.get('reduction_date', '')))

        # 更新股票数据
        stock_info['reduction_history'] = cleaned_history
        cleaned_count = len(cleaned_history)
        removed_count = original_count - cleaned_count

        # 保存数据
        save_stock_data()

        print(f"🧹 清理减半历史记录: {stock_name} ({stock_code})")
        print(f"📊 原始记录: {original_count} 条，清理后: {cleaned_count} 条，删除: {removed_count} 条")

        return jsonify({
            'success': True,
            'message': f'{stock_name} 的减半历史记录已清理完成',
            'data': {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'original_count': original_count,
                'cleaned_count': cleaned_count,
                'removed_count': removed_count,
                'clean_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        })

    except Exception as e:
        print(f"❌ 清理减半历史记录失败: {e}")
        return jsonify({
            'success': False,
            'message': f'清理失败: {str(e)}'
        }), 500

@app.route('/api/batch-clean-reduction-history', methods=['POST'])
def batch_clean_reduction_history():
    """批量清理所有股票的减半历史记录"""
    try:
        # 获取所有已减半的股票
        reduced_stocks = []
        for stock_code, stock_info in stock_data.items():
            if stock_info.get('is_reduced', False):
                reduced_stocks.append({
                    'code': stock_code,
                    'name': stock_info.get('name', ''),
                    'original_count': len(stock_info.get('reduction_history', []))
                })

        if not reduced_stocks:
            return jsonify({
                'success': True,
                'message': '没有找到需要清理的已减半股票',
                'data': {
                    'total_stocks': 0,
                    'cleaned_stocks': 0,
                    'total_removed': 0
                }
            })

        # 批量清理
        cleaned_count = 0
        total_removed = 0

        for stock_info in reduced_stocks:
            stock_code = stock_info['code']
            stock_data_item = stock_data[stock_code]

            # 获取原始历史记录
            original_history = stock_data_item.get('reduction_history', [])
            original_count = len(original_history)

            if original_count <= 1:
                continue

            # 清理策略：只保留最新的一条记录
            if original_history:
                # 按时间排序，保留最新的一条
                sorted_history = sorted(original_history,
                                       key=lambda x: x.get('reduction_time', x.get('reduction_date', '')),
                                       reverse=True)

                # 只保留最新的一条记录
                cleaned_history = [sorted_history[0]] if sorted_history else []

                # 更新数据
                stock_data_item['reduction_history'] = cleaned_history

                removed_count = original_count - len(cleaned_history)
                total_removed += removed_count
                cleaned_count += 1

                print(f"🧹 清理 {stock_info['name']} ({stock_code}): {original_count} → {len(cleaned_history)} 条")

        # 保存数据
        if cleaned_count > 0:
            save_stock_data()

        print(f"🎉 批量清理完成: 处理 {cleaned_count} 只股票，删除 {total_removed} 条重复记录")

        return jsonify({
            'success': True,
            'message': f'批量清理完成：处理 {cleaned_count} 只股票，删除 {total_removed} 条重复记录',
            'data': {
                'total_stocks': len(reduced_stocks),
                'cleaned_stocks': cleaned_count,
                'total_removed': total_removed,
                'clean_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'details': reduced_stocks
            }
        })

    except Exception as e:
        print(f"❌ 批量清理减半历史失败: {e}")
        return jsonify({
            'success': False,
            'message': f'批量清理失败: {str(e)}'
        }), 500

@app.route('/api/generate-demo-data', methods=['POST'])
def generate_demo_data():
    """生成演示数据API"""
    try:
        import random

        # 清空现有数据
        global stock_data, last_update_time, imported_stock_list
        stock_data = {}
        imported_stock_list = []

        # 30支演示股票数据
        demo_stocks = [
            {'code': '000001', 'name': '平安银行', 'industry': '银行', 'holdings': 1000},
            {'code': '000002', 'name': '万科A', 'industry': '房地产开发', 'holdings': 800},
            {'code': '000858', 'name': '五粮液', 'industry': '白酒', 'holdings': 200},
            {'code': '000876', 'name': '新希望', 'industry': '农牧饲渔', 'holdings': 1500},
            {'code': '002415', 'name': '海康威视', 'industry': '安防设备', 'holdings': 600},
            {'code': '002594', 'name': '比亚迪', 'industry': '汽车整车', 'holdings': 300},
            {'code': '600036', 'name': '招商银行', 'industry': '银行', 'holdings': 1200},
            {'code': '600519', 'name': '贵州茅台', 'industry': '白酒', 'holdings': 50},
            {'code': '600887', 'name': '伊利股份', 'industry': '乳品', 'holdings': 900},
            {'code': '000568', 'name': '泸州老窖', 'industry': '白酒', 'holdings': 400},
            {'code': '002304', 'name': '洋河股份', 'industry': '白酒', 'holdings': 350},
            {'code': '000895', 'name': '双汇发展', 'industry': '肉制品', 'holdings': 700},
            {'code': '600276', 'name': '恒瑞医药', 'industry': '化学制药', 'holdings': 500},
            {'code': '000661', 'name': '长春高新', 'industry': '生物制药', 'holdings': 250},
            {'code': '002142', 'name': '宁波银行', 'industry': '银行', 'holdings': 1100},
            {'code': '600309', 'name': '万华化学', 'industry': '化工', 'holdings': 450},
            {'code': '000338', 'name': '潍柴动力', 'industry': '机械设备', 'holdings': 800},
            {'code': '002475', 'name': '立讯精密', 'industry': '电子制造', 'holdings': 600},
            {'code': '600030', 'name': '中信证券', 'industry': '证券', 'holdings': 550},
            {'code': '000725', 'name': '京东方A', 'industry': '面板', 'holdings': 2000},
            {'code': '002230', 'name': '科大讯飞', 'industry': '软件开发', 'holdings': 400},
            {'code': '000063', 'name': '中兴通讯', 'industry': '通信设备', 'holdings': 700},
            {'code': '600741', 'name': '华域汽车', 'industry': '汽车零部件', 'holdings': 650},
            {'code': '002027', 'name': '分众传媒', 'industry': '广告', 'holdings': 800},
            {'code': '600585', 'name': '海螺水泥', 'industry': '水泥制造', 'holdings': 500},
            {'code': '000166', 'name': '申万宏源', 'industry': '证券', 'holdings': 900},
            {'code': '600104', 'name': '上汽集团', 'industry': '汽车整车', 'holdings': 750},
            {'code': '002008', 'name': '大族激光', 'industry': '激光设备', 'holdings': 300},
            {'code': '000977', 'name': '浪潮信息', 'industry': '计算机设备', 'holdings': 400},
            {'code': '600660', 'name': '福耀玻璃', 'industry': '汽车零部件', 'holdings': 600}
        ]

        # 为每支股票生成随机数据
        for stock in demo_stocks:
            # 基础价格范围
            if '茅台' in stock['name']:
                base_price = random.uniform(1600, 1800)
            elif '银行' in stock['industry']:
                base_price = random.uniform(15, 45)
            elif '白酒' in stock['industry']:
                base_price = random.uniform(80, 200)
            elif stock['name'] in ['比亚迪', '海康威视']:
                base_price = random.uniform(200, 300)
            else:
                base_price = random.uniform(8, 80)

            # 生成年内最低价（当前价格的60%-85%）
            yearly_low = base_price * random.uniform(0.60, 0.85)

            # 当前价格（基于最低价的涨幅）
            gain_pct = random.uniform(15, 85)  # 15%-85%的涨幅
            current_price = yearly_low * (1 + gain_pct / 100)

            # 计算距离最低点涨幅
            distance_from_low = ((current_price - yearly_low) / yearly_low) * 100

            # 随机生成其他数据
            pe_ratio = random.choice([None, random.uniform(-20, 35)]) if random.random() < 0.9 else None
            pb_ratio = random.uniform(0.8, 8.5)
            dividend_yield = random.uniform(0.5, 6.8)
            change_pct = random.uniform(-3.5, 4.2)

            # 计算市值
            if current_price > 0 and stock['holdings'] != 0:
                market_value = round(current_price * stock['holdings'], 2)
            else:
                market_value = 0.0

            # 计算回本涨幅（随机生成成本价）
            cost_price = current_price * random.uniform(0.85, 1.15)
            profit_margin = ((current_price - cost_price) / cost_price) * 100
            profit_status = 'profit' if profit_margin > 0 else 'loss'

            # 生成卖出信号
            sell_signal_data = {
                'distance_from_low_pct': distance_from_low,
                'pe_ratio': pe_ratio,
                'pb_ratio': pb_ratio,
                'profit_margin': profit_margin,
                'profit_status': profit_status,
                'dividend_yield': dividend_yield,
                'scan_score': random.randint(60, 95)  # 提前生成扫雷评分
            }
            sell_signal = calculate_sell_signal(sell_signal_data)

            # 生成扫雷数据（使用已生成的评分）
            scan_score = sell_signal_data['scan_score']
            risk_count = random.randint(0, 3)

            # 生成成本价和单位成本
            unit_cost = cost_price

            # 构建股票信息 - 确保包含所有表格列的数据
            info = {
                'code': stock['code'],  # 添加代码字段
                'name': stock['name'],
                'price': round(current_price, 2),
                'change_pct': round(change_pct, 2),
                'yearly_low': round(yearly_low, 2),
                'distance_from_low_pct': round(distance_from_low, 1),
                'low_date': f"2024-{random.randint(1,12):02d}-{random.randint(1,28):02d}",
                'pe_ratio': round(pe_ratio, 2) if pe_ratio else None,
                'pb_ratio': round(pb_ratio, 2),
                'dividend_yield': round(dividend_yield, 2),
                'industry': stock['industry'],
                'holdings': stock['holdings'],
                'market_value': round(market_value, 2),
                'profit_margin': round(profit_margin, 1),
                'profit_status': profit_status,
                'unit_cost': round(unit_cost, 3),  # 添加单位成本
                'sell_signal': sell_signal['signal'],
                'sell_reason': sell_signal['reason'],
                'sell_color': sell_signal['color'],
                'sell_priority': sell_signal['priority'],
                'scan_score': scan_score,
                'scan_risk_level': '低风险' if scan_score > 80 else '中风险' if scan_score > 70 else '高风险',
                'scan_risk_color': '#2ed573' if scan_score > 80 else '#ffa502' if scan_score > 70 else '#ff4757',
                'scan_triggered_risks': risk_count,
                'scan_display_text': f'{scan_score}分',
                'update_time': datetime.now().strftime('%H:%M:%S'),
                'is_reduced': False,  # 默认未减半
                'reduced_time': None  # 减半时间
            }

            stock_data[stock['code']] = info
            imported_stock_list.append({
                'code': stock['code'],
                'name': stock['name'],
                'holdings': stock['holdings']
            })

        # 更新时间
        last_update_time = datetime.now()

        # 保存数据
        save_stock_data()
        save_imported_list()

        return jsonify({
            'success': True,
            'message': f'演示数据生成成功！已生成 {len(demo_stocks)} 支股票的模拟数据',
            'data': {
                'stock_count': len(demo_stocks),
                'total_market_value': sum(info['market_value'] for info in stock_data.values()),
                'sell_signals': len([s for s in stock_data.values() if s['sell_signal'] == 'sell']),
                'warning_signals': len([s for s in stock_data.values() if s['sell_signal'] == 'warning'])
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'生成演示数据失败: {str(e)}'
        }), 500

# HTML模板
HTML_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>持仓系统 V14 智能状态管理版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
        }

        .header .subtitle {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .update-info {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 5px;
            margin-top: 15px;
        }

        .controls {
            background: white;
            padding: 20px;
            margin: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-box {
            flex: 1;
            max-width: 300px;
            margin-right: 20px;
        }

        .search-box input {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }

        .stats {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            font-size: 0.9em;
            color: #666;
        }

        /* 分类筛选按钮 */
        .filter-buttons {
            background: white;
            padding: 15px 20px;
            margin: 0 20px 20px 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .filter-btn {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            color: #495057;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
        }

        .filter-btn:hover {
            background: #e9ecef;
            border-color: #adb5bd;
            transform: translateY(-2px);
        }

        .filter-btn.active {
            background: #667eea;
            border-color: #667eea;
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        /* 统计面板 */
        .stats-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 0 20px 20px 20px;
        }

        .stats-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .stats-card h3 {
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .total-assets {
            border-left: 4px solid #28a745;
        }

        .asset-allocation {
            border-left: 4px solid #17a2b8;
        }



        .stats-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .stats-item:last-child {
            border-bottom: none;
        }

        .stats-value {
            font-weight: bold;
            color: #667eea;
        }

        /* ==================== 按钮系统优化 ==================== */

        /* 数据管理按钮容器 */
        .management-buttons {
            background: white;
            padding: 15px 20px;
            margin: 0 20px 20px 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        /* 基础按钮样式 */
        .management-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            position: relative;
            overflow: hidden;
        }

        .management-btn:hover {
            background: #5a6268;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .management-btn:active {
            transform: translateY(0);
        }

        .management-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* 按钮颜色变体 - 使用CSS自定义属性优化 */
        .management-btn.danger {
            --btn-bg: #dc3545;
            --btn-hover: #c82333;
            background: var(--btn-bg);
        }

        .management-btn.danger:hover:not(:disabled) {
            background: var(--btn-hover);
        }

        .management-btn.success {
            --btn-bg: #28a745;
            --btn-hover: #218838;
            background: var(--btn-bg);
        }

        .management-btn.success:hover:not(:disabled) {
            background: var(--btn-hover);
        }

        .management-btn.primary {
            --btn-bg: #007bff;
            --btn-hover: #0056b3;
            background: var(--btn-bg);
        }

        .management-btn.primary:hover:not(:disabled) {
            background: var(--btn-hover);
        }

        .management-btn.warning {
            --btn-bg: #ffc107;
            --btn-hover: #e0a800;
            background: var(--btn-bg);
            color: #212529;
        }

        .management-btn.warning:hover:not(:disabled) {
            background: var(--btn-hover);
        }

        .management-btn.info {
            --btn-bg: #17a2b8;
            --btn-hover: #138496;
            background: var(--btn-bg);
        }

        .management-btn.info:hover:not(:disabled) {
            background: var(--btn-hover);
        }

        .management-btn.strategy {
            --btn-bg: #6f42c1;
            --btn-hover: #5a2d91;
            background: var(--btn-bg);
        }

        .management-btn.strategy:hover:not(:disabled) {
            background: var(--btn-hover);
        }

        .management-btn.priority {
            --btn-bg: #e83e8c;
            --btn-hover: #d91a72;
            background: var(--btn-bg);
        }

        .management-btn.priority:hover:not(:disabled) {
            background: var(--btn-hover);
        }

        .management-btn.auto-update {
            --btn-bg: #17a2b8;
            --btn-hover: #138496;
            background: var(--btn-bg);
        }

        .management-btn.auto-update:hover:not(:disabled) {
            background: var(--btn-hover);
        }

        .management-btn.auto-update.disabled {
            background: #6c757d;
        }

        .management-btn.signal-refresh {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        }

        .management-btn.signal-refresh:hover:not(:disabled) {
            background: linear-gradient(135deg, #ff8a8e 0%, #fdbfdf 100%);
        }

        /* 优先级控制面板样式 */
        .priority-panel {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .priority-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            max-width: 600px;
            width: 90%;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .priority-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e83e8c;
        }

        .priority-header h3 {
            margin: 0;
            color: #e83e8c;
        }

        .priority-description {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #e83e8c;
        }

        .priority-description p {
            margin: 0 0 10px 0;
            font-weight: bold;
            color: #333;
        }

        .priority-description ul {
            margin: 0;
            padding-left: 20px;
        }

        .priority-description li {
            margin-bottom: 8px;
            color: #555;
        }

        .priority-control {
            margin-bottom: 20px;
        }

        .priority-setting {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
        }

        .priority-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
        }

        .priority-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .priority-toggle-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .priority-toggle {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .priority-toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .priority-toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .priority-toggle-slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        .priority-toggle input:checked + .priority-toggle-slider {
            background-color: #e83e8c;
        }

        .priority-toggle input:checked + .priority-toggle-slider:before {
            transform: translateX(26px);
        }

        .priority-status {
            font-weight: bold;
            min-width: 40px;
        }

        .priority-explanation {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px;
            border-radius: 8px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
        }

        .priority-explanation.disabled {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
        }

        .priority-icon {
            font-size: 18px;
        }

        .priority-text {
            color: #333;
        }

        .priority-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }

        .priority-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .priority-btn.cancel {
            background: #6c757d;
            color: white;
        }

        .priority-btn.cancel:hover {
            background: #5a6268;
        }

        .priority-btn.save {
            background: #e83e8c;
            color: white;
        }

        .priority-btn.save:hover {
            background: #d91a72;
        }

        /* 减半监控配置面板样式 */
        .reduction-monitoring-panel {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .reduction-monitoring-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .reduction-monitoring-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #007bff;
        }

        .reduction-monitoring-header h3 {
            margin: 0;
            color: #007bff;
            font-size: 20px;
        }

        .reduction-config-section {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #007bff;
        }

        .reduction-config-section h4 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 16px;
        }

        .reduction-config-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .reduction-config-item:last-child {
            margin-bottom: 0;
        }

        .reduction-config-label {
            flex: 1;
            font-weight: 500;
            color: #333;
        }

        .reduction-config-input {
            width: 120px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .reduction-config-toggle {
            position: relative;
            width: 50px;
            height: 25px;
            background: #ccc;
            border-radius: 25px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .reduction-config-toggle.active {
            background: #007bff;
        }

        .reduction-config-toggle::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 21px;
            height: 21px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
        }

        .reduction-config-toggle.active::after {
            transform: translateX(25px);
        }

        .reduction-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .reduction-summary h4 {
            margin: 0 0 15px 0;
            font-size: 16px;
        }

        .reduction-summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
        }

        .reduction-summary-item {
            text-align: center;
        }

        .reduction-summary-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .reduction-summary-label {
            font-size: 12px;
            opacity: 0.9;
        }

        .reduction-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
        }

        .reduction-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .reduction-btn.cancel {
            background: #6c757d;
            color: white;
        }

        .reduction-btn.cancel:hover {
            background: #5a6268;
        }

        .reduction-btn.save {
            background: #007bff;
            color: white;
        }

        .reduction-btn.save:hover {
            background: #0056b3;
        }

        /* 策略配置面板样式 */
        .strategy-panel {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .strategy-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        /* 策略配置面板移动端优化 */
        @media (max-width: 768px) {
            .strategy-card {
                max-width: 95vw;
                margin: 10px;
                padding: 15px;
                max-height: 85vh;
            }

            .preset-buttons {
                flex-direction: column;
                gap: 8px;
            }

            .preset-btn {
                width: 100%;
                padding: 12px 16px;
                font-size: 14px;
            }

            .strategy-params {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .strategy-actions {
                flex-direction: column;
                gap: 10px;
            }

            .action-btn {
                width: 100%;
                padding: 12px 20px;
                font-size: 16px;
            }
        }

        .strategy-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }

        .close-btn {
            background: #ff4757;
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            cursor: pointer;
            font-size: 18px;
            line-height: 1;
        }

        .strategy-presets {
            margin-bottom: 20px;
        }

        .preset-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .preset-btn {
            background: #74b9ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .preset-btn:hover {
            background: #0984e3;
            transform: translateY(-2px);
        }

        .strategy-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }

        .strategy-item.enabled {
            border-color: #28a745;
            background: #f8fff9;
        }

        .strategy-item.disabled {
            border-color: #dc3545;
            background: #fff8f8;
            opacity: 0.7;
        }

        .strategy-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .strategy-toggle {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .strategy-toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: #28a745;
        }

        input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }

        .strategy-params {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }

        .param-group {
            display: flex;
            flex-direction: column;
        }

        .param-group label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .param-group input {
            padding: 5px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .strategy-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 2px solid #eee;
        }

        .action-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .action-btn.primary {
            background: #007bff;
            color: white;
        }

        .action-btn.primary:hover {
            background: #0056b3;
        }

        .action-btn.secondary {
            background: #6c757d;
            color: white;
        }

        .action-btn.secondary:hover {
            background: #545b62;
        }

        .action-btn.info {
            background: #17a2b8;
            color: white;
        }

        .action-btn.info:hover {
            background: #138496;
        }

        .action-btn.success {
            background: #28a745;
            color: white;
        }

        .action-btn.success:hover {
            background: #218838;
        }

        /* 自定义策略对话框 */
        .custom-strategy-dialog {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1100;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .dialog-content {
            background: white;
            border-radius: 10px;
            padding: 20px;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .dialog-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }

        .dialog-body {
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-group textarea {
            height: 80px;
            resize: vertical;
        }

        .add-condition-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }

        .delete-strategy-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .delete-strategy-btn:hover {
            background: #c82333;
            transform: scale(1.1);
        }

        .condition-item {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
            background: #f8f9fa;
        }

        .dialog-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            padding-top: 20px;
            border-top: 2px solid #eee;
        }

        /* 单只股票更新按钮样式 */
        .update-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
            min-width: 30px;
        }

        .update-btn:hover {
            background: #218838;
            transform: scale(1.1);
        }

        .update-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .update-btn.updating {
            background: #ffc107;
            color: #212529;
            animation: pulse 1s infinite;
        }

        /* 操作按钮容器 */
        .action-buttons {
            display: flex;
            gap: 4px;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* 编辑持仓按钮 */
        .edit-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
            min-width: 30px;
        }

        .edit-btn:hover {
            background: #0056b3;
            transform: scale(1.1);
        }

        /* 操作按钮 */
        .action-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
            min-width: 30px;
        }

        .action-btn:hover {
            background: #218838;
            transform: scale(1.1);
        }

        /* 操作菜单样式 */
        .action-menu-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .action-menu {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            min-width: 300px;
            max-width: 400px;
        }

        .action-menu-header {
            padding: 16px;
            border-bottom: 1px solid #eee;
        }

        .action-menu-header h3 {
            margin: 0 0 8px 0;
            color: #333;
        }

        .stock-info {
            font-size: 14px;
            color: #666;
        }

        .price-info {
            margin-top: 8px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
            font-size: 12px;
            line-height: 1.4;
        }

        .action-menu-content {
            padding: 8px 0;
        }

        .action-menu-item {
            padding: 12px 16px;
            cursor: pointer;
            transition: background 0.2s;
            border-bottom: 1px solid #f0f0f0;
        }

        .action-menu-item:hover {
            background: #f8f9fa;
        }

        .action-menu-item:last-child {
            border-bottom: none;
        }

        .action-menu-footer {
            padding: 12px 16px;
            border-top: 1px solid #eee;
            text-align: right;
        }

        .action-menu-footer button {
            background: #6c757d;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
        }

        .action-menu-footer button:hover {
            background: #5a6268;
        }

        /* 策略信息显示样式 */
        .signal-cell {
            min-width: 120px;
        }

        .signal-container {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .strategy-info {
            font-size: 10px;
            line-height: 1.3;
            color: #666;
            display: flex;
            flex-direction: column;
            gap: 1px;
            max-width: 200px;
        }

        .strategy-signal {
            font-weight: 600;
            color: #333;
            font-size: 11px;
        }

        .strategy-name {
            font-weight: 500;
            color: #555;
            font-size: 10px;
        }

        .strategy-explanation {
            color: #777;
            font-size: 9px;
            line-height: 1.2;
        }

        .strategy-price {
            color: #dc3545;
            font-weight: 600;
            font-size: 10px;
            margin-top: 1px;
        }

        .custom-status-info {
            font-size: 10px;
            color: #999;
            line-height: 1.2;
        }

        .custom-status-time {
            font-size: 10px;
            color: #999;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .signal-cell {
                min-width: 120px;
            }

            .strategy-info {
                font-size: 9px;
                max-width: 150px;
            }

            .strategy-signal {
                font-size: 10px;
            }

            .strategy-name {
                font-size: 9px;
            }

            .strategy-explanation {
                font-size: 8px;
            }

            .strategy-price {
                font-size: 9px;
            }

            /* 移动端固定表头和固定列 */
            .table-container {
                position: relative;
                overflow: auto;
                -webkit-overflow-scrolling: touch;
                max-height: 90vh;
                /* 确保在移动端有正确的滚动行为 */
                overscroll-behavior: contain;
            }

            /* 移动端表头固定 - 增强版本 */
            th {
                position: sticky;
                top: 0;
                z-index: 20;
                background: #f8f9fa;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                /* 确保表头在滚动时保持可见 */
                backdrop-filter: blur(5px);
                -webkit-backdrop-filter: blur(5px);
                /* 防止表头内容换行 */
                white-space: nowrap;
            }

            /* 移动端固定第一列（代码列） - 增强版本 */
            th.sticky-left-1,
            td.sticky-left-1 {
                position: sticky;
                left: 0;
                z-index: 15;
                background: #f8f9fa;
                box-shadow: 2px 0 4px rgba(0,0,0,0.08);
                min-width: 60px;
                /* 确保固定列在表头固定时也能正确显示 */
                backdrop-filter: blur(5px);
                -webkit-backdrop-filter: blur(5px);
            }

            /* 移动端固定第二列（名称列） - 增强版本 */
            th.sticky-left-2,
            td.sticky-left-2 {
                position: sticky;
                left: 60px;
                z-index: 15;
                background: #f8f9fa;
                box-shadow: 2px 0 4px rgba(0,0,0,0.08);
                min-width: 80px;
                /* 确保固定列在表头固定时也能正确显示 */
                backdrop-filter: blur(5px);
                -webkit-backdrop-filter: blur(5px);
            }

            /* 移动端表头固定列的最高优先级 */
            th.sticky-left-1 {
                z-index: 25;
                background: #f8f9fa;
            }

            th.sticky-left-2 {
                z-index: 25;
                background: #f8f9fa;
            }

            /* 移动端数据行固定列背景 */
            tbody td.sticky-left-1,
            tbody td.sticky-left-2 {
                background: white;
            }

            /* 移动端悬停时保持固定列背景 */
            tbody tr:hover td.sticky-left-1,
            tbody tr:hover td.sticky-left-2 {
                background: #e3f2fd !important;
            }
        }

        /* 已减半标记 */
        .reduced-mark {
            background: #6c757d;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            white-space: nowrap;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        /* 内联编辑持仓样式 */
        .holdings-cell {
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            padding: 8px 12px;
        }

        .holdings-cell:hover {
            background-color: #f8f9fa;
            border-radius: 4px;
        }

        .holdings-value {
            display: inline-block;
            min-width: 40px;
            text-align: center;
        }

        .holdings-input {
            border: 2px solid #007bff;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 14px;
            text-align: center;
            width: 80px;
            background: #fff;
            box-shadow: 0 2px 8px rgba(0,123,255,0.3);
            outline: none;
        }

        .holdings-input:focus {
            border-color: #0056b3;
            box-shadow: 0 2px 12px rgba(0,123,255,0.5);
        }

        .holdings-saving {
            background-color: #e3f2fd;
            border-color: #2196f3;
        }

        .holdings-success {
            background-color: #e8f5e8;
            border-color: #4caf50;
            animation: successPulse 0.6s ease-out;
        }

        .holdings-error {
            background-color: #ffebee;
            border-color: #f44336;
            animation: errorShake 0.6s ease-out;
        }

        @keyframes successPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes errorShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-4px); }
            75% { transform: translateX(4px); }
        }

        /* 自定义策略条件显示 */
        .custom-strategy-conditions {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }

        .condition-display {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            margin: 5px 0;
            background: #f0f8ff;
            border-radius: 4px;
            border-left: 3px solid #007bff;
            font-size: 13px;
        }

        .condition-display:hover {
            background: #e3f2fd;
        }

        .edit-condition-btn, .add-condition-to-strategy-btn {
            transition: all 0.2s ease;
        }

        .edit-condition-btn:hover {
            background: #0056b3 !important;
            transform: scale(1.05);
        }

        .add-condition-to-strategy-btn:hover {
            background: #218838 !important;
            transform: translateY(-1px);
        }

        .edit-strategy-btn:hover {
            background: #0056b3 !important;
            transform: scale(1.05);
        }

        .delete-strategy-btn:hover {
            background: #c82333 !important;
            transform: scale(1.05);
        }

        /* 操作列样式 */
        .no-sort {
            cursor: default !important;
        }

        .no-sort:hover {
            background: transparent !important;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 持仓系统 V14 智能状态管理版</h1>
        <div class="subtitle">A股股票数据监控 | 智能交易时间检测 | 扫雷风险评估 | 自动状态保存恢复 | 自动过滤非股票证券</div>
        <div class="update-info">
            <span id="updateTime">等待数据加载...</span> |
            <span id="stockCount">0</span> 只A股股票 |
            <span id="tradingStatus">检查交易状态中...</span>
        </div>
    </div>

    <!-- 数据管理按钮 -->
    <div class="management-buttons">
        <button class="management-btn danger" onclick="clearData()">🗑️ 清空数据</button>
        <input type="file" id="fileInput" accept=".xlsx,.xls" style="display: none;" onchange="uploadFile()">
        <button class="management-btn primary" onclick="document.getElementById('fileInput').click()">📁 导入持仓表格</button>
        <button class="management-btn info" onclick="generateDemoData()">🎭 生成演示数据</button>
        <button class="management-btn success" onclick="refreshData()">🔄 重新获取数据</button>
        <button class="management-btn" onclick="checkCacheStatus()">📊 缓存状态</button>
        <button class="management-btn warning" onclick="testWeChatAlerts()">📱 测试企业微信提醒</button>
        <button class="management-btn strategy" onclick="openStrategyPanel()">⚙️ 策略配置</button>
        <button class="management-btn priority" onclick="openPriorityPanel()" id="priorityBtn">🎯 优先级控制</button>
        <button class="management-btn auto-update" onclick="toggleAutoUpdate()" id="autoUpdateBtn">🔄 自动更新</button>
        <button class="management-btn signal-refresh" onclick="recalculateSellSignals()">🔄 刷新卖出信号</button>
        <button class="management-btn" onclick="batchCancelCustomSignals()" id="batchCancelBtn" style="background: linear-gradient(135deg, #ff6b6b, #ee5a52); color: white;">🔄 批量取消自定义信号</button>
        <button class="management-btn" onclick="openReductionMonitoringPanel()" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white;">📊 减半监控配置</button>
        <button class="management-btn" onclick="debugAllStockPriorities()" style="background: linear-gradient(135deg, #ffa502, #ff6348); color: white;">🔍 调试优先级</button>
        <button class="management-btn" onclick="batchCleanReductionHistory()" style="background: linear-gradient(135deg, #e74c3c, #c0392b); color: white;">🧹 批量清理减半历史</button>

        <!-- 状态管理按钮组 -->
        <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #ddd;">
            <div style="color: #666; font-size: 12px; margin-bottom: 5px;">📋 状态管理</div>
            <div style="color: #888; font-size: 11px; margin-bottom: 8px; line-height: 1.4;">
                💡 系统会自动保存股票状态，导入Excel后自动恢复已减半/已清仓等状态
            </div>
            <button class="management-btn" onclick="exportStockStates()" style="background: linear-gradient(135deg, #f39c12, #e67e22); color: white;">
                💾 导出状态
            </button>
            <button class="management-btn" onclick="importStockStates()" style="background: linear-gradient(135deg, #3498db, #2980b9); color: white;">
                📥 导入状态
            </button>
            <button class="management-btn" onclick="autoRestoreStates()" style="background: linear-gradient(135deg, #27ae60, #229954); color: white;">
                🔄 自动恢复
            </button>
        </div>

        <!-- 隐藏的文件输入 -->
        <input type="file" id="stateFileInput" accept=".json" style="display: none;" onchange="handleStateFileImport(event)">
    </div>

    <!-- 股票资产统计面板 -->
    <div class="stats-panel">
        <div class="stats-card total-assets">
            <h3>💰 股票资产统计</h3>
            <div class="stats-item">
                <span>总市值</span>
                <span class="stats-value" id="totalMarketValue">¥0</span>
            </div>
            <div class="stats-item">
                <span>股票数量</span>
                <span class="stats-value" id="stockCountDisplay">0只</span>
            </div>
        </div>
    </div>

    <!-- 卖出信号优先级控制面板 -->
    <div class="priority-panel" id="priorityPanel" style="display: none;">
        <div class="priority-card">
            <div class="priority-header">
                <h3>🎯 卖出信号优先级控制</h3>
                <button class="close-btn" onclick="closePriorityPanel()">×</button>
            </div>

            <div class="priority-content">
                <div class="priority-description">
                    <p>📋 <strong>功能说明：</strong></p>
                    <ul>
                        <li>🔴 <strong>启用手动优先</strong>：手动标记的卖出信号（如"已清仓"、"已减半"）将优先于系统自动生成的卖出信号</li>
                        <li>🔵 <strong>禁用手动优先</strong>：系统将始终使用最新的自动计算结果，可能覆盖手动标记</li>
                        <li>⚠️ <strong>默认推荐</strong>：启用手动优先，避免手动操作被意外覆盖</li>
                    </ul>
                </div>

                <div class="priority-control">
                    <div class="priority-setting">
                        <label class="priority-label">
                            <span class="priority-title">🎯 手动标记优先级控制</span>
                            <div class="priority-toggle-container">
                                <label class="priority-toggle">
                                    <input type="checkbox" id="manualPriorityToggle" checked>
                                    <span class="priority-toggle-slider"></span>
                                </label>
                                <span class="priority-status" id="priorityStatus">启用</span>
                            </div>
                        </label>
                    </div>

                    <div class="priority-explanation" id="priorityExplanation">
                        <span class="priority-icon">✅</span>
                        <span class="priority-text">手动标记的卖出信号优先于自动生成的信号</span>
                    </div>
                </div>

                <div class="priority-actions">
                    <button class="priority-btn cancel" onclick="closePriorityPanel()">取消</button>
                    <button class="priority-btn save" onclick="savePrioritySettings()">保存设置</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 减半监控配置面板 -->
    <div class="reduction-monitoring-panel" id="reductionMonitoringPanel" style="display: none;">
        <div class="reduction-monitoring-card">
            <div class="reduction-monitoring-header">
                <h3>📊 减半监控配置</h3>
                <button class="close-btn" onclick="closeReductionMonitoringPanel()">×</button>
            </div>

            <div class="reduction-summary" id="reductionSummary">
                <h4>📈 监控状态概览</h4>
                <div class="reduction-summary-grid">
                    <div class="reduction-summary-item">
                        <div class="reduction-summary-value" id="totalStocksCount">0</div>
                        <div class="reduction-summary-label">总股票数</div>
                    </div>
                    <div class="reduction-summary-item">
                        <div class="reduction-summary-value" id="reducedStocksCount">0</div>
                        <div class="reduction-summary-label">已减半股票</div>
                    </div>
                    <div class="reduction-summary-item">
                        <div class="reduction-summary-value" id="coolingDownStocksCount">0</div>
                        <div class="reduction-summary-label">冷却期股票</div>
                    </div>
                    <div class="reduction-summary-item">
                        <div class="reduction-summary-value" id="strategySwitchedStocksCount">0</div>
                        <div class="reduction-summary-label">策略切换股票</div>
                    </div>
                </div>
            </div>

            <div class="reduction-config-section">
                <h4>⏰ 冷却期设置</h4>
                <div class="reduction-config-item">
                    <span class="reduction-config-label">启用冷却期</span>
                    <div class="reduction-config-toggle" id="enableCoolingPeriodToggle" onclick="toggleCoolingPeriod()">
                    </div>
                </div>
                <div class="reduction-config-item">
                    <span class="reduction-config-label">冷却期时长（天）</span>
                    <input type="number" class="reduction-config-input" id="coolingPeriodDays" min="1" max="3650" value="365">
                </div>
            </div>

            <div class="reduction-config-section">
                <h4>📊 基本面监控</h4>
                <div class="reduction-config-item">
                    <span class="reduction-config-label">TTM阈值</span>
                    <input type="number" class="reduction-config-input" id="ttmThreshold" min="0" max="1000" step="0.1" value="30.0">
                </div>
                <div class="reduction-config-item">
                    <span class="reduction-config-label">检查间隔（天）</span>
                    <input type="number" class="reduction-config-input" id="fundamentalsCheckInterval" min="1" max="30" value="1">
                </div>
                <div class="reduction-config-item">
                    <span class="reduction-config-label">自动策略切换</span>
                    <div class="reduction-config-toggle" id="autoStrategySwitchToggle" onclick="toggleAutoStrategySwitch()">
                    </div>
                </div>
            </div>

            <div class="reduction-actions">
                <button class="reduction-btn cancel" onclick="closeReductionMonitoringPanel()">取消</button>
                <button class="reduction-btn save" onclick="saveReductionMonitoringConfig()">保存配置</button>
            </div>
        </div>
    </div>

    <!-- 卖出策略配置面板 -->
    <div class="strategy-panel" id="strategyPanel" style="display: none;">
        <div class="strategy-card">
            <div class="strategy-header">
                <h3>⚙️ 卖出策略配置</h3>
                <button class="close-btn" onclick="closeStrategyPanel()">×</button>
            </div>

            <!-- 策略预设 -->
            <div class="strategy-presets">
                <h4>📋 策略预设</h4>
                <div class="preset-buttons">
                    <button class="preset-btn" onclick="applyPreset('conservative')">🛡️ 保守型</button>
                    <button class="preset-btn" onclick="applyPreset('balanced')">⚖️ 平衡型</button>
                    <button class="preset-btn" onclick="applyPreset('aggressive')">🚀 激进型</button>
                </div>
            </div>

            <!-- 策略列表 -->
            <div class="strategy-list" id="strategyList">
                <!-- 策略项将通过JavaScript动态生成 -->
            </div>

            <!-- 操作按钮 -->
            <div class="strategy-actions">
                <button class="action-btn primary" onclick="saveStrategies()">💾 保存配置</button>
                <button class="action-btn secondary" onclick="resetStrategies()">🔄 重置默认</button>
                <button class="action-btn info" onclick="showCustomStrategyDialog()">➕ 自定义策略</button>
                <button class="action-btn success" onclick="testStrategies()">🧪 测试策略</button>
            </div>
        </div>
    </div>

    <!-- 自定义策略对话框 -->
    <div class="custom-strategy-dialog" id="customStrategyDialog" style="display: none;">
        <div class="dialog-content">
            <div class="dialog-header">
                <h3>➕ 创建自定义策略</h3>
                <button class="close-btn" onclick="closeCustomStrategyDialog()">×</button>
            </div>
            <div class="dialog-body">
                <div class="form-group">
                    <label>策略名称:</label>
                    <input type="text" id="customStrategyName" placeholder="输入策略名称">
                </div>
                <div class="form-group">
                    <label>策略描述:</label>
                    <textarea id="customStrategyDesc" placeholder="描述策略的作用和逻辑"></textarea>
                </div>
                <div class="form-group">
                    <label>条件设置:</label>
                    <div id="customConditions">
                        <!-- 条件将通过JavaScript动态生成 -->
                    </div>
                    <button class="add-condition-btn" onclick="addCustomCondition()">+ 添加条件</button>
                </div>
            </div>
            <div class="dialog-actions">
                <button class="action-btn primary" onclick="saveCustomStrategy()">创建策略</button>
                <button class="action-btn secondary" onclick="closeCustomStrategyDialog()">取消</button>
            </div>
        </div>
    </div>

    <!-- 搜索框 -->
    <div class="controls">
        <div class="search-box">
            <input type="text" id="searchInput" placeholder="搜索股票代码或名称...">
        </div>
    </div>

    <!-- 卖出信号筛选按钮 -->
    <div class="filter-buttons">
        <div style="text-align: center; margin-bottom: 15px;">
            <button class="filter-btn" onclick="filterBySellSignal('all')" id="filterAll">全部股票</button>
            <button class="filter-btn" onclick="filterBySellSignal('clearance')" id="filterClearance" style="background: #8b0000;">🔥 立即清仓</button>
            <button class="filter-btn" onclick="filterBySellSignal('reduce')" id="filterReduce" style="background: #ff6b35;">📉 立即减半</button>
            <button class="filter-btn" onclick="filterBySellSignal('prepare_clearance')" id="filterPrepareClearance" style="background: #ff8c00;">🔥 准备清仓</button>
            <button class="filter-btn" onclick="filterBySellSignal('prepare_reduce')" id="filterPrepareReduce" style="background: #ffa500;">📉 准备减半</button>
            <button class="filter-btn" onclick="filterBySellSignal('sell')" id="filterSell" style="background: #ff4757;">🚨 卖出信号</button>
            <button class="filter-btn" onclick="filterBySellSignal('hold')" id="filterHold" style="background: #2ed573;">✅ 持有</button>
        </div>
        <div style="text-align: center; color: #666; font-size: 14px; padding: 10px;">
            📊 当前显示：仅A股股票（已自动过滤ETF、基金、可转债等其他证券）
        </div>
    </div>

    <!-- 表格容器 -->
    <div class="table-container">
        <table id="stockTable">
            <thead>
                <tr>
                    <th onclick="sortTable('code', event)" class="sticky-left-1">代码 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('name', event)" class="sticky-left-2">名称 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('price', event)">最新价 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('change_pct', event)">涨跌幅 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('yearly_low', event)" class="mobile-hidden">年内最低价 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('low_date', event)" class="mobile-hidden">最低价日期 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('distance_from_low_pct', event)" class="mobile-hidden">距最低点涨幅 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('pb_ratio', event)" class="mobile-hidden">市净率 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('pe_ratio', event)" class="mobile-hidden">TTM市盈率 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('dividend_yield', event)" class="mobile-hidden">股息率 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('industry', event)" class="extra-mobile-hidden">行业 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('sell_priority', event)">卖出信号 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('scan_score', event)">扫雷 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('holdings', event)">持仓 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('profit_margin', event)" class="mobile-hidden">回本涨幅 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('market_value', event)">市值 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('update_time', event)" class="mobile-hidden">更新时间 <span class="sort-indicator"></span></th>
                    <th class="no-sort">操作</th>
                </tr>
            </thead>
            <tbody id="stockTableBody">
                <tr>
                    <td colspan="18" style="text-align: center; padding: 40px;">
                        <div style="color: #666;">
                            <div style="font-size: 48px; margin-bottom: 20px;">📊</div>
                            <div style="font-size: 18px; margin-bottom: 10px;">暂无股票数据</div>
                            <div style="font-size: 14px;">请导入持仓表格或等待数据加载</div>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <style>
        /* 表格样式 */
        .table-container {
            background: white;
            margin: 15px; /* 减小外边距 */
            border-radius: 8px; /* 减小圆角 */
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: auto;
            max-height: 85vh; /* 增加表格最大高度到85% */
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px; /* 进一步减小字体到12px */
            border-spacing: 0;
            line-height: 1.2; /* 进一步减小表格行高 */
        }

        th, td {
            text-align: left;
            white-space: nowrap;
            margin: 0;
            border: none;
        }

        /* 表头保持合理的内边距 */
        th {
            padding: 8px 10px;
            line-height: 1.3;
        }

        /* 表格数据行使用更紧凑的样式 */
        tbody td {
            padding: 2px 6px; /* 只对数据行使用紧凑内边距 */
            line-height: 1.0; /* 数据行使用最紧凑行高 */
            height: 24px; /* 强制设置行高 */
            vertical-align: middle;
        }

        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #495057;
            cursor: pointer;
            user-select: none;
            position: sticky;
            top: 0;
            z-index: 10;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* 固定左侧列样式 */
        .sticky-left-1 {
            position: sticky;
            left: 0;
            min-width: 80px;
        }

        .sticky-left-2 {
            position: sticky;
            left: 80px; /* 第一列的宽度 */
            min-width: 100px;
        }

        /* 表头固定列样式 - 最高优先级 */
        th.sticky-left-1 {
            z-index: 1000;
            background: #f8f9fa;
            box-shadow: 2px 0 4px rgba(0,0,0,0.08);
        }

        th.sticky-left-2 {
            z-index: 1000;
            background: #f8f9fa;
            box-shadow: 2px 0 4px rgba(0,0,0,0.08);
        }

        /* 数据行的固定列样式 */
        td.sticky-left-1 {
            z-index: 100;
            background: white;
            box-shadow: 2px 0 4px rgba(0,0,0,0.05);
        }

        td.sticky-left-2 {
            z-index: 100;
            background: white;
            box-shadow: 2px 0 4px rgba(0,0,0,0.05);
        }

        th:hover {
            background: #e9ecef;
        }

        .sort-indicator {
            margin-left: 5px;
            font-size: 12px;
            color: #999;
            font-weight: bold;
            min-width: 20px;
            display: inline-block;
            text-align: center;
        }

        .sort-indicator.asc {
            color: #007bff;
        }

        .sort-indicator.desc {
            color: #007bff;
        }

        /* 表头悬停效果 */
        th[onclick]:hover {
            background-color: #f8f9fa;
            cursor: pointer;
        }

        th[onclick]:hover .sort-indicator {
            color: #007bff;
        }

        /* 多列排序提示 */
        .sort-help {
            font-size: 11px;
            color: #6c757d;
            margin: 5px 0;
            text-align: center;
            padding: 5px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }

        /* 移动端多列排序按钮 */
        .mobile-multi-sort-enable-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 10px;
            margin-left: 10px;
            cursor: pointer;
        }

        .mobile-multi-sort-enable-btn:hover {
            background: #0056b3;
        }

        .mobile-multi-sort-btn {
            display: none;
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 12px;
            margin: 5px 0;
            cursor: pointer;
            width: 100%;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .mobile-multi-sort-btn:hover {
            background: #c82333;
        }

        /* 表格行优化 - 提高数据密度 */
        tbody tr {
            border-bottom: 1px solid #f8f9fa;
        }

        tbody tr:hover {
            background-color: #f8f9fa;
        }

        /* 按钮样式优化 - 确保触摸友好 */
        .btn, button {
            min-height: 32px; /* 确保按钮足够大以便触摸 */
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            line-height: 1.2;
        }

        /* 操作按钮优化 */
        .action-btn {
            padding: 1px 3px;
            font-size: 9px;
            min-height: 16px;
            max-height: 18px; /* 限制最大高度 */
            margin: 0.5px;
            line-height: 1.0;
            display: inline-block;
            vertical-align: middle;
            box-sizing: border-box;
        }

        /* 数字列样式优化 */
        .number-cell {
            text-align: right;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        /* 紧凑模式样式 */
        .compact-mode table {
            font-size: 11px;
        }

        .compact-mode th, .compact-mode td {
            padding: 3px 6px;
            line-height: 1.1;
        }

        /* 状态指示器优化 */
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 4px;
        }

        /* 表格行高度控制 */
        tbody tr {
            height: 26px; /* 桌面端固定行高 */
        }

        /* 操作列样式优化 */
        .action-cell {
            padding: 1px 4px !important;
            vertical-align: middle;
        }

        .action-cell .action-btn {
            margin: 0 1px;
        }

        /* 移动端排序指示器优化 */
        @media (max-width: 768px) {
            .sort-indicator {
                font-size: 10px;
                min-width: 15px;
            }

            th[onclick] {
                position: relative;
                -webkit-tap-highlight-color: transparent;
                touch-action: manipulation;
            }

            th[onclick]:active {
                background-color: #e3f2fd;
                transform: scale(0.98);
            }

            /* 移动端表头触摸反馈 */
            th[onclick]::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 123, 255, 0.1);
                opacity: 0;
                transition: opacity 0.2s;
                pointer-events: none;
            }

            th[onclick]:active::after {
                opacity: 1;
            }
        }

        /* 表格行样式 - 统一背景色 */
        tbody tr {
            background-color: white;
        }

        tbody tr:hover {
            background-color: #e3f2fd !important;
            transform: translateY(-1px);
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        tbody tr:hover td {
            background-color: #e3f2fd !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .positive {
            color: #dc3545;
        }

        .negative {
            color: #28a745;
        }

        .scan-score {
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
        }

        /* 卖出信号样式 */
        .sell-signal {
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            font-size: 12px;
            text-align: center;
            min-width: 60px;
            display: inline-block;
        }

        .sell-signal.sell {
            background-color: #ff4757;
            animation: pulse 2s infinite;
        }

        /* 清仓信号特殊样式 */
        .sell-signal.clearance {
            background-color: #8b0000 !important;
            color: white !important;
            font-weight: bold !important;
            border: 2px solid #ff0000 !important;
            animation: clearance-blink 2s infinite !important;
        }

        @keyframes clearance-blink {
            0%, 50% { opacity: 1; }
            25%, 75% { opacity: 0.7; }
        }

        .sell-signal.warning {
            background-color: #ffa502;
        }

        .sell-signal.hold {
            background-color: #2ed573;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        /* 可点击的卖出信号样式 */
        .sell-signal.clickable {
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .sell-signal.clickable:hover {
            transform: scale(1.05);
            border: 2px solid #fff;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        /* 操作标记样式 */
        .action-mark {
            color: #666;
            font-size: 10px;
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            margin-top: 2px;
            display: inline-block;
        }

        /* 状态菜单样式 */
        .status-menu {
            position: fixed;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.15);
            z-index: 10000;
            min-width: 200px;
            max-width: 250px;
            max-height: 350px;
            overflow-y: auto;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .status-menu-header {
            padding: 10px 15px 8px;
            font-weight: 600;
            font-size: 14px;
            color: #333;
            border-bottom: 1px solid #eee;
            background: #f8f9fa;
            border-radius: 8px 8px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-menu-item {
            padding: 8px 15px;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 8px;
            position: relative;
        }

        .status-menu-item:hover {
            background-color: #f8f9fa;
        }

        .status-menu-item.cancel {
            color: #6c757d;
            border-top: 1px solid #eee;
            margin-top: 4px;
            background: #f8f9fa;
        }

        .status-menu-item.cancel:hover {
            background-color: #e9ecef;
        }

        .status-menu-item.cancel-custom {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            border: none;
            font-weight: 600;
            margin-top: 4px;
        }

        .status-menu-item.cancel-custom:hover {
            background: linear-gradient(135deg, #ff5252, #e53935);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(255, 107, 107, 0.3);
        }

        .status-menu-item.cancel-custom .status-hint {
            color: #ffe0e0 !important;
        }

        .status-menu-item.original {
            border-left: 3px solid #007bff;
        }

        .status-menu-item.custom {
            border-left: 3px solid #6c757d;
        }

        .status-emoji {
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        .status-text {
            flex: 1;
            font-weight: 500;
            color: #333;
        }

        .status-hint {
            color: #666;
            font-size: 11px;
            opacity: 0.8;
        }

        .menu-manage-btn {
            background: none;
            border: none;
            color: #007bff;
            cursor: pointer;
            font-size: 12px;
            padding: 2px 4px;
            border-radius: 3px;
        }

        .menu-manage-btn:hover {
            background: #e3f2fd;
        }

        /* 自定义状态样式 */
        .custom-status {
            border: 2px solid #6c757d !important;
            background: linear-gradient(135deg, #6c757d, #5a6268) !important;
        }

        .custom-status:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                gap: 15px;
            }

            .search-box {
                max-width: none;
                margin-right: 0;
            }

            .stats {
                justify-content: center;
            }

            .stats-panel {
                grid-template-columns: 1fr;
                margin: 0 10px 20px 10px;
            }

            .filter-buttons, .management-buttons {
                margin: 0 10px 20px 10px;
            }

            /* 移动端管理按钮优化 */
            .management-buttons {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
                justify-content: center;
            }

            .management-btn {
                font-size: 12px !important;
                padding: 8px 12px !important;
                min-width: auto !important;
                flex: 0 0 auto;
            }

            .management-btn.strategy {
                background: #6f42c1 !important;
                color: white !important;
                font-weight: bold !important;
            }

            /* 移动端筛选按钮优化 */
            .filter-buttons div {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
                justify-content: center;
            }

            .filter-btn {
                font-size: 12px !important;
                padding: 8px 12px !important;
                min-width: auto !important;
                flex: 0 0 auto;
            }

            /* 清仓信号按钮在移动端的特殊样式 */
            #filterClearance {
                background: #8b0000 !important;
                color: white !important;
                font-weight: bold !important;
                border: 1px solid #ff0000 !important;
            }

            .table-container {
                margin: 5px; /* 移动端更小的外边距 */
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                border-radius: 6px;
                box-shadow: 0 2px 6px rgba(0,0,0,0.1);
                max-height: 90vh; /* 移动端更大的表格高度 */
                /* 移动端固定表头增强 */
                position: relative;
                overscroll-behavior: contain;
                /* 防止iOS Safari的弹性滚动影响固定表头 */
                -webkit-transform: translateZ(0);
                transform: translateZ(0);
            }

            /* 移动端表头固定增强样式 */
            thead th {
                position: sticky !important;
                top: 0 !important;
                z-index: 20 !important;
                background: #f8f9fa !important;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
                /* 增强的背景模糊效果 */
                backdrop-filter: blur(5px) !important;
                -webkit-backdrop-filter: blur(5px) !important;
                /* 确保表头内容不换行 */
                white-space: nowrap !important;
                /* 防止表头在滚动时闪烁 */
                -webkit-transform: translateZ(0);
                transform: translateZ(0);
            }

            /* 移动端固定列表头的特殊处理 */
            thead th.sticky-left-1,
            thead th.sticky-left-2 {
                z-index: 25 !important;
                /* 确保固定列表头在滚动时保持稳定 */
                -webkit-transform: translateZ(0);
                transform: translateZ(0);
            }

            table {
                min-width: 1200px; /* 增加最小宽度以显示所有列 */
                font-size: 11px; /* 进一步减小移动端字体 */
            }

            /* 移动端表头样式 */
            th {
                padding: 6px 4px; /* 移动端表头适中内边距 */
                font-size: 11px;
                line-height: 1.2;
            }

            /* 移动端数据行样式 */
            tbody td {
                padding: 1px 4px; /* 移动端数据行最小内边距 */
                font-size: 10px; /* 移动端数据更小字体 */
                line-height: 1.0; /* 最紧凑行高 */
                height: 20px; /* 移动端更小的固定行高 */
                vertical-align: middle;
            }

            /* 移动端卖出信号优化 */
            .sell-signal {
                font-size: 9px !important;
                padding: 2px 4px !important;
                min-width: 50px !important;
                text-align: center !important;
            }

            /* 移动端清仓信号特殊样式 */
            .sell-signal.clearance {
                background-color: #8b0000 !important;
                color: white !important;
                font-weight: bold !important;
                border: 1px solid #ff0000 !important;
                animation: clearance-blink 2s infinite !important;
            }

            /* 移动端调整固定列位置 - 与上面的样式保持一致 */
            .sticky-left-1 {
                left: 0;
                min-width: 60px;
                position: sticky;
                z-index: 15;
                background: #f8f9fa;
                box-shadow: 2px 0 4px rgba(0,0,0,0.08);
            }

            .sticky-left-2 {
                left: 60px; /* 调整为移动端第一列的宽度 */
                min-width: 80px;
                position: sticky;
                z-index: 15;
                background: #f8f9fa;
                box-shadow: 2px 0 4px rgba(0,0,0,0.08);
            }

            /* 移动端数据行固定列背景色 */
            tbody td.sticky-left-1,
            tbody td.sticky-left-2 {
                background: white;
            }

            /* 移动端显示所有列，通过横向滚动查看 */
            /* .mobile-hidden { display: none; } 注释掉，显示所有列 */

            /* 移动端表格行优化 */
            tbody tr {
                border-bottom: 1px solid #f0f0f0;
                height: 22px !important; /* 移动端强制更小的行高 */
                max-height: 22px;
            }

            /* 移动端操作按钮进一步缩小 */
            .action-btn {
                padding: 0 2px !important;
                font-size: 8px !important;
                min-height: 14px !important;
                max-height: 16px !important;
                line-height: 1.0 !important;
            }



            /* 移动端按钮优化 */
            .btn, button {
                min-height: 36px; /* 移动端按钮更大 */
                padding: 6px 10px;
                font-size: 13px;
                touch-action: manipulation;
            }

            .action-btn {
                min-height: 22px;
                padding: 2px 6px;
                font-size: 10px;
                line-height: 1.0;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.5em; /* 减小标题字体 */
                padding: 8px; /* 减小标题内边距 */
            }

            .table-container {
                margin: 2px; /* 超小屏幕最小外边距 */
                border-radius: 4px;
                max-height: 92vh; /* 超小屏幕最大化表格高度 */
            }

            /* 超小屏幕筛选按钮优化 */
            .filter-btn {
                font-size: 10px !important;
                padding: 6px 8px !important;
                margin: 2px !important;
            }

            /* 超小屏幕卖出信号优化 */
            .sell-signal {
                font-size: 9px !important;
                padding: 3px 4px !important;
                min-width: 40px !important;
            }

            /* 超小屏幕表格优化 */
            table {
                min-width: 1000px;
                font-size: 10px; /* 进一步减小字体 */
            }

            /* 超小屏幕数据密度优化 */
            .sell-signal {
                font-size: 8px !important;
                padding: 2px 3px !important;
                min-width: 35px !important;
                line-height: 1.0 !important;
            }

            th, td {
                padding: 3px 2px; /* 超小屏幕最小内边距 */
                font-size: 10px;
                line-height: 1.0; /* 超小屏幕最紧凑行高 */
            }

            /* 移动端触摸优化 */
            .filter-btn, .management-btn {
                -webkit-tap-highlight-color: transparent;
                touch-action: manipulation;
                min-height: 36px; /* 减小触摸目标高度 */
            }

            .action-btn {
                -webkit-tap-highlight-color: transparent;
                touch-action: manipulation;
                min-height: 24px; /* 操作按钮更小的触摸目标 */
            }

            /* 超小屏幕也强制横向排列 */
            .action-buttons {
                flex-direction: row !important;
                gap: 1px !important;
            }

            /* 移动端策略配置按钮优化 */
            .management-btn.strategy {
                background: #6f42c1 !important;
                border: 2px solid #5a2d91 !important;
            }

            /* 移动端操作按钮优化 - 强制横向排列 */
            .action-buttons {
                flex-direction: row !important; /* 强制横向排列 */
                gap: 2px;
                flex-wrap: wrap; /* 允许换行 */
                justify-content: center; /* 居中对齐 */
                align-items: center;
            }

            /* 确保所有操作按钮都在同一行 */
            td .action-buttons {
                flex-direction: row !important;
                display: flex !important;
            }

            .update-btn, .edit-btn, .reduce-btn {
                font-size: 10px !important;
                padding: 3px 6px !important;
                min-width: 25px !important;
            }

            .reduced-mark {
                font-size: 8px !important;
                padding: 1px 4px !important;
            }

            .header .subtitle {
                font-size: 0.85em;
                padding: 0 10px;
            }

            .filter-btn, .management-btn {
                padding: 6px 10px;
                font-size: 11px;
                margin: 2px;
            }

            table {
                min-width: 1000px; /* 超小屏幕也保持足够宽度显示所有列 */
                font-size: 11px;
            }

            th, td {
                padding: 4px 2px;
                font-size: 10px;
            }

            /* 超小屏幕固定列调整 */
            .sticky-left-1 {
                left: 0;
                min-width: 50px;
                position: sticky;
                z-index: 15;
                background: #f8f9fa;
                box-shadow: 2px 0 4px rgba(0,0,0,0.08);
            }

            .sticky-left-2 {
                left: 50px; /* 调整为超小屏幕第一列的宽度 */
                min-width: 70px;
                position: sticky;
                z-index: 15;
                background: #f8f9fa;
                box-shadow: 2px 0 4px rgba(0,0,0,0.08);
            }

            /* 超小屏幕表头固定列的最高优先级 */
            th.sticky-left-1 {
                z-index: 25;
                background: #f8f9fa;
            }

            th.sticky-left-2 {
                z-index: 25;
                background: #f8f9fa;
            }

            /* 超小屏幕数据行固定列背景色 */
            tbody td.sticky-left-1,
            tbody td.sticky-left-2 {
                background: white;
            }

            /* 超小屏幕悬停时保持固定列背景 */
            tbody tr:hover td.sticky-left-1,
            tbody tr:hover td.sticky-left-2 {
                background: #e3f2fd !important;
            }

            /* 超小屏幕也显示所有列 */
            /* .extra-mobile-hidden { display: none; } 注释掉，显示所有列 */
        }
    </style>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // ==================== 全局状态管理 ====================

        class AppState {
            constructor() {
                this.stockData = [];
                this.filteredData = [];
                this.sortColumns = [];
                this.currentSellFilter = 'all';
                this.sortPreferences = {};
                this.isLoading = false;
                this.lastUpdateTime = null;
            }

            // 初始化默认排序配置
            initializeDefaultSort() {
                // 默认排序：已清仓股票在最下面，其他按卖出信号优先级和市值排序
                const defaultSortColumns = [
                    { column: 'sell_priority', direction: 'asc' },
                    { column: 'market_value', direction: 'desc' }
                ];

                // 检查是否有有效的用户偏好
                const hasUserPreferences = this.hasValidSortPreferences();
                if (!hasUserPreferences) {
                    // 使用默认排序，确保已清仓股票在最下面
                    this.sortColumns = defaultSortColumns;
                    console.log('🔧 使用默认排序：已清仓股票在最下面', this.sortColumns);

                    // 立即保存默认排序，避免下次重复设置
                    this.saveSortPreferences();
                } else {
                    // 加载用户保存的排序偏好
                    this.loadSortPreferences();
                    console.log('📋 加载用户排序偏好:', this.sortColumns);
                }
            }

            // 检查是否有有效的用户排序偏好
            hasValidSortPreferences() {
                try {
                    const saved = localStorage.getItem('stockTableSortPreferences');
                    if (saved) {
                        const preferences = JSON.parse(saved);
                        return preferences.sortColumns && Array.isArray(preferences.sortColumns) && preferences.sortColumns.length > 0;
                    }
                    return false;
                } catch (error) {
                    return false;
                }
            }

            // 加载用户排序偏好
            loadSortPreferences() {
                try {
                    const saved = localStorage.getItem('stockTableSortPreferences');
                    if (saved) {
                        this.sortPreferences = JSON.parse(saved);
                        if (this.sortPreferences.sortColumns && Array.isArray(this.sortPreferences.sortColumns)) {
                            this.sortColumns = this.sortPreferences.sortColumns;
                        }
                    }
                } catch (error) {
                    console.warn('加载排序偏好失败:', error);
                }
            }

            // 保存用户排序偏好
            saveSortPreferences() {
                try {
                    this.sortPreferences.sortColumns = this.sortColumns;
                    localStorage.setItem('stockTableSortPreferences', JSON.stringify(this.sortPreferences));
                } catch (error) {
                    console.warn('保存排序偏好失败:', error);
                }
            }
        }

        // 创建全局应用状态
        const appState = new AppState();

        // 向后兼容的全局变量
        let stockData = appState.stockData;
        let filteredData = appState.filteredData;
        let sortColumns = appState.sortColumns;
        let currentSellFilter = appState.currentSellFilter;
        let sortPreferences = appState.sortPreferences;

        // ==================== 工具函数 ====================

        class Utils {
            // 防抖函数
            static debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }

            // 节流函数
            static throttle(func, limit) {
                let inThrottle;
                return function() {
                    const args = arguments;
                    const context = this;
                    if (!inThrottle) {
                        func.apply(context, args);
                        inThrottle = true;
                        setTimeout(() => inThrottle = false, limit);
                    }
                }
            }

            // 安全的数值转换
            static safeParseFloat(value, defaultValue = 0) {
                const parsed = parseFloat(value);
                return isNaN(parsed) ? defaultValue : parsed;
            }

            // 格式化数字
            static formatNumber(num, decimals = 2) {
                if (num === null || num === undefined || isNaN(num)) return '--';
                return Number(num).toFixed(decimals);
            }

            // 显示成功提示
            static showSuccessToast(message) {
                // 简单的成功提示实现
                console.log(`✅ ${message}`);
                // 这里可以扩展为更复杂的提示组件
            }

            // 显示错误提示
            static showErrorToast(message) {
                console.error(`❌ ${message}`);
                alert(message); // 临时使用alert，可以替换为更好的提示组件
            }
        }

        // 调试所有股票的优先级
        function debugAllStockPriorities() {
            console.log('🔍 开始调试所有股票的优先级...');

            if (!stockData || stockData.length === 0) {
                alert('📋 当前没有股票数据');
                return;
            }

            // 计算所有股票的优先级
            const priorityData = stockData.map(stock => ({
                name: stock.name,
                code: stock.code,
                priority: getSellSignalPriority(stock),
                custom_status: stock.custom_status,
                custom_status_text: stock.custom_status_text,
                is_reduced: stock.is_reduced,
                is_cleared: stock.is_cleared,
                strategy_mode: stock.strategy_mode,
                sell_signal: stock.sell_signal
            }));

            // 按优先级排序
            priorityData.sort((a, b) => a.priority - b.priority);

            // 生成调试报告
            let report = '🔍 股票优先级调试报告\\n\\n';
            report += '排序结果（按优先级从高到低）:\\n';
            report += '=' .repeat(50) + '\\n';

            priorityData.forEach((stock, index) => {
                const statusInfo = [];
                if (stock.custom_status) statusInfo.push(`自定义: ${stock.custom_status_text}`);
                if (stock.is_reduced) statusInfo.push(`已减半(${stock.strategy_mode})`);
                if (stock.is_cleared) statusInfo.push('已清仓');
                if (stock.sell_signal) statusInfo.push(`信号: ${stock.sell_signal}`);

                report += `${index + 1}. [${stock.priority}] ${stock.name} (${stock.code})\\n`;
                report += `   状态: ${statusInfo.join(', ') || '正常'}\\n\\n`;
            });

            // 统计各优先级数量
            const priorityStats = {};
            priorityData.forEach(stock => {
                priorityStats[stock.priority] = (priorityStats[stock.priority] || 0) + 1;
            });

            report += '\\n优先级统计:\\n';
            report += '=' .repeat(30) + '\\n';
            Object.keys(priorityStats).sort((a, b) => a - b).forEach(priority => {
                const count = priorityStats[priority];
                const priorityName = getPriorityName(parseInt(priority));
                report += `优先级 ${priority} (${priorityName}): ${count} 只\\n`;
            });

            console.log(report);
            alert('🔍 优先级调试完成！\\n\\n详细信息已输出到浏览器控制台，请按F12查看。\\n\\n' +
                  `总计 ${stockData.length} 只股票，共 ${Object.keys(priorityStats).length} 个不同优先级。`);
        }

        // 获取优先级名称
        function getPriorityName(priority) {
            const priorityNames = {
                1: '立即清仓',
                2: '立即减半',
                3: '准备清仓',
                4: '准备减半',
                5: '卖出信号',
                6: '预警信号',
                10: '已挂单',
                15: '已减半(负TTM)',
                16: '已减半(高TTM)',
                17: '已减半(冷却期)',
                18: '已减半(正常)',
                19: '已减半(其他)',
                20: '其他自定义',
                50: '持有',
                60: '异常状态',
                999: '已清仓'
            };
            return priorityNames[priority] || '未知';
        }

        // ==================== 排序功能优化 ====================

        // 初始化默认排序配置
        function initializeDefaultSort() {
            appState.initializeDefaultSort();
        }

        // 加载用户排序偏好
        function loadSortPreferences() {
            appState.loadSortPreferences();
        }

        // 保存用户排序偏好
        function saveSortPreferences() {
            appState.saveSortPreferences();
        }

        // 调试优先级计算
        function debugStockPriority(stock) {
            const priority = getSellSignalPriority(stock);
            console.log(`📊 ${stock.name} (${stock.code}):`, {
                priority: priority,
                custom_status: stock.custom_status,
                custom_status_text: stock.custom_status_text,
                is_reduced: stock.is_reduced,
                is_cleared: stock.is_cleared,
                strategy_mode: stock.strategy_mode,
                sell_signal: stock.sell_signal
            });
            return priority;
        }

        // 获取卖出信号的优先级权重（按照完整交易流程排序）
        function getSellSignalPriority(stock) {
            // 🛑 最低优先级 - 已完全退出（放在最后）
            if (stock.is_cleared) {
                return 999; // 🛑 已清仓 - 最低优先级，排在最后
            }

            // 🧊 已减半股票 - 根据策略模式设置优先级（在系统信号之前处理）
            if (stock.is_reduced) {
                const strategyMode = stock.strategy_mode || 'normal';
                switch (strategyMode) {
                    case 'negative_ttm':
                        return 15; // ⚠️ 负TTM策略 - 需要关注
                    case 'high_ttm':
                        return 16; // 📊 高TTM策略 - 需要关注
                    case 'cooling_down':
                        return 17; // 🧊 冷却期 - 正常监控
                    case 'normal':
                        return 18; // ✅ 正常监控
                    default:
                        return 19; // 🔄 其他状态
                }
            }

            // 🚨 最高优先级 - 需要立即行动（只有在没有自定义状态时才使用系统信号）
            if (!stock.custom_status) {
                if (stock.sell_signal === 'clearance') {
                    return 1; // 🔥 立即清仓 - 最高优先级
                }
                if (stock.sell_signal === 'reduce') {
                    return 2; // 📉 立即减半
                }
                if (stock.sell_signal === 'prepare_clearance') {
                    return 3; // ⚠️ 准备清仓
                }
                if (stock.sell_signal === 'prepare_reduce') {
                    return 4; // 📊 准备减半
                }
                if (stock.sell_signal === 'sell') {
                    return 5; // 🚨 卖出信号
                }
                if (stock.sell_signal === 'warning') {
                    return 6; // ⚠️ 预警信号
                }
            }

            // 📋 处理自定义状态 - 根据具体状态文本判断优先级
            if (stock.custom_status && stock.custom_status_text) {
                const statusText = stock.custom_status_text.toLowerCase();

                // 已挂单状态
                if (statusText.includes('挂单') || statusText.includes('待成交')) {
                    return 10; // ⏳ 已挂单 - 等待成交
                }

                // 已减半状态 - 根据监控状态细分
                if (statusText.includes('已减半') || statusText.includes('减半')) {
                    const strategyMode = stock.strategy_mode || 'normal';
                    switch (strategyMode) {
                        case 'negative_ttm':
                            return 15; // ⚠️ 负TTM策略 - 需要关注
                        case 'high_ttm':
                            return 16; // 📊 高TTM策略 - 需要关注
                        case 'cooling_down':
                            return 17; // 🧊 冷却期 - 正常监控
                        case 'normal':
                            return 18; // ✅ 正常监控
                        default:
                            return 19; // 其他减半状态
                    }
                }

                // 已清仓状态（虽然应该被is_cleared捕获，但以防万一）
                if (statusText.includes('已清仓') || statusText.includes('清仓')) {
                    return 999; // 🛑 已清仓
                }

                // 其他自定义状态
                return 20; // 🔧 其他自定义状态
            }



            // 💚 正常持有
            if (stock.sell_signal === 'hold' || !stock.sell_signal) {
                return 50; // 💚 持有 - 正常状态
            }

            // 默认情况
            return 60; // 其他未知状态
        }

        // 比较两个值进行排序
        function compareValues(a, b, column, direction) {
            let aVal = a[column];
            let bVal = b[column];

            // 特殊处理卖出信号优先级
            if (column === 'sell_priority') {
                aVal = getSellSignalPriority(a);
                bVal = getSellSignalPriority(b);
            }

            // 特殊处理回本涨幅排序
            if (column === 'profit_margin') {
                return compareProfitMargin(a, b, direction);
            }

            // 特殊处理百分比列（涨跌幅、距最低点涨幅等）
            if (column === 'change_pct' || column === 'distance_from_low_pct' || column === 'dividend_yield') {
                aVal = parseFloat(aVal) || 0;
                bVal = parseFloat(bVal) || 0;
            }

            // 特殊处理日期时间列
            if (column === 'low_date' || column === 'update_time') {
                return compareDateValues(aVal, bVal, direction);
            }

            // 处理数值类型
            if (typeof aVal === 'number' && typeof bVal === 'number') {
                const result = aVal - bVal;
                return direction === 'asc' ? result : -result;
            }

            // 尝试转换为数字进行比较
            const aNum = parseFloat(aVal);
            const bNum = parseFloat(bVal);
            if (!isNaN(aNum) && !isNaN(bNum)) {
                const result = aNum - bNum;
                return direction === 'asc' ? result : -result;
            }

            // 处理字符串类型
            aVal = String(aVal || '').toLowerCase();
            bVal = String(bVal || '').toLowerCase();

            const result = aVal.localeCompare(bVal);
            return direction === 'asc' ? result : -result;
        }

        // 比较回本涨幅
        function compareProfitMargin(a, b, direction) {
            let aVal = a.profit_margin;
            let bVal = b.profit_margin;

            // 处理null值
            if (aVal === null || aVal === undefined) aVal = -999999;
            if (bVal === null || bVal === undefined) bVal = -999999;

            // 排序逻辑：亏损多的在前，盈利多的在后
            let aWeight, bWeight;

            if (a.profit_status === 'need_gain') {
                // 亏损状态：数值越大（亏损越多）排越前面
                aWeight = 1000 + aVal;  // 亏损50% -> 1050, 亏损10% -> 1010
            } else {
                // 盈利状态：数值越小（盈利越少）排越前面
                aWeight = -aVal;  // 盈利10% -> -10, 盈利50% -> -50
            }

            if (b.profit_status === 'need_gain') {
                bWeight = 1000 + bVal;
            } else {
                bWeight = -bVal;
            }

            const result = aWeight - bWeight;
            return direction === 'asc' ? result : -result;
        }

        // 比较日期值
        function compareDateValues(aVal, bVal, direction) {
            // 处理空值
            if (!aVal && !bVal) return 0;
            if (!aVal) return direction === 'asc' ? 1 : -1;
            if (!bVal) return direction === 'asc' ? -1 : 1;

            // 转换为Date对象
            const aDate = new Date(aVal);
            const bDate = new Date(bVal);

            // 检查日期有效性
            if (isNaN(aDate.getTime()) && isNaN(bDate.getTime())) return 0;
            if (isNaN(aDate.getTime())) return direction === 'asc' ? 1 : -1;
            if (isNaN(bDate.getTime())) return direction === 'asc' ? -1 : 1;

            const result = aDate.getTime() - bDate.getTime();
            return direction === 'asc' ? result : -result;
        }

        // ==================== 优先级控制功能 ====================

        let currentPriorityConfig = {};

        // 打开优先级控制面板
        async function openPriorityPanel() {
            try {
                const response = await fetch('/api/sell-signal-priority');
                const result = await response.json();

                if (result.success) {
                    currentPriorityConfig = result.data;
                    renderPriorityPanel();
                    document.getElementById('priorityPanel').style.display = 'flex';
                } else {
                    alert('加载优先级配置失败: ' + result.message);
                }
            } catch (error) {
                alert('加载优先级配置失败: ' + error.message);
            }
        }

        // 关闭优先级控制面板
        function closePriorityPanel() {
            document.getElementById('priorityPanel').style.display = 'none';
        }

        // 渲染优先级控制面板
        function renderPriorityPanel() {
            const toggle = document.getElementById('manualPriorityToggle');
            const status = document.getElementById('priorityStatus');
            const explanation = document.getElementById('priorityExplanation');

            const isEnabled = currentPriorityConfig.manual_priority_over_auto;

            toggle.checked = isEnabled;
            status.textContent = isEnabled ? '启用' : '禁用';
            status.style.color = isEnabled ? '#28a745' : '#dc3545';

            if (isEnabled) {
                explanation.className = 'priority-explanation';
                explanation.innerHTML = `
                    <span class="priority-icon">✅</span>
                    <span class="priority-text">手动标记的卖出信号优先于自动生成的信号</span>
                `;
            } else {
                explanation.className = 'priority-explanation disabled';
                explanation.innerHTML = `
                    <span class="priority-icon">⚠️</span>
                    <span class="priority-text">系统将使用自动计算结果，可能覆盖手动标记</span>
                `;
            }

            // 添加切换事件监听器
            toggle.onchange = function() {
                const newStatus = this.checked;
                status.textContent = newStatus ? '启用' : '禁用';
                status.style.color = newStatus ? '#28a745' : '#dc3545';

                if (newStatus) {
                    explanation.className = 'priority-explanation';
                    explanation.innerHTML = `
                        <span class="priority-icon">✅</span>
                        <span class="priority-text">手动标记的卖出信号优先于自动生成的信号</span>
                    `;
                } else {
                    explanation.className = 'priority-explanation disabled';
                    explanation.innerHTML = `
                        <span class="priority-icon">⚠️</span>
                        <span class="priority-text">系统将使用自动计算结果，可能覆盖手动标记</span>
                    `;
                }
            };
        }

        // 保存优先级设置
        async function savePrioritySettings() {
            try {
                const toggle = document.getElementById('manualPriorityToggle');
                const manualPriority = toggle.checked;

                const response = await fetch('/api/sell-signal-priority', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        manual_priority_over_auto: manualPriority
                    })
                });

                const result = await response.json();

                if (result.success) {
                    alert(`✅ ${result.message}\\n\\n📊 配置详情:\\n• 手动优先级: ${manualPriority ? '启用' : '禁用'}\\n• 更新时间: ${result.data.updated_time}\\n\\n🔄 设置将立即生效。`);
                    closePriorityPanel();

                    // 更新按钮显示状态
                    updatePriorityButtonStatus(manualPriority);
                } else {
                    alert('❌ 保存失败: ' + result.message);
                }
            } catch (error) {
                alert('❌ 保存失败: ' + error.message);
            }
        }

        // 更新优先级按钮状态
        function updatePriorityButtonStatus(isEnabled) {
            const btn = document.getElementById('priorityBtn');
            if (isEnabled) {
                btn.innerHTML = '🎯 优先级控制 ✅';
                btn.title = '手动标记优先级已启用';
            } else {
                btn.innerHTML = '🎯 优先级控制 ⚠️';
                btn.title = '手动标记优先级已禁用';
            }
        }

        // ==================== 自动更新控制功能 ====================

        let currentAutoUpdateStatus = {};

        // 切换自动更新状态
        async function toggleAutoUpdate() {
            try {
                const btn = document.getElementById('autoUpdateBtn');
                const originalText = btn.textContent;
                btn.textContent = '🔄 处理中...';
                btn.disabled = true;

                // 获取当前状态
                const statusResponse = await fetch('/api/auto-update');
                const statusResult = await statusResponse.json();

                if (!statusResult.success) {
                    throw new Error(statusResult.message);
                }

                const currentStatus = statusResult.data.auto_update_enabled;
                const newStatus = !currentStatus;

                // 切换状态
                const response = await fetch('/api/auto-update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        enabled: newStatus
                    })
                });

                const result = await response.json();

                if (result.success) {
                    currentAutoUpdateStatus = result.data;
                    updateAutoUpdateButtonStatus();

                    const statusText = newStatus ? '启用' : '禁用';
                    showSuccessToast(`自动更新已${statusText}`);
                } else {
                    throw new Error(result.message);
                }

            } catch (error) {
                console.error('切换自动更新状态失败:', error);
                alert(`操作失败: ${error.message}`);
            } finally {
                const btn = document.getElementById('autoUpdateBtn');
                btn.disabled = false;
                updateAutoUpdateButtonStatus();
            }
        }

        // 更新自动更新按钮状态
        function updateAutoUpdateButtonStatus() {
            const btn = document.getElementById('autoUpdateBtn');
            const isEnabled = currentAutoUpdateStatus.auto_update_enabled;
            const isRunning = currentAutoUpdateStatus.update_thread_running;

            if (isEnabled) {
                btn.className = 'management-btn auto-update';
                btn.innerHTML = '🔄 自动更新 ✅';
                btn.title = `自动更新已启用 (${currentAutoUpdateStatus.status_text})`;
            } else {
                btn.className = 'management-btn auto-update disabled';
                btn.innerHTML = '🔄 自动更新 ⏸️';
                btn.title = `自动更新已禁用 (${currentAutoUpdateStatus.status_text})`;
            }
        }

        // 初始化自动更新按钮状态
        async function initializeAutoUpdateButton() {
            try {
                const response = await fetch('/api/auto-update');
                const result = await response.json();

                if (result.success) {
                    currentAutoUpdateStatus = result.data;
                    updateAutoUpdateButtonStatus();
                }
            } catch (error) {
                console.error('初始化自动更新按钮状态失败:', error);
            }
        }

        // ==================== 策略配置功能 ====================

        let currentStrategies = {};

        // 可扩展的字段配置系统
        const STRATEGY_FIELD_CONFIG = {
            'distance_from_low_pct': {
                name: '距最低点涨幅',
                type: 'number',
                unit: '%',
                description: '股价相对于年内最低价的涨幅百分比'
            },
            'pe_ratio': {
                name: 'PE比率',
                type: 'number',
                unit: '',
                description: 'TTM市盈率，反映股票估值水平'
            },
            'pb_ratio': {
                name: 'PB比率',
                type: 'number',
                unit: '',
                description: '市净率，股价与每股净资产的比值'
            },
            'profit_margin': {
                name: '回本盈利涨幅',
                type: 'number',
                unit: '%',
                description: '相对于持仓成本的盈利涨幅百分比'
            },
            'dividend_yield': {
                name: '股息率',
                type: 'number',
                unit: '%',
                description: '年化股息收益率'
            },
            'scan_score': {
                name: '扫雷评分',
                type: 'number',
                unit: '分',
                description: '风险评估评分，分数越高风险越低'
            },
            'change_pct': {
                name: '当日涨跌幅',
                type: 'number',
                unit: '%',
                description: '当日股价涨跌幅度'
            },
            'market_value': {
                name: '持仓市值',
                type: 'number',
                unit: '元',
                description: '当前持仓的市场价值'
            }
        };

        // 获取所有可用字段
        function getAvailableFields() {
            return Object.keys(STRATEGY_FIELD_CONFIG);
        }

        // 获取字段显示名称
        function getFieldDisplayName(fieldKey) {
            const config = STRATEGY_FIELD_CONFIG[fieldKey];
            return config ? config.name : fieldKey;
        }

        // 获取字段描述
        function getFieldDescription(fieldKey) {
            const config = STRATEGY_FIELD_CONFIG[fieldKey];
            return config ? config.description : '';
        }

        // 获取操作符文本
        function getOperatorText(operator) {
            const operators = {
                '>': '大于',
                '>=': '大于等于',
                '<': '小于',
                '<=': '小于等于',
                '==': '等于'
            };
            return operators[operator] || operator;
        }

        // ==================== 减半监控配置功能 ====================

        let currentReductionConfig = {};

        // 打开减半监控配置面板
        async function openReductionMonitoringPanel() {
            try {
                const response = await fetch('/api/reduction-monitoring-config');
                const result = await response.json();

                if (result.success) {
                    currentReductionConfig = result.data;
                    renderReductionMonitoringPanel();
                    document.getElementById('reductionMonitoringPanel').style.display = 'flex';
                } else {
                    alert('加载减半监控配置失败: ' + result.message);
                }
            } catch (error) {
                alert('加载减半监控配置失败: ' + error.message);
            }
        }

        // 关闭减半监控配置面板
        function closeReductionMonitoringPanel() {
            document.getElementById('reductionMonitoringPanel').style.display = 'none';
        }

        // 渲染减半监控配置面板
        function renderReductionMonitoringPanel() {
            // 更新统计信息
            const summary = currentReductionConfig.summary || {};
            document.getElementById('totalStocksCount').textContent = summary.total_stocks || 0;
            document.getElementById('reducedStocksCount').textContent = summary.reduced_stocks || 0;
            document.getElementById('coolingDownStocksCount').textContent = summary.cooling_down_stocks || 0;
            document.getElementById('strategySwitchedStocksCount').textContent = summary.strategy_switched_stocks || 0;

            // 更新配置项
            document.getElementById('coolingPeriodDays').value = currentReductionConfig.cooling_period_days || 365;
            document.getElementById('ttmThreshold').value = currentReductionConfig.ttm_threshold || 30.0;
            document.getElementById('fundamentalsCheckInterval').value = currentReductionConfig.fundamentals_check_interval || 1;

            // 更新开关状态
            updateToggleState('enableCoolingPeriodToggle', currentReductionConfig.enable_cooling_period !== false);
            updateToggleState('autoStrategySwitchToggle', currentReductionConfig.auto_strategy_switch !== false);
        }

        // 更新开关状态
        function updateToggleState(toggleId, isActive) {
            const toggle = document.getElementById(toggleId);
            if (isActive) {
                toggle.classList.add('active');
            } else {
                toggle.classList.remove('active');
            }
        }

        // 切换冷却期开关
        function toggleCoolingPeriod() {
            const toggle = document.getElementById('enableCoolingPeriodToggle');
            toggle.classList.toggle('active');
        }

        // 切换自动策略切换开关
        function toggleAutoStrategySwitch() {
            const toggle = document.getElementById('autoStrategySwitchToggle');
            toggle.classList.toggle('active');
        }

        // 保存减半监控配置
        async function saveReductionMonitoringConfig() {
            try {
                const config = {
                    cooling_period_days: parseInt(document.getElementById('coolingPeriodDays').value),
                    ttm_threshold: parseFloat(document.getElementById('ttmThreshold').value),
                    fundamentals_check_interval: parseInt(document.getElementById('fundamentalsCheckInterval').value),
                    enable_cooling_period: document.getElementById('enableCoolingPeriodToggle').classList.contains('active'),
                    auto_strategy_switch: document.getElementById('autoStrategySwitchToggle').classList.contains('active')
                };

                // 验证配置
                if (config.cooling_period_days < 1 || config.cooling_period_days > 3650) {
                    alert('冷却期时长必须在1-3650天之间');
                    return;
                }

                if (config.ttm_threshold < 0 || config.ttm_threshold > 1000) {
                    alert('TTM阈值必须在0-1000之间');
                    return;
                }

                if (config.fundamentals_check_interval < 1 || config.fundamentals_check_interval > 30) {
                    alert('检查间隔必须在1-30天之间');
                    return;
                }

                const response = await fetch('/api/reduction-monitoring-config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(config)
                });

                const result = await response.json();

                if (result.success) {
                    alert(`✅ ${result.message}`);
                    closeReductionMonitoringPanel();

                    // 重新加载数据以反映配置变化
                    await loadStockData();
                } else {
                    alert(`❌ 保存失败: ${result.message}`);
                }
            } catch (error) {
                alert(`❌ 保存失败: ${error.message}`);
            }
        }

        // 打开策略配置面板
        async function openStrategyPanel() {
            try {
                const response = await fetch('/api/strategies');
                const result = await response.json();

                if (result.success) {
                    currentStrategies = result.data;
                    renderStrategyPanel();
                    document.getElementById('strategyPanel').style.display = 'flex';
                } else {
                    alert('加载策略配置失败: ' + result.message);
                }
            } catch (error) {
                alert('加载策略配置失败: ' + error.message);
            }
        }

        // 关闭策略配置面板
        function closeStrategyPanel() {
            document.getElementById('strategyPanel').style.display = 'none';
        }

        // 渲染策略配置面板
        function renderStrategyPanel() {
            const strategyList = document.getElementById('strategyList');
            strategyList.innerHTML = '';

            for (const [strategyId, strategy] of Object.entries(currentStrategies)) {
                const strategyItem = document.createElement('div');
                strategyItem.className = `strategy-item ${strategy.enabled ? 'enabled' : 'disabled'}`;

                let paramsHtml = '';

                // 处理自定义策略的条件显示
                if (strategy.category === 'custom' && strategy.conditions) {
                    paramsHtml += '<div class="custom-strategy-conditions">';
                    paramsHtml += '<h5 style="margin: 10px 0 5px 0; color: #333;">策略条件：</h5>';

                    strategy.conditions.forEach((condition, index) => {
                        const fieldName = getFieldDisplayName(condition.field);
                        const operatorText = getOperatorText(condition.operator);

                        paramsHtml += `
                            <div class="condition-display" style="background: #f0f8ff; padding: 8px; margin: 5px 0; border-radius: 4px; border-left: 3px solid #007bff;">
                                <span style="font-weight: 500;">${fieldName}</span>
                                <span style="color: #666; margin: 0 5px;">${operatorText}</span>
                                <span style="color: #007bff; font-weight: 500;">${condition.value}</span>
                                <button class="edit-condition-btn" onclick="editCustomCondition('${strategyId}', ${index})"
                                        style="float: right; background: #007bff; color: white; border: none; padding: 2px 6px; border-radius: 3px; font-size: 11px; cursor: pointer;"
                                        title="编辑条件">✏️</button>
                            </div>
                        `;
                    });

                    paramsHtml += `
                        <button class="add-condition-to-strategy-btn" onclick="addConditionToStrategy('${strategyId}')"
                                style="background: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 4px; font-size: 12px; margin-top: 5px; cursor: pointer;">
                            + 添加条件
                        </button>
                    `;
                    paramsHtml += '</div>';
                } else {
                    // 处理系统策略的参数显示
                    for (const [paramKey, paramValue] of Object.entries(strategy.params || {})) {
                        const paramLabel = getParamLabel(paramKey);
                        const inputType = typeof paramValue === 'boolean' ? 'checkbox' : 'number';
                        const inputValue = typeof paramValue === 'boolean' ? (paramValue ? 'checked' : '') : paramValue;

                        paramsHtml += `
                            <div class="param-group">
                                <label>${paramLabel}</label>
                                <input type="${inputType}"
                                       value="${inputType === 'checkbox' ? '' : inputValue}"
                                       ${inputType === 'checkbox' ? inputValue : ''}
                                       onchange="updateStrategyParam('${strategyId}', '${paramKey}', this.${inputType === 'checkbox' ? 'checked' : 'value'})">
                            </div>
                        `;
                    }
                }

                // 为自定义策略添加编辑和删除按钮
                const customButtonsHtml = strategy.category === 'custom' ? `
                    <button class="edit-strategy-btn" onclick="editCustomStrategy('${strategyId}')" title="编辑自定义策略"
                            style="background: #007bff; color: white; border: none; padding: 4px 8px; border-radius: 3px; margin-right: 5px; cursor: pointer;">✏️</button>
                    <button class="delete-strategy-btn" onclick="deleteCustomStrategy('${strategyId}')" title="删除自定义策略"
                            style="background: #dc3545; color: white; border: none; padding: 4px 8px; border-radius: 3px; margin-right: 5px; cursor: pointer;">🗑️</button>
                ` : '';

                strategyItem.innerHTML = `
                    <div class="strategy-title">
                        <div>
                            <h4>${strategy.name}</h4>
                            <p style="font-size: 12px; color: #666; margin: 5px 0;">${strategy.description}</p>
                        </div>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            ${customButtonsHtml}
                            <label class="strategy-toggle">
                                <input type="checkbox" ${strategy.enabled ? 'checked' : ''}
                                       onchange="toggleStrategy('${strategyId}', this.checked)">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                    <div class="strategy-params">
                        ${paramsHtml}
                    </div>
                `;

                strategyList.appendChild(strategyItem);
            }
        }

        // 获取参数标签
        function getParamLabel(paramKey) {
            const labels = {
                'sell_threshold': '卖出阈值(%)',
                'warning_threshold': '预警阈值(%)',
                'warning_offset': '预警偏移量(%)',
                'profit_threshold': '回本阈值(%)',
                'pe_max': 'PE上限',
                'pe_min': 'PE下限',
                'pb_threshold': 'PB阈值',
                'profit_threshold': '盈利阈值(%)',
                'dividend_max': '股息率上限(%)',
                'score_threshold': '扫雷评分阈值',
                'require_profit': '需要盈利',
                'gain_threshold': '涨幅阈值(%)',
                'ttm_threshold': 'TTM市盈率阈值',
                'ttm_max': 'TTM上限',
                'scan_score_max': '扫雷分数上限',
                'limit_up_threshold': '涨停阈值(%)',
                'high_ttm_threshold': '高TTM阈值',
                'distance_threshold': '距最低点涨幅阈值(%)',
                'profit_margin_threshold': '回本盈利涨幅阈值(%)',
                'dividend_yield_threshold': '股息率阈值(%)'
            };
            return labels[paramKey] || paramKey;
        }

        // 切换策略启用状态
        function toggleStrategy(strategyId, enabled) {
            if (currentStrategies[strategyId]) {
                currentStrategies[strategyId].enabled = enabled;
                renderStrategyPanel();

                // 实时预览：自动保存并刷新数据
                debounceAutoSave();
            }
        }

        // 更新策略参数
        function updateStrategyParam(strategyId, paramKey, value) {
            if (currentStrategies[strategyId] && currentStrategies[strategyId].params) {
                // 转换数据类型
                if (typeof currentStrategies[strategyId].params[paramKey] === 'number') {
                    value = parseFloat(value);
                } else if (typeof currentStrategies[strategyId].params[paramKey] === 'boolean') {
                    value = Boolean(value);
                }
                currentStrategies[strategyId].params[paramKey] = value;

                // 实时预览：自动保存并刷新数据
                debounceAutoSave();
            }
        }

        // 防抖自动保存
        let autoSaveTimeout;
        function debounceAutoSave() {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = setTimeout(async () => {
                try {
                    console.log('🔄 开始自动保存策略配置...');

                    // 1. 保存策略配置
                    const saveResponse = await fetch('/api/strategies', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(currentStrategies)
                    });
                    const saveResult = await saveResponse.json();

                    if (saveResult.success) {
                        console.log('✅ 策略配置保存成功');

                        // 2. 强制刷新卖出信号
                        const refreshResponse = await fetch('/api/refresh-signals', {
                            method: 'POST'
                        });
                        const refreshResult = await refreshResponse.json();

                        if (refreshResult.success) {
                            console.log('✅ 卖出信号刷新成功');

                            // 3. 重新加载股票数据以显示更新
                            await loadStockData();
                            console.log('✅ 策略配置已自动保存并应用');
                        } else {
                            console.error('❌ 刷新卖出信号失败:', refreshResult.message);
                        }
                    } else {
                        console.error('❌ 保存策略配置失败:', saveResult.message);
                    }
                } catch (error) {
                    console.error('❌ 自动保存失败:', error);
                }
            }, 1500); // 1.5秒后自动保存
        }

        // 应用策略预设
        async function applyPreset(presetName) {
            try {
                const response = await fetch(`/api/strategies/presets/${presetName}`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    alert(result.message);
                    // 重新加载策略配置
                    openStrategyPanel();
                } else {
                    alert('应用预设失败: ' + result.message);
                }
            } catch (error) {
                alert('应用预设失败: ' + error.message);
            }
        }

        // 保存策略配置
        async function saveStrategies() {
            try {
                const response = await fetch('/api/strategies', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(currentStrategies)
                });
                const result = await response.json();

                if (result.success) {
                    alert('策略配置已保存');
                    closeStrategyPanel();
                    // 重新加载股票数据以应用新策略
                    await loadStockData();
                } else {
                    alert('保存失败: ' + result.message);
                }
            } catch (error) {
                alert('保存失败: ' + error.message);
            }
        }

        // 重置策略配置
        function resetStrategies() {
            if (confirm('确定要重置为默认策略配置吗？')) {
                // 重新加载默认配置
                openStrategyPanel();
            }
        }

        // 显示自定义策略对话框
        function showCustomStrategyDialog() {
            document.getElementById('customStrategyDialog').style.display = 'flex';
            renderCustomConditions();
        }

        // 关闭自定义策略对话框
        function closeCustomStrategyDialog() {
            const dialog = document.getElementById('customStrategyDialog');
            dialog.style.display = 'none';

            // 清空表单
            document.getElementById('customStrategyName').value = '';
            document.getElementById('customStrategyDesc').value = '';
            document.getElementById('customConditions').innerHTML = '';

            // 重置编辑状态
            dialog.removeAttribute('data-edit-strategy-id');

            // 重置按钮
            const saveButton = dialog.querySelector('.action-btn.primary');
            saveButton.textContent = '创建策略';
            saveButton.onclick = saveCustomStrategy;
        }

        // 渲染自定义条件
        function renderCustomConditions() {
            const container = document.getElementById('customConditions');

            // 动态生成字段选项
            const fieldOptions = getAvailableFields().map(fieldKey => {
                const displayName = getFieldDisplayName(fieldKey);
                const description = getFieldDescription(fieldKey);
                return `<option value="${fieldKey}" title="${description}">${displayName}</option>`;
            }).join('');

            container.innerHTML = `
                <div class="condition-item">
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr auto; gap: 10px; align-items: center;">
                        <select class="condition-field">
                            ${fieldOptions}
                        </select>
                        <select class="condition-operator">
                            <option value=">">&gt;</option>
                            <option value=">=">&gt;=</option>
                            <option value="<">&lt;</option>
                            <option value="<=">&lt;=</option>
                            <option value="==">=</option>
                        </select>
                        <input type="number" class="condition-value" placeholder="数值" step="0.1">
                        <button type="button" onclick="removeCondition(this)" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px;">删除</button>
                    </div>
                </div>
            `;
        }

        // 添加自定义条件
        function addCustomCondition() {
            const container = document.getElementById('customConditions');
            const conditionItem = document.createElement('div');
            conditionItem.className = 'condition-item';

            // 动态生成字段选项
            const fieldOptions = getAvailableFields().map(fieldKey => {
                const displayName = getFieldDisplayName(fieldKey);
                const description = getFieldDescription(fieldKey);
                return `<option value="${fieldKey}" title="${description}">${displayName}</option>`;
            }).join('');

            conditionItem.innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr auto; gap: 10px; align-items: center;">
                    <select class="condition-field">
                        ${fieldOptions}
                    </select>
                    <select class="condition-operator">
                        <option value=">">&gt;</option>
                        <option value=">=">&gt;=</option>
                        <option value="<">&lt;</option>
                        <option value="<=">&lt;=</option>
                        <option value="==">=</option>
                    </select>
                    <input type="number" class="condition-value" placeholder="数值" step="0.1">
                    <button type="button" onclick="removeCondition(this)" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px;">删除</button>
                </div>
            `;
            container.appendChild(conditionItem);
        }

        // 删除条件
        function removeCondition(button) {
            button.closest('.condition-item').remove();
        }

        // 保存自定义策略
        async function saveCustomStrategy() {
            const name = document.getElementById('customStrategyName').value.trim();
            const description = document.getElementById('customStrategyDesc').value.trim();

            if (!name) {
                alert('请输入策略名称');
                return;
            }

            // 收集条件
            const conditions = [];
            const conditionItems = document.querySelectorAll('.condition-item');

            conditionItems.forEach(item => {
                const field = item.querySelector('.condition-field').value;
                const operator = item.querySelector('.condition-operator').value;
                const value = parseFloat(item.querySelector('.condition-value').value);

                if (!isNaN(value)) {
                    conditions.push({ field, operator, value });
                }
            });

            if (conditions.length === 0) {
                alert('请至少添加一个有效条件');
                return;
            }

            try {
                const response = await fetch('/api/strategies/custom', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: name,
                        description: description,
                        config: {
                            conditions: conditions,
                            weight: 1.0
                        }
                    })
                });

                const result = await response.json();

                if (result.success) {
                    alert(result.message);
                    closeCustomStrategyDialog();
                    // 重新加载策略配置
                    openStrategyPanel();
                } else {
                    alert('创建失败: ' + result.message);
                }
            } catch (error) {
                alert('创建失败: ' + error.message);
            }
        }

        // 保存自定义策略编辑
        async function saveCustomStrategyEdit(strategyId) {
            const name = document.getElementById('customStrategyName').value.trim();
            const description = document.getElementById('customStrategyDesc').value.trim();

            if (!name) {
                alert('请输入策略名称');
                return;
            }

            // 收集条件
            const conditions = [];
            const conditionItems = document.querySelectorAll('.condition-item');

            conditionItems.forEach(item => {
                const field = item.querySelector('.condition-field').value;
                const operator = item.querySelector('.condition-operator').value;
                const value = parseFloat(item.querySelector('.condition-value').value);

                if (!isNaN(value)) {
                    conditions.push({ field, operator, value });
                }
            });

            if (conditions.length === 0) {
                alert('请至少添加一个有效条件');
                return;
            }

            try {
                // 更新现有策略
                const updatedStrategy = {
                    ...currentStrategies[strategyId],
                    name: name,
                    description: description,
                    conditions: conditions
                };

                const response = await fetch('/api/strategies', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        [strategyId]: updatedStrategy
                    })
                });

                const result = await response.json();

                if (result.success) {
                    alert('策略修改成功');
                    closeCustomStrategyDialog();
                    // 重新加载策略配置
                    openStrategyPanel();
                } else {
                    alert('修改失败: ' + result.message);
                }
            } catch (error) {
                alert('修改失败: ' + error.message);
            }
        }

        // 添加带数据的自定义条件
        function addCustomConditionWithData(conditionData) {
            const container = document.getElementById('customConditions');
            const conditionItem = document.createElement('div');
            conditionItem.className = 'condition-item';

            // 动态生成字段选项
            const fieldOptions = getAvailableFields().map(fieldKey => {
                const displayName = getFieldDisplayName(fieldKey);
                const selected = fieldKey === conditionData.field ? 'selected' : '';
                return `<option value="${fieldKey}" ${selected}>${displayName}</option>`;
            }).join('');

            conditionItem.innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr auto; gap: 10px; align-items: center;">
                    <select class="condition-field">
                        ${fieldOptions}
                    </select>
                    <select class="condition-operator">
                        <option value=">" ${conditionData.operator === '>' ? 'selected' : ''}>&gt;</option>
                        <option value=">=" ${conditionData.operator === '>=' ? 'selected' : ''}>&gt;=</option>
                        <option value="<" ${conditionData.operator === '<' ? 'selected' : ''}>&lt;</option>
                        <option value="<=" ${conditionData.operator === '<=' ? 'selected' : ''}>&lt;=</option>
                        <option value="==" ${conditionData.operator === '==' ? 'selected' : ''}>=</option>
                    </select>
                    <input type="number" class="condition-value" value="${conditionData.value}" step="0.1">
                    <button type="button" onclick="removeCondition(this)" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px;">删除</button>
                </div>
            `;
            container.appendChild(conditionItem);
        }

        // 编辑自定义策略
        function editCustomStrategy(strategyId) {
            const strategy = currentStrategies[strategyId];
            if (!strategy || strategy.category !== 'custom') {
                alert('只能编辑自定义策略');
                return;
            }

            // 填充编辑表单
            document.getElementById('customStrategyName').value = strategy.name;
            document.getElementById('customStrategyDesc').value = strategy.description || '';

            // 清空条件容器
            const container = document.getElementById('customConditions');
            container.innerHTML = '';

            // 添加现有条件
            if (strategy.conditions && strategy.conditions.length > 0) {
                strategy.conditions.forEach(condition => {
                    addCustomConditionWithData(condition);
                });
            } else {
                // 如果没有条件，添加一个空条件
                addCustomCondition();
            }

            // 显示对话框，并标记为编辑模式
            document.getElementById('customStrategyDialog').style.display = 'flex';
            document.getElementById('customStrategyDialog').setAttribute('data-edit-strategy-id', strategyId);

            // 更改按钮文本
            const saveButton = document.querySelector('#customStrategyDialog .action-btn.primary');
            saveButton.textContent = '保存修改';
            saveButton.onclick = () => saveCustomStrategyEdit(strategyId);
        }

        // 编辑自定义策略条件
        function editCustomCondition(strategyId, conditionIndex) {
            const strategy = currentStrategies[strategyId];
            if (!strategy || !strategy.conditions || !strategy.conditions[conditionIndex]) {
                alert('条件不存在');
                return;
            }

            const condition = strategy.conditions[conditionIndex];

            // 创建编辑对话框
            const editDialog = document.createElement('div');
            editDialog.className = 'condition-edit-dialog';
            editDialog.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); display: flex; align-items: center; justify-content: center; z-index: 10000;
            `;

            const fieldOptions = getAvailableFields().map(fieldKey => {
                const displayName = getFieldDisplayName(fieldKey);
                const selected = fieldKey === condition.field ? 'selected' : '';
                return `<option value="${fieldKey}" ${selected}>${displayName}</option>`;
            }).join('');

            editDialog.innerHTML = `
                <div style="background: white; padding: 20px; border-radius: 8px; min-width: 400px;">
                    <h4>编辑条件</h4>
                    <div style="margin: 15px 0;">
                        <label>字段：</label>
                        <select id="editConditionField" style="width: 100%; padding: 5px; margin-top: 5px;">
                            ${fieldOptions}
                        </select>
                    </div>
                    <div style="margin: 15px 0;">
                        <label>操作符：</label>
                        <select id="editConditionOperator" style="width: 100%; padding: 5px; margin-top: 5px;">
                            <option value=">" ${condition.operator === '>' ? 'selected' : ''}>大于 (&gt;)</option>
                            <option value=">=" ${condition.operator === '>=' ? 'selected' : ''}>大于等于 (&gt;=)</option>
                            <option value="<" ${condition.operator === '<' ? 'selected' : ''}>小于 (&lt;)</option>
                            <option value="<=" ${condition.operator === '<=' ? 'selected' : ''}>小于等于 (&lt;=)</option>
                            <option value="==" ${condition.operator === '==' ? 'selected' : ''}>等于 (=)</option>
                        </select>
                    </div>
                    <div style="margin: 15px 0;">
                        <label>数值：</label>
                        <input type="number" id="editConditionValue" value="${condition.value}" step="0.1"
                               style="width: 100%; padding: 5px; margin-top: 5px;">
                    </div>
                    <div style="text-align: right; margin-top: 20px;">
                        <button onclick="this.closest('.condition-edit-dialog').remove()"
                                style="background: #6c757d; color: white; border: none; padding: 8px 15px; border-radius: 4px; margin-right: 10px; cursor: pointer;">取消</button>
                        <button onclick="saveConditionEdit('${strategyId}', ${conditionIndex}, this)"
                                style="background: #007bff; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;">保存</button>
                    </div>
                </div>
            `;

            document.body.appendChild(editDialog);
        }

        // 保存条件编辑
        async function saveConditionEdit(strategyId, conditionIndex, button) {
            const dialog = button.closest('.condition-edit-dialog');
            const field = dialog.querySelector('#editConditionField').value;
            const operator = dialog.querySelector('#editConditionOperator').value;
            const value = parseFloat(dialog.querySelector('#editConditionValue').value);

            if (isNaN(value)) {
                alert('请输入有效的数值');
                return;
            }

            // 更新策略条件
            currentStrategies[strategyId].conditions[conditionIndex] = { field, operator, value };

            try {
                // 保存到服务器
                const response = await fetch('/api/strategies', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ [strategyId]: currentStrategies[strategyId] })
                });

                const result = await response.json();
                if (result.success) {
                    // 关闭对话框并刷新显示
                    dialog.remove();
                    renderStrategyPanel();
                    showSuccessToast('条件已更新');
                } else {
                    alert('保存失败: ' + result.message);
                }
            } catch (error) {
                alert('保存失败: ' + error.message);
            }
        }

        // 为策略添加条件
        function addConditionToStrategy(strategyId) {
            const strategy = currentStrategies[strategyId];
            if (!strategy || strategy.category !== 'custom') {
                alert('只能为自定义策略添加条件');
                return;
            }

            // 创建添加条件对话框
            const addDialog = document.createElement('div');
            addDialog.className = 'condition-add-dialog';
            addDialog.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); display: flex; align-items: center; justify-content: center; z-index: 10000;
            `;

            const fieldOptions = getAvailableFields().map(fieldKey => {
                const displayName = getFieldDisplayName(fieldKey);
                return `<option value="${fieldKey}">${displayName}</option>`;
            }).join('');

            addDialog.innerHTML = `
                <div style="background: white; padding: 20px; border-radius: 8px; min-width: 400px;">
                    <h4>添加新条件</h4>
                    <div style="margin: 15px 0;">
                        <label>字段：</label>
                        <select id="addConditionField" style="width: 100%; padding: 5px; margin-top: 5px;">
                            ${fieldOptions}
                        </select>
                    </div>
                    <div style="margin: 15px 0;">
                        <label>操作符：</label>
                        <select id="addConditionOperator" style="width: 100%; padding: 5px; margin-top: 5px;">
                            <option value=">">大于 (&gt;)</option>
                            <option value=">=">大于等于 (&gt;=)</option>
                            <option value="<">小于 (&lt;)</option>
                            <option value="<=">小于等于 (&lt;=)</option>
                            <option value="==">等于 (=)</option>
                        </select>
                    </div>
                    <div style="margin: 15px 0;">
                        <label>数值：</label>
                        <input type="number" id="addConditionValue" step="0.1" placeholder="输入数值"
                               style="width: 100%; padding: 5px; margin-top: 5px;">
                    </div>
                    <div style="text-align: right; margin-top: 20px;">
                        <button onclick="this.closest('.condition-add-dialog').remove()"
                                style="background: #6c757d; color: white; border: none; padding: 8px 15px; border-radius: 4px; margin-right: 10px; cursor: pointer;">取消</button>
                        <button onclick="saveNewCondition('${strategyId}', this)"
                                style="background: #28a745; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;">添加</button>
                    </div>
                </div>
            `;

            document.body.appendChild(addDialog);
        }

        // 保存新条件
        async function saveNewCondition(strategyId, button) {
            const dialog = button.closest('.condition-add-dialog');
            const field = dialog.querySelector('#addConditionField').value;
            const operator = dialog.querySelector('#addConditionOperator').value;
            const value = parseFloat(dialog.querySelector('#addConditionValue').value);

            if (isNaN(value)) {
                alert('请输入有效的数值');
                return;
            }

            // 添加新条件
            if (!currentStrategies[strategyId].conditions) {
                currentStrategies[strategyId].conditions = [];
            }
            currentStrategies[strategyId].conditions.push({ field, operator, value });

            try {
                // 保存到服务器
                const response = await fetch('/api/strategies', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ [strategyId]: currentStrategies[strategyId] })
                });

                const result = await response.json();
                if (result.success) {
                    // 关闭对话框并刷新显示
                    dialog.remove();
                    renderStrategyPanel();
                    showSuccessToast('条件已添加');
                } else {
                    alert('保存失败: ' + result.message);
                }
            } catch (error) {
                alert('保存失败: ' + error.message);
            }
        }

        // 删除自定义策略
        async function deleteCustomStrategy(strategyId) {
            try {
                // 获取策略名称用于确认
                const strategy = currentStrategies[strategyId];
                if (!strategy) {
                    alert('策略不存在');
                    return;
                }

                const strategyName = strategy.name;

                // 确认删除
                if (!confirm(`确定要删除自定义策略 "${strategyName}" 吗？\\n\\n此操作不可撤销！`)) {
                    return;
                }

                const response = await fetch(`/api/strategies/custom/${strategyId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    alert(result.message);
                    // 重新加载策略配置
                    openStrategyPanel();
                } else {
                    alert('删除失败: ' + result.message);
                }
            } catch (error) {
                alert('删除失败: ' + error.message);
            }
        }

        // 测试策略配置
        async function testStrategies() {
            try {
                console.log('🧪 开始测试策略配置...');

                // 1. 先保存当前配置
                const saveResponse = await fetch('/api/strategies', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(currentStrategies)
                });
                const saveResult = await saveResponse.json();

                if (saveResult.success) {
                    console.log('✅ 策略配置保存成功');

                    // 2. 强制刷新卖出信号
                    const refreshResponse = await fetch('/api/refresh-signals', {
                        method: 'POST'
                    });
                    const refreshResult = await refreshResponse.json();

                    if (refreshResult.success) {
                        console.log('✅ 卖出信号刷新成功');

                        // 3. 重新加载股票数据
                        await loadStockData();

                        // 4. 统计策略效果
                        let clearanceCount = 0;
                        let sellCount = 0;
                        let warningCount = 0;
                        let holdCount = 0;

                        stockData.forEach(stock => {
                            if (stock.sell_signal === 'clearance') clearanceCount++;
                            else if (stock.sell_signal === 'sell') sellCount++;
                            else if (stock.sell_signal === 'warning') warningCount++;
                            else holdCount++;
                        });

                        alert(`🧪 策略测试结果:\\n\\n📊 信号统计:\\n• 🔥 清仓信号: ${clearanceCount}只\\n• 🚨 卖出信号: ${sellCount}只\\n• ⚠️ 预警信号: ${warningCount}只\\n• ✅ 持有信号: ${holdCount}只\\n\\n✅ 策略配置已应用到表格中，请查看卖出信号列的变化！`);
                    } else {
                        alert('刷新信号失败: ' + refreshResult.message);
                    }
                } else {
                    alert('保存配置失败: ' + saveResult.message);
                }
            } catch (error) {
                console.error('❌ 测试失败:', error);
                alert('测试失败: ' + error.message);
            }
        }

        // 初始化优先级按钮状态
        async function initializePriorityButton() {
            try {
                const response = await fetch('/api/sell-signal-priority');
                const result = await response.json();

                if (result.success) {
                    const isEnabled = result.data.manual_priority_over_auto;
                    updatePriorityButtonStatus(isEnabled);
                }
            } catch (error) {
                console.error('初始化优先级按钮状态失败:', error);
            }
        }

        // 移动端固定表头和固定列初始化 - 增强版本
        function initializeMobileTableFeatures() {
            // 使用精确的移动端检测
            if (!isMobileDevice()) {
                console.log('🖥️ 桌面端设备，保持原有表格显示方式');
                return;
            }

            console.log('📱 检测到移动端设备，启用固定表头和固定列功能');

            // 确保表格容器有正确的样式
            const tableContainer = document.querySelector('.table-container');
            if (tableContainer) {
                tableContainer.style.position = 'relative';
                tableContainer.style.overflow = 'auto';
                tableContainer.style.webkitOverflowScrolling = 'touch';
                // 添加移动端特有的滚动优化
                tableContainer.style.overscrollBehavior = 'contain';
            }

            // 初始化移动端固定表头功能
            initMobileStickyHeader();

            // 确保固定列在数据更新后仍然有效
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.target.id === 'stockTableBody') {
                        // 表格数据更新后，确保固定列样式正确应用
                        setTimeout(() => {
                            ensureStickyColumnsOnMobile();
                            // 重新初始化固定表头
                            initMobileStickyHeader();
                        }, 100);
                    }
                });
            });

            const tableBody = document.getElementById('stockTableBody');
            if (tableBody) {
                observer.observe(tableBody, { childList: true, subtree: true });
            }
        }

        // 检测是否为移动端设备（更精确的检测）
        function isMobileDevice() {
            const userAgent = navigator.userAgent.toLowerCase();
            const mobileKeywords = ['android', 'webos', 'iphone', 'ipad', 'ipod', 'blackberry', 'iemobile', 'opera mini'];
            const isMobileUserAgent = mobileKeywords.some(keyword => userAgent.includes(keyword));
            const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
            const isSmallScreen = window.innerWidth <= 768;

            return isMobileUserAgent || (isTouchDevice && isSmallScreen);
        }

        // 初始化移动端固定表头功能
        function initMobileStickyHeader() {
            // 只在移动端设备上应用固定表头功能
            if (!isMobileDevice()) {
                console.log('🖥️ 桌面端设备，跳过移动端固定表头功能');
                return;
            }

            const table = document.getElementById('stockTable');
            const tableContainer = document.querySelector('.table-container');

            if (!table || !tableContainer) return;

            // 确保表头有正确的sticky样式
            const headers = table.querySelectorAll('thead th');
            headers.forEach(header => {
                header.style.position = 'sticky';
                header.style.top = '0';
                header.style.zIndex = '20';
                header.style.backgroundColor = '#f8f9fa';
                header.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
                header.style.backdropFilter = 'blur(5px)';
                header.style.webkitBackdropFilter = 'blur(5px)';
                header.style.whiteSpace = 'nowrap';
                // 防止表头在滚动时闪烁
                header.style.webkitTransform = 'translateZ(0)';
                header.style.transform = 'translateZ(0)';
            });

            // 为固定列的表头设置更高的z-index
            const stickyLeftHeaders = table.querySelectorAll('thead th.sticky-left-1, thead th.sticky-left-2');
            stickyLeftHeaders.forEach(header => {
                header.style.zIndex = '25';
                header.style.webkitTransform = 'translateZ(0)';
                header.style.transform = 'translateZ(0)';
            });

            // 确保表格容器有正确的滚动设置
            tableContainer.style.overscrollBehavior = 'contain';
            tableContainer.style.webkitTransform = 'translateZ(0)';
            tableContainer.style.transform = 'translateZ(0)';

            console.log('📱 移动端固定表头已初始化');

            // 验证固定表头功能
            setTimeout(() => {
                validateMobileStickyHeader();
            }, 500);
        }

        // 验证移动端固定表头功能
        function validateMobileStickyHeader() {
            if (!isMobileDevice()) return;

            const table = document.getElementById('stockTable');
            if (!table) return;

            const headers = table.querySelectorAll('thead th');
            let validHeaders = 0;

            headers.forEach(header => {
                const computedStyle = window.getComputedStyle(header);
                if (computedStyle.position === 'sticky' && computedStyle.top === '0px') {
                    validHeaders++;
                }
            });

            if (validHeaders === headers.length) {
                console.log('✅ 移动端固定表头功能验证成功');
            } else {
                console.warn(`⚠️ 移动端固定表头功能验证失败: ${validHeaders}/${headers.length} 个表头正确设置`);
            }
        }

        // 确保移动端固定列样式正确应用
        function ensureStickyColumnsOnMobile() {
            // 只在移动端设备上应用固定列功能
            if (!isMobileDevice()) return;

            // 确保所有第一列和第二列的td都有正确的class
            const tableBody = document.getElementById('stockTableBody');
            if (tableBody) {
                const rows = tableBody.querySelectorAll('tr');
                rows.forEach(row => {
                    const cells = row.querySelectorAll('td');
                    if (cells.length > 0) {
                        // 第一列（代码）
                        if (!cells[0].classList.contains('sticky-left-1')) {
                            cells[0].classList.add('sticky-left-1');
                        }
                        // 第二列（名称）
                        if (cells.length > 1 && !cells[1].classList.contains('sticky-left-2')) {
                            cells[1].classList.add('sticky-left-2');
                        }
                    }
                });
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化排序配置
            initializeDefaultSort();

            loadStockData();
            loadTradingStatus();
            initializePriorityButton();
            initializeAutoUpdateButton();

            // 初始化移动端表格功能
            initializeMobileTableFeatures();

            // 设置默认排序指示器
            updateSortIndicators();

            // 设置默认筛选按钮状态（全部股票）
            filterBySellSignal('all');

            // 监听窗口大小变化，重新初始化移动端功能
            let resizeTimeout;
            window.addEventListener('resize', function() {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(() => {
                    console.log('📱 窗口大小变化，重新初始化移动端功能');
                    initializeMobileTableFeatures();
                }, 250);
            });

            // 自动保存当前状态到备份文件（延迟执行，确保数据加载完成）
            setTimeout(autoSaveStockStates, 5000);

            // 设置定时刷新
            setInterval(loadStockData, 30000); // 30秒刷新一次
            setInterval(loadTradingStatus, 60000); // 1分钟刷新一次交易状态

            // 定期自动保存状态（每10分钟）
            setInterval(autoSaveStockStates, 600000);

            // 搜索功能
            document.getElementById('searchInput').addEventListener('input', function() {
                filterAndDisplayData();
            });

            // 添加多列排序提示
            addSortHelpText();

            // 监听窗口大小变化，重新初始化移动端功能
            window.addEventListener('resize', Utils.debounce(function() {
                initializeMobileTableFeatures();
                ensureStickyColumnsOnMobile();
            }, 300));
        });

        // 编辑持仓数量（保留原有的弹窗方式）
        async function editHoldings(stockCode, currentHoldings) {
            const newHoldings = prompt(`请输入新的持仓数量:`, currentHoldings);

            if (newHoldings === null) return; // 用户取消

            const holdings = parseFloat(newHoldings);
            if (isNaN(holdings)) {
                alert('请输入有效的数字');
                return;
            }

            try {
                const response = await fetch('/api/update-holdings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        stock_code: stockCode,
                        holdings: holdings
                    })
                });

                const result = await response.json();

                if (result.success) {
                    const data = result.data;
                    alert(`✅ ${result.message}\\n\\n📊 持仓变化:\\n• 股票: ${data.stock_name} (${data.stock_code})\\n• 原持仓: ${data.old_holdings}\\n• 新持仓: ${data.new_holdings}\\n• 新市值: ¥${formatNumber(data.new_market_value, 2)}`);

                    // 重新加载数据
                    await loadStockData();
                } else {
                    alert(`❌ 更新失败: ${result.message}`);
                }
            } catch (error) {
                alert(`❌ 更新失败: ${error.message}`);
            }
        }

        // 内联编辑持仓数量
        function editHoldingsInline(stockCode, cellElement) {
            // 防止重复编辑
            if (cellElement.querySelector('.holdings-input')) {
                return;
            }

            const valueSpan = cellElement.querySelector('.holdings-value');
            const currentValue = valueSpan.textContent.replace(/,/g, ''); // 移除千分位分隔符

            // 创建输入框
            const input = document.createElement('input');
            input.type = 'text';
            input.className = 'holdings-input';
            input.value = currentValue;
            input.setAttribute('data-original-value', currentValue);
            input.setAttribute('data-stock-code', stockCode);

            // 替换显示元素
            valueSpan.style.display = 'none';
            cellElement.appendChild(input);

            // 选中输入框内容
            input.focus();
            input.select();

            // 绑定事件
            input.addEventListener('keydown', handleHoldingsKeydown);
            input.addEventListener('blur', handleHoldingsBlur);
            input.addEventListener('input', validateHoldingsInput);
        }

        // 处理键盘事件
        function handleHoldingsKeydown(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                saveHoldingsInline(event.target);
            } else if (event.key === 'Escape') {
                event.preventDefault();
                cancelHoldingsEdit(event.target);
            }
        }

        // 处理失去焦点事件
        function handleHoldingsBlur(event) {
            // 延迟执行，避免与其他事件冲突
            setTimeout(() => {
                if (document.contains(event.target)) {
                    saveHoldingsInline(event.target);
                }
            }, 100);
        }

        // 验证输入
        function validateHoldingsInput(event) {
            const input = event.target;
            const value = input.value;

            // 允许负号、数字、小数点
            const validPattern = /^-?\\d*\\.?\\d*$/;

            if (!validPattern.test(value)) {
                // 如果输入无效，恢复到上一个有效值
                const lastValidValue = input.getAttribute('data-last-valid') || input.getAttribute('data-original-value');
                input.value = lastValidValue;
            } else {
                input.setAttribute('data-last-valid', value);
            }
        }

        // 保存内联编辑
        async function saveHoldingsInline(input) {
            const stockCode = input.getAttribute('data-stock-code');
            const originalValue = input.getAttribute('data-original-value');
            const newValue = input.value.trim();

            // 如果值没有改变，直接取消编辑
            if (newValue === originalValue) {
                cancelHoldingsEdit(input);
                return;
            }

            // 验证输入
            const holdings = parseFloat(newValue);
            if (isNaN(holdings)) {
                showHoldingsError(input, '请输入有效的数字');
                return;
            }

            // 显示保存状态
            input.className = 'holdings-input holdings-saving';
            input.disabled = true;

            try {
                const response = await fetch('/api/update-holdings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        stock_code: stockCode,
                        holdings: holdings
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // 显示成功状态
                    input.className = 'holdings-input holdings-success';

                    // 更新本地数据
                    const data = result.data;
                    updateLocalStockData(data.stock_code, {
                        holdings: data.new_holdings,
                        market_value: data.new_market_value
                    });

                    // 延迟恢复显示
                    setTimeout(() => {
                        restoreHoldingsDisplay(input, formatNumber(holdings, 0));

                        // 实时更新市值显示
                        updateMarketValueDisplay(data.stock_code, data.new_market_value);

                        // 显示成功提示，包含市值信息
                        showSuccessToast(`持仓已更新为 ${formatNumber(holdings, 0)}，市值：${data.formatted_market_value}`);
                    }, 600);

                } else {
                    showHoldingsError(input, result.message);
                }
            } catch (error) {
                showHoldingsError(input, '网络错误，请重试');
            }
        }

        // 取消编辑
        function cancelHoldingsEdit(input) {
            const originalValue = input.getAttribute('data-original-value');
            restoreHoldingsDisplay(input, originalValue);
        }

        // 恢复显示
        function restoreHoldingsDisplay(input, displayValue) {
            const cellElement = input.parentElement;
            const valueSpan = cellElement.querySelector('.holdings-value');

            valueSpan.textContent = displayValue;
            valueSpan.style.display = 'inline-block';
            cellElement.removeChild(input);
        }

        // 显示错误状态
        function showHoldingsError(input, message) {
            input.className = 'holdings-input holdings-error';
            input.disabled = false;
            input.focus();
            input.select();

            // 显示错误提示
            showErrorToast(message);

            // 恢复正常状态
            setTimeout(() => {
                input.className = 'holdings-input';
            }, 600);
        }

        // 显示成功提示
        function showSuccessToast(message) {
            showToast(message, 'success');
        }

        // 显示错误提示
        function showErrorToast(message) {
            showToast(message, 'error');
        }

        // 更新本地股票数据
        function updateLocalStockData(stockCode, updates) {
            const stock = stockData.find(s => s.code === stockCode);
            if (stock) {
                Object.assign(stock, updates);
            }
        }

        // 实时更新市值显示
        function updateMarketValueDisplay(stockCode, newMarketValue) {
            // 找到对应的表格行
            const rows = document.querySelectorAll('#stockTableBody tr');
            for (const row of rows) {
                const codeCell = row.querySelector('td:first-child');
                if (codeCell && codeCell.textContent.trim() === stockCode) {
                    // 找到市值列（倒数第3列，因为最后是操作列，倒数第2列是更新时间）
                    const cells = row.querySelectorAll('td');
                    const marketValueCell = cells[cells.length - 3]; // 市值列

                    if (marketValueCell) {
                        // 更新市值显示
                        marketValueCell.textContent = formatCurrency(newMarketValue);

                        // 添加更新动画效果
                        marketValueCell.style.transition = 'all 0.3s ease';
                        marketValueCell.style.backgroundColor = '#e8f5e8';
                        marketValueCell.style.transform = 'scale(1.05)';

                        // 恢复正常状态
                        setTimeout(() => {
                            marketValueCell.style.backgroundColor = '';
                            marketValueCell.style.transform = '';
                        }, 1000);
                    }
                    break;
                }
            }

            // 同时更新统计信息
            updateTotalStatistics();
        }

        // 更新总统计信息
        function updateTotalStatistics() {
            const totalMarketValue = stockData.reduce((sum, stock) => sum + (stock.market_value || 0), 0);
            const totalValueFormatted = formatCurrency(totalMarketValue);

            // 更新页面上的总市值显示
            const totalValueElements = document.querySelectorAll('#totalValue, #totalMarketValue');
            totalValueElements.forEach(element => {
                if (element) {
                    element.textContent = totalValueFormatted;
                }
            });
        }

        // 通用提示函数
        function showToast(message, type = 'info') {
            // 移除已存在的提示
            const existingToast = document.querySelector('.toast');
            if (existingToast) {
                existingToast.remove();
            }

            // 创建提示元素
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.textContent = message;

            // 添加样式
            Object.assign(toast.style, {
                position: 'fixed',
                top: '20px',
                right: '20px',
                padding: '12px 20px',
                borderRadius: '6px',
                color: 'white',
                fontSize: '14px',
                zIndex: '10000',
                opacity: '0',
                transform: 'translateY(-20px)',
                transition: 'all 0.3s ease'
            });

            // 根据类型设置颜色
            if (type === 'success') {
                toast.style.backgroundColor = '#4caf50';
            } else if (type === 'error') {
                toast.style.backgroundColor = '#f44336';
            } else {
                toast.style.backgroundColor = '#2196f3';
            }

            // 添加到页面
            document.body.appendChild(toast);

            // 显示动画
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateY(0)';
            }, 10);

            // 自动隐藏
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateY(-20px)';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 标记已减半
        async function markReduced(stockCode) {
            if (!confirm('确定要标记这只股票为已减半吗？\\n\\n标记后将停止所有企业微信提醒。')) {
                return;
            }

            try {
                const response = await fetch('/api/mark-reduced', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        stock_code: stockCode
                    })
                });

                const result = await response.json();

                if (result.success) {
                    const data = result.data;
                    alert(`✅ ${result.message}\\n\\n📊 操作详情:\\n• 股票: ${data.stock_name} (${data.stock_code})\\n• 操作时间: ${data.reduced_time}\\n\\n📱 该股票的所有企业微信提醒已停止。`);

                    // 重新加载数据
                    await loadStockData();

                    // 自动保存状态
                    setTimeout(autoSaveStockStates, 1000);
                } else {
                    alert(`❌ 操作失败: ${result.message}`);
                }
            } catch (error) {
                alert(`❌ 操作失败: ${error.message}`);
            }
        }

        // 获取自定义状态配置
        function getCustomStatusOptions() {
            // 从localStorage获取用户自定义状态，如果没有则使用默认值
            const defaultOptions = [
                { type: 'reduced', emoji: '✅', text: '已减半', category: 'custom' },
                { type: 'cleared', emoji: '🔥', text: '已清仓', category: 'custom' },
                { type: 'custom1', emoji: '📈', text: '已加仓', category: 'custom' },
                { type: 'custom2', emoji: '⏳', text: '观察中', category: 'custom' }
            ];

            const saved = localStorage.getItem('customStatusOptions');
            return saved ? JSON.parse(saved) : defaultOptions;
        }

        // 保存自定义状态配置
        function saveCustomStatusOptions(options) {
            localStorage.setItem('customStatusOptions', JSON.stringify(options));
        }

        // 显示状态选择菜单
        function showStatusMenu(stockCode, stockName) {
            // 移除已存在的菜单
            const existingMenu = document.querySelector('.status-menu');
            if (existingMenu) {
                existingMenu.remove();
            }

            // 创建菜单
            const menu = document.createElement('div');
            menu.className = 'status-menu';

            // 获取当前股票信息，检查是否有自定义状态
            const currentStock = appState.stockData.find(stock => stock.code === stockCode);
            const hasCustomStatus = currentStock && (
                currentStock.custom_status ||
                currentStock.is_reduced ||
                currentStock.is_cleared
            );

            // 原始系统状态选项
            const originalOptions = [
                { type: 'clearance', emoji: '🔥', text: '清仓', category: 'original' },
                { type: 'sell', emoji: '🚨', text: '卖出', category: 'original' },
                { type: 'warning', emoji: '⚠️', text: '预警', category: 'original' },
                { type: 'hold', emoji: '✅', text: '持有', category: 'original' }
            ];

            // 获取自定义状态选项
            const customOptions = getCustomStatusOptions();

            // 菜单内容
            let menuHTML = `
                <div class="status-menu-header">
                    选择状态
                    <button class="menu-manage-btn" onclick="showStatusManager()">管理</button>
                </div>
            `;

            // 如果有自定义状态，添加取消选项
            if (hasCustomStatus) {
                menuHTML += '<div style="padding: 4px 15px; font-size: 11px; color: #666; font-weight: 600;">快速操作</div>';
                menuHTML += `
                    <button class="status-menu-item cancel-custom"
                            onclick="cancelCustomSellSignal('${stockCode}', '${stockName}')"
                            style="background: linear-gradient(135deg, #ff6b6b, #ee5a52); color: white; border: none;">
                        <span class="status-emoji">🔄</span>
                        <span class="status-text">取消自定义信号</span>
                        <small class="status-hint" style="color: #ffe0e0;">恢复系统信号</small>
                    </button>
                `;
            }

            // 添加原始状态选项
            menuHTML += '<div style="padding: 4px 15px; font-size: 11px; color: #666; font-weight: 600; border-top: 1px solid #eee; margin-top: 4px; padding-top: 8px;">系统状态</div>';
            originalOptions.forEach(option => {
                menuHTML += `
                    <button class="status-menu-item original"
                            onclick="restoreOriginalStatus('${stockCode}', '${option.type}')">
                        <span class="status-emoji">${option.emoji}</span>
                        <span class="status-text">${option.text}</span>
                        <small class="status-hint">恢复提醒</small>
                    </button>
                `;
            });

            // 添加自定义状态选项
            if (customOptions.length > 0) {
                menuHTML += '<div style="padding: 4px 15px; font-size: 11px; color: #666; font-weight: 600; border-top: 1px solid #eee; margin-top: 4px; padding-top: 8px;">自定义状态</div>';
                customOptions.forEach(option => {
                    menuHTML += `
                        <button class="status-menu-item custom"
                                onclick="setCustomStatus('${stockCode}', '${option.type}', '${option.text}', '${option.emoji}')">
                            <span class="status-emoji">${option.emoji}</span>
                            <span class="status-text">${option.text}</span>
                            <small class="status-hint">停止提醒</small>
                        </button>
                    `;
                });
            }

            menuHTML += `
                <button class="status-menu-item cancel" onclick="closeStatusMenu()">
                    <span class="status-emoji">❌</span>
                    <span class="status-text">取消</span>
                </button>
            `;

            menu.innerHTML = menuHTML;

            // 添加到页面
            document.body.appendChild(menu);

            // 定位菜单（跟随鼠标）
            const rect = event.target.getBoundingClientRect();
            menu.style.left = rect.left + 'px';
            menu.style.top = (rect.bottom + 5) + 'px';

            // 确保菜单不超出屏幕
            const menuRect = menu.getBoundingClientRect();
            if (menuRect.right > window.innerWidth) {
                menu.style.left = (window.innerWidth - menuRect.width - 10) + 'px';
            }
            if (menuRect.bottom > window.innerHeight) {
                menu.style.top = (rect.top - menuRect.height - 5) + 'px';
            }

            // 点击其他地方关闭菜单
            setTimeout(() => {
                document.addEventListener('click', function closeMenuHandler(e) {
                    if (!menu.contains(e.target)) {
                        menu.remove();
                        document.removeEventListener('click', closeMenuHandler);
                    }
                });
            }, 100);
        }

        // 关闭状态菜单
        function closeStatusMenu() {
            const menu = document.querySelector('.status-menu');
            if (menu) {
                menu.remove();
            }
        }

        // 恢复原始状态
        async function restoreOriginalStatus(stockCode, statusType) {
            closeStatusMenu();

            const statusMap = {
                'clearance': { text: '清仓', emoji: '🔥' },
                'sell': { text: '卖出', emoji: '🚨' },
                'warning': { text: '预警', emoji: '⚠️' },
                'hold': { text: '持有', emoji: '✅' }
            };

            const status = statusMap[statusType];
            if (!status) return;

            if (!confirm(`确定要将这只股票恢复为"${status.emoji}${status.text}"状态吗？\\n\\n📱 恢复后将重新启用企业微信提醒功能。`)) {
                return;
            }

            try {
                const response = await fetch('/api/restore-original-status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        stock_code: stockCode,
                        status_type: statusType
                    })
                });

                const result = await response.json();

                if (result.success) {
                    const data = result.data;
                    alert(`${status.emoji} ${result.message}\\n\\n📊 状态详情:\\n• 股票: ${data.stock_name} (${data.stock_code})\\n• 状态: ${status.emoji}${status.text}\\n• 恢复时间: ${data.restore_time}\\n\\n📱 企业微信提醒已重新启用。`);

                    // 重新加载数据
                    await loadStockData();
                } else {
                    alert(`❌ 操作失败: ${result.message}`);
                }
            } catch (error) {
                alert(`❌ 操作失败: ${error.message}`);
            }
        }

        // 显示状态管理器
        function showStatusManager() {
            closeStatusMenu();

            // 创建管理器对话框
            const dialog = document.createElement('div');
            dialog.className = 'status-manager-dialog';
            dialog.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 10001;
                display: flex;
                align-items: center;
                justify-content: center;
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: white;
                border-radius: 8px;
                padding: 20px;
                max-width: 500px;
                width: 90%;
                max-height: 80%;
                overflow-y: auto;
            `;

            const customOptions = getCustomStatusOptions();

            let html = `
                <h3 style="margin: 0 0 15px 0; color: #333;">状态管理</h3>
                <div style="margin-bottom: 15px;">
                    <h4 style="margin: 0 0 10px 0; color: #666; font-size: 14px;">自定义状态列表</h4>
                    <div id="status-list">
            `;

            customOptions.forEach((option, index) => {
                html += `
                    <div style="display: flex; align-items: center; padding: 8px; border: 1px solid #eee; border-radius: 4px; margin-bottom: 5px;">
                        <span style="font-size: 16px; margin-right: 8px;">${option.emoji}</span>
                        <span style="flex: 1;">${option.text}</span>
                        <button onclick="editStatusOption(${index})" style="margin-right: 5px; padding: 2px 8px; border: 1px solid #ddd; border-radius: 3px; background: white; cursor: pointer;">编辑</button>
                        <button onclick="deleteStatusOption(${index})" style="padding: 2px 8px; border: 1px solid #dc3545; border-radius: 3px; background: #dc3545; color: white; cursor: pointer;">删除</button>
                    </div>
                `;
            });

            html += `
                    </div>
                    <button onclick="addNewStatusOption()" style="width: 100%; padding: 8px; border: 1px dashed #007bff; border-radius: 4px; background: #f8f9fa; color: #007bff; cursor: pointer; margin-top: 10px;">+ 添加新状态</button>
                </div>
                <div style="text-align: right;">
                    <button onclick="closeStatusManager()" style="padding: 8px 16px; border: 1px solid #ddd; border-radius: 4px; background: white; cursor: pointer; margin-right: 10px;">取消</button>
                    <button onclick="saveStatusManager()" style="padding: 8px 16px; border: none; border-radius: 4px; background: #007bff; color: white; cursor: pointer;">保存</button>
                </div>
            `;

            content.innerHTML = html;
            dialog.appendChild(content);
            document.body.appendChild(dialog);

            // 点击背景关闭
            dialog.addEventListener('click', (e) => {
                if (e.target === dialog) {
                    closeStatusManager();
                }
            });
        }

        // 关闭状态管理器
        function closeStatusManager() {
            const dialog = document.querySelector('.status-manager-dialog');
            if (dialog) {
                dialog.remove();
            }
        }

        // 添加新状态选项
        function addNewStatusOption() {
            const emoji = prompt('请输入表情符号（如：🎯）：');
            if (!emoji) return;

            const text = prompt('请输入状态文字（如：目标价）：');
            if (!text) return;

            const customOptions = getCustomStatusOptions();
            const newOption = {
                type: 'custom' + Date.now(),
                emoji: emoji.trim(),
                text: text.trim(),
                category: 'custom'
            };

            customOptions.push(newOption);
            saveCustomStatusOptions(customOptions);

            // 刷新管理器界面
            showStatusManager();
        }

        // 编辑状态选项
        function editStatusOption(index) {
            const customOptions = getCustomStatusOptions();
            const option = customOptions[index];

            const newEmoji = prompt('请输入表情符号：', option.emoji);
            if (newEmoji === null) return;

            const newText = prompt('请输入状态文字：', option.text);
            if (newText === null) return;

            customOptions[index].emoji = newEmoji.trim();
            customOptions[index].text = newText.trim();

            saveCustomStatusOptions(customOptions);

            // 刷新管理器界面
            showStatusManager();
        }

        // 删除状态选项
        function deleteStatusOption(index) {
            const customOptions = getCustomStatusOptions();
            const option = customOptions[index];

            if (!confirm(`确定要删除状态"${option.emoji}${option.text}"吗？`)) {
                return;
            }

            customOptions.splice(index, 1);
            saveCustomStatusOptions(customOptions);

            // 刷新管理器界面
            showStatusManager();
        }

        // 保存状态管理器
        function saveStatusManager() {
            closeStatusManager();
            showSuccessToast('状态配置已保存');
        }

        // 设置自定义状态
        async function setCustomStatus(stockCode, statusType, statusText, statusEmoji) {
            // 关闭菜单
            closeStatusMenu();

            if (!confirm(`确定要将这只股票标记为"${statusEmoji}${statusText}"吗？\\n\\n📱 标记后将停止该股票的企业微信提醒。`)) {
                return;
            }

            try {
                const response = await fetch('/api/set-custom-status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        stock_code: stockCode,
                        status_type: statusType,
                        status_text: statusText,
                        status_emoji: statusEmoji
                    })
                });

                const result = await response.json();

                if (result.success) {
                    const data = result.data;
                    alert(`${statusEmoji} ${result.message}\\n\\n📊 状态详情:\\n• 股票: ${data.stock_name} (${data.stock_code})\\n• 状态: ${data.status_emoji}${data.status_text}\\n• 设置时间: ${data.status_time}\\n\\n📱 该股票的企业微信提醒已停止。`);

                    // 重新加载数据
                    await loadStockData();
                } else {
                    alert(`❌ 操作失败: ${result.message}`);
                }
            } catch (error) {
                alert(`❌ 操作失败: ${error.message}`);
            }
        }

        // 取消自定义卖出信号
        async function cancelCustomSellSignal(stockCode, stockName) {
            // 关闭菜单
            closeStatusMenu();

            if (!confirm(`确定要取消 "${stockName}" 的自定义卖出信号吗？\\n\\n🔄 取消后将恢复系统自动生成的卖出信号，并重新启用企业微信提醒。`)) {
                return;
            }

            try {
                const response = await fetch('/api/cancel-custom-sell-signal', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        stock_code: stockCode
                    })
                });

                const result = await response.json();

                if (result.success) {
                    const data = result.data;
                    alert(`🔄 ${result.message}\\n\\n📊 操作详情:\\n• 股票: ${data.stock_name} (${data.stock_code})\\n• 取消的状态: ${data.canceled_statuses}\\n• 新的卖出信号: ${data.new_sell_reason}\\n• 操作时间: ${data.cancel_time}\\n\\n📱 企业微信提醒已重新启用。`);

                    // 重新加载数据
                    await loadStockData();
                } else {
                    alert(`❌ 操作失败: ${result.message}`);
                }
            } catch (error) {
                alert(`❌ 操作失败: ${error.message}`);
            }
        }

        // 批量取消自定义卖出信号
        async function batchCancelCustomSignals() {
            // 获取所有有自定义状态的股票
            const customStocks = appState.stockData.filter(stock =>
                stock.custom_status || stock.is_reduced || stock.is_cleared
            );

            if (customStocks.length === 0) {
                alert('📋 当前没有设置自定义卖出信号的股票。');
                return;
            }

            const stockList = customStocks.map(stock =>
                `• ${stock.name} (${stock.code}) - ${stock.custom_status_text || (stock.is_reduced ? '已减半' : '') || (stock.is_cleared ? '已清仓' : '')}`
            ).join('\\n');

            if (!confirm(`确定要批量取消以下 ${customStocks.length} 只股票的自定义卖出信号吗？\\n\\n${stockList}\\n\\n🔄 取消后将恢复系统自动生成的卖出信号，并重新启用企业微信提醒。`)) {
                return;
            }

            try {
                const stockCodes = customStocks.map(stock => stock.code);

                const response = await fetch('/api/batch-cancel-custom-signals', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        stock_codes: stockCodes
                    })
                });

                const result = await response.json();

                if (result.success) {
                    const data = result.data;
                    let message = `🔄 ${result.message}\\n\\n📊 操作详情:\\n• 总处理数量: ${data.total_processed}\\n• 成功数量: ${data.success_count}\\n• 失败数量: ${data.failed_count}\\n• 操作时间: ${data.process_time}`;

                    if (data.success_count > 0) {
                        message += '\\n\\n✅ 成功取消的股票:';
                        data.success_stocks.slice(0, 5).forEach(stock => {
                            message += `\\n• ${stock.stock_name} - ${stock.canceled_statuses}`;
                        });
                        if (data.success_stocks.length > 5) {
                            message += `\\n• ... 还有 ${data.success_stocks.length - 5} 只股票`;
                        }
                    }

                    if (data.failed_count > 0) {
                        message += '\\n\\n❌ 失败的股票:';
                        data.failed_stocks.slice(0, 3).forEach(stock => {
                            message += `\\n• ${stock.stock_name || stock.stock_code} - ${stock.reason}`;
                        });
                        if (data.failed_stocks.length > 3) {
                            message += `\\n• ... 还有 ${data.failed_stocks.length - 3} 只股票`;
                        }
                    }

                    alert(message);

                    // 重新加载数据
                    await loadStockData();
                } else {
                    alert(`❌ 批量操作失败: ${result.message}`);
                }
            } catch (error) {
                alert(`❌ 批量操作失败: ${error.message}`);
            }
        }

        // 显示策略操作菜单
        function showActionMenu(stockCode, stockName, signalType) {
            const stock = stockData.find(s => s.code === stockCode);
            if (!stock) return;

            let menuContent = '';
            let title = '';

            if (signalType === 'clearance') {
                title = '🔥 立即清仓策略';
                menuContent = `
                    <div class="action-menu-item" onclick="markAction('${stockCode}', 'cleared')">
                        🔥 标记已清仓
                    </div>
                    <div class="action-menu-item" onclick="showStatusMenu('${stockCode}', '${stockName}')">
                        📝 自定义状态
                    </div>
                `;
            } else if (signalType === 'reduce') {
                title = '📉 立即减半策略';
                menuContent = `
                    <div class="action-menu-item" onclick="markAction('${stockCode}', 'reduced')">
                        📉 标记已减半
                    </div>
                    <div class="action-menu-item" onclick="showStatusMenu('${stockCode}', '${stockName}')">
                        📝 自定义状态
                    </div>
                `;
            } else if (signalType === 'prepare_clearance') {
                title = '🔥 准备清仓策略';
                menuContent = `
                    <div class="action-menu-item" onclick="markAction('${stockCode}', 'cleared')">
                        🔥 标记已清仓
                    </div>
                    <div class="action-menu-item" onclick="markAction('${stockCode}', 'reduced')">
                        📉 标记已减半
                    </div>
                    <div class="action-menu-item" onclick="showStatusMenu('${stockCode}', '${stockName}')">
                        📝 自定义状态
                    </div>
                `;
            } else if (signalType === 'prepare_reduce') {
                title = '📉 准备减半策略';
                menuContent = `
                    <div class="action-menu-item" onclick="markAction('${stockCode}', 'reduced')">
                        📉 标记已减半
                    </div>
                    <div class="action-menu-item" onclick="showStatusMenu('${stockCode}', '${stockName}')">
                        📝 自定义状态
                    </div>
                `;
            } else {
                title = '📊 操作选择';
                menuContent = `
                    <div class="action-menu-item" onclick="markAction('${stockCode}', 'reduced')">
                        📉 标记已减半
                    </div>
                    <div class="action-menu-item" onclick="markAction('${stockCode}', 'cleared')">
                        🔥 标记已清仓
                    </div>
                    <div class="action-menu-item" onclick="showStatusMenu('${stockCode}', '${stockName}')">
                        📝 自定义状态
                    </div>
                `;
            }

            // 添加价格信息
            const targetPrice = calculateStrategyPrice(stock);
            const currentPrice = parseFloat(stock.price);
            let priceInfo = `当前价格: ${formatNumber(currentPrice, 2)}元`;

            if (targetPrice && targetPrice !== currentPrice) {
                priceInfo += `<br>目标价格: ${formatNumber(targetPrice, 2)}元`;
                const priceDiff = ((targetPrice - currentPrice) / currentPrice * 100);
                priceInfo += ` (${priceDiff > 0 ? '+' : ''}${formatNumber(priceDiff, 1)}%)`;
            }

            if (signalType === 'warning') {
                priceInfo += `<br>建议挂单: ${formatNumber(currentPrice * 1.02, 2)}元 (+2%)`;
            }

            const menuHtml = `
                <div class="action-menu-overlay" onclick="closeActionMenu()">
                    <div class="action-menu" onclick="event.stopPropagation()">
                        <div class="action-menu-header">
                            <h3>${title}</h3>
                            <div class="stock-info">
                                <strong>${stockName} (${stockCode})</strong>
                                <div class="price-info">${priceInfo}</div>
                            </div>
                        </div>
                        <div class="action-menu-content">
                            ${menuContent}
                        </div>
                        <div class="action-menu-footer">
                            <button onclick="closeActionMenu()">取消</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', menuHtml);
        }

        // 关闭操作菜单
        function closeActionMenu() {
            const overlay = document.querySelector('.action-menu-overlay');
            if (overlay) {
                overlay.remove();
            }
        }

        // 标记操作（已减半或已清仓）
        async function markAction(stockCode, actionType) {
            // 关闭菜单
            closeActionMenu();

            const actionText = actionType === 'reduced' ? '已减半' : '已清仓';
            const actionEmoji = actionType === 'reduced' ? '📉' : '🔥';

            if (!confirm(`确定要标记这只股票为${actionText}吗？\\n\\n${actionEmoji} 标记后将停止所有企业微信提醒。`)) {
                return;
            }

            try {
                const response = await fetch('/api/mark-action', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        stock_code: stockCode,
                        action_type: actionType
                    })
                });

                const result = await response.json();

                if (result.success) {
                    const data = result.data;
                    alert(`${actionEmoji} ${result.message}\\n\\n📊 操作详情:\\n• 股票: ${data.stock_name} (${data.stock_code})\\n• 操作: ${data.action_text}\\n• 操作时间: ${data.action_time}\\n\\n📱 该股票的所有企业微信提醒已停止。`);

                    // 重新加载数据
                    await loadStockData();

                    // 自动保存状态
                    setTimeout(autoSaveStockStates, 1000);
                } else {
                    alert(`❌ 操作失败: ${result.message}`);
                }
            } catch (error) {
                alert(`❌ 操作失败: ${error.message}`);
            }
        }

        // 更新单只股票数据
        async function updateSingleStock(stockCode) {
            const button = document.querySelector(`button[onclick="updateSingleStock('${stockCode}')"]`);
            if (!button) return;

            // 禁用按钮并显示更新状态
            button.disabled = true;
            button.classList.add('updating');
            button.textContent = '⏳';

            try {
                const response = await fetch(`/api/update-single-stock/${stockCode}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    // 显示更新成功信息
                    const data = result.data;
                    const changeText = data.price_change >= 0 ?
                        `+${data.price_change.toFixed(2)}` :
                        `${data.price_change.toFixed(2)}`;
                    const changePctText = data.price_change_pct >= 0 ?
                        `+${data.price_change_pct.toFixed(1)}%` :
                        `${data.price_change_pct.toFixed(1)}%`;

                    alert(`✅ ${result.message}\\n\\n📊 价格变化:\\n• 原价格: ${data.old_price.toFixed(2)}元\\n• 新价格: ${data.new_price.toFixed(2)}元\\n• 变化: ${changeText}元 (${changePctText})\\n• 更新时间: ${data.update_time}`);

                    // 重新加载数据以更新表格
                    await loadStockData();
                } else {
                    alert(`❌ 更新失败: ${result.message}`);
                }
            } catch (error) {
                console.error('更新单只股票失败:', error);
                alert(`❌ 更新失败: ${error.message}`);
            } finally {
                // 恢复按钮状态
                button.disabled = false;
                button.classList.remove('updating');
                button.textContent = '🔄';
            }
        }

        // ==================== API调用管理 ====================

        class APIManager {
            constructor() {
                this.baseURL = '';
                this.defaultHeaders = {
                    'Content-Type': 'application/json'
                };
            }

            // 通用API调用方法
            async request(url, options = {}) {
                const config = {
                    headers: this.defaultHeaders,
                    ...options
                };

                try {
                    const response = await fetch(url, config);
                    const result = await response.json();

                    if (!result.success) {
                        throw new Error(result.message || '请求失败');
                    }

                    return result;
                } catch (error) {
                    console.error(`API请求失败 ${url}:`, error);
                    throw error;
                }
            }

            // GET请求
            async get(url) {
                return this.request(url, { method: 'GET' });
            }

            // POST请求
            async post(url, data = null) {
                const options = { method: 'POST' };
                if (data) {
                    options.body = JSON.stringify(data);
                }
                return this.request(url, options);
            }
        }

        // 创建API管理器实例
        const apiManager = new APIManager();

        // ==================== 数据加载优化 ====================

        class DataLoader {
            constructor() {
                this.isLoading = false;
                this.loadingIndicators = new Set();
            }

            // 显示加载状态
            showLoading(indicator = 'default') {
                this.isLoading = true;
                this.loadingIndicators.add(indicator);
                // 可以在这里添加加载动画
            }

            // 隐藏加载状态
            hideLoading(indicator = 'default') {
                this.loadingIndicators.delete(indicator);
                if (this.loadingIndicators.size === 0) {
                    this.isLoading = false;
                }
                // 可以在这里隐藏加载动画
            }

            // 加载股票数据
            async loadStockData() {
                if (this.isLoading) return;

                this.showLoading('stockData');
                try {
                    const result = await apiManager.get('/api/stocks');

                    // 更新应用状态
                    appState.stockData = result.data || [];
                    appState.lastUpdateTime = result.last_update;

                    // 更新全局变量（向后兼容）
                    stockData = appState.stockData;

                    // 更新UI
                    this.updateHeader(result);
                    this.updateStatistics(result.stats);
                    filterAndDisplayData();
                    updateSortIndicators();

                    return result;
                } catch (error) {
                    Utils.showErrorToast('加载股票数据失败: ' + error.message);
                    throw error;
                } finally {
                    this.hideLoading('stockData');
                }
            }

            // 加载交易状态
            async loadTradingStatus() {
                try {
                    const result = await apiManager.get('/api/trading-status');
                    const statusElement = document.getElementById('tradingStatus');
                    if (statusElement && result.data) {
                        statusElement.textContent = result.data.message;
                    }
                    return result;
                } catch (error) {
                    console.error('获取交易状态失败:', error);
                }
            }

            // 更新页面头部信息
            updateHeader(result) {
                const updateTimeElement = document.getElementById('updateTime');
                const stockCountElement = document.getElementById('stockCount');

                if (updateTimeElement) {
                    updateTimeElement.textContent =
                        result.last_update ? `最后更新: ${result.last_update}` : '暂未更新';
                }

                if (stockCountElement) {
                    stockCountElement.textContent = result.count || 0;
                }
            }

            // 更新统计信息
            updateStatistics(stats) {
                if (!stats) return;

                const elements = {
                    totalValue: document.getElementById('totalValue'),
                    totalMarketValue: document.getElementById('totalMarketValue'),
                    stockCountStat: document.getElementById('stockCountStat'),
                    stockCountDisplay: document.getElementById('stockCountDisplay')
                };

                const totalValue = formatCurrency(stats.total_market_value);

                if (elements.totalValue) elements.totalValue.textContent = totalValue;
                if (elements.totalMarketValue) elements.totalMarketValue.textContent = totalValue;
                if (elements.stockCountStat) elements.stockCountStat.textContent = stockData.length;
                if (elements.stockCountDisplay) elements.stockCountDisplay.textContent = stats.stock_count + '只';
            }
        }

        // 创建数据加载器实例
        const dataLoader = new DataLoader();

        // 向后兼容的函数
        async function loadStockData() {
            return dataLoader.loadStockData();
        }

        async function loadTradingStatus() {
            return dataLoader.loadTradingStatus();
        }

        function updateHeader(result) {
            dataLoader.updateHeader(result);
        }

        function updateStatistics(stats) {
            dataLoader.updateStatistics(stats);
        }

        // 筛选和显示数据
        function filterAndDisplayData() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();

            // 应用筛选条件
            filteredData = stockData.filter(stock => {
                // 搜索筛选
                if (searchTerm &&
                    !stock.code.toLowerCase().includes(searchTerm) &&
                    !stock.name.toLowerCase().includes(searchTerm)) {
                    return false;
                }

                // 卖出信号筛选
                if (currentSellFilter !== 'all') {
                    if (stock.sell_signal !== currentSellFilter) {
                        return false;
                    }
                }

                return true;
            });

            // 应用多列排序
            if (sortColumns.length > 0) {
                filteredData.sort((a, b) => {
                    // 遍历所有排序列，按优先级比较
                    for (const sortConfig of sortColumns) {
                        const result = compareValues(a, b, sortConfig.column, sortConfig.direction);
                        if (result !== 0) {
                            return result;
                        }
                    }
                    return 0;
                });
            }

            displayStockTable();
        }

        // 卖出信号筛选
        function filterBySellSignal(signal) {
            currentSellFilter = signal;

            // 更新按钮样式
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.style.opacity = '0.6';
                btn.style.transform = 'none';
            });

            // 处理特殊的按钮ID映射
            let btnId = '';
            switch(signal) {
                case 'all': btnId = 'filterAll'; break;
                case 'clearance': btnId = 'filterClearance'; break;
                case 'reduce': btnId = 'filterReduce'; break;
                case 'prepare_clearance': btnId = 'filterPrepareClearance'; break;
                case 'prepare_reduce': btnId = 'filterPrepareReduce'; break;
                case 'sell': btnId = 'filterSell'; break;
                case 'hold': btnId = 'filterHold'; break;
                default: btnId = `filter${signal.charAt(0).toUpperCase() + signal.slice(1)}`;
            }

            const activeBtn = document.getElementById(btnId);
            if (activeBtn) {
                activeBtn.style.opacity = '1';
                activeBtn.style.transform = 'translateY(-2px)';
            }

            // 重新筛选和显示数据
            filterAndDisplayData();

            // 显示筛选结果统计
            const clearanceCount = stockData.filter(s => s.sell_signal === 'clearance').length;
            const reduceCount = stockData.filter(s => s.sell_signal === 'reduce').length;
            const prepareClearanceCount = stockData.filter(s => s.sell_signal === 'prepare_clearance').length;
            const prepareReduceCount = stockData.filter(s => s.sell_signal === 'prepare_reduce').length;
            const sellCount = stockData.filter(s => s.sell_signal === 'sell').length;
            const holdCount = stockData.filter(s => s.sell_signal === 'hold').length;

            console.log(`卖出信号统计: 立即清仓${clearanceCount}只, 立即减半${reduceCount}只, 准备清仓${prepareClearanceCount}只, 准备减半${prepareReduceCount}只, 卖出${sellCount}只, 持有${holdCount}只`);
        }

        // 显示股票表格
        function displayStockTable() {
            const tbody = document.getElementById('stockTableBody');

            if (filteredData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="18" style="text-align: center; padding: 40px;">
                            <div style="color: #666;">
                                <div style="font-size: 48px; margin-bottom: 20px;">📊</div>
                                <div style="font-size: 18px; margin-bottom: 10px;">暂无匹配的股票数据</div>
                                <div style="font-size: 14px;">请调整筛选条件或导入持仓表格</div>
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = filteredData.map(stock => `
                <tr>
                    <td class="sticky-left-1">${stock.code}</td>
                    <td class="sticky-left-2">${stock.name}</td>
                    <td>${formatNumber(stock.price, 2)}</td>
                    <td class="${stock.change_pct >= 0 ? 'positive' : 'negative'}">
                        ${formatPercent(stock.change_pct)}
                    </td>
                    <td class="mobile-hidden">${formatNumber(stock.yearly_low, 2)}</td>
                    <td class="mobile-hidden">${stock.low_date || '-'}</td>
                    <td class="mobile-hidden ${stock.distance_from_low_pct >= 0 ? 'positive' : 'negative'}">
                        ${stock.distance_from_low_pct ? formatPercent(stock.distance_from_low_pct) : '-'}
                    </td>
                    <td class="mobile-hidden">${formatNumber(stock.pb_ratio, 2)}</td>
                    <td class="mobile-hidden">${formatNumber(stock.pe_ratio, 2)}</td>
                    <td class="mobile-hidden">${formatPercent(stock.dividend_yield)}</td>
                    <td class="extra-mobile-hidden">${stock.industry || '-'}</td>
                    <td class="signal-cell">
                        <div class="signal-container">
                            ${stock.custom_status ?
                                `<span class="sell-signal custom-status"
                                      style="background-color: #6c757d; cursor: pointer;"
                                      onclick="showStatusMenu('${stock.code}', '${stock.name}')">
                                    ${stock.custom_status_emoji}${stock.custom_status_text}
                                </span>
                                ${getCustomStatusInfo(stock)}
                                ${getReductionMonitoringInfo(stock)}` :
                                (stock.sell_signal === 'clearance' || stock.sell_signal === 'reduce' || stock.sell_signal === 'sell' || stock.sell_signal === 'warning') ?
                                    `<span class="sell-signal clickable ${stock.sell_signal || 'hold'}"
                                          style="background-color: ${stock.sell_color || '#2ed573'}; cursor: pointer;"
                                          onclick="showStatusMenu('${stock.code}', '${stock.name}')">
                                        ${getSellSignalDisplay(stock.sell_signal)}
                                    </span>
                                    ${getStrategyDisplayInfo(stock)}
                                    ${getReductionMonitoringInfo(stock)}` :
                                    `<span class="sell-signal ${stock.sell_signal || 'hold'}"
                                          style="background-color: ${stock.sell_color || '#2ed573'}; cursor: pointer;"
                                          onclick="showStatusMenu('${stock.code}', '${stock.name}')">
                                        ✅持有
                                    </span>
                                    ${getReductionMonitoringInfo(stock)}`
                            }
                        </div>
                    </td>
                    <td>
                        <span class="scan-score" style="background-color: ${stock.scan_risk_color || '#cccccc'}">
                            ${stock.scan_display_text || '-'}
                        </span>
                    </td>
                    <td class="holdings-cell" onclick="editHoldingsInline('${stock.code}', this)" title="点击编辑持仓数量">
                        <span class="holdings-value">${formatNumber(stock.holdings || stock.quantity || 0, 0)}</span>
                    </td>
                    <td class="mobile-hidden">
                        ${stock.profit_margin !== null && stock.profit_margin !== undefined ?
                            stock.profit_status === 'need_gain' ?
                                `<span class="negative">还需+${formatPercent(stock.profit_margin)}</span>` :
                            stock.profit_status === 'profit' ?
                                `<span class="positive">已盈利+${formatPercent(stock.profit_margin)}</span>` :
                                `<span>${formatPercent(stock.profit_margin)}</span>`
                            : '-'
                        }
                        ${stock.unit_cost !== 0 ? `<br><small>(成本:${formatNumber(stock.unit_cost, 3)})</small>` : ''}
                    </td>
                    <td>${formatCurrency(stock.market_value)}</td>
                    <td class="mobile-hidden">${stock.update_time || '-'}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="update-btn" onclick="updateSingleStock('${stock.code}')" title="更新${stock.name}数据">
                                🔄
                            </button>
                            <button class="edit-btn" onclick="editHoldings('${stock.code}', ${stock.holdings || 0})" title="修改持仓数量">
                                ✏️
                            </button>
                            ${stock.custom_status ?
                                `<span class="custom-status-mark"
                                      title="${stock.custom_status_emoji}${stock.custom_status_text} (${stock.custom_status_time})"
                                      style="background: #6c757d; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                                    ${stock.custom_status_emoji}${stock.custom_status_text}
                                </span>` :
                                (stock.sell_signal === 'clearance' || stock.sell_signal === 'reduce' || stock.sell_signal === 'sell' || stock.sell_signal === 'warning') && !stock.is_reduced && !stock.is_cleared ?
                                    `<button class="action-btn" onclick="showActionMenu('${stock.code}', '${stock.name}', '${stock.sell_signal}')" title="执行策略操作">
                                        ${stock.sell_signal === 'clearance' ? '🔥' : stock.sell_signal === 'reduce' ? '📉' : '✅'}
                                    </button>` : ''
                            }
                            ${!stock.custom_status && stock.is_reduced ?
                                `<span class="reduced-mark" title="已减半 ${stock.reduced_time || ''}">
                                    ✅已减半
                                </span>` : ''
                            }
                            ${!stock.custom_status && stock.is_cleared ?
                                `<span class="reduced-mark" title="已清仓 ${stock.cleared_time || ''}" style="background: #dc3545;">
                                    🔥已清仓
                                </span>` : ''
                            }
                        </div>
                    </td>
                </tr>
            `).join('');

            // 更新批量取消按钮状态
            updateBatchCancelButtonStatus();

            // 确保移动端固定列样式正确应用
            setTimeout(ensureStickyColumnsOnMobile, 50);
        }

        // 更新批量取消按钮状态
        function updateBatchCancelButtonStatus() {
            const batchCancelBtn = document.getElementById('batchCancelBtn');
            if (!batchCancelBtn) return;

            // 检查是否有自定义状态的股票
            const customStocks = stockData.filter(stock =>
                stock.custom_status || stock.is_reduced || stock.is_cleared
            );

            if (customStocks.length > 0) {
                batchCancelBtn.style.display = 'inline-block';
                batchCancelBtn.innerHTML = `🔄 批量取消自定义信号 (${customStocks.length})`;
                batchCancelBtn.title = `当前有 ${customStocks.length} 只股票设置了自定义卖出信号`;
            } else {
                batchCancelBtn.style.display = 'none';
            }
        }

        // 更新排序指示器
        // 更新排序指示器
        function updateSortIndicators() {
            // 清除所有指示器
            document.querySelectorAll('.sort-indicator').forEach(indicator => {
                indicator.className = 'sort-indicator';
                indicator.textContent = '';
            });

            // 为每个排序列设置指示器
            sortColumns.forEach((sortConfig, index) => {
                const indicator = document.querySelector(`th[onclick*="'${sortConfig.column}'"] .sort-indicator`);
                if (indicator) {
                    const direction = sortConfig.direction === 'asc' ? '↑' : '↓';
                    const priority = sortColumns.length > 1 ? (index + 1) : '';

                    indicator.className = `sort-indicator ${sortConfig.direction}`;
                    indicator.textContent = direction + priority;
                }
            });
        }

        // 显示排序加载状态
        function showSortingLoader() {
            const tbody = document.getElementById('stockTableBody');
            if (tbody) {
                tbody.style.opacity = '0.6';
                tbody.style.pointerEvents = 'none';
            }
        }

        // 隐藏排序加载状态
        function hideSortingLoader() {
            const tbody = document.getElementById('stockTableBody');
            if (tbody) {
                tbody.style.opacity = '1';
                tbody.style.pointerEvents = 'auto';
            }
        }

        // 移动端多列排序反馈
        function showMobileMultiSortFeedback(column) {
            const columnNames = {
                'code': '代码',
                'name': '名称',
                'price': '最新价',
                'change_pct': '涨跌幅',
                'sell_priority': '卖出信号',
                'market_value': '市值',
                'holdings': '持仓'
            };

            const columnName = columnNames[column] || column;
            showSuccessToast(`已添加"${columnName}"到多列排序 (${sortColumns.length}列)`);
        }

        // 重置移动端多列排序超时
        function resetMobileMultiSortTimeout() {
            if (mobileMultiSortTimeout) {
                clearTimeout(mobileMultiSortTimeout);
            }

            mobileMultiSortTimeout = setTimeout(() => {
                mobileMultiSortMode = false;
                hideMobileMultiSortButton();
                showSuccessToast('多列排序模式已退出');
            }, 10000); // 10秒后自动退出多列排序模式
        }

        // 启用移动端多列排序模式
        function enableMobileMultiSort() {
            mobileMultiSortMode = true;
            showMobileMultiSortButton();
            showSuccessToast('多列排序模式已启用，点击列头添加排序');
            resetMobileMultiSortTimeout();
        }

        // 退出移动端多列排序模式
        function exitMobileMultiSort() {
            mobileMultiSortMode = false;
            hideMobileMultiSortButton();
            if (mobileMultiSortTimeout) {
                clearTimeout(mobileMultiSortTimeout);
                mobileMultiSortTimeout = null;
            }
            showSuccessToast('多列排序模式已退出');
        }

        // 显示移动端多列排序按钮
        function showMobileMultiSortButton() {
            let button = document.getElementById('mobileMultiSortButton');
            if (!button) {
                button = document.createElement('button');
                button.id = 'mobileMultiSortButton';
                button.className = 'mobile-multi-sort-btn';
                button.innerHTML = '🔄 退出多列排序';
                button.onclick = exitMobileMultiSort;

                const tableContainer = document.querySelector('.table-container');
                if (tableContainer) {
                    tableContainer.insertBefore(button, tableContainer.firstChild);
                }
            }
            button.style.display = 'block';
        }

        // 隐藏移动端多列排序按钮
        function hideMobileMultiSortButton() {
            const button = document.getElementById('mobileMultiSortButton');
            if (button) {
                button.style.display = 'none';
            }
        }

        // 添加排序帮助文本
        function addSortHelpText() {
            const tableContainer = document.querySelector('.table-container');
            if (tableContainer && !document.querySelector('.sort-help')) {
                const helpText = document.createElement('div');
                helpText.className = 'sort-help';

                if (isMobileDevice()) {
                    helpText.innerHTML = `
                        💡 移动端排序：点击列头单列排序
                        <button onclick="enableMobileMultiSort()" class="mobile-multi-sort-enable-btn">启用多列排序</button>
                    `;
                } else {
                    helpText.innerHTML = '💡 提示：点击列头排序，按住Ctrl+点击可多列排序';
                }

                tableContainer.insertBefore(helpText, tableContainer.firstChild);
            }
        }

        // 检测是否为移动设备
        function isMobileDevice() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                   ('ontouchstart' in window) ||
                   (navigator.maxTouchPoints > 0);
        }

        // 移动端多列排序状态
        let mobileMultiSortMode = false;
        let mobileMultiSortTimeout = null;

        // 表格排序
        function sortTable(column, event) {
            // 显示加载状态
            showSortingLoader();

            // 移动端多列排序处理
            if (isMobileDevice()) {
                handleMobileSorting(column, event);
            } else {
                handleDesktopSorting(column, event);
            }

            // 保存排序偏好
            saveSortPreferences();

            // 更新排序指示器
            updateSortIndicators();

            // 应用排序并显示
            setTimeout(() => {
                filterAndDisplayData();
                hideSortingLoader();
            }, 50);
        }

        // 桌面端排序处理
        function handleDesktopSorting(column, event) {
            // 检查是否按住Ctrl键进行多列排序
            const isMultiSort = event && (event.ctrlKey || event.metaKey);

            if (isMultiSort) {
                // 多列排序模式
                const existingIndex = sortColumns.findIndex(s => s.column === column);

                if (existingIndex >= 0) {
                    // 如果列已存在，切换排序方向
                    sortColumns[existingIndex].direction =
                        sortColumns[existingIndex].direction === 'asc' ? 'desc' : 'asc';
                } else {
                    // 添加新的排序列
                    sortColumns.push({ column: column, direction: 'asc' });
                }
            } else {
                // 单列排序模式
                const existingSort = sortColumns.find(s => s.column === column);

                if (existingSort) {
                    // 如果是当前唯一的排序列，切换方向
                    if (sortColumns.length === 1) {
                        existingSort.direction = existingSort.direction === 'asc' ? 'desc' : 'asc';
                    } else {
                        // 如果有多列排序，重置为单列排序
                        sortColumns = [{ column: column, direction: 'asc' }];
                    }
                } else {
                    // 新的单列排序
                    sortColumns = [{ column: column, direction: 'asc' }];
                }
            }
        }

        // 移动端排序处理
        function handleMobileSorting(column, event) {
            // 防止事件冒泡
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }

            // 检查是否在多列排序模式
            if (mobileMultiSortMode) {
                // 多列排序模式
                const existingIndex = sortColumns.findIndex(s => s.column === column);

                if (existingIndex >= 0) {
                    // 如果列已存在，切换排序方向
                    sortColumns[existingIndex].direction =
                        sortColumns[existingIndex].direction === 'asc' ? 'desc' : 'asc';
                } else {
                    // 添加新的排序列
                    sortColumns.push({ column: column, direction: 'asc' });
                }

                // 显示提示
                showMobileMultiSortFeedback(column);

                // 重置多列排序模式超时
                resetMobileMultiSortTimeout();
            } else {
                // 单列排序模式
                const existingSort = sortColumns.find(s => s.column === column);

                if (existingSort) {
                    // 如果是当前唯一的排序列，切换方向
                    if (sortColumns.length === 1) {
                        existingSort.direction = existingSort.direction === 'asc' ? 'desc' : 'asc';
                    } else {
                        // 如果有多列排序，重置为单列排序
                        sortColumns = [{ column: column, direction: 'asc' }];
                    }
                } else {
                    // 新的单列排序
                    sortColumns = [{ column: column, direction: 'asc' }];
                }
            }
        }

        // 数据管理功能
        async function clearData() {
            if (!confirm('确定要清空所有数据吗？此操作不可恢复。')) {
                return;
            }

            try {
                const response = await fetch('/api/clear-data', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    alert('数据已清空');
                    stockData = [];
                    filterAndDisplayData();
                    updateStatistics({});
                } else {
                    alert('清空失败: ' + result.message);
                }
            } catch (error) {
                alert('清空失败: ' + error.message);
            }
        }

        async function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];

            if (!file) {
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            try {
                const response = await fetch('/api/upload-holdings', {
                    method: 'POST',
                    body: formData
                });
                const result = await response.json();

                if (result.success) {
                    showMessage(`✅ 导入成功: ${result.message}`, 'success');

                    // 自动尝试恢复股票状态
                    setTimeout(async () => {
                        try {
                            showMessage('🔄 正在自动恢复股票状态...', 'info');
                            const restoreResponse = await fetch('/api/auto_restore_states');
                            const restoreResult = await restoreResponse.json();

                            if (restoreResult.success) {
                                showMessage(`✅ ${restoreResult.message}`, 'success');
                                // 刷新数据显示
                                await loadStockData();
                            } else {
                                showMessage(`⚠️ 自动恢复状态失败: ${restoreResult.message}`, 'warning');
                            }
                        } catch (error) {
                            console.warn('自动恢复状态失败:', error);
                            showMessage('⚠️ 自动恢复状态失败，请手动点击"🔄 自动恢复"按钮', 'warning');
                        }
                    }, 1000); // 延迟1秒执行，确保导入完成
                } else {
                    showMessage(`❌ 导入失败: ${result.message}`, 'error');
                }
            } catch (error) {
                alert('导入失败: ' + error.message);
            }

            fileInput.value = '';
        }

        async function refreshData() {
            if (!confirm('确定要重新获取所有股票数据吗？这可能需要几分钟时间。')) {
                return;
            }

            try {
                const response = await fetch('/api/refresh-data', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    alert(result.message);
                } else {
                    alert('更新失败: ' + result.message);
                }
            } catch (error) {
                alert('更新失败: ' + error.message);
            }
        }

        async function checkCacheStatus() {
            try {
                const [cacheResponse, scanResponse] = await Promise.all([
                    fetch('/api/cache-status'),
                    fetch('/api/scan-cache-info')
                ]);

                const cacheResult = await cacheResponse.json();
                const scanResult = await scanResponse.json();

                let message = '缓存状态信息:\\n\\n';

                if (cacheResult.success) {
                    const cache = cacheResult.data;
                    message += `年内最低价缓存:\\n`;
                    message += `- 状态: ${cache.exists ? '存在' : '不存在'}\\n`;
                    message += `- 有效: ${cache.valid ? '是' : '否'}\\n`;
                    if (cache.success_rate) {
                        message += `- 成功率: ${cache.success_rate.toFixed(1)}%\\n`;
                    }
                } else {
                    message += `年内最低价缓存: 获取失败\\n`;
                }

                message += '\\n';

                if (scanResult.success) {
                    const scan = scanResult.data;
                    message += `扫雷数据缓存:\\n`;
                    message += `- 缓存股票数: ${scan.cached_stocks}\\n`;
                    message += `- 缓存时长: ${scan.cache_duration}秒\\n`;
                } else {
                    message += `扫雷数据缓存: 获取失败\\n`;
                }

                alert(message);
            } catch (error) {
                alert('获取缓存状态失败: ' + error.message);
            }
        }

        async function generateDemoData() {
            if (!confirm('确定要生成演示数据吗？这将清空现有数据并生成30支随机股票的模拟持仓数据，仅供演示使用。')) {
                return;
            }

            try {
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = '🎭 生成中...';
                button.disabled = true;

                const response = await fetch('/api/generate-demo-data', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    alert(`🎉 ${result.message}\\n\\n📊 数据概览:\\n• 股票数量: ${result.data.stock_count}只\\n• 总市值: ${(result.data.total_market_value / 10000).toFixed(1)}万元\\n• 卖出信号: ${result.data.sell_signals}只\\n• 卖出预警: ${result.data.warning_signals}只\\n\\n💡 这是模拟数据，仅供演示使用！`);

                    // 刷新页面数据
                    await loadStockData();
                } else {
                    alert('生成演示数据失败: ' + result.message);
                }
            } catch (error) {
                alert('生成演示数据失败: ' + error.message);
            } finally {
                const button = document.querySelector('.management-btn.info');
                if (button) {
                    button.textContent = '🎭 生成演示数据';
                    button.disabled = false;
                }
            }
        }

        async function recalculateSellSignals() {
            if (!confirm('确定要重新计算所有股票的卖出信号吗？\\n\\n这将根据当前策略配置重新评估所有股票的卖出信号。')) {
                return;
            }

            try {
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = '🔄 计算中...';
                button.disabled = true;

                const response = await fetch('/api/recalculate-sell-signals', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    alert(`✅ ${result.message}\\n\\n页面将自动刷新以显示最新的卖出信号。`);
                    // 重新加载数据以显示更新后的卖出信号
                    await loadStockData();
                } else {
                    alert('重新计算失败: ' + result.message);
                }
            } catch (error) {
                alert('重新计算失败: ' + error.message);
            } finally {
                const button = document.querySelector('.management-btn.signal-refresh');
                if (button) {
                    button.textContent = '🔄 刷新卖出信号';
                    button.disabled = false;
                }
            }
        }

        async function testWeChatAlerts() {
            if (!confirm('确定要发送企业微信测试提醒吗？将发送18支随机股票的卖出信号演示。')) {
                return;
            }

            try {
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = '📱 发送中...';
                button.disabled = true;

                const response = await fetch('/api/test-wechat-alerts', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    let message = `🎉 ${result.message}\\n\\n`;
                    message += `📊 详细结果:\\n`;

                    // 按信号类型分组显示
                    const sellSignals = result.data.results.filter(r => r.signal === 'sell');
                    const warningSignals = result.data.results.filter(r => r.signal === 'warning');

                    if (sellSignals.length > 0) {
                        message += `\\n🚨 卖出信号 (${sellSignals.length}只):\\n`;
                        sellSignals.forEach(stock => {
                            message += `  • ${stock.name} (${stock.code}) - ${stock.status}\\n`;
                        });
                    }

                    if (warningSignals.length > 0) {
                        message += `\\n⚠️ 卖出预警 (${warningSignals.length}只):\\n`;
                        warningSignals.forEach(stock => {
                            message += `  • ${stock.name} (${stock.code}) - ${stock.status}\\n`;
                        });
                    }

                    message += `\\n💡 提示: 每只股票每种提醒类型每天最多发送1次，重复的提醒会被自动跳过。`;

                    alert(message);
                } else {
                    alert('测试失败: ' + result.message);
                }
            } catch (error) {
                alert('测试失败: ' + error.message);
            } finally {
                const button = document.querySelector('.management-btn.warning');
                if (button) {
                    button.textContent = '📱 测试企业微信提醒';
                    button.disabled = false;
                }
            }
        }

        // 显示成功提示
        function showSuccessToast(message) {
            // 创建提示元素
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 10000;
                background: #28a745; color: white; padding: 12px 20px;
                border-radius: 6px; font-size: 14px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                transform: translateX(100%); transition: transform 0.3s ease;
            `;
            toast.textContent = message;

            document.body.appendChild(toast);

            // 显示动画
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 工具函数
        function formatNumber(value, decimals = 2) {
            if (value === null || value === undefined || value === '') return '-';
            return Number(value).toFixed(decimals);
        }

        // 获取卖出信号显示文本
        function getSellSignalDisplay(signal) {
            switch(signal) {
                case 'clearance': return '🔥立即清仓';
                case 'reduce': return '📉立即减半';
                case 'prepare_clearance': return '🔥准备清仓';
                case 'prepare_reduce': return '📉准备减半';
                case 'sell': return '🚨卖出';
                case 'warning': return '⚠️预警';
                default: return '✅持有';
            }
        }

        // 计算策略目标价格
        function calculateStrategyPrice(stock) {
            if (!stock.price || !stock.sell_signal) return null;

            const currentPrice = parseFloat(stock.price);
            const lowPrice = parseFloat(stock.yearly_low || stock.low_price || stock.price);
            const costPrice = parseFloat(stock.unit_cost || 0);

            // 调试华泰证券价格计算
            if (stock.code === '601688') {
                console.log('🔍 华泰证券价格计算调试:');
                console.log('  当前价格:', currentPrice);
                console.log('  年内最低价:', stock.yearly_low);
                console.log('  使用的lowPrice:', lowPrice);
                console.log('  卖出信号:', stock.sell_signal);
                console.log('  卖出原因:', stock.sell_reason);
            }

            // 根据策略类型和原因计算目标价格
            if (stock.sell_signal === 'clearance') {
                // 清仓策略
                if (stock.sell_reason && stock.sell_reason.includes('基础涨幅') && stock.sell_reason.includes('145.0%')) {
                    return lowPrice * 2.4; // 140%涨幅策略
                } else if (stock.sell_reason && stock.sell_reason.includes('高TTM') && stock.sell_reason.includes('100%')) {
                    return lowPrice * 2.0; // 100%涨幅策略
                } else if (stock.sell_reason && stock.sell_reason.includes('涨停') && stock.sell_reason.includes('70%')) {
                    return lowPrice * 1.7; // 涨停70%策略
                } else if (stock.sell_reason && stock.sell_reason.includes('TTM') && stock.sell_reason.includes('50%')) {
                    return lowPrice * 1.5; // TTM 50%策略
                } else if (stock.sell_reason && stock.sell_reason.includes('回本')) {
                    return costPrice; // 回本价格
                }
                return null; // 市价清仓，不显示目标价格

            } else if (stock.sell_signal === 'reduce') {
                // 减半策略
                if (stock.sell_reason && stock.sell_reason.includes('基础涨幅') && stock.sell_reason.includes('75.0%')) {
                    return lowPrice * 1.7; // 70%涨幅策略
                } else if (stock.sell_reason && stock.sell_reason.includes('高TTM') && stock.sell_reason.includes('55.0%')) {
                    return lowPrice * 1.5; // 50%涨幅策略
                } else if (stock.sell_reason && stock.sell_reason.includes('涨停') && stock.sell_reason.includes('70%')) {
                    return lowPrice * 1.7; // 涨停70%策略
                } else if (stock.sell_reason && stock.sell_reason.includes('TTM') && stock.sell_reason.includes('50%')) {
                    return lowPrice * 1.5; // TTM 50%策略
                } else if (stock.sell_reason && stock.sell_reason.includes('负TTM') && stock.sell_reason.includes('30%')) {
                    return lowPrice * 1.3; // 30%涨幅策略
                }
                return null; // 市价减半，不显示目标价格

            } else if (stock.sell_signal === 'warning') {
                // 预警策略 - 建议挂单价格
                if (stock.sell_reason && stock.sell_reason.includes('基础减半策略触发还有')) {
                    // 基础减半预警：70%涨幅目标
                    const targetPrice = lowPrice * 1.7;

                    // 调试华泰证券
                    if (stock.code === '601688') {
                        console.log('  计算目标价格:', lowPrice, '× 1.7 =', targetPrice);
                    }

                    return targetPrice;
                } else if (stock.sell_reason && stock.sell_reason.includes('负TTM清仓策略触发')) {
                    // 负TTM清仓预警：50%涨幅目标
                    return lowPrice * 1.5; // 50%涨幅目标
                } else if (stock.sell_reason && stock.sell_reason.includes('已超减半线')) {
                    // 已超减半线，清仓预警：50%涨幅目标
                    return lowPrice * 1.5; // 50%涨幅目标
                }
                return currentPrice * 1.02; // 默认高2%挂单

            } else if (stock.sell_signal === 'sell') {
                return null; // 市价卖出，不显示目标价格
            }

            return null;
        }

        // 获取策略显示信息
        function getStrategyDisplayInfo(stock) {
            if (!stock.sell_signal || stock.sell_signal === 'hold') return '';

            const targetPrice = calculateStrategyPrice(stock);
            const currentPrice = parseFloat(stock.price);
            const lowPrice = parseFloat(stock.yearly_low || stock.low_price || stock.price);

            // 构建完整的策略信息
            let signalText = '';
            let strategyText = '';
            let explanationText = '';
            let priceText = '';

            if (stock.sell_reason) {
                if (stock.sell_reason.includes('基础涨幅')) {
                    if (stock.sell_signal === 'clearance') {
                        signalText = '信号：清仓';
                        strategyText = '策略：基础清仓(涨幅≥140%)';
                        explanationText = '说明：基础涨幅达到清仓标准';
                        priceText = targetPrice ? `卖出价: ${formatNumber(targetPrice, 3)}元 (${formatNumber(lowPrice, 3)} × 2.4)` : '卖出价: 市价清仓';
                    } else {
                        signalText = '信号：减半';
                        strategyText = '策略：基础减半(涨幅≥70%)';
                        explanationText = '说明：基础涨幅达到减半标准';
                        priceText = targetPrice ? `卖出价: ${formatNumber(targetPrice, 3)}元 (${formatNumber(lowPrice, 3)} × 1.7)` : '卖出价: 市价减半';
                    }
                } else if (stock.sell_reason.includes('基础减半策略触发还有')) {
                    signalText = '信号：预警（接近减半）';
                    strategyText = '策略：基础减半预警(涨幅≥70%)';
                    const match = stock.sell_reason.match(/还有(\\d+\\.\\d+)%涨幅/);
                    const remaining = match ? match[1] : '?';
                    explanationText = `说明：距离基础70%减半触发还有${remaining}%涨幅`;
                    priceText = targetPrice ? `卖出价: ${formatNumber(targetPrice, 3)}元 (${formatNumber(lowPrice, 3)} × 1.7)` : '卖出价: 准备减半';
                } else if (stock.sell_reason.includes('紧急预警：距离负TTM清仓策略触发')) {
                    signalText = '信号：预警（接近清仓）';
                    strategyText = '策略：负TTM清仓预警(TTM≤0, 股息≤2%, 扫雷≤70, 涨幅≥50%)';
                    const match = stock.sell_reason.match(/还有(\\d+\\.\\d+)%涨幅/);
                    const remaining = match ? match[1] : '?';
                    explanationText = `说明：距离TTM策略50%清仓触发还有${remaining}%涨幅`;
                    priceText = targetPrice ? `卖出价: ${formatNumber(targetPrice, 3)}元 (${formatNumber(lowPrice, 3)} × 1.5)` : '卖出价: 准备清仓';
                } else if (stock.sell_reason.includes('已超减半线，距离负TTM清仓策略触发')) {
                    signalText = '信号：预警（已超减半线）';
                    strategyText = '策略：负TTM清仓预警(TTM≤0, 股息≤2%, 扫雷≤70, 涨幅≥50%)';
                    const match = stock.sell_reason.match(/还有(\\d+\\.\\d+)%涨幅/);
                    const remaining = match ? match[1] : '?';
                    explanationText = `说明：已超30%减半线，距离50%清仓还有${remaining}%涨幅`;
                    priceText = targetPrice ? `卖出价: ${formatNumber(targetPrice, 3)}元 (${formatNumber(lowPrice, 3)} × 1.5)` : '卖出价: 准备清仓';
                } else if (stock.sell_reason.includes('负TTM回本预警')) {
                    signalText = '信号：预警（接近回本）';
                    strategyText = '策略：负TTM回本预警(TTM≤0, 股息≤3%, 接近回本)';
                    const match = stock.sell_reason.match(/还需(\\d+\\.\\d+)%涨幅/);
                    const remaining = match ? match[1] : '?';
                    explanationText = `说明：距离负TTM回本清仓还需${remaining}%涨幅`;
                    const costPrice = parseFloat(stock.unit_cost || 0);
                    priceText = `卖出价: ${formatNumber(costPrice, 3)}元 (回本价)`;
                } else if (stock.sell_reason.includes('负TTM回本')) {
                    signalText = '信号：清仓';
                    strategyText = '策略：负TTM回本清仓(TTM≤0, 股息≤3%, 已回本)';
                    explanationText = '说明：负TTM股票已回本，建议清仓';
                    const costPrice = parseFloat(stock.unit_cost || 0);
                    priceText = `卖出价: ${formatNumber(costPrice, 3)}元 (回本价)`;
                } else if (stock.sell_reason.includes('负TTM低股息低扫雷') && stock.sell_signal === 'clearance') {
                    signalText = '信号：清仓';
                    strategyText = '策略：负TTM清仓(TTM≤0, 股息≤2%, 扫雷≤70, 涨幅≥50%)';
                    explanationText = '说明：负TTM风险股票涨幅过高，建议清仓';
                    priceText = targetPrice ? `卖出价: ${formatNumber(targetPrice, 3)}元 (${formatNumber(lowPrice, 3)} × 1.5)` : '卖出价: 市价清仓';
                } else if (stock.sell_reason.includes('涨停高PB')) {
                    signalText = '信号：清仓';
                    strategyText = '策略：涨停高PB清仓(涨停+PB≥1.5+涨幅≥70%)';
                    explanationText = '说明：涨停且高PB，建议清仓';
                    priceText = '卖出价: 市价清仓';
                } else if (stock.sell_reason.includes('涨停低PB')) {
                    signalText = '信号：减半';
                    strategyText = '策略：涨停低PB减半(涨停+PB<1.5+涨幅≥70%)';
                    explanationText = '说明：涨停但低PB，建议减半';
                    priceText = '卖出价: 市价减半';
                } else if (stock.sell_reason.includes('高TTM')) {
                    if (stock.sell_signal === 'clearance') {
                        signalText = '信号：清仓';
                        strategyText = '策略：高TTM清仓(TTM≥30+涨幅≥90%)';
                        explanationText = '说明：高TTM股票涨幅过高，建议清仓';
                        priceText = targetPrice ? `卖出价: ${formatNumber(targetPrice, 3)}元 (${formatNumber(lowPrice, 3)} × 2.0)` : '卖出价: 市价清仓';
                    } else {
                        signalText = '信号：减半';
                        strategyText = '策略：高TTM减半(TTM≥30+涨幅≥50%)';
                        explanationText = '说明：高TTM股票涨幅较高，建议减半';
                        priceText = targetPrice ? `卖出价: ${formatNumber(targetPrice, 3)}元 (${formatNumber(lowPrice, 3)} × 1.5)` : '卖出价: 市价减半';
                    }
                } else {
                    signalText = `信号：${stock.sell_signal}`;
                    strategyText = '策略：其他策略';
                    explanationText = `说明：${stock.sell_reason}`;
                    priceText = targetPrice ? `卖出价: ${formatNumber(targetPrice, 3)}元` : '卖出价: 市价操作';
                }
            }

            return `<div class="strategy-info">
                <div class="strategy-signal">${signalText}</div>
                <div class="strategy-name">${strategyText}</div>
                <div class="strategy-explanation">${explanationText}</div>
                <div class="strategy-price">${priceText}</div>
            </div>`;
        }

        // 获取自定义状态显示信息
        function getCustomStatusInfo(stock) {
            if (!stock.custom_status) return '';

            return `<div class="custom-status-info">
                <span class="custom-status-time">${stock.custom_status_time || ''}</span>
            </div>`;
        }

        // 获取减半监控信息
        function getReductionMonitoringInfo(stock) {
            if (!stock.is_reduced) return '';

            let monitoringInfo = '';

            // 显示策略模式
            const strategyMode = stock.strategy_mode || 'normal';
            let strategyText = '';
            let strategyColor = '';

            switch (strategyMode) {
                case 'cooling_down':
                    strategyText = '🧊冷却期';
                    strategyColor = '#17a2b8';
                    break;
                case 'high_ttm':
                    strategyText = '📊高TTM';
                    strategyColor = '#fd7e14';
                    break;
                case 'negative_ttm':
                    strategyText = '⚠️负TTM';
                    strategyColor = '#dc3545';
                    break;
                case 'normal':
                    strategyText = '✅正常';
                    strategyColor = '#28a745';
                    break;
                default:
                    strategyText = '❓未知';
                    strategyColor = '#6c757d';
            }

            monitoringInfo += `<div class="reduction-monitoring-info">
                <span class="strategy-mode" style="background-color: ${strategyColor}; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px; margin-top: 2px; display: inline-block; cursor: pointer;"
                      onclick="debugReductionStatus('${stock.code}', '${stock.name}')"
                      title="点击查看详细调试信息">
                    ${strategyText}
                </span>`;

            // 显示冷却期剩余时间
            if (strategyMode === 'cooling_down' && stock.cooling_down_until) {
                const coolingDate = new Date(stock.cooling_down_until);
                const now = new Date();
                const daysRemaining = Math.max(0, Math.ceil((coolingDate - now) / (1000 * 60 * 60 * 24)));

                if (daysRemaining > 0) {
                    monitoringInfo += `<br><span style="font-size: 10px; color: #6c757d;">
                        还剩${daysRemaining}天
                    </span>`;
                }
            }

            // 显示基本面状态
            if (strategyMode === 'high_ttm' || strategyMode === 'negative_ttm') {
                const currentTTM = stock.pe_ratio || 0;
                monitoringInfo += `<br><span style="font-size: 10px; color: #6c757d;">
                    TTM: ${currentTTM.toFixed(1)}
                </span>`;
            }

            // 显示减半信息：次数和最后一次时间
            const reductionHistory = stock.reduction_history || [];
            const reductionCount = reductionHistory.length;

            if (reductionCount > 0) {
                // 获取最后一次减半时间
                const lastReduction = reductionHistory[reductionHistory.length - 1];
                const lastTime = lastReduction ? (lastReduction.reduction_time || lastReduction.reduction_date) : '';

                // 格式化时间显示
                let timeDisplay = '';
                if (lastTime) {
                    try {
                        const date = new Date(lastTime);
                        const now = new Date();
                        const diffDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));

                        if (diffDays === 0) {
                            timeDisplay = '今天';
                        } else if (diffDays === 1) {
                            timeDisplay = '昨天';
                        } else if (diffDays < 30) {
                            timeDisplay = `${diffDays}天前`;
                        } else {
                            timeDisplay = date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
                        }
                    } catch (e) {
                        timeDisplay = lastTime.substring(5, 10); // 显示月-日
                    }
                }

                // 显示减半信息
                if (reductionCount === 1) {
                    monitoringInfo += `<br><span style="font-size: 10px; color: #6c757d; cursor: pointer;"
                                            onclick="debugReductionHistory('${stock.code}', '${stock.name}')"
                                            title="点击查看减半历史详情">
                        首次减半 ${timeDisplay} 🔍
                    </span>`;
                } else {
                    monitoringInfo += `<br><span style="font-size: 10px; color: #6c757d; cursor: pointer;"
                                            onclick="debugReductionHistory('${stock.code}', '${stock.name}')"
                                            title="点击查看减半历史详情">
                        ${reductionCount}次减半 最近${timeDisplay} 🔍
                    </span>`;
                }

                // 调试信息：如果数量异常，在控制台输出详细信息
                if (reductionCount > 10) {
                    console.warn(`⚠️ 异常减半次数: ${stock.name} (${stock.code}) 有 ${reductionCount} 条减半记录`, reductionHistory);
                }
            }

            monitoringInfo += '</div>';

            return monitoringInfo;
        }

        // 调试减半监控状态
        async function debugReductionStatus(stockCode, stockName) {
            try {
                const response = await fetch(`/api/debug-reduction-status/${stockCode}`);
                const result = await response.json();

                if (result.success) {
                    const data = result.data;
                    let debugMessage = `🔍 ${stockName} (${stockCode}) 减半监控调试信息:\\n\\n`;

                    debugMessage += `📊 基本信息:\\n`;
                    debugMessage += `• 是否已减半: ${data.is_reduced}\\n`;
                    debugMessage += `• 减半时间: ${data.reduced_time}\\n`;
                    debugMessage += `• 监控状态: ${data.monitoring_status}\\n`;
                    debugMessage += `• 策略模式: ${data.strategy_mode}\\n`;
                    debugMessage += `• 冷却期至: ${data.cooling_down_until}\\n`;
                    debugMessage += `• 当前TTM: ${data.current_ttm}\\n\\n`;

                    debugMessage += `⚙️ 配置信息:\\n`;
                    debugMessage += `• 冷却期天数: ${data.config.cooling_period_days}\\n`;
                    debugMessage += `• TTM阈值: ${data.config.ttm_threshold}\\n`;
                    debugMessage += `• 启用冷却期: ${data.config.enable_cooling_period}\\n`;
                    debugMessage += `• 自动策略切换: ${data.config.auto_strategy_switch}\\n\\n`;

                    debugMessage += `🔍 状态检查:\\n`;
                    debugMessage += `• 是否在冷却期: ${data.is_in_cooling_period}\\n`;
                    debugMessage += `• 基本面恶化: ${data.fundamentals_check[0]} (${data.fundamentals_check[2] || '正常'})\\n`;
                    debugMessage += `• 当前时间: ${data.current_time}\\n\\n`;

                    if (data.reduction_history && data.reduction_history.length > 0) {
                        debugMessage += `📝 减半历史 (${data.reduction_history.length}次):\\n`;
                        data.reduction_history.forEach((record, index) => {
                            debugMessage += `${index + 1}. ${record.reduction_time} - ${record.reason}\\n`;
                        });
                    }

                    // 如果发现问题，提供修复选项
                    if (!data.is_in_cooling_period && data.is_reduced && data.strategy_mode !== 'cooling_down') {
                        debugMessage += `\\n🔧 检测到监控状态异常！\\n`;
                        debugMessage += `建议点击"确定"后选择修复。`;

                        if (confirm(debugMessage + '\\n\\n是否立即修复减半监控状态？')) {
                            await fixReductionMonitoring(stockCode, stockName);
                        }
                    } else {
                        alert(debugMessage);
                    }
                } else {
                    alert(`❌ 调试失败: ${result.message}`);
                }
            } catch (error) {
                alert(`❌ 调试失败: ${error.message}`);
            }
        }

        // 修复减半监控状态
        async function fixReductionMonitoring(stockCode, stockName) {
            try {
                const response = await fetch(`/api/fix-reduction-monitoring/${stockCode}`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    const data = result.data;
                    alert(`✅ ${result.message}\\n\\n📊 修复详情:\\n• 股票: ${data.stock_name} (${data.stock_code})\\n• 冷却期至: ${data.cooling_down_until}\\n• 策略模式: ${data.strategy_mode}\\n• 修复时间: ${data.fix_time}\\n\\n🔄 页面将自动刷新以显示最新状态。`);

                    // 重新加载数据
                    await loadStockData();
                } else {
                    alert(`❌ 修复失败: ${result.message}`);
                }
            } catch (error) {
                alert(`❌ 修复失败: ${error.message}`);
            }
        }

        // 调试减半历史记录
        async function debugReductionHistory(stockCode, stockName) {
            try {
                const response = await fetch(`/api/debug-reduction-status/${stockCode}`);
                const result = await response.json();

                if (result.success) {
                    const data = result.data;
                    const history = data.reduction_history || [];

                    let message = `🔍 ${stockName} (${stockCode}) 减半历史调试\\n\\n`;
                    message += `📊 基本信息:\\n`;
                    message += `• 减半历史记录数量: ${history.length}\\n`;
                    message += `• 是否已减半: ${data.is_reduced}\\n`;
                    message += `• 减半时间: ${data.reduced_time}\\n\\n`;

                    if (history.length === 0) {
                        message += `❌ 没有减半历史记录！\\n`;
                        message += `这可能是数据异常，建议重新标记减半状态。`;
                    } else {
                        message += `📝 减半历史记录 (${history.length} 条):\\n`;
                        message += '=' .repeat(40) + '\\n';

                        history.forEach((record, index) => {
                            message += `${index + 1}. 时间: ${record.reduction_time || record.reduction_date}\\n`;
                            message += `   价格: ${record.reduction_price || '未知'}\\n`;
                            message += `   TTM: ${record.ttm_at_reduction || '未知'}\\n`;
                            message += `   原因: ${record.reason || '未知'}\\n`;
                            message += `   涨幅: ${record.distance_from_low_pct || '未知'}%\\n\\n`;
                        });

                        // 检查是否有重复记录
                        const uniqueDates = new Set(history.map(r => r.reduction_date));
                        if (uniqueDates.size < history.length) {
                            message += `⚠️ 检测到重复记录！\\n`;
                            message += `• 总记录数: ${history.length}\\n`;
                            message += `• 唯一日期数: ${uniqueDates.size}\\n`;
                            message += `建议清理重复记录。\\n\\n`;
                        }
                    }

                    if (confirm(message + '\\n是否需要清理减半历史记录？')) {
                        await cleanReductionHistory(stockCode, stockName);
                    }
                } else {
                    alert(`❌ 获取减半历史失败: ${result.message}`);
                }
            } catch (error) {
                alert(`❌ 调试失败: ${error.message}`);
            }
        }

        // 清理减半历史记录
        async function cleanReductionHistory(stockCode, stockName) {
            try {
                const response = await fetch(`/api/clean-reduction-history/${stockCode}`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    const data = result.data;
                    alert(`✅ ${result.message}\\n\\n📊 清理详情:\\n• 原始记录: ${data.original_count} 条\\n• 清理后: ${data.cleaned_count} 条\\n• 删除重复: ${data.removed_count} 条\\n• 清理时间: ${data.clean_time}\\n\\n🔄 页面将自动刷新以显示最新状态。`);

                    // 重新加载数据
                    await loadStockData();
                } else {
                    alert(`❌ 清理失败: ${result.message}`);
                }
            } catch (error) {
                alert(`❌ 清理失败: ${error.message}`);
            }
        }

        // 批量清理所有股票的减半历史记录
        async function batchCleanReductionHistory() {
            if (!confirm('⚠️ 确认批量清理所有股票的减半历史记录？\\n\\n这将：\\n• 清理所有已减半股票的重复历史记录\\n• 每只股票只保留最新的一条减半记录\\n• 修复异常的减半次数显示\\n\\n此操作不可撤销，请确认继续？')) {
                return;
            }

            try {
                const response = await fetch('/api/batch-clean-reduction-history', {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    const data = result.data;
                    alert(`✅ ${result.message}\\n\\n📊 清理详情:\\n• 总股票数: ${data.total_stocks}\\n• 清理股票数: ${data.cleaned_stocks}\\n• 删除记录数: ${data.total_removed}\\n• 清理时间: ${data.clean_time}\\n\\n🔄 页面将自动刷新以显示最新状态。`);

                    // 重新加载数据
                    await loadStockData();
                } else {
                    alert(`❌ 批量清理失败: ${result.message}`);
                }
            } catch (error) {
                alert(`❌ 批量清理失败: ${error.message}`);
            }
        }

        function formatPercent(value) {
            if (value === null || value === undefined || value === '') return '-';
            return Number(value).toFixed(2) + '%';
        }

        function formatCurrency(value) {
            if (value === null || value === undefined || value === '' || value === 0) return '¥0';
            return '¥' + Number(value).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
        }

        // ==================== 股票状态管理功能 ====================

        // 显示消息提示
        function showMessage(message, type = 'info') {
            // 创建消息元素
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 5px;
                color: white;
                font-weight: bold;
                z-index: 10000;
                max-width: 400px;
                word-wrap: break-word;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                transition: all 0.3s ease;
            `;

            // 根据类型设置颜色
            switch(type) {
                case 'success':
                    messageDiv.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';
                    break;
                case 'error':
                    messageDiv.style.background = 'linear-gradient(135deg, #f44336, #d32f2f)';
                    break;
                case 'warning':
                    messageDiv.style.background = 'linear-gradient(135deg, #ff9800, #f57c00)';
                    break;
                default: // info
                    messageDiv.style.background = 'linear-gradient(135deg, #2196F3, #1976D2)';
            }

            messageDiv.textContent = message;
            document.body.appendChild(messageDiv);

            // 3秒后自动消失
            setTimeout(() => {
                messageDiv.style.opacity = '0';
                messageDiv.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                }, 300);
            }, 3000);
        }

        // 导出股票状态
        async function exportStockStates() {
            try {
                showMessage('正在导出股票状态...', 'info');

                const response = await fetch('/api/export_stock_states');
                const result = await response.json();

                if (result.success) {
                    // 创建下载链接
                    const dataStr = JSON.stringify(result.data, null, 2);
                    const dataBlob = new Blob([dataStr], {type: 'application/json'});
                    const url = URL.createObjectURL(dataBlob);

                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `股票状态备份_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);

                    // 同时保存到本地备份文件
                    await saveBackupFile(result.data);

                    showMessage(`✅ 成功导出 ${result.data.total_stocks} 只股票的状态信息`, 'success');
                } else {
                    showMessage(`❌ 导出失败: ${result.message}`, 'error');
                }
            } catch (error) {
                console.error('导出股票状态失败:', error);
                showMessage(`❌ 导出失败: ${error.message}`, 'error');
            }
        }

        // 导入股票状态
        function importStockStates() {
            const fileInput = document.getElementById('stateFileInput');
            fileInput.click();
        }

        // 处理状态文件导入
        async function handleStateFileImport(event) {
            const file = event.target.files[0];
            if (!file) return;

            try {
                showMessage('正在导入股票状态...', 'info');

                const text = await file.text();
                const data = JSON.parse(text);

                const response = await fetch('/api/import_stock_states', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    showMessage(`✅ ${result.message}`, 'success');
                    // 刷新数据显示
                    await loadStockData();
                } else {
                    showMessage(`❌ 导入失败: ${result.message}`, 'error');
                }
            } catch (error) {
                console.error('导入股票状态失败:', error);
                showMessage(`❌ 导入失败: ${error.message}`, 'error');
            }

            // 清空文件输入
            event.target.value = '';
        }

        // 自动恢复股票状态
        async function autoRestoreStates() {
            try {
                showMessage('正在自动恢复股票状态...', 'info');

                const response = await fetch('/api/auto_restore_states');
                const result = await response.json();

                if (result.success) {
                    showMessage(`✅ ${result.message}`, 'success');
                    // 刷新数据显示
                    await loadStockData();
                } else {
                    showMessage(`❌ 自动恢复失败: ${result.message}`, 'error');
                }
            } catch (error) {
                console.error('自动恢复股票状态失败:', error);
                showMessage(`❌ 自动恢复失败: ${error.message}`, 'error');
            }
        }

        // 保存备份文件到服务器
        async function saveBackupFile(data) {
            try {
                const response = await fetch('/api/save_backup', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                if (response.ok) {
                    console.log('✅ 备份文件已保存到服务器');
                }
            } catch (error) {
                console.warn('保存备份文件失败:', error);
            }
        }

        // 自动保存股票状态（静默执行）
        async function autoSaveStockStates() {
            try {
                const response = await fetch('/api/export_stock_states');
                const result = await response.json();

                if (result.success) {
                    // 静默保存到服务器备份文件
                    await saveBackupFile(result.data);
                    console.log(`🔄 自动保存状态完成: ${result.data.total_stocks} 只股票`);
                } else {
                    console.warn('自动保存状态失败:', result.message);
                }
            } catch (error) {
                console.warn('自动保存状态异常:', error);
            }
        }
    </script>
</body>
</html>
'''

if __name__ == '__main__':
    import sys
    sys.stdout.flush()

    print("=== 🚀 启动持仓系统 V12 股票专版 ===", flush=True)
    print("📊 功能特性:", flush=True)
    print("   • 只显示A股股票（自动过滤ETF、基金、可转债等）", flush=True)
    print("   • 实时股价、年内最低价、股息率、行业信息", flush=True)
    print("   • 智能交易时间监测", flush=True)
    print("   • 持仓数量和市值计算", flush=True)
    print("   • Excel数据管理（上传、清空、刷新）", flush=True)
    print("   • 行业分布统计分析", flush=True)
    print("   • 扫雷风险评估", flush=True)
    print("   • 移动端响应式设计", flush=True)
    print("   • 年内最低价缓存系统", flush=True)
    print(flush=True)
    print("⚠️  注意：本版本只处理A股股票，会自动跳过其他证券类型", flush=True)

    # 检查缓存状态
    try:
        cache_status = check_cache_status()
        if cache_status['exists'] and cache_status['valid']:
            print("✅ 年内最低价缓存可用")
            if 'success_rate' in cache_status:
                print(f"   📊 缓存统计: {cache_status['success_count']}/{cache_status['total_count']} ({cache_status['success_rate']:.1f}%)")
        else:
            print("⚠️ 年内最低价缓存不可用")
            print("   建议运行: python yearly_low_cache_system.py")
    except Exception as e:
        print(f"⚠️ 缓存检查失败: {e}")

    # 显示交易状态
    try:
        status = trading_monitor.get_trading_status()
        print(f"📈 当前交易状态: {status['message']}")
        print(f"⏰ 更新间隔: {trading_monitor.get_update_interval()}秒")
    except Exception as e:
        print(f"⚠️ 获取交易状态失败: {e}")

    # 启动时加载缓存数据
    print("\n📂 正在加载缓存数据...")
    load_imported_list()  # 加载导入的股票列表
    load_stock_data()     # 加载股票数据缓存
    load_auto_update_config()  # 加载自动更新配置

    # 清理过期的企业微信提醒记录
    print("\n🧹 正在清理过期提醒记录...")
    try:
        wechat_alert.clean_old_alerts()
    except Exception as e:
        print(f"⚠️ 清理过期提醒记录失败: {e}")

    print(f"\n🌐 访问地址: http://localhost:5000")
    print("📱 支持移动端访问，响应式设计")
    print("🔄 智能更新：只在交易时间更新数据")
    print("📊 完整功能：数据管理、统计分析、风险评估")
    print("💾 数据持久化：自动保存和恢复上次数据")

    # 启动后台更新线程
    update_thread = threading.Thread(target=background_update)
    update_thread.daemon = True
    update_thread.start()

# ==================== 股票状态保存和恢复功能 ====================

@app.route('/api/export_stock_states')
def export_stock_states():
    """导出当前所有股票状态"""
    try:
        # 构建状态数据（从内存中的stock_data获取）
        stock_states = {}
        special_states_count = 0

        for stock_code, stock_info in stock_data.items():
            sell_signal = stock_info.get('sell_signal', 'hold')
            halved_quantity = stock_info.get('halved_quantity', 0)

            # 导出所有股票的状态信息，包括正常持有的股票
            stock_states[stock_code] = {
                'stock_name': stock_info.get('stock_name', ''),
                'sell_signal': sell_signal,
                'halved_quantity': halved_quantity,
                'halved_date': stock_info.get('halved_date', ''),
                'cleared_date': stock_info.get('cleared_date', ''),
                'notes': stock_info.get('notes', ''),
                'last_updated': stock_info.get('last_updated', ''),
                'is_reduced': stock_info.get('is_reduced', False),
                'reduction_history': stock_info.get('reduction_history', []),
                'monitoring_status': stock_info.get('monitoring_status', ''),
                'cooling_down_until': stock_info.get('cooling_down_until', ''),
                'strategy_mode': stock_info.get('strategy_mode', 'normal'),
                'export_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            # 统计有特殊状态的股票数量
            if sell_signal in ['halved', 'cleared'] or halved_quantity > 0:
                special_states_count += 1

        # 添加导出元数据
        export_data = {
            'export_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'total_stocks': len(stock_states),
            'special_states_count': special_states_count,
            'version': 'v14_enhanced',
            'description': '股票状态备份文件 - 包含所有股票的完整状态信息',
            'stock_states': stock_states
        }

        return jsonify({
            'success': True,
            'data': export_data,
            'message': f'成功导出 {len(stock_states)} 只股票的状态信息（其中 {special_states_count} 只有特殊状态）'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'导出股票状态失败: {str(e)}'
        })

@app.route('/api/import_stock_states', methods=['POST'])
def import_stock_states():
    """导入股票状态并自动恢复"""
    try:
        data = request.get_json()
        if not data or 'stock_states' not in data:
            return jsonify({
                'success': False,
                'message': '无效的状态数据格式'
            })

        result = import_stock_states_from_data(data)
        return jsonify(result)

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'导入股票状态失败: {str(e)}'
        })

@app.route('/api/auto_restore_states')
def auto_restore_states():
    """自动恢复股票状态（在Excel导入后调用）"""
    try:
        # 从本地存储或默认位置读取最近的状态备份
        backup_file = 'stock_states_backup.json'

        if not os.path.exists(backup_file):
            return jsonify({
                'success': False,
                'message': '未找到状态备份文件，请先导出股票状态'
            })

        with open(backup_file, 'r', encoding='utf-8') as f:
            backup_data = json.load(f)

        # 调用导入功能
        result = import_stock_states_from_data(backup_data)
        return jsonify(result)

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'自动恢复状态失败: {str(e)}'
        })

def auto_restore_stock_states():
    """自动恢复股票状态（在Excel导入后自动调用）"""
    try:
        # 从本地存储读取最近的状态备份
        backup_file = 'stock_states_backup.json'

        if not os.path.exists(backup_file):
            print("⚠️ 未找到状态备份文件，跳过自动恢复")
            return {
                'success': False,
                'message': '未找到状态备份文件',
                'restored_count': 0,
                'skipped_count': 0
            }

        with open(backup_file, 'r', encoding='utf-8') as f:
            backup_data = json.load(f)

        # 调用导入功能
        result = import_stock_states_from_data(backup_data)

        if result['success']:
            print(f"✅ 自动恢复股票状态完成：恢复 {result['restored_count']} 只，跳过 {result['skipped_count']} 只")

        return result

    except Exception as e:
        print(f"❌ 自动恢复股票状态失败: {e}")
        return {
            'success': False,
            'message': f'自动恢复失败: {str(e)}',
            'restored_count': 0,
            'skipped_count': 0
        }

def import_stock_states_from_data(data):
    """从数据导入股票状态的内部函数"""
    try:
        global stock_data

        if not data or 'stock_states' not in data:
            return {
                'success': False,
                'message': '无效的状态数据格式',
                'restored_count': 0,
                'skipped_count': 0
            }

        stock_states = data['stock_states']
        restored_count = 0
        skipped_count = 0

        for stock_code, state_info in stock_states.items():
            # 检查股票是否存在于当前持仓中
            if stock_code in stock_data:
                # 恢复基本股票状态
                stock_data[stock_code]['sell_signal'] = state_info.get('sell_signal', 'hold')
                stock_data[stock_code]['halved_quantity'] = state_info.get('halved_quantity', 0)
                stock_data[stock_code]['halved_date'] = state_info.get('halved_date', '')
                stock_data[stock_code]['cleared_date'] = state_info.get('cleared_date', '')
                stock_data[stock_code]['notes'] = state_info.get('notes', '')
                stock_data[stock_code]['last_updated'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                # 恢复减半监控相关状态
                stock_data[stock_code]['is_reduced'] = state_info.get('is_reduced', False)
                stock_data[stock_code]['reduction_history'] = state_info.get('reduction_history', [])
                stock_data[stock_code]['monitoring_status'] = state_info.get('monitoring_status', '')
                stock_data[stock_code]['cooling_down_until'] = state_info.get('cooling_down_until', '')
                stock_data[stock_code]['strategy_mode'] = state_info.get('strategy_mode', 'normal')

                # 如果是已减半状态，需要设置相关标记并更新监控状态
                if state_info.get('sell_signal') == 'halved' or state_info.get('is_reduced', False):
                    stock_data[stock_code]['is_reduced'] = True
                    # 更新减半监控状态
                    try:
                        reduction_monitor.update_strategy_mode(stock_code, stock_data[stock_code])
                    except Exception as e:
                        print(f"⚠️ 更新减半监控状态失败 {stock_code}: {e}")

                restored_count += 1
                print(f"✅ 恢复股票状态: {stock_code} - {state_info.get('sell_signal', 'hold')}")
            else:
                skipped_count += 1

        # 保存更新后的数据
        save_stock_data()

        return {
            'success': True,
            'message': f'自动恢复完成！恢复 {restored_count} 只股票状态，跳过 {skipped_count} 只',
            'restored_count': restored_count,
            'skipped_count': skipped_count
        }

    except Exception as e:
        return {
            'success': False,
            'message': f'自动恢复失败: {str(e)}',
            'restored_count': 0,
            'skipped_count': 0
        }

@app.route('/api/save_backup', methods=['POST'])
def save_backup():
    """保存备份文件到服务器"""
    try:
        data = request.get_json()
        backup_file = 'stock_states_backup.json'

        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        return jsonify({
            'success': True,
            'message': '备份文件已保存到服务器'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'保存备份文件失败: {str(e)}'
        })

if __name__ == '__main__':
    # 启动Flask应用
    # 生产环境使用Gunicorn，开发环境使用内置服务器
    import os
    if os.environ.get('FLASK_ENV') == 'production':
        # 生产环境由Gunicorn启动，这里不需要app.run()
        pass
    else:
        # 开发环境
        app.run(host='0.0.0.0', port=5000, debug=False)

