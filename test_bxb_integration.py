#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通达信扫雷模块集成测试
======================

用于验证模块在您的环境中是否能正常工作
运行此脚本来测试所有功能是否正常
"""

import sys
import time
from datetime import datetime

def test_module_import():
    """测试模块导入"""
    print("1. 测试模块导入...")
    try:
        from tdx_bxb_module import TdxBxbClient, get_stock_bxb_score, get_batch_bxb_scores
        print("   ✅ 模块导入成功")
        return True
    except ImportError as e:
        print(f"   ❌ 模块导入失败: {e}")
        return False

def test_single_stock():
    """测试单只股票查询"""
    print("\n2. 测试单只股票查询...")
    try:
        from tdx_bxb_module import TdxBxbClient
        
        client = TdxBxbClient()
        result = client.get_stock_score('000001')
        
        if result and 'score' in result:
            print(f"   ✅ 查询成功: {result['stock_name']} - {result['score']}分 ({result['risk_level']})")
            print(f"   📊 触发风险: {result['triggered_risks']}项")
            return True
        else:
            print("   ❌ 查询失败: 未获取到有效数据")
            return False
            
    except Exception as e:
        print(f"   ❌ 查询异常: {e}")
        return False

def test_batch_query():
    """测试批量查询"""
    print("\n3. 测试批量查询...")
    try:
        from tdx_bxb_module import get_batch_bxb_scores
        
        test_codes = ['000001', '000002', '600036']
        results = get_batch_bxb_scores(test_codes)
        
        if results and len(results) > 0:
            print(f"   ✅ 批量查询成功，获取到 {len(results)} 只股票数据:")
            for code, data in results.items():
                if data:
                    print(f"      {code} ({data['stock_name']}): {data['score']}分")
            return True
        else:
            print("   ❌ 批量查询失败: 未获取到数据")
            return False
            
    except Exception as e:
        print(f"   ❌ 批量查询异常: {e}")
        return False

def test_cache_functionality():
    """测试缓存功能"""
    print("\n4. 测试缓存功能...")
    try:
        from tdx_bxb_module import TdxBxbClient
        
        client = TdxBxbClient(cache_duration=60)  # 1分钟缓存
        
        # 第一次查询
        start_time = time.time()
        result1 = client.get_stock_score('000001')
        first_query_time = time.time() - start_time
        
        # 第二次查询 (应该从缓存获取)
        start_time = time.time()
        result2 = client.get_stock_score('000001')
        second_query_time = time.time() - start_time
        
        if result1 and result2:
            print(f"   ✅ 缓存功能正常")
            print(f"      首次查询耗时: {first_query_time:.2f}秒")
            print(f"      缓存查询耗时: {second_query_time:.2f}秒")
            
            # 获取缓存信息
            cache_info = client.get_cache_info()
            print(f"      缓存股票数: {cache_info['cached_stocks']}")
            return True
        else:
            print("   ❌ 缓存测试失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 缓存测试异常: {e}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n5. 测试错误处理...")
    try:
        from tdx_bxb_module import TdxBxbClient
        
        client = TdxBxbClient(request_timeout=1)  # 设置很短的超时时间
        
        # 测试无效股票代码
        result = client.get_stock_score('INVALID')
        
        if result is None or 'error' in result:
            print("   ✅ 错误处理正常: 无效代码返回None或错误信息")
            return True
        else:
            print("   ⚠️  错误处理可能有问题: 无效代码返回了数据")
            return True  # 不算失败，可能是服务器返回了默认数据
            
    except Exception as e:
        print(f"   ✅ 错误处理正常: 捕获到异常 {type(e).__name__}")
        return True

def test_data_format():
    """测试数据格式"""
    print("\n6. 测试数据格式...")
    try:
        from tdx_bxb_module import get_stock_bxb_score
        
        result = get_stock_bxb_score('000001')
        
        if not result:
            print("   ❌ 数据格式测试失败: 未获取到数据")
            return False
        
        # 检查必要字段
        required_fields = ['stock_code', 'stock_name', 'score', 'risk_level', 'risk_color']
        missing_fields = [field for field in required_fields if field not in result]
        
        if missing_fields:
            print(f"   ❌ 数据格式不完整，缺少字段: {missing_fields}")
            return False
        
        # 检查数据类型
        if not isinstance(result['score'], (int, float)) or not (1 <= result['score'] <= 100):
            print(f"   ❌ 分数格式错误: {result['score']}")
            return False
        
        print("   ✅ 数据格式正确")
        print(f"      包含字段: {list(result.keys())}")
        return True
        
    except Exception as e:
        print(f"   ❌ 数据格式测试异常: {e}")
        return False

def test_integration_example():
    """测试集成示例"""
    print("\n7. 测试集成示例...")
    try:
        # 模拟持仓系统中的股票数据
        sample_stock_data = {
            'code': '000001',
            'name': '平安银行',
            'price': 10.50,
            'change_percent': 2.3,
            'market_value': 1000000
        }
        
        # 使用集成示例中的函数
        from bxb_integration_example import enhance_stock_data_with_bxb
        enhanced_data = enhance_stock_data_with_bxb(sample_stock_data)
        
        # 检查是否添加了爆雷宝数据
        bxb_fields = ['bxb_score', 'bxb_risk_level', 'bxb_risk_color', 'bxb_display_text']
        added_fields = [field for field in bxb_fields if field in enhanced_data]
        
        if len(added_fields) >= 3:  # 至少添加了3个字段
            print("   ✅ 集成示例正常")
            print(f"      添加的字段: {added_fields}")
            print(f"      扫雷信息: {enhanced_data.get('bxb_display_text', 'N/A')}")
            return True
        else:
            print(f"   ❌ 集成示例失败，只添加了字段: {added_fields}")
            return False
            
    except Exception as e:
        print(f"   ❌ 集成示例测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("通达信扫雷模块集成测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Python版本: {sys.version}")
    
    # 运行所有测试
    tests = [
        test_module_import,
        test_single_stock,
        test_batch_query,
        test_cache_functionality,
        test_error_handling,
        test_data_format,
        test_integration_example
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    print(f"总测试数: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！模块可以正常使用。")
        print("\n📝 接下来您可以:")
        print("   1. 将 tdx_bxb_module.py 集成到您的持仓系统中")
        print("   2. 参考 BXB_MODULE_README.md 文档进行集成")
        print("   3. 使用 bxb_integration_example.py 中的示例代码")
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请检查:")
        print("   1. 网络连接是否正常")
        print("   2. 是否安装了所需的依赖包 (requests)")
        print("   3. 防火墙是否阻止了网络请求")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
