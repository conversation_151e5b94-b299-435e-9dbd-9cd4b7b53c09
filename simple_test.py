"""
简单测试扫雷宝评分获取
"""
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simple():
    """简单测试"""
    try:
        from 持仓系统_v10_排雷功能版 import get_mine_clearance_score
        
        print("🧪 简单测试扫雷宝评分获取")
        print("=" * 50)
        
        # 测试神思电子
        code = '300479'
        print(f"📊 测试股票: {code} (神思电子)")
        
        score = get_mine_clearance_score(code)
        
        print(f"\n🎯 最终结果: {code} 的扫雷宝评分为 {score}分")
        
        if score == 74.0:
            print("🎉 成功！获取到了与截图一致的真实评分！")
        elif score is not None:
            print("✅ 获取到评分，但可能不是最准确的真实值")
        else:
            print("❌ 未能获取到评分")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_simple()
