#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试API问题
"""

import requests
import json

def test_api():
    """测试API"""
    print("🔍 测试API连接...")
    
    try:
        # 测试基本连接
        response = requests.get("http://localhost:5000", timeout=5)
        print(f"✅ 主页连接成功: {response.status_code}")
    except Exception as e:
        print(f"❌ 主页连接失败: {e}")
        return
    
    try:
        # 测试股票API
        response = requests.get("http://localhost:5000/api/stocks", timeout=10)
        print(f"📊 股票API状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API响应成功")
            print(f"   success: {data.get('success')}")
            print(f"   count: {data.get('count')}")
            print(f"   last_update: {data.get('last_update')}")
            
            if data.get('data'):
                print(f"   第一只股票: {data['data'][0].get('name')} ({data['data'][0].get('code')})")
            else:
                print("   ⚠️ 数据为空")
        else:
            print(f"❌ API响应失败: {response.status_code}")
            print(f"   响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 股票API调用失败: {e}")

def test_cache_files():
    """测试缓存文件"""
    print("\n📁 检查缓存文件...")
    
    try:
        with open('stock_data_cache.json', 'r', encoding='utf-8') as f:
            cache_data = json.load(f)
        
        stock_data = cache_data.get('stock_data', {})
        print(f"✅ 缓存文件读取成功")
        print(f"   股票数量: {len(stock_data)}")
        
        if stock_data:
            first_stock = list(stock_data.values())[0]
            print(f"   第一只股票: {first_stock.get('name')} ({first_stock.get('code')})")
            print(f"   价格: {first_stock.get('price')}")
            print(f"   卖出信号: {first_stock.get('sell_signal')}")
        
    except Exception as e:
        print(f"❌ 缓存文件读取失败: {e}")

def test_imported_list():
    """测试导入列表"""
    print("\n📋 检查导入列表...")
    
    try:
        with open('imported_stock_list.json', 'r', encoding='utf-8') as f:
            list_data = json.load(f)
        
        imported_list = list_data.get('imported_stock_list', [])
        print(f"✅ 导入列表读取成功")
        print(f"   股票数量: {len(imported_list)}")
        
        if imported_list:
            first_stock = imported_list[0]
            print(f"   第一只股票: {first_stock.get('名称')} ({first_stock.get('代码')})")
            print(f"   持仓数量: {first_stock.get('持仓数量')}")
        
    except Exception as e:
        print(f"❌ 导入列表读取失败: {e}")

if __name__ == "__main__":
    print("🧪 API调试工具")
    print("=" * 50)
    
    test_cache_files()
    test_imported_list()
    test_api()
