#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask, render_template, jsonify, request
import pandas as pd
from datetime import datetime
import threading
import time

# 导入第二版模块
from eastmoney_api_v2 import get_eastmoney_stock_data_v2, get_stock_industry_info, load_stock_codes_from_excel, save_to_csv_v2
from wechat_alert_v2 import WeChatAlertV2

app = Flask(__name__)

# 全局变量
stock_data = []
last_update_time = None
is_updating = False

# 企业微信配置
WEBHOOK_URL = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7bddd706-ef0f-4d6c-9e1f-3cc8a0a18d9e'
wechat_alert = WeChatAlertV2(WEBHOOK_URL)

def get_sample_data():
    """获取样本数据用于测试"""
    sample_codes = ['600036', '000001', '600519', '000002', '600000']
    results = []
    
    for code in sample_codes:
        print(f"获取 {code} 数据...")
        
        # 获取基础数据
        stock_data = get_eastmoney_stock_data_v2(code)
        if stock_data:
            # 获取行业信息
            industry_info = get_stock_industry_info(code)
            stock_data.update(industry_info)
            
            # 模拟年内最低价数据
            stock_data['yearly_low'] = stock_data['price'] * 0.6
            stock_data['low_date'] = '2024-02-15'
            stock_data['distance_from_low_pct'] = ((stock_data['price'] - stock_data['yearly_low']) / stock_data['yearly_low']) * 100
            
            results.append(stock_data)
            print(f"✅ {code} {stock_data['name']} - 行业:{stock_data.get('industry', '未知')} 股息率:{stock_data.get('dividend_yield', 0):.2f}%")
        
        time.sleep(0.5)
    
    return results

def update_stock_data():
    """更新股票数据"""
    global stock_data, last_update_time, is_updating
    
    if is_updating:
        return {"status": "error", "message": "正在更新中，请稍候..."}
    
    is_updating = True
    
    try:
        print("开始更新股票数据...")
        
        # 使用样本数据进行测试
        new_data = get_sample_data()
        
        if new_data:
            stock_data = new_data
            last_update_time = datetime.now()
            
            print(f"✅ 成功更新 {len(stock_data)} 只股票数据")
            return {"status": "success", "message": f"成功更新 {len(stock_data)} 只股票数据"}
        else:
            return {"status": "error", "message": "获取股票数据失败"}
            
    except Exception as e:
        print(f"更新股票数据失败: {e}")
        return {"status": "error", "message": f"更新失败: {str(e)}"}
    finally:
        is_updating = False

@app.route('/')
def index():
    """主页"""
    return render_template('comprehensive_v2.html')

@app.route('/api/stocks')
def get_stocks():
    """获取股票数据API"""
    global stock_data, last_update_time
    
    # 计算统计信息
    stats = {
        'total': len(stock_data),
        'up': len([s for s in stock_data if s.get('change_pct', 0) > 0]),
        'down': len([s for s in stock_data if s.get('change_pct', 0) < 0]),
        'flat': len([s for s in stock_data if s.get('change_pct', 0) == 0]),
        'high_gain': len([s for s in stock_data if s.get('distance_from_low_pct', 0) > 50]),
        'medium_gain': len([s for s in stock_data if 20 <= s.get('distance_from_low_pct', 0) <= 50]),
        'low_gain': len([s for s in stock_data if s.get('distance_from_low_pct', 0) < 20]),
        'last_update': last_update_time.strftime('%Y-%m-%d %H:%M:%S') if last_update_time else '未更新'
    }
    
    # 行业统计
    industry_stats = {}
    for stock in stock_data:
        industry = stock.get('industry', '未知')
        industry_stats[industry] = industry_stats.get(industry, 0) + 1
    
    # 股息率统计
    dividend_stocks = [s for s in stock_data if s.get('dividend_yield', 0) > 0]
    dividend_stats = {
        'count': len(dividend_stocks),
        'avg': sum(s['dividend_yield'] for s in dividend_stocks) / len(dividend_stocks) if dividend_stocks else 0,
        'max': max(s['dividend_yield'] for s in dividend_stocks) if dividend_stocks else 0
    }
    
    return jsonify({
        'stocks': stock_data,
        'stats': stats,
        'industry_stats': industry_stats,
        'dividend_stats': dividend_stats,
        'is_updating': is_updating
    })

@app.route('/api/update', methods=['POST'])
def update_data():
    """更新数据API"""
    def async_update():
        update_stock_data()
    
    # 异步更新
    thread = threading.Thread(target=async_update)
    thread.start()
    
    return jsonify({"status": "started", "message": "开始更新数据..."})

@app.route('/api/send_summary', methods=['POST'])
def send_summary():
    """发送每日摘要到企业微信"""
    try:
        if not stock_data:
            return jsonify({"status": "error", "message": "没有股票数据，请先更新数据"})
        
        # 发送摘要
        wechat_alert.send_daily_summary_v2(stock_data)
        
        return jsonify({"status": "success", "message": "每日摘要已发送到企业微信"})
        
    except Exception as e:
        return jsonify({"status": "error", "message": f"发送失败: {str(e)}"})

@app.route('/api/send_alert', methods=['POST'])
def send_alert():
    """发送股票提醒"""
    try:
        data = request.get_json()
        stock_code = data.get('code')
        
        if not stock_code:
            return jsonify({"status": "error", "message": "缺少股票代码"})
        
        # 查找股票数据
        stock_info = None
        for stock in stock_data:
            if stock['code'] == stock_code:
                stock_info = stock
                break
        
        if not stock_info:
            return jsonify({"status": "error", "message": "未找到该股票数据"})
        
        # 发送提醒
        wechat_alert.send_stock_alert_v2(stock_info, "手动提醒")
        
        return jsonify({"status": "success", "message": f"已发送 {stock_info['name']} 的提醒"})
        
    except Exception as e:
        return jsonify({"status": "error", "message": f"发送失败: {str(e)}"})

@app.route('/api/test_wechat', methods=['POST'])
def test_wechat():
    """测试企业微信连接"""
    try:
        success = wechat_alert.test_connection()
        if success:
            return jsonify({"status": "success", "message": "企业微信连接测试成功"})
        else:
            return jsonify({"status": "error", "message": "企业微信连接测试失败"})
    except Exception as e:
        return jsonify({"status": "error", "message": f"测试失败: {str(e)}"})

if __name__ == '__main__':
    print("=== 持仓系统 V2 (简化版) 启动 ===")
    print("新功能: 股息率 + 行业信息")
    print("使用样本数据进行测试...")
    
    # 初始化样本数据
    print("正在初始化样本数据...")
    result = update_stock_data()
    if result["status"] == "success":
        print("✅ 初始化完成")
    else:
        print(f"⚠️ 初始化警告: {result['message']}")
    
    print("\n🚀 启动Web服务器...")
    print("📱 访问地址: http://localhost:5000")
    print("📊 新增功能: 股息率分析、行业分布")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
