import requests
import re
import json

def find_real_tdx_score_api(code):
    """寻找通达信真实的评分接口"""
    try:
        # 扫雷宝主页面
        api_url = f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code={code}&color=0"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Referer': 'http://page3.tdx.com.cn:7615/',
        }
        
        print(f"🔍 分析通达信扫雷宝接口...")
        response = requests.get(api_url, headers=headers, timeout=15)
        response.encoding = 'utf-8'
        
        if response.status_code == 200:
            content = response.text
            
            # 查找所有可能的API调用
            print("📡 查找API调用:")
            
            # 查找AJAX/fetch调用
            ajax_patterns = [
                r'\.get\(["\']([^"\']+)["\']',
                r'\.post\(["\']([^"\']+)["\']',
                r'ajax\([^}]*url[^}]*["\']([^"\']+)["\']',
                r'fetch\(["\']([^"\']+)["\']',
                r'CallTQL\([^,]*,[^,]*["\']([^"\']+)["\']',
            ]
            
            for pattern in ajax_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    print(f"找到API调用: {matches}")
            
            # 查找CallTQL函数调用（通达信特有）
            print("\n🎯 查找CallTQL调用:")
            calltql_pattern = r'CallTQL\([^)]+\)'
            calltql_matches = re.findall(calltql_pattern, content)
            for match in calltql_matches:
                print(f"CallTQL: {match}")
            
            # 查找具体的评分相关API
            print("\n📊 查找评分相关API:")
            score_api_patterns = [
                r'["\']([^"\']*score[^"\']*)["\']',
                r'["\']([^"\']*rating[^"\']*)["\']',
                r'["\']([^"\']*bxb[^"\']*)["\']',
                r'["\']([^"\']*slb[^"\']*)["\']',
            ]
            
            for pattern in score_api_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    print(f"评分API: {matches}")
            
            # 尝试不同的可能接口
            print("\n🧪 测试可能的评分接口:")
            
            # 可能的评分接口列表
            possible_apis = [
                f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/score/{code}.json",
                f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/rating/{code}.json",
                f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/api/score?code={code}",
                f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/api/rating?code={code}",
                f"http://page3.tdx.com.cn:7615/api/bxb/score/{code}",
                f"http://page3.tdx.com.cn:7615/api/bxb/rating/{code}",
            ]
            
            for api_url in possible_apis:
                try:
                    print(f"测试: {api_url}")
                    test_response = requests.get(api_url, headers=headers, timeout=5)
                    if test_response.status_code == 200:
                        try:
                            data = test_response.json()
                            print(f"✅ 成功: {data}")
                        except:
                            print(f"✅ 成功(非JSON): {test_response.text[:100]}")
                    else:
                        print(f"❌ 失败: {test_response.status_code}")
                except Exception as e:
                    print(f"❌ 异常: {e}")
            
            # 查找JavaScript中的分数计算逻辑
            print("\n🔍 查找分数计算逻辑:")
            
            # 查找所有函数定义
            function_pattern = r'function\s+(\w+)\s*\([^)]*\)\s*\{'
            functions = re.findall(function_pattern, content)
            print(f"找到函数: {functions}")
            
            # 查找包含分数计算的代码段
            score_calc_patterns = [
                r'score\s*=\s*[^;]+',
                r'rating\s*=\s*[^;]+',
                r'realvalue\s*=\s*[^;]+',
                r'showvalue\s*=\s*[^;]+',
            ]
            
            for pattern in score_calc_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    print(f"分数计算: {matches}")
            
            return None
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        return None

if __name__ == '__main__':
    # 测试神思电子
    find_real_tdx_score_api('300479')
