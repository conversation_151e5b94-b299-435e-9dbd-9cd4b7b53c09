#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import glob
import pandas as pd
import json
import sqlite3
from pathlib import Path
import time

def find_huabao_data_files():
    """查找华宝证券的数据文件"""
    print("=== 查找华宝证券数据文件 ===")
    
    # 可能的数据文件位置
    possible_paths = [
        r"C:\Users\<USER>\AppData\Local\华宝证券*",
        r"C:\Users\<USER>\AppData\Roaming\华宝证券*", 
        r"C:\Program Files*\华宝证券*",
        r"C:\华宝证券*",
        r"D:\华宝证券*",
        # 通用的证券软件数据目录
        r"C:\Users\<USER>\AppData\Local\*证券*",
        r"C:\Users\<USER>\Documents\*证券*",
    ]
    
    found_paths = []
    
    for pattern in possible_paths:
        try:
            matches = glob.glob(pattern, recursive=True)
            for match in matches:
                if os.path.exists(match):
                    found_paths.append(match)
                    print(f"找到: {match}")
        except Exception as e:
            continue
    
    return found_paths

def search_data_files_in_directory(directory):
    """在指定目录中搜索数据文件"""
    print(f"\n=== 搜索目录: {directory} ===")
    
    data_files = []
    
    # 常见的数据文件扩展名
    extensions = ['*.db', '*.sqlite', '*.csv', '*.txt', '*.json', '*.xml', '*.dat']
    
    for ext in extensions:
        pattern = os.path.join(directory, '**', ext)
        try:
            files = glob.glob(pattern, recursive=True)
            for file in files:
                if os.path.getsize(file) > 0:  # 只要非空文件
                    data_files.append(file)
                    print(f"  数据文件: {file}")
        except Exception as e:
            continue
    
    return data_files

def check_clipboard_data():
    """检查剪贴板数据（如果用户复制了股票数据）"""
    print("\n=== 检查剪贴板数据 ===")
    
    try:
        import pyperclip
        clipboard_content = pyperclip.paste()
        
        if clipboard_content and len(clipboard_content) > 10:
            print("剪贴板中有数据:")
            print(clipboard_content[:200] + "..." if len(clipboard_content) > 200 else clipboard_content)
            
            # 尝试解析为股票数据
            if parse_clipboard_stock_data(clipboard_content):
                return True
        else:
            print("剪贴板为空或数据太少")
            
    except ImportError:
        print("需要安装 pyperclip: pip install pyperclip")
    except Exception as e:
        print(f"读取剪贴板失败: {e}")
    
    return False

def parse_clipboard_stock_data(content):
    """解析剪贴板中的股票数据"""
    print("\n=== 解析剪贴板股票数据 ===")
    
    lines = content.strip().split('\n')
    
    # 查找包含股票代码的行
    stock_lines = []
    for line in lines:
        # 简单的股票代码模式匹配
        if any(char.isdigit() for char in line) and len(line) > 10:
            stock_lines.append(line)
    
    if stock_lines:
        print(f"找到 {len(stock_lines)} 行可能的股票数据:")
        for i, line in enumerate(stock_lines[:5]):  # 只显示前5行
            print(f"  {i+1}. {line}")
        
        # 保存到文件
        with open("clipboard_stock_data.txt", "w", encoding="utf-8") as f:
            f.write(content)
        print("✅ 已保存剪贴板数据到: clipboard_stock_data.txt")
        return True
    
    return False

def monitor_huabao_process():
    """监控华宝证券进程的文件访问"""
    print("\n=== 监控华宝证券进程 ===")
    
    try:
        import psutil
        
        # 查找华宝证券相关进程
        huabao_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                if proc.info['name'] and '华宝' in proc.info['name']:
                    huabao_processes.append(proc)
                    print(f"找到进程: {proc.info['name']} (PID: {proc.info['pid']})")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if huabao_processes:
            # 获取进程打开的文件
            for proc in huabao_processes:
                try:
                    open_files = proc.open_files()
                    print(f"\n进程 {proc.info['name']} 打开的文件:")
                    for file in open_files:
                        print(f"  {file.path}")
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    print(f"  无法访问进程 {proc.info['pid']} 的文件")
        
        return huabao_processes
        
    except ImportError:
        print("需要安装 psutil: pip install psutil")
        return []

def main():
    print("=== 华宝证券后台数据获取 ===")
    
    # 方法1: 查找数据文件
    data_paths = find_huabao_data_files()
    
    if data_paths:
        for path in data_paths:
            search_data_files_in_directory(path)
    
    # 方法2: 检查剪贴板
    print("\n" + "="*50)
    print("提示: 如果您在华宝证券软件中选中股票数据并复制(Ctrl+C)")
    print("然后运行此脚本，我们可以解析剪贴板中的数据")
    check_clipboard_data()
    
    # 方法3: 监控进程
    print("\n" + "="*50)
    monitor_huabao_process()
    
    print("\n=== 建议的操作方式 ===")
    print("1. 【最简单】在华宝证券软件中选择股票列表，按Ctrl+A全选，然后Ctrl+C复制")
    print("2. 复制后重新运行此脚本，我们将解析剪贴板数据")
    print("3. 或者告诉我华宝证券软件的数据导出功能在哪里")
    print("4. 我们也可以尝试其他实时数据源")

if __name__ == "__main__":
    main()
