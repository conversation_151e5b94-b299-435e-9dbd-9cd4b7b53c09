#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动刷新开关是否正确工作
"""

import json
import os

def check_current_config():
    """检查当前配置"""
    print("🔍 检查当前配置...")
    
    if os.path.exists('app_config.json'):
        try:
            with open('app_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            auto_refresh = config.get('auto_refresh_on_startup', False)
            print(f"✅ 配置文件存在")
            print(f"🔄 auto_refresh_on_startup: {auto_refresh}")
            print(f"💾 保存时间: {config.get('save_time', '未知')}")
            
            return auto_refresh
        except Exception as e:
            print(f"❌ 读取配置文件失败: {e}")
            return None
    else:
        print("❌ 配置文件不存在")
        return None

def set_auto_refresh(enabled):
    """设置自动刷新开关"""
    print(f"\n⚙️ 设置自动刷新为: {'开启' if enabled else '关闭'}")
    
    try:
        # 读取现有配置
        config = {
            "stock_interval": 0.2,
            "round_interval": 600,
            "request_timeout": 15,
            "alert_threshold": 70.0,
            "auto_refresh_on_startup": enabled,
        }
        
        if os.path.exists('app_config.json'):
            with open('app_config.json', 'r', encoding='utf-8') as f:
                existing_config = json.load(f)
                config.update(existing_config)
        
        # 更新配置
        config['auto_refresh_on_startup'] = enabled
        config['save_time'] = "2025-07-24 12:30:00"
        
        # 保存配置
        with open('app_config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 配置已保存: auto_refresh_on_startup = {enabled}")
        return True
    except Exception as e:
        print(f"❌ 保存配置失败: {e}")
        return False

def show_expected_behavior():
    """显示预期行为"""
    print("\n📋 预期行为说明:")
    print("=" * 50)
    
    print("\n🔴 当自动刷新关闭时:")
    print("启动日志应显示:")
    print("  ℹ️ 启动时自动刷新已关闭，后台更新线程将跳过第一轮刷新")
    print("  🔄 第 1 轮检查 - [时间]")
    print("  ⏸️ 启动时自动刷新已关闭，跳过第一轮数据更新")
    print("  ℹ️ 后续将按正常定时更新策略执行")
    
    print("\n🟢 当自动刷新开启时:")
    print("启动日志应显示:")
    print("  🚀 启动时自动刷新已开启")
    print("  ✅ 当前为交易时间，后台更新线程将立即开始刷新数据")
    print("  🔄 第 1 轮检查 - [时间]")
    print("  ✅ 启动时自动刷新已开启，当前为交易时间，开始更新股票数据...")

def main():
    """主测试函数"""
    print("🧪 自动刷新开关测试")
    print("=" * 60)
    
    # 检查当前配置
    current_setting = check_current_config()
    
    if current_setting is None:
        print("❌ 无法读取配置，退出测试")
        return
    
    print(f"\n当前设置: {'开启' if current_setting else '关闭'}")
    
    # 提供操作选项
    print("\n🔧 操作选项:")
    print("1. 关闭自动刷新")
    print("2. 开启自动刷新")
    print("3. 查看预期行为")
    print("4. 退出")
    
    try:
        choice = input("\n请选择操作 (1-4): ").strip()
        
        if choice == "1":
            if set_auto_refresh(False):
                print("\n✅ 自动刷新已关闭")
                print("🔄 请重启持仓系统验证效果")
                show_expected_behavior()
        elif choice == "2":
            if set_auto_refresh(True):
                print("\n✅ 自动刷新已开启")
                print("🔄 请重启持仓系统验证效果")
                show_expected_behavior()
        elif choice == "3":
            show_expected_behavior()
        elif choice == "4":
            print("👋 退出测试")
        else:
            print("❌ 无效选择")
    except KeyboardInterrupt:
        print("\n👋 测试已取消")

if __name__ == "__main__":
    main()
