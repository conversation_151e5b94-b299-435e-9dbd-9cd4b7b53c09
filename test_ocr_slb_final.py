"""
最终版OCR扫雷宝评分测试
"""
import os
import re
try:
    import ocr_config
    ocr_config.setup_ocr()
    import pytesseract
    from PIL import Image
    OCR_AVAILABLE = True
    print("✅ OCR环境就绪")
except ImportError as e:
    print(f"❌ OCR环境不可用: {e}")
    OCR_AVAILABLE = False

def test_ocr_slb_score(code='300479'):
    """测试OCR识别扫雷宝评分"""
    if not OCR_AVAILABLE:
        print("❌ OCR功能不可用")
        return None
        
    print(f"🎯 测试OCR识别 {code} 的扫雷宝评分")
    print("=" * 50)
    
    # 检查截图文件
    screenshot_files = [
        f"slb_screenshot_{code}.png",
        f"扫雷宝_{code}.png", 
        f"{code}_slb.png",
        f"screenshot_{code}.png",
        f"slb_{code}.png",
        f"{code}.png",
        "slb.png",
        "screenshot.png"
    ]
    
    screenshot_path = None
    for file_path in screenshot_files:
        if os.path.exists(file_path):
            screenshot_path = file_path
            break
    
    if not screenshot_path:
        print(f"❌ 未找到 {code} 的截图文件")
        print("📸 请保存扫雷宝截图为以下任一文件名:")
        for file_path in screenshot_files[:5]:
            print(f"   - {file_path}")
        
        # 提供扫雷宝链接
        slb_url = f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code={code}&color=0"
        print(f"\n📱 扫雷宝链接: {slb_url}")
        print("📸 请打开链接，截图后保存到当前目录")
        return None
    
    print(f"🖼️ 找到截图文件: {screenshot_path}")
    
    try:
        # 加载图片
        image = Image.open(screenshot_path)
        print(f"📏 图片尺寸: {image.size}")
        
        # OCR识别 - 使用中英文混合识别
        print("🔍 开始OCR识别...")
        text = pytesseract.image_to_string(image, lang='chi_sim+eng', config='--psm 6')
        
        print("📝 OCR识别原始结果:")
        print("-" * 40)
        print(text)
        print("-" * 40)
        
        # 从OCR结果中提取分数
        score_patterns = [
            r'(\d+)分',           # "74分"
            r'评分[：:]\s*(\d+)',  # "评分：74"
            r'得分[：:]\s*(\d+)',  # "得分：74"
            r'分数[：:]\s*(\d+)',  # "分数：74"
            r'总分[：:]\s*(\d+)',  # "总分：74"
            r'(\d{1,3})\s*分',    # 数字+分
            r'(\d{1,3})\s*$',     # 单独的数字（行末）
        ]
        
        found_scores = []
        for i, pattern in enumerate(score_patterns):
            matches = re.findall(pattern, text, re.MULTILINE)
            if matches:
                print(f"🎯 模式 {i+1} 匹配到: {matches}")
                for match in matches:
                    try:
                        score = int(match)
                        if 0 <= score <= 100:
                            found_scores.append(score)
                    except ValueError:
                        continue
        
        if found_scores:
            # 如果找到多个分数，选择最合理的一个
            final_score = max(set(found_scores), key=found_scores.count)  # 选择出现最多的分数
            print(f"✅ OCR提取到评分: {final_score}分")
            
            # 验证是否是合理的扫雷宝评分
            if 60 <= final_score <= 100:
                print(f"🎉 获得合理的扫雷宝评分: {final_score}分")
            else:
                print(f"⚠️ 评分偏低，请确认截图是否正确: {final_score}分")
            
            return float(final_score)
        else:
            print("❌ OCR未能识别到有效评分")
            
            # 尝试更宽松的数字匹配
            print("🔄 尝试更宽松的数字匹配...")
            all_numbers = re.findall(r'\d+', text)
            valid_scores = [int(num) for num in all_numbers if 0 <= int(num) <= 100]
            if valid_scores:
                print(f"🔍 找到可能的分数: {valid_scores}")
                # 选择最可能的分数（通常是较大的数字）
                likely_score = max(valid_scores)
                print(f"💡 推测评分: {likely_score}分")
                return float(likely_score)
            
            return None
        
    except Exception as e:
        print(f"❌ OCR识别失败: {e}")
        return None

def create_demo_instructions():
    """创建演示说明"""
    print("\n📋 使用说明:")
    print("=" * 50)
    print("1. 打开扫雷宝页面:")
    print("   http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code=300479")
    print("\n2. 等待页面完全加载，确保评分显示")
    print("\n3. 截图保存为以下任一文件名:")
    print("   - slb_screenshot_300479.png")
    print("   - 扫雷宝_300479.png")
    print("   - 300479_slb.png")
    print("\n4. 运行此脚本进行OCR识别")
    print("\n💡 截图技巧:")
    print("   - 确保评分数字清晰可见")
    print("   - 包含完整的评分圆圈")
    print("   - 避免遮挡和模糊")

def main():
    """主函数"""
    print("🎯 扫雷宝OCR评分识别测试")
    print("=" * 60)
    
    if not OCR_AVAILABLE:
        print("❌ OCR环境不可用，请先运行 setup_ocr.py")
        return
    
    # 测试神思电子
    code = '300479'
    score = test_ocr_slb_score(code)
    
    if score is not None:
        print(f"\n🎉 最终结果: {code} 的扫雷宝评分为 {score}分")
        
        # 与已知真实值比较
        if score == 74.0:
            print("🎊 完美！与截图中的真实值完全一致！")
        elif abs(score - 74.0) <= 5:
            print("✅ 很好！与真实值接近")
        else:
            print("⚠️ 与预期值有差异，请检查截图质量")
    else:
        print(f"\n❌ 无法获取 {code} 的扫雷宝评分")
        create_demo_instructions()

if __name__ == '__main__':
    main()
