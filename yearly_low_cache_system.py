#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
年内最低价获取和缓存系统
独立运行，为持仓系统提供年内最低价数据缓存
"""

import requests
import pandas as pd
import time
import os
from datetime import datetime, timedelta
from urllib.parse import urlencode
import json

class YearlyLowCacheSystem:
    def __init__(self):
        self.config = {
            'request_timeout': 10,
            'retry_count': 3,
            'delay_between_stocks': 1.5,  # 股票间延时（秒）
            'delay_between_retries': 2.0,  # 重试间延时（秒）
        }
        
        self.cache_dir = 'cache'
        self.ensure_cache_dir()
        
        self.failed_stocks = []  # 记录获取失败的股票
        
    def ensure_cache_dir(self):
        """确保缓存目录存在"""
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)
            print(f"📁 创建缓存目录: {self.cache_dir}")
    
    def gen_secid(self, rawcode: str) -> str:
        """生成东方财富专用的secid"""
        if rawcode[0] == '6':
            return f'1.{rawcode}'  # 沪市
        return f'0.{rawcode}'      # 深市
    
    def get_yearly_low_price(self, code: str) -> dict:
        """获取股票一年内的最低价（前复权）"""
        
        # 计算一年前的日期
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365)
        
        start_date_str = start_date.strftime('%Y%m%d')
        end_date_str = end_date.strftime('%Y%m%d')
        
        secid = self.gen_secid(code)
        
        params = {
            'fields1': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13',
            'fields2': 'f51,f52,f53,f54,f55',
            'beg': start_date_str,
            'end': end_date_str,
            'rtntype': '6',
            'secid': secid,
            'klt': '101',  # 日K
            'fqt': '1',    # 前复权
        }
        
        base_url = 'https://push2his.eastmoney.com/api/qt/stock/kline/get'
        url = base_url + '?' + urlencode(params)
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; WOW64; Trident/7.0; Touch; rv:11.0) like Gecko',
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.8',
            'Referer': 'http://quote.eastmoney.com/',
        }
        
        # 重试机制
        for attempt in range(self.config['retry_count']):
            try:
                response = requests.get(url, headers=headers, timeout=self.config['request_timeout'])
                
                if response.status_code == 200:
                    json_data = response.json()
                    
                    if 'data' in json_data and json_data['data'] and 'klines' in json_data['data']:
                        klines = json_data['data']['klines']
                        
                        if klines:
                            # 找到最低价
                            min_low = float('inf')
                            low_date = None
                            
                            for kline in klines:
                                kline_data = kline.split(',')
                                if len(kline_data) >= 5:
                                    low_price = float(kline_data[4])  # 最低价
                                    if low_price < min_low:
                                        min_low = low_price
                                        low_date = kline_data[0]  # 日期
                            
                            return {
                                'yearly_low': min_low,
                                'low_date': low_date,
                                'data_points': len(klines),
                                'success': True
                            }
                
                # 如果不是最后一次尝试，等待后重试
                if attempt < self.config['retry_count'] - 1:
                    time.sleep(self.config['delay_between_retries'])
                    
            except Exception as e:
                if attempt < self.config['retry_count'] - 1:
                    time.sleep(self.config['delay_between_retries'])
                else:
                    print(f"❌ 获取 {code} 失败: {e}")
        
        return {
            'yearly_low': None,
            'low_date': None,
            'data_points': 0,
            'success': False,
            'error': '网络请求失败'
        }
    
    def load_stock_list(self, file_path='stocks_list.csv'):
        """加载股票列表"""
        try:
            if os.path.exists(file_path):
                df = pd.read_csv(file_path, encoding='utf-8-sig')
                return df.to_dict('records')
            else:
                print(f"❌ 股票列表文件不存在: {file_path}")
                return []
        except Exception as e:
            print(f"❌ 加载股票列表失败: {e}")
            return []
    
    def get_cache_filename(self, date_str=None):
        """获取缓存文件名"""
        if date_str is None:
            date_str = datetime.now().strftime('%Y%m%d')
        return os.path.join(self.cache_dir, f'yearly_low_cache_{date_str}.csv')
    
    def is_cache_valid(self, cache_file=None):
        """检查缓存是否有效（当天的缓存）"""
        if cache_file is None:
            cache_file = self.get_cache_filename()
        
        if not os.path.exists(cache_file):
            return False
        
        # 检查文件是否是今天创建的
        file_time = datetime.fromtimestamp(os.path.getmtime(cache_file))
        today = datetime.now().date()
        
        return file_time.date() == today
    
    def load_cache(self, cache_file=None):
        """加载缓存数据"""
        if cache_file is None:
            cache_file = self.get_cache_filename()
        
        try:
            if os.path.exists(cache_file):
                df = pd.read_csv(cache_file, encoding='utf-8-sig')
                print(f"✅ 加载缓存文件: {cache_file} ({len(df)} 条记录)")
                return df
            else:
                print(f"⚠️ 缓存文件不存在: {cache_file}")
                return pd.DataFrame()
        except Exception as e:
            print(f"❌ 加载缓存失败: {e}")
            return pd.DataFrame()
    
    def save_cache(self, data, cache_file=None):
        """保存缓存数据"""
        if cache_file is None:
            cache_file = self.get_cache_filename()

        try:
            df = pd.DataFrame(data)
            df.to_csv(cache_file, index=False, encoding='utf-8-sig')
            print(f"✅ 保存缓存文件: {cache_file} ({len(df)} 条记录)")
            return True
        except Exception as e:
            print(f"❌ 保存缓存失败: {e}")
            return False

    def fetch_all_yearly_lows(self, stock_list=None, force_refresh=False):
        """获取所有股票的年内最低价"""

        if stock_list is None:
            stock_list = self.load_stock_list()

        if not stock_list:
            print("❌ 没有找到股票列表")
            return False

        cache_file = self.get_cache_filename()

        # 检查缓存是否有效
        if not force_refresh and self.is_cache_valid(cache_file):
            print(f"✅ 发现有效缓存文件，跳过获取")
            cached_data = self.load_cache(cache_file)
            if not cached_data.empty:
                return True

        print(f"🚀 开始获取 {len(stock_list)} 只股票的年内最低价...")
        print(f"⚙️ 配置: 股票间延时 {self.config['delay_between_stocks']}秒, 重试 {self.config['retry_count']} 次")

        results = []
        self.failed_stocks = []
        success_count = 0

        start_time = datetime.now()

        for i, stock in enumerate(stock_list):
            code = str(stock['代码']).zfill(6)
            name = stock.get('名称', '')

            print(f"[{i+1:3d}/{len(stock_list)}] 获取 {code} {name}...", end=' ')

            # 获取年内最低价
            result = self.get_yearly_low_price(code)

            if result['success']:
                results.append({
                    '股票代码': code,
                    '股票名称': name,
                    '年内最低价': result['yearly_low'],
                    '最低价日期': result['low_date'],
                    '数据点数': result['data_points'],
                    '获取时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    '状态': '成功'
                })
                success_count += 1
                print(f"✅ 最低价: {result['yearly_low']:.2f} ({result['low_date']})")
            else:
                results.append({
                    '股票代码': code,
                    '股票名称': name,
                    '年内最低价': None,
                    '最低价日期': None,
                    '数据点数': 0,
                    '获取时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    '状态': '失败',
                    '错误信息': result.get('error', '未知错误')
                })
                self.failed_stocks.append({'code': code, 'name': name})
                print(f"❌ 获取失败")

            # 股票间延时
            if i < len(stock_list) - 1:
                time.sleep(self.config['delay_between_stocks'])

        # 保存结果
        if results:
            self.save_cache(results, cache_file)

        # 统计信息
        end_time = datetime.now()
        duration = end_time - start_time
        success_rate = success_count / len(stock_list) * 100

        print(f"\n📊 获取完成统计:")
        print(f"   ✅ 成功: {success_count}/{len(stock_list)} ({success_rate:.1f}%)")
        print(f"   ❌ 失败: {len(self.failed_stocks)} 只")
        print(f"   ⏱️ 耗时: {duration}")
        print(f"   📁 缓存文件: {cache_file}")

        if self.failed_stocks:
            print(f"\n❌ 获取失败的股票:")
            for stock in self.failed_stocks:
                print(f"   - {stock['code']} {stock['name']}")

        return success_count > 0

    def retry_failed_stocks(self):
        """重试获取失败的股票"""
        if not self.failed_stocks:
            print("✅ 没有需要重试的股票")
            return True

        print(f"🔄 重试获取 {len(self.failed_stocks)} 只失败的股票...")

        # 加载现有缓存
        cache_file = self.get_cache_filename()
        cached_data = self.load_cache(cache_file)

        if cached_data.empty:
            print("❌ 没有找到缓存数据")
            return False

        success_count = 0
        remaining_failed = []

        for i, stock in enumerate(self.failed_stocks):
            code = stock['code']
            name = stock['name']

            print(f"[{i+1:3d}/{len(self.failed_stocks)}] 重试 {code} {name}...", end=' ')

            result = self.get_yearly_low_price(code)

            if result['success']:
                # 更新缓存中的数据
                mask = cached_data['股票代码'] == code
                cached_data.loc[mask, '年内最低价'] = result['yearly_low']
                cached_data.loc[mask, '最低价日期'] = result['low_date']
                cached_data.loc[mask, '数据点数'] = result['data_points']
                cached_data.loc[mask, '获取时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                cached_data.loc[mask, '状态'] = '成功'
                cached_data.loc[mask, '错误信息'] = ''

                success_count += 1
                print(f"✅ 最低价: {result['yearly_low']:.2f} ({result['low_date']})")
            else:
                remaining_failed.append(stock)
                print(f"❌ 仍然失败")

            # 延时
            if i < len(self.failed_stocks) - 1:
                time.sleep(self.config['delay_between_stocks'])

        # 保存更新后的缓存
        if success_count > 0:
            cached_data.to_csv(cache_file, index=False, encoding='utf-8-sig')
            print(f"✅ 更新缓存文件: {success_count} 只股票")

        # 更新失败列表
        self.failed_stocks = remaining_failed

        print(f"\n📊 重试结果:")
        print(f"   ✅ 成功: {success_count}")
        print(f"   ❌ 仍失败: {len(remaining_failed)}")

        return len(remaining_failed) == 0

    def get_cache_info(self):
        """获取缓存信息"""
        cache_file = self.get_cache_filename()

        if not os.path.exists(cache_file):
            return {
                'exists': False,
                'file_path': cache_file,
                'message': '缓存文件不存在'
            }

        try:
            # 文件信息
            file_stat = os.stat(cache_file)
            file_size = file_stat.st_size
            file_time = datetime.fromtimestamp(file_stat.st_mtime)

            # 数据信息
            df = pd.read_csv(cache_file, encoding='utf-8-sig')
            total_count = len(df)
            success_count = len(df[df['状态'] == '成功'])
            failed_count = total_count - success_count

            return {
                'exists': True,
                'file_path': cache_file,
                'file_size': file_size,
                'file_time': file_time.strftime('%Y-%m-%d %H:%M:%S'),
                'is_today': file_time.date() == datetime.now().date(),
                'total_count': total_count,
                'success_count': success_count,
                'failed_count': failed_count,
                'success_rate': success_count / total_count * 100 if total_count > 0 else 0
            }
        except Exception as e:
            return {
                'exists': True,
                'file_path': cache_file,
                'error': str(e),
                'message': '读取缓存文件失败'
            }

    def clean_old_cache(self, keep_days=7):
        """清理旧的缓存文件"""
        if not os.path.exists(self.cache_dir):
            return

        cutoff_date = datetime.now() - timedelta(days=keep_days)
        cleaned_count = 0

        for filename in os.listdir(self.cache_dir):
            if filename.startswith('yearly_low_cache_') and filename.endswith('.csv'):
                file_path = os.path.join(self.cache_dir, filename)
                file_time = datetime.fromtimestamp(os.path.getmtime(file_path))

                if file_time < cutoff_date:
                    try:
                        os.remove(file_path)
                        cleaned_count += 1
                        print(f"🗑️ 删除旧缓存: {filename}")
                    except Exception as e:
                        print(f"❌ 删除失败 {filename}: {e}")

        if cleaned_count > 0:
            print(f"✅ 清理完成，删除了 {cleaned_count} 个旧缓存文件")
        else:
            print("✅ 没有需要清理的旧缓存文件")


def main():
    """主程序"""
    import argparse

    parser = argparse.ArgumentParser(description='年内最低价获取和缓存系统')
    parser.add_argument('--force', '-f', action='store_true', help='强制刷新缓存')
    parser.add_argument('--retry', '-r', action='store_true', help='重试失败的股票')
    parser.add_argument('--info', '-i', action='store_true', help='显示缓存信息')
    parser.add_argument('--clean', '-c', action='store_true', help='清理旧缓存文件')
    parser.add_argument('--stock-file', '-s', default='stocks_list.csv', help='股票列表文件路径')

    args = parser.parse_args()

    # 创建缓存系统实例
    cache_system = YearlyLowCacheSystem()

    print("=== 📊 年内最低价缓存系统 ===")
    print(f"📁 缓存目录: {cache_system.cache_dir}")

    # 显示缓存信息
    if args.info:
        info = cache_system.get_cache_info()
        print(f"\n📋 缓存信息:")
        print(f"   文件路径: {info['file_path']}")

        if info['exists']:
            if 'error' in info:
                print(f"   ❌ 错误: {info['error']}")
            else:
                print(f"   📊 总数量: {info['total_count']}")
                print(f"   ✅ 成功: {info['success_count']}")
                print(f"   ❌ 失败: {info['failed_count']}")
                print(f"   📈 成功率: {info['success_rate']:.1f}%")
                print(f"   🕐 更新时间: {info['file_time']}")
                print(f"   📅 是否今日: {'是' if info['is_today'] else '否'}")
                print(f"   📦 文件大小: {info['file_size']} 字节")
        else:
            print(f"   ❌ {info['message']}")
        return

    # 清理旧缓存
    if args.clean:
        cache_system.clean_old_cache()
        return

    # 重试失败的股票
    if args.retry:
        cache_system.retry_failed_stocks()
        return

    # 获取年内最低价数据
    stock_list = cache_system.load_stock_list(args.stock_file)
    if not stock_list:
        print(f"❌ 无法加载股票列表: {args.stock_file}")
        return

    print(f"📈 股票列表: {len(stock_list)} 只股票")

    # 执行获取
    success = cache_system.fetch_all_yearly_lows(stock_list, args.force)

    if success:
        print("\n✅ 年内最低价缓存更新完成！")

        # 显示最终统计
        info = cache_system.get_cache_info()
        if info['exists'] and 'error' not in info:
            print(f"📊 最终统计: 成功 {info['success_count']}/{info['total_count']} ({info['success_rate']:.1f}%)")
    else:
        print("\n❌ 年内最低价缓存更新失败！")


if __name__ == '__main__':
    main()
