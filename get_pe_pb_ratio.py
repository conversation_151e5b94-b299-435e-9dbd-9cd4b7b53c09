#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
从东方财富获取股票市净率(PB)和TTM市盈率(PE)的脚本
"""

import requests
import time
import json
import pandas as pd
from datetime import datetime

def get_stock_pe_pb_ratio(stock_code):
    """
    从东方财富获取单只股票的市净率和TTM市盈率
    
    Args:
        stock_code: 股票代码，如 '000001'
    
    Returns:
        dict: 包含市净率和TTM市盈率的字典
    """
    
    # 根据股票代码确定市场
    if stock_code.startswith('6'):
        secid = f'1.{stock_code}'  # 上海
    else:
        secid = f'0.{stock_code}'  # 深圳
    
    url = 'http://push2.eastmoney.com/api/qt/stock/get'
    
    # 请求参数 - 重点是f23(市净率)和f115(TTM市盈率)
    params = {
        'fltt': '2',
        'invt': '2',
        'fields': 'f57,f58,f23,f115,f9,f114',  # f57:代码, f58:名称, f23:市净率, f115:TTM市盈率, f9:动态市盈率, f114:静态市盈率
        'secid': secid,
        '_': str(int(time.time() * 1000))
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'http://quote.eastmoney.com/',
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = json.loads(response.text)['data']
            
            if data:
                return {
                    'code': data['f57'],                    # 股票代码
                    'name': data['f58'],                    # 股票名称
                    'pb_ratio': data['f23'],                # 市净率
                    'pe_ttm': data['f115'],                 # TTM市盈率
                    'pe_dynamic': data['f9'],               # 动态市盈率
                    'pe_static': data['f114'],              # 静态市盈率
                    'update_time': datetime.now().strftime('%H:%M:%S')
                }
        
        return None
        
    except Exception as e:
        print(f"获取股票 {stock_code} 市盈率市净率失败: {e}")
        return None

def get_multiple_stocks_pe_pb(stock_codes, delay=0.5):
    """
    批量获取多只股票的市净率和TTM市盈率
    
    Args:
        stock_codes: 股票代码列表
        delay: 请求间隔时间（秒）
    
    Returns:
        list: 股票数据列表
    """
    
    results = []
    
    print(f"开始获取 {len(stock_codes)} 只股票的市盈率和市净率...")
    
    for i, code in enumerate(stock_codes):
        print(f"[{i+1:3d}/{len(stock_codes)}] 正在获取 {code}...", end=' ')
        
        data = get_stock_pe_pb_ratio(code)
        
        if data:
            results.append(data)
            print(f"✅ {data['name']} PB:{data['pb_ratio']} PE(TTM):{data['pe_ttm']}")
        else:
            print(f"❌ 获取失败")
        
        # 避免请求过于频繁
        if i < len(stock_codes) - 1:
            time.sleep(delay)
    
    return results

def load_stock_codes_from_csv():
    """从CSV文件加载股票代码"""
    try:
        df = pd.read_csv('stocks_list.csv', encoding='utf-8-sig')
        stock_codes = df['代码'].astype(str).str.zfill(6).tolist()
        print(f"从CSV文件加载了 {len(stock_codes)} 只股票代码")
        return stock_codes
    except Exception as e:
        print(f"加载股票代码失败: {e}")
        return []

def save_to_csv(data_list, filename=None):
    """保存数据到CSV文件"""
    if not data_list:
        print("没有数据可保存")
        return False
    
    if filename is None:
        filename = f"pe_pb_ratio_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    df = pd.DataFrame(data_list)
    
    # 重新排列列的顺序
    columns_order = ['code', 'name', 'pb_ratio', 'pe_ttm', 'pe_dynamic', 'pe_static', 'update_time']
    df = df[columns_order]
    
    # 添加中文列名
    df.columns = ['股票代码', '股票名称', '市净率(PB)', 'TTM市盈率', '动态市盈率', '静态市盈率', '更新时间']
    
    df.to_csv(filename, index=False, encoding='utf-8-sig')
    print(f"✅ 数据已保存到 {filename}")
    return True

def analyze_pe_pb_data(data_list):
    """分析市盈率市净率数据"""
    if not data_list:
        print("没有数据可分析")
        return
    
    df = pd.DataFrame(data_list)
    
    # 过滤掉无效数据（负值或空值）
    valid_pb = df[(df['pb_ratio'] > 0) & (df['pb_ratio'].notna())]
    valid_pe_ttm = df[(df['pe_ttm'] > 0) & (df['pe_ttm'].notna())]
    
    print(f"\n=== 市盈率市净率分析 ===")
    print(f"总股票数: {len(data_list)}")
    print(f"有效市净率数据: {len(valid_pb)} 只")
    print(f"有效TTM市盈率数据: {len(valid_pe_ttm)} 只")
    
    if len(valid_pb) > 0:
        print(f"\n📊 市净率(PB)统计:")
        print(f"   平均值: {valid_pb['pb_ratio'].mean():.2f}")
        print(f"   中位数: {valid_pb['pb_ratio'].median():.2f}")
        print(f"   最小值: {valid_pb['pb_ratio'].min():.2f}")
        print(f"   最大值: {valid_pb['pb_ratio'].max():.2f}")
        
        # PB分布
        pb_ranges = [
            (0, 1, "低估值(PB<1)"),
            (1, 2, "合理估值(1≤PB<2)"),
            (2, 3, "偏高估值(2≤PB<3)"),
            (3, float('inf'), "高估值(PB≥3)")
        ]
        
        print(f"\n📈 市净率分布:")
        for min_val, max_val, desc in pb_ranges:
            count = len(valid_pb[(valid_pb['pb_ratio'] >= min_val) & (valid_pb['pb_ratio'] < max_val)])
            percentage = count / len(valid_pb) * 100
            print(f"   {desc}: {count} 只 ({percentage:.1f}%)")
    
    if len(valid_pe_ttm) > 0:
        print(f"\n📊 TTM市盈率统计:")
        print(f"   平均值: {valid_pe_ttm['pe_ttm'].mean():.2f}")
        print(f"   中位数: {valid_pe_ttm['pe_ttm'].median():.2f}")
        print(f"   最小值: {valid_pe_ttm['pe_ttm'].min():.2f}")
        print(f"   最大值: {valid_pe_ttm['pe_ttm'].max():.2f}")
        
        # PE分布
        pe_ranges = [
            (0, 15, "低估值(PE<15)"),
            (15, 25, "合理估值(15≤PE<25)"),
            (25, 40, "偏高估值(25≤PE<40)"),
            (40, float('inf'), "高估值(PE≥40)")
        ]
        
        print(f"\n📈 TTM市盈率分布:")
        for min_val, max_val, desc in pe_ranges:
            count = len(valid_pe_ttm[(valid_pe_ttm['pe_ttm'] >= min_val) & (valid_pe_ttm['pe_ttm'] < max_val)])
            percentage = count / len(valid_pe_ttm) * 100
            print(f"   {desc}: {count} 只 ({percentage:.1f}%)")

def test_single_stock():
    """测试单只股票"""
    print("=== 测试单只股票 ===")
    
    test_codes = ['000001', '600000', '600036', '000002', '600519']
    
    for code in test_codes:
        data = get_stock_pe_pb_ratio(code)
        if data:
            print(f"✅ {data['code']} {data['name']}")
            print(f"   市净率(PB): {data['pb_ratio']}")
            print(f"   TTM市盈率: {data['pe_ttm']}")
            print(f"   动态市盈率: {data['pe_dynamic']}")
            print(f"   静态市盈率: {data['pe_static']}")
            print()
        else:
            print(f"❌ {code} 获取失败")
        
        time.sleep(0.5)

def main():
    """主函数"""
    print("=== 东方财富股票市盈率市净率获取工具 ===")
    
    # 选择运行模式
    print("\n请选择运行模式:")
    print("1. 测试模式（获取几只测试股票）")
    print("2. 批量模式（从CSV文件获取所有持仓股票）")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == '1':
        test_single_stock()
    
    elif choice == '2':
        # 批量模式
        stock_codes = load_stock_codes_from_csv()
        
        if not stock_codes:
            print("未找到股票代码，退出程序")
            return
        
        # 获取数据
        data_list = get_multiple_stocks_pe_pb(stock_codes, delay=1.0)
        
        if data_list:
            # 保存数据
            save_to_csv(data_list)
            
            # 分析数据
            analyze_pe_pb_data(data_list)
        else:
            print("❌ 未获取到任何数据")
    
    else:
        print("无效选择，退出程序")

if __name__ == "__main__":
    main()
