from flask import Flask, render_template, jsonify
import requests
import time
import json
import pandas as pd
from datetime import datetime, timedelta
import threading
from urllib.parse import urlencode
from wechat_alert import WeChatAlert

app = Flask(__name__)

# 全局变量存储股票数据
stock_data = {}
last_update_time = None

# 配置
CONFIG = {
    'stock_interval': 0.1,      # 每只股票间隔时间（秒）
    'round_interval': 600,      # 每轮更新间隔时间（秒）
    'request_timeout': 15,      # 请求超时时间（秒）
    'alert_threshold': 70.0,    # 企业微信提醒阈值（距离最低点涨幅%）
}

# 企业微信提醒配置
WECHAT_WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7bddd706-ef0f-4d6c-9e1f-3cc8a0a18d9e"

# 初始化企业微信提醒
wechat_alert = WeChatAlert(WECHAT_WEBHOOK_URL)

def gen_secid(rawcode: str) -> str:
    """生成东方财富专用的secid"""
    if rawcode[0] == '6':
        return f'1.{rawcode}'  # 沪市
    return f'0.{rawcode}'      # 深市

def get_yearly_low_price(code: str) -> dict:
    """获取股票一年内的最低价（前复权）"""
    
    # 计算一年前的日期
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365)
    
    start_date_str = start_date.strftime('%Y%m%d')
    end_date_str = end_date.strftime('%Y%m%d')
    
    secid = gen_secid(code)
    
    params = {
        'fields1': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13',
        'fields2': 'f51,f52,f53,f54,f55',
        'beg': start_date_str,
        'end': end_date_str,
        'rtntype': '6',
        'secid': secid,
        'klt': '101',  # 日K
        'fqt': '1',    # 前复权
    }
    
    base_url = 'https://push2his.eastmoney.com/api/qt/stock/kline/get'
    url = base_url + '?' + urlencode(params)
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; WOW64; Trident/7.0; Touch; rv:11.0) like Gecko',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.8',
        'Referer': 'http://quote.eastmoney.com/',
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=CONFIG['request_timeout'])
        
        if response.status_code == 200:
            json_data = response.json()
            
            if 'data' in json_data and json_data['data'] and 'klines' in json_data['data']:
                klines = json_data['data']['klines']
                
                if klines:
                    # 找到最低价
                    min_low = float('inf')
                    low_date = None
                    
                    for kline in klines:
                        kline_data = kline.split(',')
                        if len(kline_data) >= 5:
                            low_price = float(kline_data[4])  # 最低价
                            if low_price < min_low:
                                min_low = low_price
                                low_date = kline_data[0]  # 日期
                    
                    return {
                        'yearly_low': min_low,
                        'low_date': low_date,
                        'data_points': len(klines)
                    }
        
        return {'yearly_low': None, 'low_date': None, 'data_points': 0}
        
    except Exception as e:
        print(f"获取股票 {code} 历史数据失败: {e}")
        return {'yearly_low': None, 'low_date': None, 'data_points': 0}

def get_single_stock_dividend_industry(code):
    """获取单只股票的股息率和行业信息"""
    if code.startswith('6'):
        secid = f'1.{code}'
    else:
        secid = f'0.{code}'

    url = 'http://push2.eastmoney.com/api/qt/ulist.np/get'
    params = {
        'fltt': '2',
        'invt': '2',
        'fields': 'f12,f14,f133,f127',  # 只获取股息率和行业
        'secids': secid,
        '_': str(int(time.time() * 1000))
    }

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'http://quote.eastmoney.com/',
    }

    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)
        if response.status_code == 200:
            data = json.loads(response.text)
            if 'data' in data and 'diff' in data['data'] and data['data']['diff']:
                item = data['data']['diff'][0]

                # 处理股息率
                dividend_yield = item.get('f133', None)
                if dividend_yield == '-' or dividend_yield == 0 or dividend_yield is None:
                    dividend_yield = 0
                else:
                    try:
                        dividend_yield = float(dividend_yield)
                    except:
                        dividend_yield = 0

                # 处理行业
                industry = item.get('f127', '')
                if industry == '-' or industry is None:
                    industry = ''

                return {
                    'dividend_yield': dividend_yield,
                    'industry': industry
                }
    except Exception as e:
        print(f"获取 {code} 股息率和行业失败: {e}")

    return {'dividend_yield': 0, 'industry': ''}

def get_stock_realtime_and_valuation(stock_codes):
    """批量获取股票实时价格和估值数据"""
    
    # 构建股票代码列表
    secids = []
    for code in stock_codes:
        if code.startswith('6'):
            secids.append(f'1.{code}')
        else:
            secids.append(f'0.{code}')
    
    secids_str = ','.join(secids)
    
    url = 'http://push2.eastmoney.com/api/qt/ulist.np/get'
    
    params = {
        'fltt': '2',
        'invt': '2',
        'fields': 'f12,f14,f2,f3,f4,f23,f115,f114,f9,f133,f127',  # 添加f127行业字段
        'secids': secids_str,
        '_': str(int(time.time() * 1000))
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'http://quote.eastmoney.com/',
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=CONFIG['request_timeout'])
        
        if response.status_code == 200:
            data = json.loads(response.text)
            
            if 'data' in data and 'diff' in data['data']:
                results = {}
                
                for item in data['data']['diff']:
                    try:
                        code = item.get('f12', '')
                        
                        # 处理数据
                        pb_ratio = item.get('f23', None)
                        if pb_ratio == '-':
                            pb_ratio = None

                        pe_ttm = item.get('f115', None)
                        if pe_ttm == '-':
                            pe_ttm = None

                        # 处理股息率
                        dividend_yield = item.get('f133', None)
                        if dividend_yield == '-' or dividend_yield == 0:
                            dividend_yield = 0

                        # 处理行业
                        industry = item.get('f127', '')
                        if industry == '-':
                            industry = ''

                        results[code] = {
                            'code': code,
                            'name': item.get('f14', ''),
                            'price': item.get('f2', 0),
                            'change': item.get('f4', 0),
                            'change_pct': item.get('f3', 0),
                            'pb_ratio': pb_ratio,
                            'pe_ttm': pe_ttm,
                            'dividend_yield': dividend_yield,  # 新增股息率
                            'industry': industry,              # 新增行业
                            'update_time': datetime.now().strftime('%H:%M:%S')
                        }
                        
                    except Exception as e:
                        print(f"处理股票数据时出错: {e}")
                        continue
                
                return results
        
        return {}
        
    except Exception as e:
        print(f"批量获取股票数据失败: {e}")
        return {}

def load_stock_list():
    """加载股票列表"""
    try:
        df = pd.read_csv('stocks_list.csv', encoding='utf-8-sig')
        return df.to_dict('records')
    except Exception as e:
        print(f"加载股票列表失败: {e}")
        return []

def update_all_stocks():
    """更新所有股票数据"""
    global stock_data, last_update_time
    
    stock_list = load_stock_list()
    stock_codes = [str(stock['代码']).zfill(6) for stock in stock_list]
    
    print(f"\n🚀 开始更新 {len(stock_codes)} 只股票的综合数据...")
    print(f"⚙️  配置: 股票间隔 {CONFIG['stock_interval']}秒, 轮次间隔 {CONFIG['round_interval']}秒")
    
    # 第一步：批量获取实时价格和估值数据
    print("📊 第一步：获取实时价格和估值数据...")
    realtime_data = get_stock_realtime_and_valuation(stock_codes)
    
    if not realtime_data:
        print("❌ 无法获取实时数据")
        return
    
    print(f"✅ 成功获取 {len(realtime_data)} 只股票的实时数据")
    
    # 第二步：逐个获取一年内最低价
    print("📈 第二步：获取一年内最低价数据...")
    
    stock_data.clear()
    success_count = 0
    
    for i, stock in enumerate(stock_list):
        code = str(stock['代码']).zfill(6)
        
        print(f"[{i+1:3d}/{len(stock_list)}] 正在处理 {code}...", end=' ')
        
        # 获取实时数据
        if code in realtime_data:
            real_data = realtime_data[code]
            
            # 获取历史最低价
            historical_data = get_yearly_low_price(code)

            # 获取股息率和行业信息
            dividend_industry_data = get_single_stock_dividend_industry(code)

            # 计算距离最低价的涨幅
            yearly_low = historical_data.get('yearly_low')
            current_price = real_data['price']

            if yearly_low and yearly_low > 0 and current_price > 0:
                distance_from_low_pct = (current_price - yearly_low) / yearly_low * 100
            else:
                distance_from_low_pct = None
            
            stock_data[code] = {
                'code': code,
                'name': real_data['name'],
                'price': current_price,
                'change': real_data['change'],
                'change_pct': real_data['change_pct'],
                'yearly_low': yearly_low,
                'low_date': historical_data.get('low_date'),
                'distance_from_low_pct': distance_from_low_pct,
                'pb_ratio': real_data['pb_ratio'],
                'pe_ttm': real_data['pe_ttm'],
                'dividend_yield': dividend_industry_data['dividend_yield'],  # 使用正确的股息率
                'industry': dividend_industry_data['industry'],              # 使用正确的行业
                'data_points': historical_data.get('data_points', 0),
                'update_time': datetime.now().strftime('%H:%M:%S')
            }
            
            success_count += 1
            
            # 显示结果
            dividend_str = f"{dividend_industry_data['dividend_yield']:.2f}%" if dividend_industry_data['dividend_yield'] > 0 else "无"
            industry_str = dividend_industry_data['industry'] if dividend_industry_data['industry'] else "未知"

            if yearly_low:
                print(f"✅ {real_data['name']} 价格:{current_price} 年内最低:{yearly_low:.2f} 距最低:{distance_from_low_pct:+.1f}% 股息率:{dividend_str} 行业:{industry_str}")
            else:
                print(f"⚠️  {real_data['name']} 价格:{current_price} 年内最低:无数据 股息率:{dividend_str} 行业:{industry_str}")
        else:
            # 如果没有实时数据，使用基本信息
            stock_data[code] = {
                'code': code,
                'name': stock['名称'],
                'price': 0, 'change': 0, 'change_pct': 0,
                'yearly_low': None, 'low_date': None, 'distance_from_low_pct': None,
                'pb_ratio': None, 'pe_ttm': None, 'data_points': 0,
                'update_time': datetime.now().strftime('%H:%M:%S'),
                'error': '暂无数据'
            }
            print(f"❌ 获取失败")
        
        # 每只股票间隔时间
        if i < len(stock_list) - 1:
            time.sleep(CONFIG['stock_interval'])
    
    last_update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    success_rate = success_count / len(stock_list) * 100

    print(f"\n✅ 更新完成！")
    print(f"   📈 成功率: {success_rate:.1f}% ({success_count}/{len(stock_list)})")
    print(f"   🕐 更新时间: {last_update_time}")

    # 检查高涨幅股票并发送企业微信提醒
    try:
        valid_stocks = [stock for stock in stock_data.values() if stock.get('distance_from_low_pct') is not None]
        if valid_stocks:
            high_gain_stocks = wechat_alert.check_and_alert_high_gains(valid_stocks, CONFIG['alert_threshold'])
            if high_gain_stocks:
                print(f"🚀 发现 {len(high_gain_stocks)} 只高涨幅股票，已发送企业微信提醒")
    except Exception as e:
        print(f"❌ 企业微信提醒发送失败: {e}")

def background_update():
    """后台定时更新股票数据"""
    round_count = 0
    
    while True:
        round_count += 1
        print(f"\n{'='*60}")
        print(f"🔄 第 {round_count} 轮更新开始 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*60}")
        
        update_all_stocks()
        
        print(f"\n⏰ 下一轮更新将在 {CONFIG['round_interval']} 秒后开始")
        print(f"   (约 {CONFIG['round_interval']/60:.1f} 分钟后)")
        
        time.sleep(CONFIG['round_interval'])

@app.route('/')
def index():
    """主页"""
    return render_template('持仓页面_v1.html')

@app.route('/api/stocks')
def get_stocks():
    """获取所有股票数据API"""
    return jsonify({
        'stocks': list(stock_data.values()),
        'last_update': last_update_time,
        'total_count': len(stock_data),
        'config': CONFIG
    })

@app.route('/api/stock/<stock_code>')
def get_stock(stock_code):
    """获取单只股票数据API"""
    if stock_code in stock_data:
        return jsonify(stock_data[stock_code])
    else:
        return jsonify({'error': '股票代码不存在'}), 404

@app.route('/api/refresh')
def refresh_data():
    """手动刷新数据"""
    try:
        threading.Thread(target=update_all_stocks, daemon=True).start()
        return jsonify({
            'success': True,
            'message': '数据刷新已启动',
            'last_update': last_update_time,
            'total_count': len(stock_data)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'刷新失败: {str(e)}'
        }), 500

@app.route('/api/wechat/test')
def test_wechat():
    """测试企业微信连接"""
    try:
        success = wechat_alert.test_connection()
        return jsonify({
            'success': success,
            'message': '企业微信连接测试成功' if success else '企业微信连接测试失败'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'测试失败: {str(e)}'
        }), 500

@app.route('/api/wechat/summary')
def send_wechat_summary():
    """发送每日摘要到企业微信"""
    try:
        valid_stocks = [stock for stock in stock_data.values() if stock.get('distance_from_low_pct') is not None]
        if valid_stocks:
            wechat_alert.send_daily_summary(valid_stocks)
            return jsonify({
                'success': True,
                'message': f'每日摘要已发送，包含 {len(valid_stocks)} 只股票'
            })
        else:
            return jsonify({
                'success': False,
                'message': '暂无有效股票数据'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'发送失败: {str(e)}'
        }), 500

@app.route('/api/wechat/alert/<float:threshold>')
def manual_alert_check(threshold):
    """手动检查高涨幅股票并发送提醒"""
    try:
        valid_stocks = [stock for stock in stock_data.values() if stock.get('distance_from_low_pct') is not None]
        if valid_stocks:
            # 临时清空已发送记录，确保能发送
            wechat_alert.clear_sent_alerts()
            high_gain_stocks = wechat_alert.check_and_alert_high_gains(valid_stocks, threshold)

            if high_gain_stocks:
                return jsonify({
                    'success': True,
                    'message': f'发现 {len(high_gain_stocks)} 只高涨幅股票，提醒已发送',
                    'stocks': [{'code': s['code'], 'name': s['name'], 'gain': s['distance_from_low_pct']} for s in high_gain_stocks]
                })
            else:
                return jsonify({
                    'success': True,
                    'message': f'暂无涨幅超过 {threshold}% 的股票'
                })
        else:
            return jsonify({
                'success': False,
                'message': '暂无有效股票数据'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'检查失败: {str(e)}'
        }), 500

if __name__ == '__main__':
    print("=== 🚀 启动综合股票数据实时系统 ===")
    print("📊 数据包含：最新价、一年内最低价、距最低点涨幅、市净率、TTM市盈率")
    
    # 启动后台更新线程
    print("🔄 启动后台更新线程...")
    update_thread = threading.Thread(target=background_update, daemon=True)
    update_thread.start()
    
    print("🌐 启动Flask Web服务...")
    print("🔗 访问 http://localhost:5000 查看综合股票数据")
    
    # 启动Flask应用
    app.run(debug=False, host='0.0.0.0', port=5000)
