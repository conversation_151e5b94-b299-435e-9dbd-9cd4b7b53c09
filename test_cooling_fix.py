#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试冷却器显示修复
"""

import sys
import os
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入持仓系统模块
from 持仓系统_v14 import reduction_monitor

def test_cooling_period_logic():
    """测试冷却期逻辑"""
    print("🧪 测试冷却期逻辑...")
    
    # 创建测试股票数据
    test_stock_data = {
        'code': '000001',
        'name': '平安银行',
        'is_reduced': True,
        'reduced_time': '2024-01-01 10:00:00',
        'pe_ratio': 5.5,  # 正常TTM
        'cooling_down_until': (datetime.now() + timedelta(days=300)).strftime('%Y-%m-%d'),
        'strategy_mode': 'normal'  # 初始状态为normal
    }
    
    print(f"📊 测试前状态:")
    print(f"   - 是否已减半: {test_stock_data['is_reduced']}")
    print(f"   - 冷却期至: {test_stock_data['cooling_down_until']}")
    print(f"   - 策略模式: {test_stock_data['strategy_mode']}")
    print(f"   - TTM: {test_stock_data['pe_ratio']}")
    
    # 测试冷却期检查
    is_cooling = reduction_monitor.is_in_cooling_period(test_stock_data)
    print(f"\n🔍 冷却期检查结果: {is_cooling}")
    
    # 测试策略模式更新
    strategy_mode, reason = reduction_monitor.update_strategy_mode('000001', test_stock_data)
    print(f"\n📊 策略模式更新结果:")
    print(f"   - 新策略模式: {strategy_mode}")
    print(f"   - 原因: {reason}")
    print(f"   - 数据中的策略模式: {test_stock_data['strategy_mode']}")
    
    # 验证结果
    if strategy_mode == 'cooling_down' and test_stock_data['strategy_mode'] == 'cooling_down':
        print("\n✅ 测试通过：冷却期逻辑正常工作")
        return True
    else:
        print(f"\n❌ 测试失败：期望 'cooling_down'，实际得到 '{strategy_mode}'")
        return False

def test_ttm_priority():
    """测试TTM优先级逻辑"""
    print("\n🧪 测试TTM优先级逻辑...")
    
    # 创建高TTM的测试数据
    test_stock_data = {
        'code': '000002',
        'name': '万科A',
        'is_reduced': True,
        'reduced_time': '2024-01-01 10:00:00',
        'pe_ratio': 35.0,  # 高TTM
        'cooling_down_until': (datetime.now() + timedelta(days=300)).strftime('%Y-%m-%d'),
        'strategy_mode': 'normal'
    }
    
    print(f"📊 测试前状态:")
    print(f"   - TTM: {test_stock_data['pe_ratio']} (高于30阈值)")
    print(f"   - 冷却期至: {test_stock_data['cooling_down_until']}")
    print(f"   - 策略模式: {test_stock_data['strategy_mode']}")
    
    # 测试策略模式更新（修复后应该优先冷却期）
    strategy_mode, reason = reduction_monitor.update_strategy_mode('000002', test_stock_data)
    print(f"\n📊 策略模式更新结果:")
    print(f"   - 新策略模式: {strategy_mode}")
    print(f"   - 原因: {reason}")
    
    # 验证结果（修复后应该是冷却期优先）
    if strategy_mode == 'cooling_down':
        print("\n✅ 测试通过：冷却期优先于TTM检查")
        return True
    else:
        print(f"\n❌ 测试失败：期望冷却期优先，实际得到 '{strategy_mode}'")
        return False

def test_normal_mode():
    """测试正常模式"""
    print("\n🧪 测试正常模式...")
    
    # 创建正常状态的测试数据
    test_stock_data = {
        'code': '000003',
        'name': '中国平安',
        'is_reduced': True,
        'reduced_time': '2023-01-01 10:00:00',  # 很久以前减半
        'pe_ratio': 8.0,  # 正常TTM
        'cooling_down_until': '2023-12-31',  # 冷却期已过
        'strategy_mode': 'cooling_down'  # 初始状态
    }
    
    print(f"📊 测试前状态:")
    print(f"   - TTM: {test_stock_data['pe_ratio']}")
    print(f"   - 冷却期至: {test_stock_data['cooling_down_until']} (已过期)")
    print(f"   - 策略模式: {test_stock_data['strategy_mode']}")
    
    # 测试策略模式更新
    strategy_mode, reason = reduction_monitor.update_strategy_mode('000003', test_stock_data)
    print(f"\n📊 策略模式更新结果:")
    print(f"   - 新策略模式: {strategy_mode}")
    print(f"   - 原因: {reason}")
    
    # 验证结果
    if strategy_mode == 'normal':
        print("\n✅ 测试通过：正确切换到正常模式")
        return True
    else:
        print(f"\n❌ 测试失败：期望 'normal'，实际得到 '{strategy_mode}'")
        return False

if __name__ == '__main__':
    print("🔧 开始测试冷却器显示修复...")
    
    test_results = []
    
    # 运行测试
    test_results.append(test_cooling_period_logic())
    test_results.append(test_ttm_priority())
    test_results.append(test_normal_mode())
    
    # 汇总结果
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n📊 测试结果汇总:")
    print(f"   - 通过: {passed}/{total}")
    print(f"   - 失败: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 所有测试通过！冷却器显示修复成功。")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查。")
    
    print("\n💡 修复说明:")
    print("   1. 调整了策略模式更新的优先级，冷却期检查优先于基本面检查")
    print("   2. 在股票数据更新时自动调用策略模式更新")
    print("   3. 确保已减半股票能正确显示冷却器状态")
