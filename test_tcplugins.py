"""
测试TCPlugins目录中的DLL
重点关注可能包含SAFESCORE的插件
"""
import ctypes
import os

def test_tcplugins():
    """测试TCPlugins目录中的DLL"""
    print("🎯 测试TCPlugins目录中的DLL")
    print("=" * 50)
    
    tcplugins_path = r"C:\zd_hbzq\TCPlugins"
    
    if not os.path.exists(tcplugins_path):
        print(f"❌ TCPlugins目录不存在: {tcplugins_path}")
        return False
    
    try:
        files = os.listdir(tcplugins_path)
        dll_files = [f for f in files if f.endswith('.dll')]
        
        print(f"📁 TCPlugins目录包含 {len(dll_files)} 个DLL文件:")
        for dll in dll_files:
            print(f"   📄 {dll}")
        
        # 重点测试可能包含SAFESCORE的DLL
        priority_dlls = [
            'AddinStock.dll',      # 股票相关
            'AddinService.dll',    # 服务相关
            'AddinSafeControl.dll', # 安全控制（可能包含SAFESCORE）
            'AddinCommonControl.dll', # 通用控制
        ]
        
        success_count = 0
        
        for dll_name in priority_dlls:
            if dll_name in dll_files:
                print(f"\n🔍 重点测试: {dll_name}")
                dll_path = os.path.join(tcplugins_path, dll_name)
                
                try:
                    # 尝试加载DLL
                    dll = ctypes.WinDLL(dll_path)
                    print(f"✅ 成功加载: {dll_name}")
                    success_count += 1
                    
                    # 检查SAFESCORE相关函数
                    safescore_functions = [
                        'SAFESCORE', 'SafeScore', 'GetSafeScore', 'GetSafescore',
                        'SLBSCORE', 'SlbScore', 'GetSlbScore', 'GetSlbscore',
                        'BXB', 'GetBxb', 'SLB', 'GetSlb',
                        'SCORE', 'Score', 'GetScore',
                        'RATING', 'Rating', 'GetRating',
                        'CALC', 'Calc', 'Calculate',
                        'INDICATOR', 'Indicator', 'GetIndicator',
                    ]
                    
                    found_functions = []
                    for func_name in safescore_functions:
                        if hasattr(dll, func_name):
                            found_functions.append(func_name)
                    
                    if found_functions:
                        print(f"   🎯 找到相关函数: {found_functions}")
                        
                        # 尝试调用函数
                        for func_name in found_functions:
                            try:
                                func = getattr(dll, func_name)
                                print(f"   📞 尝试调用 {func_name}...")
                                
                                # 尝试不同的函数签名
                                signatures = [
                                    # 签名1: 字符串参数，返回浮点数
                                    ([ctypes.c_char_p], ctypes.c_float),
                                    # 签名2: 字符串参数，返回整数
                                    ([ctypes.c_char_p], ctypes.c_int),
                                    # 签名3: 字符串参数，返回双精度
                                    ([ctypes.c_char_p], ctypes.c_double),
                                    # 签名4: 无参数，返回浮点数
                                    ([], ctypes.c_float),
                                ]
                                
                                for argtypes, restype in signatures:
                                    try:
                                        func.argtypes = argtypes
                                        func.restype = restype
                                        
                                        if argtypes:
                                            result = func(b'300479')  # 神思电子
                                        else:
                                            result = func()
                                        
                                        print(f"   ✅ {func_name} 调用成功: {result}")
                                        
                                        if isinstance(result, (int, float)) and 0 <= result <= 100:
                                            print(f"   🎉 获得有效评分: {result}分")
                                            return True
                                        
                                        break  # 成功调用就不尝试其他签名了
                                        
                                    except Exception as sig_e:
                                        continue  # 尝试下一个签名
                                        
                            except Exception as e:
                                print(f"   ❌ 调用 {func_name} 失败: {e}")
                    else:
                        print(f"   📝 未找到SAFESCORE相关函数")
                        
                        # 尝试列出所有可能的函数（这通常不会成功，但可以尝试）
                        print(f"   🔍 尝试探测其他函数...")
                        
                        # 常见的函数名模式
                        common_patterns = [
                            'Get', 'Set', 'Calc', 'Calculate', 'Init', 'Load', 'Save',
                            'Query', 'Request', 'Fetch', 'Retrieve', 'Process'
                        ]
                        
                        for pattern in common_patterns:
                            for i in range(10):  # 尝试一些变体
                                test_names = [
                                    f"{pattern}Data",
                                    f"{pattern}Info",
                                    f"{pattern}Value",
                                    f"{pattern}Score",
                                    f"{pattern}Rating",
                                    f"{pattern}{i}" if i > 0 else pattern,
                                ]
                                
                                for test_name in test_names:
                                    if hasattr(dll, test_name):
                                        print(f"   🔍 发现函数: {test_name}")
                        
                except Exception as e:
                    print(f"❌ 加载失败 {dll_name}: {e}")
            else:
                print(f"❌ 未找到: {dll_name}")
        
        # 如果重点DLL都失败了，尝试其他所有DLL
        if success_count == 0:
            print(f"\n🔄 重点DLL测试失败，尝试其他DLL...")
            
            for dll_name in dll_files:
                if dll_name not in priority_dlls:
                    print(f"\n🔍 测试: {dll_name}")
                    dll_path = os.path.join(tcplugins_path, dll_name)
                    
                    try:
                        dll = ctypes.WinDLL(dll_path)
                        print(f"✅ 成功加载: {dll_name}")
                        success_count += 1
                        
                        if success_count >= 3:  # 只测试前3个成功的
                            break
                            
                    except Exception as e:
                        print(f"❌ 加载失败: {e}")
        
        print(f"\n📊 总结: 成功加载 {success_count} 个DLL")
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 访问TCPlugins目录失败: {e}")
        return False

if __name__ == '__main__':
    test_tcplugins()
