#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API修复
"""

import requests
import json
import time

def test_api():
    """测试API响应"""
    print("🧪 测试API响应...")
    
    # 等待服务器启动
    for i in range(10):
        try:
            response = requests.get('http://localhost:5000/api/stocks', timeout=5)
            break
        except requests.exceptions.ConnectionError:
            print(f"等待服务器启动... ({i+1}/10)")
            time.sleep(2)
    else:
        print("❌ 无法连接到服务器")
        return False
    
    if response.status_code == 200:
        data = response.json()
        
        print(f"✅ API响应成功: HTTP {response.status_code}")
        print(f"📊 响应结构:")
        print(f"  - success: {data.get('success')}")
        print(f"  - data type: {type(data.get('data'))}")
        
        if isinstance(data.get('data'), list):
            stocks = data['data']
            print(f"  - 股票数量: {len(stocks)}")
            
            # 查找已减半股票
            reduced_stocks = [s for s in stocks if s.get('is_reduced', False)]
            print(f"  - 已减半股票: {len(reduced_stocks)}只")
            
            # 检查策略模式
            cooling_count = 0
            for stock in reduced_stocks:
                strategy_mode = stock.get('strategy_mode', 'unknown')
                if strategy_mode == 'cooling_down':
                    cooling_count += 1
                print(f"    {stock.get('code')} {stock.get('name', '')}: {strategy_mode}")
            
            print(f"  - 冷却期状态: {cooling_count}/{len(reduced_stocks)}只")
            
            return cooling_count == len(reduced_stocks)
        else:
            print(f"❌ API返回格式错误: 期望list，实际{type(data.get('data'))}")
            return False
    else:
        print(f"❌ API调用失败: HTTP {response.status_code}")
        return False

def main():
    print("🔧 测试API修复...")
    print("=" * 50)
    
    success = test_api()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 API修复成功！")
        print("✅ 返回格式正确（数组）")
        print("✅ 所有已减半股票都显示冷却期状态")
        print("\n💡 现在可以刷新浏览器页面，应该不会再出现JavaScript错误了。")
    else:
        print("⚠️ API仍有问题，需要进一步检查。")

if __name__ == '__main__':
    main()
