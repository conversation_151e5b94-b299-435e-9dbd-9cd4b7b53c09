#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试随机股票的爆雷宝分数
"""

from bxb_integration import BXBIntegration
import random
import time

def test_stocks():
    """测试股票分数"""
    # 初始化爆雷宝集成模块
    bxb = BXBIntegration()
    
    # 检测600006
    print('=== 检测 600006 ===')
    result = bxb.get_bxb_score('600006')
    if result:
        print(f'股票名称: {result["stock_name"]}')
        print(f'爆雷宝分数: {result["score"]} 分')
        print(f'风险等级: {result["risk_level"]}')
        print(f'触发风险: {result["triggered_risks"]} 项')
        print(f'显示格式: {bxb.format_for_display(result)}')
        
        if result['details']:
            print('触发的风险详情:')
            for detail in result['details']:
                print(f'  - {detail["name"]} (扣分: {detail["score"]})')
                if detail.get('reason'):
                    print(f'    原因: {detail["reason"][:100]}...' if len(detail["reason"]) > 100 else f'    原因: {detail["reason"]}')
    else:
        print('获取600006数据失败')
    
    print('\n' + '='*60)
    
    # 随机选择3个股票进行检测
    random_stocks = ['000858', '002415', '600519', '000002', '600036', '000001', '002594', '300059', '600887', '002230', '000063', '600030']
    selected_stocks = random.sample(random_stocks, 3)
    
    print(f'\n=== 随机检测3个股票: {selected_stocks} ===')
    
    results = []
    
    for i, stock_code in enumerate(selected_stocks, 1):
        print(f'\n--- 股票 {i}: {stock_code} ---')
        result = bxb.get_bxb_score(stock_code)
        if result:
            print(f'股票名称: {result["stock_name"]}')
            print(f'爆雷宝分数: {result["score"]} 分')
            print(f'风险等级: {result["risk_level"]}')
            print(f'触发风险: {result["triggered_risks"]} 项')
            
            if result['details']:
                print('主要风险:')
                for detail in result['details'][:3]:  # 只显示前3个风险
                    print(f'  - {detail["name"]} (扣分: {detail["score"]})')
            
            results.append((stock_code, result))
        else:
            print(f'获取{stock_code}数据失败')
            results.append((stock_code, None))
        
        time.sleep(0.5)  # 避免请求过快
    
    # 汇总显示
    print(f'\n=== 汇总结果 ===')
    print('股票代码 | 股票名称     | 分数 | 风险等级 | 触发风险数')
    print('-' * 65)
    
    # 先显示600006
    result_600006 = bxb.get_bxb_score('600006')
    if result_600006:
        name = result_600006['stock_name'][:10] + ('...' if len(result_600006['stock_name']) > 10 else '')
        print(f'600006   | {name:12} | {result_600006["score"]:3}分 | {result_600006["risk_level"]:6} | {result_600006["triggered_risks"]:2}项')
    
    # 显示随机选择的股票
    for code, result in results:
        if result:
            name = result['stock_name'][:10] + ('...' if len(result['stock_name']) > 10 else '')
            print(f'{code:8} | {name:12} | {result["score"]:3}分 | {result["risk_level"]:6} | {result["triggered_risks"]:2}项')
        else:
            print(f'{code:8} | 获取失败')
    
    # 风险分析
    print(f'\n=== 风险分析 ===')
    all_results = [result_600006] + [r[1] for r in results if r[1]]
    
    if all_results:
        scores = [r['score'] for r in all_results]
        avg_score = sum(scores) / len(scores)
        max_score = max(scores)
        min_score = min(scores)
        
        print(f'平均分数: {avg_score:.1f}')
        print(f'最高分数: {max_score}')
        print(f'最低分数: {min_score}')
        
        # 风险等级分布
        risk_levels = {}
        for result in all_results:
            level = result['risk_level']
            risk_levels[level] = risk_levels.get(level, 0) + 1
        
        print(f'\n风险等级分布:')
        for level, count in risk_levels.items():
            print(f'  {level}: {count}只')

if __name__ == "__main__":
    test_stocks()
