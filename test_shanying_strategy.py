#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试山鹰国际的策略逻辑修正
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入策略管理器
from 持仓系统_v14 import SellStrategyManager, calculate_sell_signal

def test_shanying_strategy():
    """测试山鹰国际策略逻辑"""
    print("🧪 测试山鹰国际策略逻辑修正...")
    
    # 山鹰国际实际数据
    shanying_data = {
        'name': '山鹰国际',
        'code': '600567',
        'price': 1.97,
        'yearly_low': 1.34,
        'unit_cost': 2.936,
        'distance_from_low_pct': 47.01,  # 关键：47%涨幅
        'pe_ratio': -23.59,              # 负TTM
        'pb_ratio': 0.71,
        'dividend_yield': 0.46,          # 低股息 ≤2%
        'scan_score': 64,                # 低扫雷 ≤70
        'profit_status': 'need_gain',
        'profit_margin': 49.04
    }
    
    print(f"\n📊 山鹰国际数据:")
    print(f"   当前价格: {shanying_data['price']}元")
    print(f"   年内最低价: {shanying_data['yearly_low']}元")
    print(f"   涨幅: {shanying_data['distance_from_low_pct']:.1f}%")
    print(f"   TTM: {shanying_data['pe_ratio']}")
    print(f"   股息率: {shanying_data['dividend_yield']:.1f}%")
    print(f"   扫雷分数: {shanying_data['scan_score']}")
    
    # 计算卖出信号
    result = calculate_sell_signal(shanying_data)
    
    print(f"\n🎯 策略评估结果:")
    print(f"   信号: {result['signal']}")
    print(f"   原因: {result['reason']}")
    print(f"   优先级: {result['priority']}")
    print(f"   来源: {result['source']}")
    
    # 分析策略逻辑
    print(f"\n🔍 策略逻辑分析:")
    print(f"   负TTM条件: {shanying_data['pe_ratio']} ≤ 0 ✅")
    print(f"   低股息条件: {shanying_data['dividend_yield']:.1f}% ≤ 2% ✅")
    print(f"   低扫雷条件: {shanying_data['scan_score']} ≤ 70 ✅")
    print(f"   涨幅47.01%:")
    print(f"     - 超过30%减半线 ✅")
    print(f"     - 距离50%清仓线还有 {50 - shanying_data['distance_from_low_pct']:.1f}%")
    
    # 预期结果
    print(f"\n✅ 预期结果:")
    print(f"   信号应该是: warning (清仓预警)")
    print(f"   策略应该是: 已超减半线，距离负TTM清仓策略触发还有{50 - shanying_data['distance_from_low_pct']:.1f}%涨幅")
    print(f"   目标价格: {shanying_data['yearly_low']} × 1.5 = {shanying_data['yearly_low'] * 1.5:.2f}元")
    
    # 验证结果
    if result['signal'] == 'warning' and '清仓策略触发' in result['reason']:
        print(f"\n🎉 策略逻辑修正成功！")
        print(f"   ✅ 正确识别为清仓预警")
        print(f"   ✅ 正确计算剩余涨幅")
    else:
        print(f"\n❌ 策略逻辑仍有问题:")
        print(f"   期望: warning + 清仓预警")
        print(f"   实际: {result['signal']} + {result['reason']}")

def test_different_stages():
    """测试不同涨幅阶段的策略表现"""
    print(f"\n🧪 测试不同涨幅阶段的策略表现...")
    
    base_data = {
        'name': '测试股票',
        'code': '000000',
        'price': 2.00,
        'yearly_low': 1.00,
        'unit_cost': 1.50,
        'pe_ratio': -10.0,      # 负TTM
        'pb_ratio': 0.8,
        'dividend_yield': 1.5,  # 低股息
        'scan_score': 60,       # 低扫雷
        'profit_status': 'profit',
        'profit_margin': 30.0
    }
    
    test_stages = [
        (27, "减半预警阶段"),
        (32, "已超减半线阶段"),
        (47, "清仓预警阶段"),
        (52, "清仓信号阶段")
    ]
    
    for gain_pct, stage_name in test_stages:
        print(f"\n--- {stage_name} ({gain_pct}%涨幅) ---")
        
        test_data = base_data.copy()
        test_data['distance_from_low_pct'] = gain_pct
        test_data['price'] = test_data['yearly_low'] * (1 + gain_pct / 100)
        
        result = calculate_sell_signal(test_data)
        
        print(f"   涨幅: {gain_pct}%")
        print(f"   信号: {result['signal']}")
        print(f"   原因: {result['reason']}")

def main():
    """主函数"""
    try:
        test_shanying_strategy()
        test_different_stages()
        print("\n✅ 策略逻辑测试完成!")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
