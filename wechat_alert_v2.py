#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
from datetime import datetime

class WeChatAlertV2:
    def __init__(self, webhook_url):
        """
        初始化企业微信机器人 - 第二版
        :param webhook_url: 企业微信机器人webhook地址
        """
        self.webhook_url = webhook_url
    
    def send_text_message(self, content):
        """发送文本消息"""
        data = {
            "msgtype": "text",
            "text": {
                "content": content
            }
        }
        
        try:
            response = requests.post(self.webhook_url, json=data, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    print("✅ 企业微信文本消息发送成功")
                    return True
                else:
                    print(f"❌ 企业微信消息发送失败: {result.get('errmsg')}")
            else:
                print(f"❌ HTTP请求失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 发送消息异常: {e}")
        
        return False
    
    def send_markdown_message(self, content):
        """发送Markdown消息"""
        data = {
            "msgtype": "markdown",
            "markdown": {
                "content": content
            }
        }
        
        try:
            response = requests.post(self.webhook_url, json=data, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    print("✅ 企业微信Markdown消息发送成功")
                    return True
                else:
                    print(f"❌ 企业微信消息发送失败: {result.get('errmsg')}")
            else:
                print(f"❌ HTTP请求失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 发送消息异常: {e}")
        
        return False
    
    def send_daily_summary_v2(self, stocks_data):
        """
        发送每日持仓摘要 - 第二版，包含股息率和行业信息
        :param stocks_data: 股票数据列表
        """
        if not stocks_data:
            self.send_text_message("📊 今日无持仓数据")
            return
        
        # 统计数据
        total_stocks = len(stocks_data)
        up_stocks = [s for s in stocks_data if s.get('change_pct', 0) > 0]
        down_stocks = [s for s in stocks_data if s.get('change_pct', 0) < 0]
        flat_stocks = [s for s in stocks_data if s.get('change_pct', 0) == 0]
        
        # 按距离年内最低价涨幅分类
        high_gain_stocks = [s for s in stocks_data if s.get('distance_from_low_pct', 0) > 50]
        medium_gain_stocks = [s for s in stocks_data if 20 <= s.get('distance_from_low_pct', 0) <= 50]
        low_gain_stocks = [s for s in stocks_data if s.get('distance_from_low_pct', 0) < 20]
        
        # 按行业分组
        industry_groups = {}
        for stock in stocks_data:
            industry = stock.get('industry', '未知')
            if industry not in industry_groups:
                industry_groups[industry] = []
            industry_groups[industry].append(stock)
        
        # 按股息率排序
        dividend_stocks = [s for s in stocks_data if s.get('dividend_yield', 0) > 0]
        dividend_stocks.sort(key=lambda x: x.get('dividend_yield', 0), reverse=True)
        
        # 构建Markdown消息
        current_time = datetime.now()
        markdown_content = f"""# 📊 持仓股票每日摘要

🕐 **更新时间:** {current_time.strftime('%Y-%m-%d %H:%M:%S')}

📈 **统计范围:** {total_stocks} 只持仓股票

## 📊 涨跌分布统计
- 🚀 **高涨幅 (>50%):** {len(high_gain_stocks)} 只
- 📈 **中等涨幅 (20%-50%):** {len(medium_gain_stocks)} 只  
- 📉 **低涨幅 (<20%):** {len(low_gain_stocks)} 只

## 🏭 行业分布TOP3"""
        
        # 显示行业分布前3
        industry_sorted = sorted(industry_groups.items(), key=lambda x: len(x[1]), reverse=True)
        for i, (industry, stocks) in enumerate(industry_sorted[:3]):
            if i < 3:
                markdown_content += f"\n{i+1}. **{industry}:** {len(stocks)} 只"
        
        # 显示涨幅榜TOP3
        markdown_content += f"\n\n## 🏆 涨幅榜 TOP3"
        top_gainers = sorted([s for s in stocks_data if s.get('distance_from_low_pct', 0) > 0], 
                           key=lambda x: x.get('distance_from_low_pct', 0), reverse=True)[:3]
        
        for i, stock in enumerate(top_gainers):
            markdown_content += f"\n{i+1}. **{stock['code']} {stock['name']}** +{stock['distance_from_low_pct']:.1f}%"
            if stock.get('industry'):
                markdown_content += f" ({stock['industry']})"
        
        # 显示高股息股票TOP3
        if dividend_stocks:
            markdown_content += f"\n\n## 💰 高股息榜 TOP3"
            for i, stock in enumerate(dividend_stocks[:3]):
                markdown_content += f"\n{i+1}. **{stock['code']} {stock['name']}** {stock['dividend_yield']:.2f}%"
                if stock.get('industry'):
                    markdown_content += f" ({stock['industry']})"
        
        # 显示价值发现（接近最低点）
        value_stocks = sorted([s for s in stocks_data if 0 < s.get('distance_from_low_pct', 0) < 10], 
                            key=lambda x: x.get('distance_from_low_pct', 0))[:3]
        
        if value_stocks:
            markdown_content += f"\n\n## 💎 价值发现 (接近最低点)"
            for i, stock in enumerate(value_stocks):
                markdown_content += f"\n{i+1}. **{stock['code']} {stock['name']}** +{stock['distance_from_low_pct']:.1f}%"
                if stock.get('dividend_yield', 0) > 0:
                    markdown_content += f" (股息率:{stock['dividend_yield']:.2f}%)"
        
        markdown_content += f"""

---
📱 **数据来源:** 东方财富网"""
        
        # 发送Markdown消息
        self.send_markdown_message(markdown_content)
    
    def send_stock_alert_v2(self, stock_data, alert_type="价格提醒"):
        """
        发送单只股票提醒 - 第二版
        :param stock_data: 股票数据
        :param alert_type: 提醒类型
        """
        
        dividend_info = ""
        if stock_data.get('dividend_yield', 0) > 0:
            dividend_info = f" | 股息率: {stock_data['dividend_yield']:.2f}%"
        
        industry_info = ""
        if stock_data.get('industry'):
            industry_info = f" | 行业: {stock_data['industry']}"
        
        content = f"""🔔 **{alert_type}**

📊 **{stock_data['code']} {stock_data['name']}**
💰 当前价格: {stock_data['price']}
📈 涨跌幅: {stock_data['change_pct']:+.2f}%
📉 年内最低: {stock_data.get('yearly_low', 'N/A')}
🚀 距最低点: +{stock_data.get('distance_from_low_pct', 0):.1f}%{dividend_info}{industry_info}

⏰ 时间: {datetime.now().strftime('%H:%M:%S')}"""
        
        self.send_markdown_message(content)
    
    def test_connection(self):
        """测试连接"""
        test_message = f"🤖 大A机器人连接测试 V2\n⏰ 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n✅ 连接正常！\n🆕 新功能: 股息率 + 行业信息"
        return self.send_text_message(test_message)

def test_wechat_v2():
    """测试企业微信功能 - 第二版"""
    
    webhook_url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7bddd706-ef0f-4d6c-9e1f-3cc8a0a18d9e'
    alert = WeChatAlertV2(webhook_url)
    
    print("=== Zz.大A机器人功能测试 V2 ===")
    
    # 1. 测试连接
    print("1. 测试连接...")
    alert.test_connection()
    
    # 2. 测试股票数据（模拟第二版数据格式）
    test_stocks = [
        {
            'code': '600036', 'name': '招商银行', 'price': 44.89, 'change_pct': -0.36,
            'yearly_low': 23.45, 'distance_from_low_pct': 91.5, 'low_date': '2024-02-15',
            'industry': '银行', 'dividend_yield': 4.46
        },
        {
            'code': '000001', 'name': '平安银行', 'price': 12.58, 'change_pct': -0.94,
            'yearly_low': 7.72, 'distance_from_low_pct': 62.9, 'low_date': '2024-01-10',
            'industry': '银行', 'dividend_yield': 4.83
        },
        {
            'code': '600519', 'name': '贵州茅台', 'price': 1443.05, 'change_pct': 0.42,
            'yearly_low': 1194.35, 'distance_from_low_pct': 20.8, 'low_date': '2024-03-05',
            'industry': '酿酒行业', 'dividend_yield': 3.57
        }
    ]
    
    print("2. 测试每日摘要...")
    alert.send_daily_summary_v2(test_stocks)
    
    print("测试完成！")

if __name__ == "__main__":
    test_wechat_v2()
