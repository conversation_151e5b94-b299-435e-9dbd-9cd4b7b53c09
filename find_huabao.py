#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pygetwindow as gw
import pyautogui
import time

def find_all_windows():
    """查找所有窗口"""
    print("=== 查找所有窗口 ===")
    
    windows = gw.getAllWindows()
    
    print(f"找到 {len(windows)} 个窗口:")
    
    for i, window in enumerate(windows):
        if window.title and len(window.title.strip()) > 0:
            print(f"{i+1:3d}. {window.title}")
            print(f"     位置: ({window.left}, {window.top})")
            print(f"     大小: {window.width}x{window.height}")
            print()

def find_huabao_windows():
    """查找可能的华宝证券窗口"""
    print("=== 查找华宝证券相关窗口 ===")
    
    windows = gw.getAllWindows()
    
    # 可能的关键词
    keywords = [
        '华宝', '证券', 'huabao', 'Huabao', 'HUABAO',
        '股票', '行情', '交易', '客户端', '终端',
        'securities', 'Securities', 'SECURITIES'
    ]
    
    found = []
    
    for window in windows:
        if window.title:
            title_lower = window.title.lower()
            for keyword in keywords:
                if keyword.lower() in title_lower:
                    found.append(window)
                    print(f"✅ 找到可能的窗口: {window.title}")
                    print(f"   位置: ({window.left}, {window.top})")
                    print(f"   大小: {window.width}x{window.height}")
                    break
    
    if not found:
        print("❌ 未找到华宝证券相关窗口")
        print("\n请检查:")
        print("1. 华宝证券软件是否已经打开")
        print("2. 软件窗口是否最小化")
        print("3. 软件标题是否包含'华宝'或'证券'等关键词")
    
    return found

def take_screenshot():
    """截取整个屏幕"""
    print("=== 截取屏幕截图 ===")
    
    try:
        screenshot = pyautogui.screenshot()
        screenshot.save("desktop_screenshot.png")
        print("✅ 已保存桌面截图: desktop_screenshot.png")
        print("请查看截图，确认华宝证券软件是否可见")
        return True
    except Exception as e:
        print(f"❌ 截图失败: {e}")
        return False

def main():
    print("=== 华宝证券软件检测 ===")
    
    # 1. 查找华宝证券窗口
    huabao_windows = find_huabao_windows()
    
    # 2. 如果没找到，显示所有窗口
    if not huabao_windows:
        print("\n" + "="*50)
        find_all_windows()
    
    # 3. 截取桌面截图
    print("\n" + "="*50)
    take_screenshot()
    
    print("\n=== 下一步建议 ===")
    if huabao_windows:
        print("✅ 找到华宝证券窗口，可以继续获取数据")
    else:
        print("❌ 未找到华宝证券窗口，请:")
        print("1. 确保华宝证券软件已打开")
        print("2. 查看桌面截图确认软件位置")
        print("3. 告诉我软件的确切窗口标题")

if __name__ == "__main__":
    main()
