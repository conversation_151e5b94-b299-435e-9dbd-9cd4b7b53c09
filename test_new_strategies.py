#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新策略系统测试脚本
测试重构后的11个卖出策略是否正常工作
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入策略管理器
from 持仓系统_v14 import SellStrategyManager, calculate_sell_signal

def test_strategy_system():
    """测试策略系统"""
    print("🧪 开始测试新策略系统...")
    
    # 初始化策略管理器
    strategy_manager = SellStrategyManager()
    
    print(f"\n📋 已加载策略数量: {len(strategy_manager.strategies)}")
    
    # 打印所有策略
    print("\n📊 策略列表:")
    for strategy_id, strategy in strategy_manager.strategies.items():
        enabled = "✅" if strategy['enabled'] else "❌"
        action = strategy.get('action', '未知')
        print(f"  {enabled} {strategy['name']} ({strategy['category']}) - {action}")
    
    # 测试数据
    test_cases = [
        {
            'name': '基础涨幅减半测试',
            'data': {
                'name': '测试股票A',
                'distance_from_low_pct': 75.0,  # 触发70%减半
                'pe_ratio': 15.0,
                'pb_ratio': 1.5,
                'change_pct': 5.0,
                'dividend_yield': 3.0,
                'scan_score': 80,
                'profit_status': 'profit',
                'profit_margin': 20.0
            }
        },
        {
            'name': '基础涨幅清仓测试',
            'data': {
                'name': '测试股票B',
                'distance_from_low_pct': 145.0,  # 触发140%清仓
                'pe_ratio': 20.0,
                'pb_ratio': 2.0,
                'change_pct': 3.0,
                'dividend_yield': 2.5,
                'scan_score': 75,
                'profit_status': 'profit',
                'profit_margin': 50.0
            }
        },
        {
            'name': '涨停高PB清仓测试',
            'data': {
                'name': '测试股票C',
                'distance_from_low_pct': 80.0,  # 触发70%涨幅
                'pe_ratio': 25.0,
                'pb_ratio': 2.0,  # 触发1.75 PB
                'change_pct': 10.0,  # 触发涨停
                'dividend_yield': 1.5,
                'scan_score': 85,
                'profit_status': 'profit',
                'profit_margin': 30.0
            }
        },
        {
            'name': '高TTM减半测试',
            'data': {
                'name': '测试股票D',
                'distance_from_low_pct': 55.0,  # 触发50%涨幅
                'pe_ratio': 35.0,  # 触发30 TTM
                'pb_ratio': 1.2,
                'change_pct': 2.0,
                'dividend_yield': 2.0,
                'scan_score': 90,
                'profit_status': 'profit',
                'profit_margin': 15.0
            }
        },
        {
            'name': '负TTM低股息低扫雷减半测试',
            'data': {
                'name': '测试股票E',
                'distance_from_low_pct': 35.0,  # 触发30%涨幅
                'pe_ratio': -5.0,  # 触发负TTM
                'pb_ratio': 0.8,
                'change_pct': 1.0,
                'dividend_yield': 1.5,  # 触发≤2%股息
                'scan_score': 65,  # 触发≤70扫雷
                'profit_status': 'profit',
                'profit_margin': 10.0
            }
        },
        {
            'name': '负TTM回本清仓测试',
            'data': {
                'name': '测试股票F',
                'distance_from_low_pct': 20.0,
                'pe_ratio': -2.0,  # 触发负TTM
                'pb_ratio': 1.0,
                'change_pct': 0.5,
                'dividend_yield': 2.5,  # 触发≤3%股息
                'scan_score': 80,
                'profit_status': 'profit',  # 触发已回本
                'profit_margin': 5.0
            }
        },
        {
            'name': '预警测试',
            'data': {
                'name': '测试股票G',
                'distance_from_low_pct': 67.0,  # 接近70%减半阈值
                'pe_ratio': 18.0,
                'pb_ratio': 1.3,
                'change_pct': 2.0,
                'dividend_yield': 3.5,
                'scan_score': 85,
                'profit_status': 'profit',
                'profit_margin': 12.0
            }
        }
    ]
    
    print(f"\n🧪 开始测试 {len(test_cases)} 个测试用例...")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试用例 {i}: {test_case['name']} ---")
        
        # 计算卖出信号
        result = calculate_sell_signal(test_case['data'])
        
        print(f"📊 测试数据:")
        print(f"   涨幅: {test_case['data']['distance_from_low_pct']:.1f}%")
        print(f"   TTM: {test_case['data']['pe_ratio']}")
        print(f"   PB: {test_case['data']['pb_ratio']}")
        print(f"   涨跌幅: {test_case['data']['change_pct']:.1f}%")
        print(f"   股息: {test_case['data']['dividend_yield']:.1f}%")
        print(f"   扫雷: {test_case['data']['scan_score']}")
        
        print(f"🎯 测试结果:")
        print(f"   信号: {result['signal']}")
        print(f"   原因: {result['reason']}")
        print(f"   优先级: {result['priority']}")
        print(f"   来源: {result['source']}")
        
        if result['strategies']:
            print(f"   触发策略数: {len(result['strategies'])}")
            for strategy in result['strategies']:
                print(f"     - {strategy.get('strategy', '未知')}: {strategy.get('action', '未知')}")

def main():
    """主函数"""
    try:
        test_strategy_system()
        print("\n✅ 策略系统测试完成!")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
