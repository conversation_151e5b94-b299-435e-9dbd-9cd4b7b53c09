#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def show_export_guide():
    """显示华宝证券数据导出指南"""
    
    print("=== 华宝证券数据导出指南 ===")
    print()
    
    print("🔍 方法1: 使用软件的导出功能")
    print("1. 在华宝证券软件中找到'持仓'或'股票列表'页面")
    print("2. 右键点击表格，查看是否有'导出'、'保存'或'复制'选项")
    print("3. 常见的导出格式: Excel(.xlsx), CSV(.csv), 文本(.txt)")
    print("4. 导出后告诉我文件位置，我来解析数据")
    print()
    
    print("📋 方法2: 复制粘贴数据")
    print("1. 在华宝证券软件中选中所有股票数据")
    print("2. 按 Ctrl+A 全选，然后 Ctrl+C 复制")
    print("3. 运行下面的命令来解析剪贴板数据:")
    print("   python get_huabao_background.py")
    print()
    
    print("🔧 方法3: 查找配置文件")
    print("1. 华宝证券可能将持仓数据保存在配置文件中")
    print("2. 常见位置:")
    print("   - C:\\Users\\<USER>\\AppData\\Local\\华宝证券\\")
    print("   - C:\\Users\\<USER>\\Documents\\华宝证券\\")
    print("   - 软件安装目录下的data或config文件夹")
    print()
    
    print("🌐 方法4: 使用其他数据源")
    print("1. 既然您有持仓Excel文件，我们可以:")
    print("   - 从Excel读取股票代码")
    print("   - 使用其他免费API获取实时价格")
    print("   - 如: 新浪财经、腾讯财经、雅虎财经等")
    print()
    
    print("⚡ 推荐方案:")
    print("由于AKSHARE接口不稳定，建议使用以下替代方案:")
    print("1. 新浪财经API (免费，稳定)")
    print("2. 腾讯财经API (免费)")
    print("3. 东方财富API")
    print()
    
    print("🎯 立即可行的方案:")
    print("1. 您先尝试在华宝证券中复制股票数据")
    print("2. 我创建一个使用新浪财经API的版本")
    print("3. 这样就不依赖华宝证券软件了")

def create_sina_finance_version():
    """创建使用新浪财经API的版本"""
    
    print("\n=== 创建新浪财经API版本 ===")
    
    sina_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import pandas as pd
from datetime import datetime
import time

def get_sina_stock_data(stock_codes):
    """使用新浪财经API获取股票数据"""
    
    # 新浪财经API URL
    # 格式: http://hq.sinajs.cn/list=股票代码
    # 上海股票代码前加sh，深圳股票代码前加sz
    
    results = []
    
    for code in stock_codes:
        try:
            # 格式化股票代码
            if code.startswith('6'):
                sina_code = f'sh{code}'
            else:
                sina_code = f'sz{code}'
            
            url = f'http://hq.sinajs.cn/list={sina_code}'
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                # 解析返回数据
                data_str = response.text
                if 'var hq_str_' in data_str:
                    # 提取数据部分
                    data_part = data_str.split('"')[1]
                    data_fields = data_part.split(',')
                    
                    if len(data_fields) >= 32:
                        stock_info = {
                            'code': code,
                            'name': data_fields[0],
                            'open': float(data_fields[1]) if data_fields[1] else 0,
                            'yesterday_close': float(data_fields[2]) if data_fields[2] else 0,
                            'price': float(data_fields[3]) if data_fields[3] else 0,
                            'high': float(data_fields[4]) if data_fields[4] else 0,
                            'low': float(data_fields[5]) if data_fields[5] else 0,
                            'volume': int(data_fields[8]) if data_fields[8] else 0,
                            'amount': float(data_fields[9]) if data_fields[9] else 0,
                            'update_time': datetime.now().strftime('%H:%M:%S')
                        }
                        
                        # 计算涨跌额和涨跌幅
                        if stock_info['yesterday_close'] > 0:
                            stock_info['change'] = stock_info['price'] - stock_info['yesterday_close']
                            stock_info['change_pct'] = (stock_info['change'] / stock_info['yesterday_close']) * 100
                        else:
                            stock_info['change'] = 0
                            stock_info['change_pct'] = 0
                        
                        results.append(stock_info)
                        print(f"✅ {code} {stock_info['name']} {stock_info['price']}")
                    
        except Exception as e:
            print(f"❌ 获取 {code} 数据失败: {e}")
            continue
        
        # 避免请求过于频繁
        time.sleep(0.1)
    
    return results

# 测试代码
if __name__ == "__main__":
    # 测试几只股票
    test_codes = ['000001', '600000', '600036']
    print("测试新浪财经API...")
    
    data = get_sina_stock_data(test_codes)
    
    if data:
        df = pd.DataFrame(data)
        print("\\n获取到的数据:")
        print(df[['code', 'name', 'price', 'change', 'change_pct']])
    else:
        print("未获取到数据")
'''
    
    # 保存新浪财经版本
    with open('sina_finance_api.py', 'w', encoding='utf-8') as f:
        f.write(sina_code)
    
    print("✅ 已创建新浪财经API版本: sina_finance_api.py")
    print("您可以运行: python sina_finance_api.py 来测试")

def main():
    show_export_guide()
    create_sina_finance_version()
    
    print("\n" + "="*60)
    print("🎯 建议您现在:")
    print("1. 运行 python sina_finance_api.py 测试新浪API")
    print("2. 如果可用，我们就用这个API替代华宝证券")
    print("3. 这样您的电脑就可以正常使用，不需要激活华宝证券窗口")

if __name__ == "__main__":
    main()
