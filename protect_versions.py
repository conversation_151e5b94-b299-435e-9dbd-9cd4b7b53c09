#!/usr/bin/env python3
"""
版本保护脚本 - 防止意外修改历史版本
"""
import os
import stat

def protect_version_files():
    """将历史版本文件设置为只读"""
    version_files = [
        '持仓系统_v1_完整版.py',
        '持仓系统_v2_智能监测版.py',
        '持仓系统_v3_持仓详情版.py',
        '持仓系统_v4_数据管理版.py',
        '持仓系统_v5_分类筛选版.py',
        '持仓系统_v6_统计分析版.py',
        '持仓系统_v7_界面优化版.py',
        '持仓系统_v8_饼图版.py',
        '持仓系统_v9_移动端适配版.py',
    ]
    
    for file in version_files:
        if os.path.exists(file):
            # 设置为只读
            os.chmod(file, stat.S_IRUSR | stat.S_IRGRP | stat.S_IROTH)
            print(f"✅ {file} 已设置为只读保护")

if __name__ == "__main__":
    protect_version_files()