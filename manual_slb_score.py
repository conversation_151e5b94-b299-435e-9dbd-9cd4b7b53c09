"""
手动输入扫雷宝评分功能
当OCR不可用时的备用方案
"""
import json
import os
from datetime import datetime

class ManualSLBScore:
    def __init__(self):
        self.score_file = "slb_scores_manual.json"
        self.scores = self.load_scores()
    
    def load_scores(self):
        """加载已保存的评分数据"""
        if os.path.exists(self.score_file):
            try:
                with open(self.score_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                return {}
        return {}
    
    def save_scores(self):
        """保存评分数据"""
        try:
            with open(self.score_file, 'w', encoding='utf-8') as f:
                json.dump(self.scores, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存失败: {e}")
            return False
    
    def get_score(self, code):
        """获取股票的扫雷宝评分"""
        if code in self.scores:
            score_data = self.scores[code]
            print(f"📊 {code} 的扫雷宝评分: {score_data['score']}分 (录入时间: {score_data['timestamp']})")
            return score_data['score']
        return None
    
    def set_score(self, code, score, stock_name=""):
        """设置股票的扫雷宝评分"""
        try:
            score = float(score)
            if not (0 <= score <= 100):
                print("❌ 评分必须在0-100之间")
                return False
            
            self.scores[code] = {
                'score': score,
                'stock_name': stock_name,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            if self.save_scores():
                print(f"✅ 已保存 {stock_name}({code}) 的扫雷宝评分: {score}分")
                return True
            else:
                print("❌ 保存失败")
                return False
                
        except ValueError:
            print("❌ 评分必须是数字")
            return False
    
    def batch_input(self):
        """批量输入评分"""
        print("🎯 批量输入扫雷宝评分")
        print("=" * 50)
        print("输入格式: 股票代码,评分,股票名称")
        print("例如: 300479,74,神思电子")
        print("输入 'quit' 退出")
        print("-" * 50)
        
        while True:
            try:
                user_input = input("请输入: ").strip()
                
                if user_input.lower() == 'quit':
                    break
                
                if not user_input:
                    continue
                
                parts = user_input.split(',')
                if len(parts) < 2:
                    print("❌ 格式错误，请使用: 代码,评分,名称")
                    continue
                
                code = parts[0].strip()
                score = parts[1].strip()
                stock_name = parts[2].strip() if len(parts) > 2 else ""
                
                # 验证股票代码
                if not (len(code) == 6 and code.isdigit()):
                    print("❌ 股票代码必须是6位数字")
                    continue
                
                self.set_score(code, score, stock_name)
                
            except KeyboardInterrupt:
                print("\n👋 退出批量输入")
                break
            except Exception as e:
                print(f"❌ 输入错误: {e}")
    
    def list_scores(self):
        """列出所有已保存的评分"""
        if not self.scores:
            print("📝 暂无保存的评分数据")
            return
        
        print("📊 已保存的扫雷宝评分:")
        print("=" * 60)
        print(f"{'股票代码':<8} {'股票名称':<12} {'评分':<6} {'录入时间':<20}")
        print("-" * 60)
        
        for code, data in sorted(self.scores.items()):
            print(f"{code:<8} {data.get('stock_name', ''):<12} {data['score']:<6.1f} {data['timestamp']:<20}")
    
    def delete_score(self, code):
        """删除指定股票的评分"""
        if code in self.scores:
            stock_name = self.scores[code].get('stock_name', '')
            del self.scores[code]
            if self.save_scores():
                print(f"✅ 已删除 {stock_name}({code}) 的评分")
                return True
        else:
            print(f"❌ 未找到 {code} 的评分数据")
        return False
    
    def interactive_menu(self):
        """交互式菜单"""
        while True:
            print("\n🎯 扫雷宝评分管理")
            print("=" * 30)
            print("1. 查看单个评分")
            print("2. 录入单个评分")
            print("3. 批量录入评分")
            print("4. 列出所有评分")
            print("5. 删除评分")
            print("6. 退出")
            print("-" * 30)
            
            choice = input("请选择操作 (1-6): ").strip()
            
            if choice == '1':
                code = input("请输入股票代码: ").strip()
                score = self.get_score(code)
                if score is None:
                    print(f"❌ 未找到 {code} 的评分")
                    
            elif choice == '2':
                code = input("请输入股票代码: ").strip()
                score = input("请输入评分 (0-100): ").strip()
                name = input("请输入股票名称 (可选): ").strip()
                self.set_score(code, score, name)
                
            elif choice == '3':
                self.batch_input()
                
            elif choice == '4':
                self.list_scores()
                
            elif choice == '5':
                code = input("请输入要删除的股票代码: ").strip()
                self.delete_score(code)
                
            elif choice == '6':
                print("👋 再见！")
                break
                
            else:
                print("❌ 无效选择，请输入1-6")

def get_manual_slb_score(code):
    """获取手动录入的扫雷宝评分"""
    manager = ManualSLBScore()
    return manager.get_score(code)

if __name__ == '__main__':
    # 运行交互式菜单
    manager = ManualSLBScore()
    
    print("🎯 扫雷宝评分手动管理工具")
    print("当OCR功能不可用时，可以手动录入真实的扫雷宝评分")
    print()
    
    # 示例数据
    print("💡 使用示例:")
    print("1. 打开扫雷宝页面: http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code=300479")
    print("2. 查看真实评分（如74分）")
    print("3. 在此工具中录入: 300479,74,神思电子")
    print()
    
    manager.interactive_menu()
