"""
测试通达信SAFESCORE指标调用
"""
import ctypes
import os
import requests
import json

def find_tdx_installation():
    """查找通达信安装路径"""
    print("🔍 查找通达信安装路径...")
    
    # 常见的通达信安装路径
    tdx_paths = [
        r"C:\new_tdx",
        r"C:\zd_zszq", 
        r"C:\zd_ghzq",
        r"C:\通达信",
        r"C:\TDX",
        r"D:\new_tdx",
        r"D:\zd_zszq",
        r"D:\通达信",
        r"D:\TDX",
    ]
    
    found_paths = []
    for path in tdx_paths:
        if os.path.exists(path):
            found_paths.append(path)
            print(f"✅ 找到通达信目录: {path}")
            
            # 查看子目录结构
            try:
                subdirs = [d for d in os.listdir(path) if os.path.isdir(os.path.join(path, d))]
                print(f"   子目录: {subdirs}")
                
                # 查找DLL目录
                dll_dirs = [os.path.join(path, d) for d in subdirs if 'dll' in d.lower() or d.startswith('T')]
                for dll_dir in dll_dirs:
                    if os.path.exists(dll_dir):
                        print(f"   DLL目录: {dll_dir}")
                        try:
                            dll_files = [f for f in os.listdir(dll_dir) if f.endswith('.dll')]
                            if dll_files:
                                print(f"     DLL文件: {dll_files[:5]}...")  # 只显示前5个
                        except:
                            pass
            except:
                pass
    
    return found_paths

def test_dll_loading():
    """测试DLL加载"""
    print("\n🧪 测试DLL加载...")
    
    # 查找通达信安装
    tdx_paths = find_tdx_installation()
    
    if not tdx_paths:
        print("❌ 未找到通达信安装目录")
        return False
    
    # 尝试加载DLL
    for base_path in tdx_paths:
        dll_dirs = [
            os.path.join(base_path, "T0002", "dlls"),
            os.path.join(base_path, "dlls"),
            os.path.join(base_path, "bin"),
        ]
        
        for dll_dir in dll_dirs:
            if not os.path.exists(dll_dir):
                continue
                
            print(f"🔍 检查DLL目录: {dll_dir}")
            
            try:
                dll_files = [f for f in os.listdir(dll_dir) if f.endswith('.dll')]
                print(f"   找到DLL文件: {len(dll_files)}个")
                
                # 尝试加载一些常见的DLL
                test_dlls = ['TdxData.dll', 'TdxCalc.dll', 'Plugin.dll']
                for dll_name in test_dlls:
                    dll_path = os.path.join(dll_dir, dll_name)
                    if os.path.exists(dll_path):
                        try:
                            dll = ctypes.WinDLL(dll_path)
                            print(f"✅ 成功加载: {dll_name}")
                            
                            # 尝试列出函数（这个可能不会成功，但可以尝试）
                            try:
                                # 检查是否有SAFESCORE相关函数
                                if hasattr(dll, 'SAFESCORE'):
                                    print(f"   找到SAFESCORE函数!")
                                elif hasattr(dll, 'SafeScore'):
                                    print(f"   找到SafeScore函数!")
                                elif hasattr(dll, 'GetSafeScore'):
                                    print(f"   找到GetSafeScore函数!")
                                else:
                                    print(f"   未找到SAFESCORE相关函数")
                            except:
                                pass
                                
                            return True
                        except Exception as e:
                            print(f"❌ 加载失败 {dll_name}: {e}")
                            
            except Exception as e:
                print(f"❌ 访问目录失败: {e}")
    
    return False

def test_safescore_api(code='300479'):
    """测试SAFESCORE API调用"""
    print(f"\n🧪 测试SAFESCORE API调用 ({code})...")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Referer': 'http://page3.tdx.com.cn:7615/',
    }
    
    # 尝试各种可能的SAFESCORE接口
    safescore_apis = [
        f"http://page3.tdx.com.cn:7615/api/safescore/{code}",
        f"http://page3.tdx.com.cn:7615/tql/safescore?code={code}",
        f"http://page3.tdx.com.cn:7615/indicator/safescore/{code}",
        f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/safescore/{code}",
        f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/safescore/{code}",
        f"http://page3.tdx.com.cn:7615/formula/safescore?code={code}",
        f"http://page3.tdx.com.cn:7615/calc/safescore/{code}",
        # 基于扫雷宝的接口
        f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/json/{code}.json",
    ]
    
    success_count = 0
    for api_url in safescore_apis:
        try:
            print(f"🔍 测试: {api_url}")
            response = requests.get(api_url, headers=headers, timeout=8)
            
            if response.status_code == 200:
                print(f"✅ 响应成功 (状态码: {response.status_code})")
                
                # 尝试解析响应
                try:
                    data = response.json()
                    print(f"   JSON数据: {data}")
                    
                    # 查找分数相关字段
                    score_fields = ['safescore', 'score', 'value', 'result', 'data', 'num', 'total']
                    for field in score_fields:
                        if field in data:
                            print(f"   找到字段 {field}: {data[field]}")
                    
                    success_count += 1
                    
                except json.JSONDecodeError:
                    text = response.text.strip()
                    print(f"   文本响应: {text[:100]}...")
                    if text and text.replace('.', '').isdigit():
                        print(f"   可能的数值: {text}")
                        success_count += 1
                        
            else:
                print(f"❌ 响应失败 (状态码: {response.status_code})")
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")
    
    print(f"\n📊 API测试结果: {success_count}/{len(safescore_apis)} 个接口响应成功")
    return success_count > 0

def main():
    """主测试函数"""
    print("🎯 通达信SAFESCORE指标测试")
    print("=" * 60)
    
    # 测试1: 查找通达信安装
    print("测试1: 查找通达信安装")
    find_tdx_installation()
    
    # 测试2: DLL加载测试
    print("\n测试2: DLL加载测试")
    dll_success = test_dll_loading()
    
    # 测试3: API调用测试
    print("\n测试3: API调用测试")
    api_success = test_safescore_api()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试总结:")
    print(f"DLL加载: {'✅ 成功' if dll_success else '❌ 失败'}")
    print(f"API调用: {'✅ 成功' if api_success else '❌ 失败'}")
    
    if dll_success or api_success:
        print("\n🎉 至少有一种方法可以工作！")
        print("💡 建议: 集成到持仓系统中使用")
    else:
        print("\n❌ 所有方法都失败了")
        print("💡 建议: 检查通达信安装或网络连接")

if __name__ == '__main__':
    main()
