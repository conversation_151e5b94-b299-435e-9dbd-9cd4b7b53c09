<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>持仓系统 V12 完整整合版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
        }
        
        .header .subtitle {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .update-info {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 5px;
            margin-top: 15px;
        }
        
        .controls {
            background: white;
            padding: 20px;
            margin: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .search-box {
            flex: 1;
            max-width: 300px;
            margin-right: 20px;
        }
        
        .search-box input {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .stats {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 0.9em;
            color: #666;
        }
        
        /* 分类筛选按钮 */
        .filter-buttons {
            background: white;
            padding: 15px 20px;
            margin: 0 20px 20px 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .filter-btn {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            color: #495057;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
        }
        
        .filter-btn:hover {
            background: #e9ecef;
            border-color: #adb5bd;
            transform: translateY(-2px);
        }
        
        .filter-btn.active {
            background: #667eea;
            border-color: #667eea;
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        
        /* 统计面板 */
        .stats-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 0 20px 20px 20px;
        }
        
        .stats-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stats-card h3 {
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .total-assets {
            border-left: 4px solid #28a745;
        }
        
        .asset-allocation {
            border-left: 4px solid #17a2b8;
        }
        
        .industry-distribution {
            border-left: 4px solid #ffc107;
        }
        
        .stats-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .stats-item:last-child {
            border-bottom: none;
        }
        
        .stats-value {
            font-weight: bold;
            color: #667eea;
        }
        
        /* 数据管理按钮 */
        .management-buttons {
            background: white;
            padding: 15px 20px;
            margin: 0 20px 20px 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .management-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        
        .management-btn:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }
        
        .management-btn.danger {
            background: #dc3545;
        }
        
        .management-btn.danger:hover {
            background: #c82333;
        }
        
        .management-btn.success {
            background: #28a745;
        }
        
        .management-btn.success:hover {
            background: #218838;
        }
        
        .management-btn.primary {
            background: #007bff;
        }
        
        .management-btn.primary:hover {
            background: #0056b3;
        }
        
        /* 表格样式 */
        .table-container {
            background: white;
            margin: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        
        th, td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #eee;
            white-space: nowrap;
        }
        
        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #495057;
            cursor: pointer;
            user-select: none;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        th:hover {
            background: #e9ecef;
        }
        
        .sort-indicator {
            margin-left: 5px;
            opacity: 0.5;
        }
        
        .sort-indicator.asc::after {
            content: '↑';
            opacity: 1;
        }
        
        .sort-indicator.desc::after {
            content: '↓';
            opacity: 1;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .positive {
            color: #dc3545;
        }
        
        .negative {
            color: #28a745;
        }
        
        .scan-score {
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                gap: 15px;
            }
            
            .search-box {
                max-width: none;
                margin-right: 0;
            }
            
            .stats {
                justify-content: center;
            }
            
            .stats-panel {
                grid-template-columns: 1fr;
                margin: 0 10px 20px 10px;
            }
            
            .filter-buttons, .management-buttons {
                margin: 0 10px 20px 10px;
            }
            
            .table-container {
                margin: 10px;
                overflow-x: auto;
            }
            
            table {
                min-width: 1200px;
            }
            
            th, td {
                padding: 8px 4px;
                font-size: 12px;
            }
            
            /* 移动端隐藏次要列 */
            .mobile-hidden {
                display: none;
            }
        }
        
        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.8em;
            }
            
            .header .subtitle {
                font-size: 0.9em;
            }
            
            .filter-btn, .management-btn {
                padding: 8px 12px;
                font-size: 12px;
                margin: 3px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 持仓系统 V12 完整整合版</h1>
        <div class="subtitle">实时股票数据监控 | 智能交易时间检测 | 扫雷风险评估 | 移动端适配</div>
        <div class="update-info">
            <span id="updateTime">等待数据加载...</span> |
            <span id="stockCount">0</span> 只股票 |
            <span id="tradingStatus">检查交易状态中...</span>
        </div>
    </div>

    <!-- 搜索和统计 -->
    <div class="controls">
        <div class="search-box">
            <input type="text" id="searchInput" placeholder="搜索股票代码或名称...">
        </div>
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number" id="totalValue">¥0</div>
                <div class="stat-label">总市值</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="stockCountStat">0</div>
                <div class="stat-label">股票数量</div>
            </div>
        </div>
    </div>

    <!-- 分类筛选按钮 -->
    <div class="filter-buttons">
        <button class="filter-btn active" data-type="all">全部</button>
        <button class="filter-btn" data-type="股票">股票</button>
        <button class="filter-btn" data-type="ETF">ETF</button>
        <button class="filter-btn" data-type="基金">基金</button>
    </div>

    <!-- 统计面板 -->
    <div class="stats-panel">
        <div class="stats-card total-assets">
            <h3>💰 总资产统计</h3>
            <div class="stats-item">
                <span>总市值</span>
                <span class="stats-value" id="totalMarketValue">¥0</span>
            </div>
        </div>

        <div class="stats-card asset-allocation">
            <h3>📊 资产配置</h3>
            <div id="assetAllocationContent">
                <div class="stats-item">
                    <span>暂无数据</span>
                    <span class="stats-value">-</span>
                </div>
            </div>
        </div>

        <div class="stats-card industry-distribution">
            <h3>🏭 行业分布</h3>
            <div id="industryDistributionContent">
                <div class="stats-item">
                    <span>暂无数据</span>
                    <span class="stats-value">-</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据管理按钮 -->
    <div class="management-buttons">
        <button class="management-btn danger" onclick="clearData()">🗑️ 清空数据</button>
        <input type="file" id="fileInput" accept=".xlsx,.xls" style="display: none;" onchange="uploadFile()">
        <button class="management-btn primary" onclick="document.getElementById('fileInput').click()">📁 导入持仓表格</button>
        <button class="management-btn success" onclick="refreshData()">🔄 重新获取数据</button>
        <button class="management-btn" onclick="checkCacheStatus()">📊 缓存状态</button>
    </div>

    <!-- 表格容器 -->
    <div class="table-container">
        <table id="stockTable">
            <thead>
                <tr>
                    <th onclick="sortTable('code')">代码 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('name')">名称 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('price')">最新价 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('change_pct')">涨跌幅 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('yearly_low')" class="mobile-hidden">年内最低价 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('low_date')" class="mobile-hidden">最低价日期 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('distance_from_low_pct')" class="mobile-hidden">距最低点涨幅 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('pb_ratio')" class="mobile-hidden">市净率 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('pe_ratio')" class="mobile-hidden">TTM市盈率 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('dividend_yield')" class="mobile-hidden">股息率 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('industry')">行业 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('scan_score')">扫雷分数 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('quantity')">持仓数量 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('market_value')">市值 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('update_time')" class="mobile-hidden">更新时间 <span class="sort-indicator"></span></th>
                </tr>
            </thead>
            <tbody id="stockTableBody">
                <tr>
                    <td colspan="15" style="text-align: center; padding: 40px;">
                        <div style="color: #666;">
                            <div style="font-size: 48px; margin-bottom: 20px;">📊</div>
                            <div style="font-size: 18px; margin-bottom: 10px;">暂无股票数据</div>
                            <div style="font-size: 14px;">请导入持仓表格或等待数据加载</div>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        let stockData = [];
        let filteredData = [];
        let currentFilter = 'all';
        let sortColumn = '';
        let sortDirection = 'asc';

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStockData();
            loadTradingStatus();

            // 设置定时刷新
            setInterval(loadStockData, 30000); // 30秒刷新一次
            setInterval(loadTradingStatus, 60000); // 1分钟刷新一次交易状态

            // 搜索功能
            document.getElementById('searchInput').addEventListener('input', function() {
                filterAndDisplayData();
            });

            // 分类筛选
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    currentFilter = this.dataset.type;
                    filterAndDisplayData();
                });
            });
        });

        // 加载股票数据
        async function loadStockData() {
            try {
                const response = await fetch('/api/stocks');
                const result = await response.json();

                if (result.success) {
                    stockData = result.data;
                    updateHeader(result);
                    updateStatistics(result.stats);
                    filterAndDisplayData();
                } else {
                    console.error('加载数据失败:', result.message);
                }
            } catch (error) {
                console.error('请求失败:', error);
            }
        }

        // 加载交易状态
        async function loadTradingStatus() {
            try {
                const response = await fetch('/api/trading-status');
                const result = await response.json();

                if (result.success) {
                    document.getElementById('tradingStatus').textContent = result.data.message;
                }
            } catch (error) {
                console.error('获取交易状态失败:', error);
            }
        }

        // 更新页面头部信息
        function updateHeader(result) {
            document.getElementById('updateTime').textContent =
                result.last_update ? `最后更新: ${result.last_update}` : '暂未更新';
            document.getElementById('stockCount').textContent = result.count;
        }

        // 更新统计信息
        function updateStatistics(stats) {
            if (!stats) return;

            // 更新总市值
            const totalValue = formatCurrency(stats.total_market_value);
            document.getElementById('totalValue').textContent = totalValue;
            document.getElementById('totalMarketValue').textContent = totalValue;
            document.getElementById('stockCountStat').textContent = stockData.length;

            // 更新资产配置
            const assetContent = document.getElementById('assetAllocationContent');
            if (stats.asset_allocation && Object.keys(stats.asset_allocation).length > 0) {
                assetContent.innerHTML = '';
                for (const [type, data] of Object.entries(stats.asset_allocation)) {
                    const item = document.createElement('div');
                    item.className = 'stats-item';
                    item.innerHTML = `
                        <span>${type} (${data.count}只)</span>
                        <span class="stats-value">${formatCurrency(data.market_value)} (${data.percentage.toFixed(1)}%)</span>
                    `;
                    assetContent.appendChild(item);
                }
            } else {
                assetContent.innerHTML = '<div class="stats-item"><span>暂无数据</span><span class="stats-value">-</span></div>';
            }

            // 更新行业分布
            const industryContent = document.getElementById('industryDistributionContent');
            if (stats.industry_distribution && Object.keys(stats.industry_distribution).length > 0) {
                industryContent.innerHTML = '';
                for (const [industry, data] of Object.entries(stats.industry_distribution)) {
                    const item = document.createElement('div');
                    item.className = 'stats-item';
                    item.innerHTML = `
                        <span>${industry} (${data.count}只)</span>
                        <span class="stats-value">${formatCurrency(data.market_value)} (${data.percentage.toFixed(1)}%)</span>
                    `;
                    industryContent.appendChild(item);
                }
            } else {
                industryContent.innerHTML = '<div class="stats-item"><span>暂无数据</span><span class="stats-value">-</span></div>';
            }
        }

        // 筛选和显示数据
        function filterAndDisplayData() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();

            // 应用筛选条件
            filteredData = stockData.filter(stock => {
                // 类型筛选
                if (currentFilter !== 'all' && stock.security_type !== currentFilter) {
                    return false;
                }

                // 搜索筛选
                if (searchTerm &&
                    !stock.code.toLowerCase().includes(searchTerm) &&
                    !stock.name.toLowerCase().includes(searchTerm)) {
                    return false;
                }

                return true;
            });

            // 应用排序
            if (sortColumn) {
                filteredData.sort((a, b) => {
                    let aVal = a[sortColumn];
                    let bVal = b[sortColumn];

                    // 处理数值类型
                    if (typeof aVal === 'number' && typeof bVal === 'number') {
                        return sortDirection === 'asc' ? aVal - bVal : bVal - aVal;
                    }

                    // 处理字符串类型
                    aVal = String(aVal || '').toLowerCase();
                    bVal = String(bVal || '').toLowerCase();

                    if (sortDirection === 'asc') {
                        return aVal.localeCompare(bVal);
                    } else {
                        return bVal.localeCompare(aVal);
                    }
                });
            }

            displayStockTable();
        }

        // 显示股票表格
        function displayStockTable() {
            const tbody = document.getElementById('stockTableBody');

            if (filteredData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="15" style="text-align: center; padding: 40px;">
                            <div style="color: #666;">
                                <div style="font-size: 48px; margin-bottom: 20px;">📊</div>
                                <div style="font-size: 18px; margin-bottom: 10px;">暂无匹配的股票数据</div>
                                <div style="font-size: 14px;">请调整筛选条件或导入持仓表格</div>
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = filteredData.map(stock => `
                <tr>
                    <td>${stock.code}</td>
                    <td>${stock.name}</td>
                    <td>${formatNumber(stock.price, 2)}</td>
                    <td class="${stock.change_pct >= 0 ? 'positive' : 'negative'}">
                        ${formatPercent(stock.change_pct)}
                    </td>
                    <td class="mobile-hidden">${formatNumber(stock.yearly_low, 2)}</td>
                    <td class="mobile-hidden">${stock.low_date || '-'}</td>
                    <td class="mobile-hidden ${stock.distance_from_low_pct >= 0 ? 'positive' : 'negative'}">
                        ${stock.distance_from_low_pct ? formatPercent(stock.distance_from_low_pct) : '-'}
                    </td>
                    <td class="mobile-hidden">${formatNumber(stock.pb_ratio, 2)}</td>
                    <td class="mobile-hidden">${formatNumber(stock.pe_ratio, 2)}</td>
                    <td class="mobile-hidden">${formatPercent(stock.dividend_yield)}</td>
                    <td>${stock.industry || '-'}</td>
                    <td>
                        <span class="scan-score" style="background-color: ${stock.scan_risk_color || '#cccccc'}">
                            ${stock.scan_display_text || '-'}
                        </span>
                    </td>
                    <td>${formatNumber(stock.quantity, 0)}</td>
                    <td>${formatCurrency(stock.market_value)}</td>
                    <td class="mobile-hidden">${stock.update_time || '-'}</td>
                </tr>
            `).join('');
        }

        // 表格排序
        function sortTable(column) {
            if (sortColumn === column) {
                sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                sortColumn = column;
                sortDirection = 'asc';
            }

            // 更新排序指示器
            document.querySelectorAll('.sort-indicator').forEach(indicator => {
                indicator.className = 'sort-indicator';
            });

            const currentIndicator = document.querySelector(`th[onclick="sortTable('${column}')"] .sort-indicator`);
            if (currentIndicator) {
                currentIndicator.className = `sort-indicator ${sortDirection}`;
            }

            filterAndDisplayData();
        }

        // 数据管理功能
        async function clearData() {
            if (!confirm('确定要清空所有数据吗？此操作不可恢复。')) {
                return;
            }

            try {
                const response = await fetch('/api/clear-data', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    alert('数据已清空');
                    stockData = [];
                    filterAndDisplayData();
                    updateStatistics({});
                } else {
                    alert('清空失败: ' + result.message);
                }
            } catch (error) {
                alert('清空失败: ' + error.message);
            }
        }

        async function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];

            if (!file) {
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            try {
                const response = await fetch('/api/upload-holdings', {
                    method: 'POST',
                    body: formData
                });
                const result = await response.json();

                if (result.success) {
                    alert(`导入成功: ${result.message}`);
                } else {
                    alert('导入失败: ' + result.message);
                }
            } catch (error) {
                alert('导入失败: ' + error.message);
            }

            fileInput.value = '';
        }

        async function refreshData() {
            if (!confirm('确定要重新获取所有股票数据吗？这可能需要几分钟时间。')) {
                return;
            }

            try {
                const response = await fetch('/api/refresh-data', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    alert(result.message);
                } else {
                    alert('更新失败: ' + result.message);
                }
            } catch (error) {
                alert('更新失败: ' + error.message);
            }
        }

        async function checkCacheStatus() {
            try {
                const [cacheResponse, scanResponse] = await Promise.all([
                    fetch('/api/cache-status'),
                    fetch('/api/scan-cache-info')
                ]);

                const cacheResult = await cacheResponse.json();
                const scanResult = await scanResponse.json();

                let message = '缓存状态信息:\n\n';

                if (cacheResult.success) {
                    const cache = cacheResult.data;
                    message += `年内最低价缓存:\n`;
                    message += `- 状态: ${cache.exists ? '存在' : '不存在'}\n`;
                    message += `- 有效: ${cache.valid ? '是' : '否'}\n`;
                    if (cache.success_rate) {
                        message += `- 成功率: ${cache.success_rate.toFixed(1)}%\n`;
                    }
                } else {
                    message += `年内最低价缓存: 获取失败\n`;
                }

                message += '\n';

                if (scanResult.success) {
                    const scan = scanResult.data;
                    message += `扫雷数据缓存:\n`;
                    message += `- 缓存股票数: ${scan.cached_stocks}\n`;
                    message += `- 缓存时长: ${scan.cache_duration}秒\n`;
                } else {
                    message += `扫雷数据缓存: 获取失败\n`;
                }

                alert(message);
            } catch (error) {
                alert('获取缓存状态失败: ' + error.message);
            }
        }

        // 工具函数
        function formatNumber(value, decimals = 2) {
            if (value === null || value === undefined || value === '') return '-';
            return Number(value).toFixed(decimals);
        }

        function formatPercent(value) {
            if (value === null || value === undefined || value === '') return '-';
            return Number(value).toFixed(2) + '%';
        }

        function formatCurrency(value) {
            if (value === null || value === undefined || value === '' || value === 0) return '¥0';
            return '¥' + Number(value).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
        }
    </script>
</body>
</html>
