# 持仓系统版本说明

## 📋 开发规则

### 🇨🇳 中文命名规则
- 所有文件使用中文名称
- 功能模块用中文描述
- 注释和说明用中文

### 📦 版本管理规则
- 每个完整版本都要备份
- 新功能创建新版本，不修改老版本
- 版本命名：`功能名_v1.py`, `功能名_v2.py`
- 模板文件：`模板_v1.html`, `模板_v2.html`

### 🔄 版本控制流程
1. 完成一个版本后，立即备份
2. 新增功能时，复制上一版本创建新版本
3. 保留所有历史版本，方便回退

---

## 📚 版本历史

### V1 完整版 (2025-07-21)
**文件:**
- `持仓系统_v1_完整版.py` - 主程序
- `templates/持仓页面_v1.html` - 前端页面
- `wechat_alert.py` - 微信推送模块

**功能:**
- ✅ 基础持仓管理
- ✅ 实时股价获取
- ✅ 年内最低价分析
- ✅ 距最低点涨幅计算
- ✅ 市盈率、市净率显示
- ✅ **股息率显示** (新增)
- ✅ **行业信息显示** (新增)
- ✅ 企业微信推送
- ✅ 实时数据更新

**启动方式:**
```bash
python 持仓系统_v1_完整版.py
```

### V1.2 完整版 (2025-07-21) ⭐ **推荐使用**
**文件:**
- `持仓系统_v1.2_最终修复版.py` - 主程序
- `templates/持仓页面_v1.2_完整版.html` - 前端页面
- `wechat_alert.py` - 微信推送模块

**功能:**
- ✅ 基础持仓管理
- ✅ 实时股价获取
- ✅ 年内最低价分析
- ✅ 距最低点涨幅计算
- ✅ 市盈率、市净率显示
- ✅ **正确的股息率显示** (修复)
- ✅ **正确的行业信息显示** (修复)
- ✅ **行业排序功能** (修复)
- ✅ 企业微信推送
- ✅ 实时数据更新

**修复内容:**
- 🔧 使用第二版验证过的API获取股息率
- 🔧 使用单独接口获取行业信息
- 🔧 修复行业排序按钮功能
- 🔧 支持字符串和数值混合排序

**启动方式:**
```bash
python 持仓系统_v1.2_最终修复版.py
```

**访问地址:**
http://localhost:5000

### V2 智能监测版 (2025-07-21) ⭐ **最新版本**
**文件:**
- `持仓系统_v2_智能监测版.py` - 主程序
- `A股交易时间监测_简化版.py` - 交易时间监测模块
- `templates/comprehensive.html` - 前端页面
- `wechat_alert.py` - 微信推送模块

**新增功能:**
- ✅ **智能交易时间监测** (核心新功能)
- ✅ **只在交易时间更新数据** (节省资源)
- ✅ **包含集合竞价时间** (9:15-9:25, 14:57-15:00)
- ✅ **自动获取节假日** (从API获取准确数据)
- ✅ **动态更新间隔** (交易时30秒，休市时5分钟)
- ✅ **交易状态API** (可查看当前交易状态)
- ✅ **节假日刷新API** (可手动更新节假日)

**智能特性:**
- 🕐 **交易时间**: 集合竞价(9:15-9:25) + 上午交易(9:30-11:30) + 下午交易(13:00-15:00)
- 📅 **节假日识别**: 自动从API获取准确的节假日和调休安排
- ⚡ **动态间隔**: 集合竞价10秒，连续竞价30秒，休市5分钟
- 💾 **本地缓存**: 节假日数据本地缓存，避免重复请求
- 🔄 **容错机制**: API失败时使用本地缓存

**启动方式:**
```bash
python 持仓系统_v2_智能监测版.py
```

**访问地址:**
- 主页面: http://localhost:5000
- 交易状态: http://localhost:5000/api/trading-status
- 刷新节假日: http://localhost:5000/api/refresh-holidays

### V3 持仓详情版 (2025-07-21) ⭐ **最新版本**
**文件:**
- `持仓系统_v3_持仓详情版.py` - 主程序
- `A股交易时间监测_简化版.py` - 交易时间监测模块
- `templates/持仓页面_v3.html` - 前端页面
- `stocks_list.csv` - 股票列表（包含持仓数量）
- `wechat_alert.py` - 微信推送模块

**新增功能:**
- ✅ **持仓数量显示** (核心新功能)
- ✅ **市值计算显示** (核心新功能)
- ✅ **持仓统计API** (总市值、总持仓)
- ✅ **股票列表支持持仓数量** (CSV格式扩展)

**持仓特性:**
- 💰 **持仓数量**: 显示每只股票的持仓股数
- 📊 **当前市值**: 自动计算 = 股价 × 持仓数量
- 📈 **总市值统计**: API返回所有股票的总市值
- 📋 **持仓管理**: 支持在CSV文件中配置持仓数量

**数据格式:**
```csv
代码,名称,持仓数量
600036,招商银行,5000
000001,平安银行,3000
```

**启动方式:**
```bash
python 持仓系统_v3_持仓详情版.py
```

**访问地址:**
- 主页面: http://localhost:5000
- 股票数据API: http://localhost:5000/api/stocks (包含持仓统计)
- 交易状态: http://localhost:5000/api/trading-status

### V4 数据管理版 (2025-07-21) ⭐ **最新版本**
**文件:**
- `持仓系统_v4_数据管理版.py` - 主程序
- `A股交易时间监测_简化版.py` - 交易时间监测模块
- `templates/持仓页面_v4.html` - 前端页面
- `wechat_alert.py` - 微信推送模块

**新增功能:**
- ✅ **清空数据功能** (核心新功能)
- ✅ **导入持仓表格功能** (核心新功能)
- ✅ **重新获取数据功能** (核心新功能)
- ✅ **文件上传验证** (Excel格式验证)
- ✅ **持仓数量和市值显示** (从导入表格获取)

**数据管理特性:**
- 🗑️ **清空数据**: 一键清空内存中的所有股票数据
- 📁 **导入表格**: 支持上传Excel文件，自动读取股票代码和持仓数量
- 🔄 **重新获取**: 基于导入的股票列表重新获取完整数据
- ✅ **格式验证**: 自动验证Excel文件格式和数据有效性
- 📊 **持仓管理**: 显示每只股票的持仓数量和当前市值

**Excel文件要求:**
- 支持格式: .xlsx, .xls
- 必须包含列: "代码", "持有数量"
- 股票代码: 6位数字格式
- 持有数量: 大于0的整数

**操作流程:**
1. 点击"清空数据"清除现有数据
2. 点击"导入持仓表格"上传Excel文件
3. 点击"重新获取数据"获取完整股票信息

**启动方式:**
```bash
python 持仓系统_v4_数据管理版.py
```

**访问地址:**
- 主页面: http://localhost:5000
- 清空数据: POST /api/clear-data
- 导入表格: POST /api/upload-holdings
- 重新获取: POST /api/refresh-data

### V5 分类筛选版 (2025-07-21) ⭐ **最新版本**
**文件:**
- `持仓系统_v5_分类筛选版.py` - 主程序
- `A股交易时间监测_简化版.py` - 交易时间监测模块
- `templates/持仓页面_v5.html` - 前端页面
- `wechat_alert.py` - 微信推送模块

**新增功能:**
- ✅ **分类筛选按钮** (核心新功能)
- ✅ **默认显示股票** (减少页面混乱)
- ✅ **动态筛选逻辑** (保持搜索和排序功能)
- ✅ **证券类型识别** (自动分类股票/可转债/ETF)

**分类筛选特性:**
- 🏷️ **四个筛选按钮**: 股票、可转债、ETF/基金、全部
- 🎯 **默认显示股票**: 页面加载时只显示股票，隐藏可转债和ETF
- 🔄 **动态切换**: 点击按钮实时切换显示内容
- ✨ **激活状态**: 当前选中的按钮有高亮显示
- 🔍 **搜索兼容**: 搜索功能在筛选后的数据上正常工作

**分类规则:**
- **股票**: 代码以60开头（上海A股）或00/30开头（深圳A股/创业板）
- **可转债**: 代码以11/12/13开头
- **ETF/基金**: 代码以51/15开头
- **其他**: 不符合上述规则的代码

**用户体验:**
- 📱 **清晰界面**: 默认只显示股票，避免混合显示造成混乱
- 🎨 **美观按钮**: 筛选按钮有悬停效果和激活状态
- ⚡ **快速切换**: 一键切换不同类型的持仓
- 🔄 **功能保持**: 排序、搜索等原有功能完全兼容

**启动方式:**
```bash
python 持仓系统_v5_分类筛选版.py
```

**访问地址:**
- 主页面: http://localhost:5000

### V6 统计分析版 (2025-07-21) ⭐ **最新版本**
**文件:**
- `持仓系统_v6_统计分析版.py` - 主程序
- `A股交易时间监测_简化版.py` - 交易时间监测模块
- `templates/持仓页面_v6.html` - 前端页面
- `wechat_alert.py` - 微信推送模块

**新增功能:**
- ✅ **总资产统计** (显著位置显示总市值)
- ✅ **资产类别统计** (股票/可转债/ETF分类统计)
- ✅ **行业市值统计** (股票按行业分组统计)
- ✅ **实时数据同步** (统计数据与表格数据保持同步)

**统计分析特性:**
- 💰 **总资产面板**: 显著位置显示所有持仓的总市值
- 📊 **资产配置面板**: 分别统计股票、可转债、ETF/基金的市值和占比
- 🏭 **行业分布面板**: 对股票按行业统计市值和占比，按市值排序
- 🔄 **动态更新**: 切换分类筛选时，统计数据实时更新
- 📱 **响应式布局**: 支持不同屏幕尺寸的卡片式布局

**数据精度:**
- 💰 **市值金额**: 保留2位小数，使用千分位分隔符
- 📈 **百分比**: 保留1位小数
- 🎯 **智能显示**: 对于可转债/ETF筛选，行业统计显示"该类别无行业分类"

**界面设计:**
- 🎨 **卡片式布局**: 三列网格布局，美观清晰
- 🌈 **颜色区分**: 不同统计面板使用不同的主题色
- 📱 **响应式设计**: 支持桌面、平板、手机等不同设备
- ✨ **数据可视化**: 清晰的数值展示和百分比显示

**启动方式:**
```bash
python 持仓系统_v6_统计分析版.py
```

**访问地址:**
- 主页面: http://localhost:5000

### V7 界面优化版 (2025-07-21) ⭐ **最新版本**
**文件:**
- `持仓系统_v7_界面优化版.py` - 主程序
- `A股交易时间监测_简化版.py` - 交易时间监测模块
- `templates/持仓页面_v7.html` - 前端页面
- `wechat_alert.py` - 微信推送模块

**新增功能:**
- ✅ **统计面板置顶** (移动到页面顶部显著位置)
- ✅ **行业仓位显示** (表格中显示行业占比)
- ✅ **布局优化** (统计面板在筛选和管理按钮之间)
- ✅ **数据同步优化** (行业仓位与统计面板数据一致)

**界面布局优化:**
- 🔝 **统计面板置顶**: 将总资产、资产配置、行业分布面板移至页面顶部
- 📍 **位置调整**: 统计面板位于分类筛选按钮和数据管理按钮之间
- 🎨 **保持设计**: 维持原有的卡片式布局和响应式设计
- 👁️ **视觉优化**: 重要统计信息更加显眼，用户体验更佳

**行业仓位显示:**
- 📊 **仓位标注**: 股票行业列显示格式为"电子信息 (15.2%)"
- 🎯 **精确计算**: 该行业总市值 ÷ 股票类总市值 × 100%
- 🏷️ **智能显示**: 只对股票类别显示仓位，可转债和ETF不显示
- 📈 **保留精度**: 行业仓位百分比保留1位小数

**数据同步特性:**
- 🔄 **实时更新**: 筛选条件改变时，行业仓位百分比实时更新
- 📊 **数据一致**: 表格中的行业仓位与统计面板中的行业分布完全一致
- 🎛️ **功能保持**: 搜索、排序等原有功能完全正常
- ⚡ **性能优化**: 缓存行业统计数据，避免重复计算

**显示规则:**
- ✅ **股票**: 显示"行业名称 (仓位%)"
- ❌ **可转债**: 只显示名称，不显示仓位
- ❌ **ETF/基金**: 只显示名称，不显示仓位
- 🚫 **未知行业**: 不显示仓位百分比

**启动方式:**
```bash
python 持仓系统_v7_界面优化版.py
```

**访问地址:**
- 主页面: http://localhost:5000

### V8 饼图版 (2025-07-21) ⭐ **最新版本**
**文件:**
- `持仓系统_v8_饼图版.py` - 主程序
- `A股交易时间监测_简化版.py` - 交易时间监测模块
- `templates/持仓页面_v8.html` - 前端页面
- `wechat_alert.py` - 微信推送模块

**新增功能:**
- ✅ **资产配置饼图** (股票/可转债/ETF分布可视化)
- ✅ **行业分布饼图** (股票行业分布可视化)
- ✅ **Chart.js集成** (专业图表库支持)
- ✅ **双重展示** (饼图+数据表格同时显示)

**饼图可视化特性:**
- 🥧 **资产配置饼图**: 直观显示股票、可转债、ETF/基金的资产分布
- 🏭 **行业分布饼图**: 清晰展示股票在各行业的投资分布
- 🎨 **专业配色**: 使用区分度高的颜色方案，易于识别
- 💡 **智能提示**: 鼠标悬停显示详细金额和百分比信息

**图表功能:**
- 📊 **实时更新**: 数据变化时饼图自动更新
- 🔄 **筛选联动**: 切换分类筛选时，行业饼图相应更新
- 📱 **响应式设计**: 图表大小自适应不同屏幕尺寸
- 🎯 **精确显示**: 显示精确的金额和百分比数据

**用户体验:**
- 👁️ **双重展示**: 既有饼图的直观性，又有数据表格的精确性
- 🎨 **美观界面**: 饼图与统计面板完美融合
- ⚡ **快速加载**: 使用CDN加载Chart.js，确保加载速度
- 🖱️ **交互友好**: 支持鼠标悬停查看详细信息

**技术特性:**
- 📚 **Chart.js**: 使用业界领先的图表库
- 🎨 **自定义配色**: 为不同资产类型和行业使用专门配色
- 🔧 **配置优化**: 图表配置针对持仓数据优化
- 📐 **尺寸适配**: 图表尺寸与统计面板完美匹配

**启动方式:**
```bash
python 持仓系统_v8_饼图版.py
```

**访问地址:**
- 主页面: http://localhost:5000

### V9 移动端适配版 (2025-07-21) ⭐ **最新版本**
**文件:**
- `持仓系统_v9_移动端适配版.py` - 主程序
- `A股交易时间监测_简化版.py` - 交易时间监测模块
- `templates/持仓页面_v9.html` - 前端页面
- `wechat_alert.py` - 微信推送模块

**新增功能:**
- ✅ **移动端响应式优化** (专门针对手机屏幕优化)
- ✅ **表格移动端适配** (隐藏次要列，保留核心信息)
- ✅ **统计面板布局调整** (响应式网格布局)
- ✅ **交互体验优化** (触摸友好的按钮和操作)

**移动端响应式特性:**
- 📱 **手机端优化** (< 768px): 单列布局，大按钮，易触摸
- 📟 **平板端优化** (768px-1200px): 双列布局，适中尺寸
- 🖥️ **桌面端保持** (> 1200px): 三列布局，完整功能
- 🔄 **动态适配**: 窗口大小变化时自动调整布局

**表格移动端适配:**
- 👁️ **核心信息保留**: 代码、名称、价格、涨跌幅、行业、持仓数量、市值
- 🙈 **次要列隐藏**: 一年内最低价、最低价日期、距最低点涨幅、市净率、TTM市盈率、股息率、更新时间
- 📜 **横向滚动**: 支持左右滑动查看完整表格内容
- 📏 **尺寸优化**: 调整行高、字体大小，确保清晰可读

**统计面板布局:**
- 🖥️ **桌面端**: 3列网格布局 (总资产 | 资产配置 | 行业分布)
- 📟 **平板端**: 2列网格布局 (上排2个，下排1个)
- 📱 **手机端**: 1列垂直布局 (上下排列)
- 📐 **间距调整**: 不同屏幕尺寸使用不同的边距和间距

**交互体验优化:**
- 👆 **触摸友好**: 按钮最小44px高度，符合苹果触摸标准
- 🎯 **点击区域**: 增大按钮和可点击区域
- ✨ **触摸反馈**: 按钮按下时有缩放和颜色变化效果
- 🔍 **搜索优化**: 搜索框在移动端使用16px字体防止iOS缩放

**CSS媒体查询完善:**
- 📱 **手机端** (@media max-width: 768px): 单列布局，大按钮，隐藏次要列
- 📟 **平板端** (@media 768px-1200px): 双列布局，中等尺寸
- 🖥️ **桌面端** (@media min-width: 1200px): 三列布局，完整功能
- 🎨 **细节优化**: 字体大小、间距、颜色在不同设备上的适配

**技术特性:**
- 🔧 **动态检测**: JavaScript检测屏幕尺寸并动态调整
- 📐 **响应式网格**: CSS Grid自动适配不同屏幕
- 🖱️ **平滑滚动**: iOS设备支持平滑滚动效果
- ⚡ **性能优化**: 移动端减少不必要的DOM元素

**启动方式:**
```bash
python 持仓系统_v9_移动端适配版.py
```

**访问地址:**
- 主页面: http://localhost:5000

### V12 完整整合版 (2025-07-22) ⭐ **最新版本**
**文件:**
- `持仓系统_v12_完整整合版.py` - 主程序
- `templates/持仓页面_v12_完整整合版.html` - 前端页面
- `A股交易时间监测_简化版.py` - 交易时间监测模块
- `yearly_low_cache_reader.py` - 年内最低价缓存模块
- `tdx_scan_module.py` - 扫雷风险评估模块
- `wechat_alert.py` - 微信推送模块

**整合功能:**
- ✅ **V1-V1.2功能**: 基础股价获取、年内最低价、股息率、行业信息
- ✅ **V2功能**: 智能交易时间监测、节假日识别、动态更新间隔
- ✅ **V3功能**: 持仓数量显示、市值计算、持仓统计
- ✅ **V4功能**: Excel文件上传、数据清空、重新获取数据
- ✅ **V5功能**: 分类筛选（股票/ETF/基金）、动态筛选按钮
- ✅ **V6功能**: 总资产统计、资产配置统计、行业分布统计
- ✅ **V7功能**: 统计面板置顶、行业仓位显示、界面优化
- ✅ **V8功能**: 资产配置饼图、行业分布饼图、Chart.js集成
- ✅ **V9功能**: 移动端响应式设计、表格适配、触摸优化
- ✅ **V10-V11功能**: 扫雷风险评估、年内最低价缓存系统

**核心特性:**
- 🚀 **完整功能集成**: 整合所有版本的核心功能，无功能缺失
- 📊 **数据获取优化**: 优先使用缓存，提高数据获取效率
- 🕐 **智能更新机制**: 只在交易时间更新，节省系统资源
- 📱 **全平台适配**: 桌面端、平板端、移动端完美适配
- 🔍 **风险评估完整**: 集成扫雷分数，提供全面风险评估
- 📈 **统计分析完善**: 多维度统计分析，支持可视化展示
- 🎨 **界面体验优化**: 现代化设计，操作简便直观

**技术架构:**
- 🏗️ **模块化设计**: 清晰的模块分离，易于维护和扩展
- 💾 **缓存系统**: 年内最低价缓存 + 扫雷数据缓存
- 🔄 **异步处理**: 后台数据更新，不影响前端操作
- 📡 **RESTful API**: 完整的API接口，支持前后端分离
- 🛡️ **错误处理**: 完善的异常处理和容错机制

**数据管理:**
- 📁 **Excel导入**: 支持.xlsx/.xls格式，自动验证数据
- 🗑️ **数据清空**: 一键清空所有数据，支持重新开始
- 🔄 **数据刷新**: 后台异步更新，实时获取最新数据
- 📊 **缓存状态**: 实时查看缓存状态和统计信息

**前端功能:**
- 🔍 **智能搜索**: 支持股票代码和名称搜索
- 🏷️ **分类筛选**: 股票、ETF、基金分类显示
- ⬆️⬇️ **表格排序**: 支持所有列的升序/降序排序
- 📱 **响应式布局**: 自适应不同屏幕尺寸
- 📈 **实时统计**: 总资产、资产配置、行业分布实时更新

**启动方式:**
```bash
python 持仓系统_v12_完整整合版.py
```

**访问地址:**
- 主页面: http://localhost:5000
- 股票数据API: http://localhost:5000/api/stocks
- 交易状态API: http://localhost:5000/api/trading-status
- 缓存状态API: http://localhost:5000/api/cache-status
- 数据管理API: 清空/上传/刷新数据

**系统要求:**
- Python 3.7+
- 依赖模块: Flask, pandas, requests
- 网络连接: 需要访问东方财富API和通达信API
- 缓存文件: yearly_low_cache_*.csv (可选，提高性能)

**使用建议:**
1. 首次使用建议运行 `python yearly_low_cache_system.py` 生成缓存
2. 准备Excel格式的持仓表格（包含"代码"和"持有数量"列）
3. 在交易时间使用效果最佳，可实时更新数据
4. 移动端访问体验优化，支持触摸操作

### V10 排雷功能版 (2025-07-21)
**文件:**
- `持仓系统_v10_排雷功能版.py` - 主程序
- `A股交易时间监测_简化版.py` - 交易时间监测模块
- `templates/持仓页面_v10.html` - 前端页面
- `wechat_alert.py` - 微信推送模块

**新增功能:**
- ✅ **排雷功能集成** (全新风险评估系统)
- ✅ **排雷分数显示** (实时风险评分)
- ✅ **API数据获取** (外部排雷数据源)
- ✅ **智能风险预警** (分数颜色标识)

**排雷功能特性:**
- 🛡️ **排雷分数**: 集成外部API获取股票排雷分数 (0-100分)
- 🎯 **API接口**: 使用通达信排雷数据接口获取风险评分
- 📊 **数据集成**: 排雷分数与股票基本面数据同步更新
- 🔍 **智能解析**: 多种模式解析API返回数据，确保数据准确性

**前端显示优化:**
- 📋 **新增排雷列**: 在行业列后添加排雷分数列
- 🎨 **颜色标识**:
  - 🟢 高分 (≥80): 绿色显示，风险较低
  - 🟠 中等 (60-79): 橙色显示，风险中等
  - 🔴 低分 (<60): 红色显示，风险较高
- 📱 **移动端适配**: 排雷列在移动端保留显示（重要风险指标）
- ⬆️⬇️ **排序支持**: 支持按排雷分数升序/降序排列

**API数据获取:**
- 🌐 **数据源**: `http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html`
- 🔄 **同步更新**: 排雷数据与股票价格、财务数据同步获取
- ⏱️ **超时处理**: 10秒超时机制，避免长时间等待
- 🛠️ **错误处理**: API失败时显示"-"或"N/A"，不影响其他数据

**数据解析算法:**
- 🔍 **多模式匹配**: 支持多种排雷分数格式识别
- 📝 **正则表达式**: 智能提取"排雷分数"、"分数"、"评分"等字段
- 🔢 **数值验证**: 确保分数在0-100合理范围内
- 📄 **HTML解析**: 从网页内容中提取数值信息

**性能与稳定性:**
- ⚡ **并发处理**: 排雷数据获取不影响其他数据处理速度
- 🔄 **重试机制**: 网络异常时自动重试
- 💾 **缓存优化**: 避免频繁请求同一股票数据
- 🛡️ **异常隔离**: 单只股票排雷数据失败不影响整体更新

**风险评估体系:**
- 📈 **综合评分**: 结合排雷分数与其他财务指标
- ⚠️ **风险预警**: 低排雷分数股票醒目标识
- 📊 **数据完整性**: 排雷分数与价格、估值数据统一展示
- 🎯 **投资决策**: 为投资决策提供风险参考依据

**启动方式:**
```bash
python 持仓系统_v10_排雷功能版.py
```

**访问地址:**
- 主页面: http://localhost:5000

---

## 🚀 下一版本计划

### V2 计划功能
- 增强的数据分析
- 更多筛选条件
- 图表展示
- 导出功能

---

## 📝 使用说明

1. **启动系统**: 运行对应版本的Python文件
2. **访问页面**: 浏览器打开 http://localhost:5000
3. **查看数据**: 系统会自动更新股票数据
4. **微信推送**: 系统会自动发送高涨幅提醒

## 🔧 配置说明

- `stocks_list.csv` - 股票代码列表
- 企业微信webhook地址在代码中配置
- 更新间隔等参数可在代码中调整

## ⚠️ 注意事项

- 确保网络连接正常
- 股票代码格式要正确
- 企业微信webhook要有效
- 不要同时运行多个版本
