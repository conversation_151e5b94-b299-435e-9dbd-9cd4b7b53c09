#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
从东方财富获取股票一年内最低价（前复权）的脚本
"""

from urllib.parse import urlencode
import pandas as pd
import requests
import time
from datetime import datetime, timedelta

def gen_secid(rawcode: str) -> str:
    """
    生成东方财富专用的secid
    Parameters
    ----------
    rawcode : 6 位股票代码
    Return
    ------
    str: 指定格式的字符串
    """
    # 沪市指数
    if rawcode[:3] == '000':
        return f'1.{rawcode}'
    # 深证指数
    if rawcode[:3] == '399':
        return f'0.{rawcode}'
    # 沪市股票
    if rawcode[0] == '6':
        return f'1.{rawcode}'
    # 深市股票
    return f'0.{rawcode}'

def get_k_history(code: str, beg: str, end: str, klt: int = 101, fqt: int = 1) -> pd.DataFrame:
    """
    获取k线数据
    
    参数:
        code : 6 位股票代码
        beg: 开始日期 例如 20200101
        end: 结束日期 例如 20200201
        klt: k线间距 默认为 101 即日k
            klt:1 1 分钟
            klt:5 5 分钟
            klt:101 日
            klt:102 周
        fqt: 复权方式
            不复权 : 0
            前复权 : 1
            后复权 : 2 
    """
    EastmoneyKlines = {
        'f51': '日期',
        'f52': '开盘',
        'f53': '收盘',
        'f54': '最高',
        'f55': '最低',
        'f56': '成交量',
        'f57': '成交额',
        'f58': '振幅',
        'f59': '涨跌幅',
        'f60': '涨跌额',
        'f61': '换手率',
    }
    
    EastmoneyHeaders = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; WOW64; Trident/7.0; Touch; rv:11.0) like Gecko',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
        'Referer': 'http://quote.eastmoney.com/center/gridlist.html',
    }
    
    fields = list(EastmoneyKlines.keys())
    columns = list(EastmoneyKlines.values())
    fields2 = ",".join(fields)
    secid = gen_secid(code)
    
    params = (
        ('fields1', 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13'),
        ('fields2', fields2),
        ('beg', beg),
        ('end', end),
        ('rtntype', '6'),
        ('secid', secid),
        ('klt', f'{klt}'),
        ('fqt', f'{fqt}'),
    )
    
    params = dict(params)
    base_url = 'https://push2his.eastmoney.com/api/qt/stock/kline/get'
    url = base_url + '?' + urlencode(params)
    
    try:
        json_response: dict = requests.get(url, headers=EastmoneyHeaders, timeout=10).json()
        
        data = json_response.get('data')
        if data is None:
            # 尝试切换市场
            if secid[0] == '0':
                secid = f'1.{code}'
            else:
                secid = f'0.{code}'
            params['secid'] = secid
            url = base_url + '?' + urlencode(params)
            json_response: dict = requests.get(url, headers=EastmoneyHeaders, timeout=10).json()
            data = json_response.get('data')
        
        if data is None:
            print(f'股票代码: {code} 可能有误或无数据')
            return pd.DataFrame(columns=columns)
        
        klines = data['klines']
        
        rows = []
        for _kline in klines:
            kline = _kline.split(',')
            rows.append(kline)
        
        df = pd.DataFrame(rows, columns=columns)
        
        # 转换数据类型
        numeric_columns = ['开盘', '收盘', '最高', '最低', '成交量', '成交额', '振幅', '涨跌幅', '涨跌额', '换手率']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        return df
        
    except Exception as e:
        print(f'获取股票 {code} K线数据失败: {e}')
        return pd.DataFrame(columns=columns)

def get_yearly_low_price(code: str) -> dict:
    """
    获取股票一年内的最低价（前复权）
    
    Args:
        code: 股票代码
    
    Returns:
        dict: 包含一年内最低价信息的字典
    """
    
    # 计算一年前的日期
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365)
    
    # 格式化日期
    start_date_str = start_date.strftime('%Y%m%d')
    end_date_str = end_date.strftime('%Y%m%d')
    
    print(f"正在获取 {code} 从 {start_date_str} 到 {end_date_str} 的K线数据...")
    
    # 获取前复权K线数据
    df = get_k_history(code, start_date_str, end_date_str, klt=101, fqt=1)
    
    if df.empty:
        return {
            'code': code,
            'yearly_low': None,
            'low_date': None,
            'current_price': None,
            'distance_from_low': None,
            'distance_pct': None,
            'error': '无法获取数据'
        }
    
    try:
        # 找到最低价
        min_low_idx = df['最低'].idxmin()
        yearly_low = df.loc[min_low_idx, '最低']
        low_date = df.loc[min_low_idx, '日期']
        
        # 获取最新价格（最后一天的收盘价）
        current_price = df.iloc[-1]['收盘']
        
        # 计算距离最低价的距离
        distance_from_low = current_price - yearly_low
        distance_pct = (distance_from_low / yearly_low) * 100
        
        return {
            'code': code,
            'yearly_low': yearly_low,
            'low_date': low_date,
            'current_price': current_price,
            'distance_from_low': distance_from_low,
            'distance_pct': distance_pct,
            'data_points': len(df),
            'update_time': datetime.now().strftime('%H:%M:%S')
        }
        
    except Exception as e:
        print(f"处理股票 {code} 数据时出错: {e}")
        return {
            'code': code,
            'yearly_low': None,
            'low_date': None,
            'current_price': None,
            'distance_from_low': None,
            'distance_pct': None,
            'error': str(e)
        }

def get_multiple_stocks_yearly_low(stock_codes: list, delay: float = 1.0) -> list:
    """
    批量获取多只股票的一年内最低价
    
    Args:
        stock_codes: 股票代码列表
        delay: 请求间隔时间（秒）
    
    Returns:
        list: 股票数据列表
    """
    
    results = []
    
    print(f"开始获取 {len(stock_codes)} 只股票的一年内最低价...")
    
    for i, code in enumerate(stock_codes):
        print(f"[{i+1:3d}/{len(stock_codes)}] 正在处理 {code}...")
        
        data = get_yearly_low_price(code)
        results.append(data)
        
        if data.get('yearly_low') is not None:
            print(f"✅ {code} 一年内最低价:{data['yearly_low']:.2f} 日期:{data['low_date']} 当前价:{data['current_price']:.2f} 距离最低价:{data['distance_pct']:+.1f}%")
        else:
            print(f"❌ {code} 获取失败: {data.get('error', '未知错误')}")
        
        # 避免请求过于频繁
        if i < len(stock_codes) - 1:
            time.sleep(delay)
    
    return results

def load_stock_codes_from_csv():
    """从CSV文件加载股票代码"""
    try:
        df = pd.read_csv('stocks_list.csv', encoding='utf-8-sig')
        stock_codes = df['代码'].astype(str).str.zfill(6).tolist()
        print(f"从CSV文件加载了 {len(stock_codes)} 只股票代码")
        return stock_codes
    except Exception as e:
        print(f"加载股票代码失败: {e}")
        return []

def save_to_csv(data_list, filename=None):
    """保存数据到CSV文件"""
    if not data_list:
        print("没有数据可保存")
        return False
    
    if filename is None:
        filename = f"一年内最低价_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    df = pd.DataFrame(data_list)
    
    # 重新排列列的顺序
    columns_order = [
        'code', 'yearly_low', 'low_date', 'current_price', 
        'distance_from_low', 'distance_pct', 'data_points', 'update_time'
    ]
    
    # 只保留存在的列
    existing_columns = [col for col in columns_order if col in df.columns]
    df = df[existing_columns]
    
    # 添加中文列名
    column_names = {
        'code': '股票代码',
        'yearly_low': '一年内最低价',
        'low_date': '最低价日期',
        'current_price': '当前价格',
        'distance_from_low': '距离最低价',
        'distance_pct': '距离最低价(%)',
        'data_points': '数据点数',
        'update_time': '更新时间'
    }
    
    df.columns = [column_names.get(col, col) for col in df.columns]
    
    df.to_csv(filename, index=False, encoding='utf-8-sig')
    print(f"✅ 数据已保存到 {filename}")
    return True

def analyze_yearly_low_data(data_list):
    """分析一年内最低价数据"""
    if not data_list:
        print("没有数据可分析")
        return
    
    valid_data = [d for d in data_list if d.get('yearly_low') is not None]
    
    print(f"\n=== 📊 一年内最低价分析 ===")
    print(f"总股票数: {len(data_list)}")
    print(f"有效数据: {len(valid_data)} 只 ({len(valid_data)/len(data_list)*100:.1f}%)")
    
    if valid_data:
        # 距离最低价分析
        distances = [d['distance_pct'] for d in valid_data]
        
        print(f"\n📈 距离一年内最低价分析:")
        print(f"   平均距离: {sum(distances)/len(distances):.1f}%")
        print(f"   距离范围: {min(distances):.1f}% - {max(distances):.1f}%")
        
        # 接近最低价的股票 (距离最低价 < 10%)
        near_low = [d for d in valid_data if d['distance_pct'] < 10]
        print(f"   接近最低价的股票(<10%): {len(near_low)} 只")
        
        if near_low:
            print("   接近最低价的股票列表:")
            for stock in sorted(near_low, key=lambda x: x['distance_pct'])[:5]:
                print(f"     {stock['code']} 距离最低价:{stock['distance_pct']:+.1f}% 最低价:{stock['yearly_low']:.2f} 当前价:{stock['current_price']:.2f}")
        
        # 远离最低价的股票 (距离最低价 > 50%)
        far_from_low = [d for d in valid_data if d['distance_pct'] > 50]
        print(f"   远离最低价的股票(>50%): {len(far_from_low)} 只")

def test_sample_stocks():
    """测试几只样本股票"""
    print("=== 测试样本股票 ===")
    
    test_codes = ['000001', '600000', '600036', '000002', '600519']
    results = get_multiple_stocks_yearly_low(test_codes, delay=0.5)
    
    if results:
        print(f"\n✅ 成功获取 {len(results)} 只股票数据")
        analyze_yearly_low_data(results)
    else:
        print("❌ 获取失败")
    
    return results

def main():
    """主函数"""
    print("=== 📈 东方财富股票一年内最低价获取工具 (前复权) ===")
    
    # 选择运行模式
    print("\n请选择运行模式:")
    print("1. 测试模式（获取几只样本股票）")
    print("2. 批量模式（获取所有持仓股票的一年内最低价）")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == '1':
        test_sample_stocks()
    
    elif choice == '2':
        # 批量模式
        stock_codes = load_stock_codes_from_csv()
        
        if not stock_codes:
            print("未找到股票代码，退出程序")
            return
        
        # 获取数据
        data_list = get_multiple_stocks_yearly_low(stock_codes, delay=1.5)
        
        if data_list:
            print(f"✅ 处理完成，共 {len(data_list)} 只股票")
            
            # 保存数据
            save_to_csv(data_list)
            
            # 分析数据
            analyze_yearly_low_data(data_list)
        else:
            print("❌ 未获取到任何数据")
    
    else:
        print("无效选择，退出程序")

if __name__ == "__main__":
    main()
