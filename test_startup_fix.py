#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试启动修复效果
"""

import requests
import time
import threading
import subprocess
import sys
import os

def test_web_accessibility():
    """测试网页可访问性"""
    print("🌐 测试网页可访问性...")
    
    max_attempts = 30  # 最多等待30秒
    for attempt in range(max_attempts):
        try:
            response = requests.get("http://localhost:5000", timeout=5)
            if response.status_code == 200:
                print(f"✅ 网页可访问 (尝试 {attempt + 1}/{max_attempts})")
                return True
        except requests.exceptions.RequestException:
            pass
        
        print(f"⏳ 等待网页启动... ({attempt + 1}/{max_attempts})")
        time.sleep(1)
    
    print("❌ 网页无法访问")
    return False

def test_api_endpoints():
    """测试API端点"""
    print("\n🔌 测试API端点...")
    
    endpoints = [
        ("/api/config", "配置API"),
        ("/api/data", "数据API"),
        ("/api/strategies", "策略API"),
    ]
    
    for endpoint, name in endpoints:
        try:
            response = requests.get(f"http://localhost:5000{endpoint}", timeout=10)
            if response.status_code == 200:
                print(f"✅ {name} 正常")
            else:
                print(f"⚠️ {name} 状态码: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ {name} 失败: {e}")

def check_config_file():
    """检查配置文件"""
    print("\n📁 检查配置文件...")
    
    if os.path.exists('app_config.json'):
        print("✅ app_config.json 存在")
        try:
            import json
            with open('app_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            auto_refresh = config.get('auto_refresh_on_startup', False)
            print(f"🔄 自动刷新设置: {auto_refresh}")
        except Exception as e:
            print(f"⚠️ 读取配置失败: {e}")
    else:
        print("❌ app_config.json 不存在")

def monitor_startup_logs():
    """监控启动日志（模拟）"""
    print("\n📋 启动流程检查...")
    
    expected_logs = [
        "启动配置检查",
        "自动刷新开关",
        "正在加载缓存数据",
        "正在初始化策略配置",
        "正在启动Flask服务器",
    ]
    
    print("预期的启动日志顺序:")
    for i, log in enumerate(expected_logs, 1):
        print(f"  {i}. {log}")

def main():
    """主测试函数"""
    print("🧪 启动修复效果测试")
    print("=" * 50)
    
    print("📝 修复说明:")
    print("1. 将立即刷新改为异步执行")
    print("2. Flask服务器优先启动")
    print("3. 数据刷新在后台进行")
    print("4. 网页应该能立即访问")
    
    # 检查配置文件
    check_config_file()
    
    # 监控启动日志
    monitor_startup_logs()
    
    print("\n" + "=" * 50)
    print("🚀 测试建议:")
    print("1. 重启持仓系统")
    print("2. 观察启动日志顺序")
    print("3. 立即尝试访问 http://localhost:5000")
    print("4. 检查数据是否在后台刷新")
    
    # 如果系统正在运行，测试可访问性
    print("\n🔍 检查当前系统状态...")
    if test_web_accessibility():
        test_api_endpoints()
        print("\n✅ 系统运行正常！")
    else:
        print("\n⚠️ 系统未运行或网页不可访问")
        print("请重启持仓系统进行测试")

if __name__ == "__main__":
    main()
