"""
诊断浏览器启动问题
"""

import os
import sys

def check_chrome_installation():
    """检查Chrome浏览器安装"""
    print("=== 检查Chrome浏览器安装 ===")
    
    chrome_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME', '')),
    ]
    
    chrome_found = False
    for path in chrome_paths:
        if os.path.exists(path):
            print(f"✅ 找到Chrome: {path}")
            chrome_found = True
            break
    
    if not chrome_found:
        print("❌ 未找到Chrome浏览器")
        print("💡 请从以下地址下载安装Chrome:")
        print("   https://www.google.com/chrome/")
        return False
    
    return True

def check_selenium_installation():
    """检查selenium安装"""
    print("\n=== 检查Selenium安装 ===")
    
    try:
        import selenium
        print(f"✅ Selenium已安装，版本: {selenium.__version__}")
        return True
    except ImportError:
        print("❌ Selenium未安装")
        print("💡 请运行: pip install selenium")
        return False

def check_webdriver_manager():
    """检查webdriver-manager"""
    print("\n=== 检查WebDriver Manager ===")
    
    try:
        import webdriver_manager
        print(f"✅ WebDriver Manager已安装")
        return True
    except ImportError:
        print("❌ WebDriver Manager未安装")
        print("💡 请运行: pip install webdriver-manager")
        return False

def test_basic_selenium():
    """测试基本selenium功能"""
    print("\n=== 测试基本Selenium功能 ===")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        print("✅ Selenium导入成功")
        
        # 创建Chrome选项
        options = Options()
        options.add_argument('--version')  # 只获取版本信息
        
        print("✅ Chrome选项创建成功")
        return True
        
    except Exception as e:
        print(f"❌ Selenium基本功能测试失败: {e}")
        return False

def test_chrome_driver_download():
    """测试ChromeDriver下载"""
    print("\n=== 测试ChromeDriver下载 ===")
    
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        
        print("🔄 正在下载ChromeDriver...")
        driver_path = ChromeDriverManager().install()
        print(f"✅ ChromeDriver下载成功: {driver_path}")
        
        # 检查文件是否存在
        if os.path.exists(driver_path):
            print(f"✅ ChromeDriver文件确认存在")
            return True
        else:
            print(f"❌ ChromeDriver文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ ChromeDriver下载失败: {e}")
        return False

def test_browser_startup():
    """测试浏览器启动"""
    print("\n=== 测试浏览器启动 ===")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        
        # 配置选项
        options = Options()
        options.add_argument('--headless')  # 无头模式
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        
        print("🔄 尝试启动Chrome浏览器...")
        
        # 使用webdriver-manager
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        
        print("✅ Chrome浏览器启动成功！")
        
        # 测试访问页面
        print("🔄 测试访问百度...")
        driver.get("https://www.baidu.com")
        title = driver.title
        print(f"✅ 页面访问成功，标题: {title}")
        
        # 关闭浏览器
        driver.quit()
        print("✅ 浏览器正常关闭")
        
        return True
        
    except Exception as e:
        print(f"❌ 浏览器启动失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 尝试不使用webdriver-manager
        try:
            print("\n🔄 尝试使用系统ChromeDriver...")
            driver = webdriver.Chrome(options=options)
            print("✅ 系统ChromeDriver启动成功！")
            driver.quit()
            return True
        except Exception as e2:
            print(f"❌ 系统ChromeDriver也失败: {e2}")
            return False

def test_visible_browser():
    """测试可见浏览器启动"""
    print("\n=== 测试可见浏览器启动 ===")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        
        # 配置选项 - 不使用headless模式
        options = Options()
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        print("🔄 尝试启动可见Chrome浏览器...")
        print("💡 这次会打开浏览器窗口，用于测试")
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        
        print("✅ 可见Chrome浏览器启动成功！")
        
        # 访问测试页面
        driver.get("https://www.baidu.com")
        print("✅ 页面加载完成")
        
        # 等待几秒让用户看到
        import time
        print("⏳ 等待3秒...")
        time.sleep(3)
        
        driver.quit()
        print("✅ 浏览器关闭")
        
        return True
        
    except Exception as e:
        print(f"❌ 可见浏览器启动失败: {e}")
        return False

def main():
    """主诊断流程"""
    print("🔍 浏览器问题诊断工具")
    print("=" * 50)
    
    # 1. 检查Chrome安装
    chrome_ok = check_chrome_installation()
    
    # 2. 检查Selenium
    selenium_ok = check_selenium_installation()
    
    # 3. 检查WebDriver Manager
    wdm_ok = check_webdriver_manager()
    
    # 4. 测试基本功能
    if selenium_ok:
        basic_ok = test_basic_selenium()
    else:
        basic_ok = False
    
    # 5. 测试ChromeDriver下载
    if wdm_ok:
        driver_ok = test_chrome_driver_download()
    else:
        driver_ok = False
    
    # 6. 测试浏览器启动
    if chrome_ok and selenium_ok and driver_ok:
        startup_ok = test_browser_startup()
    else:
        startup_ok = False
    
    # 7. 如果无头模式失败，测试可见模式
    if not startup_ok and chrome_ok and selenium_ok:
        print("\n💡 无头模式失败，尝试可见模式...")
        visible_ok = test_visible_browser()
    else:
        visible_ok = startup_ok
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 诊断结果总结:")
    print("=" * 50)
    
    print(f"Chrome浏览器: {'✅' if chrome_ok else '❌'}")
    print(f"Selenium: {'✅' if selenium_ok else '❌'}")
    print(f"WebDriver Manager: {'✅' if wdm_ok else '❌'}")
    print(f"基本功能: {'✅' if basic_ok else '❌'}")
    print(f"ChromeDriver: {'✅' if driver_ok else '❌'}")
    print(f"浏览器启动: {'✅' if visible_ok else '❌'}")
    
    if visible_ok:
        print("\n🎉 浏览器环境正常！可以运行自动化程序")
    else:
        print("\n❌ 浏览器环境有问题，需要修复")
        print("\n💡 建议解决步骤:")
        if not chrome_ok:
            print("1. 安装Chrome浏览器")
        if not selenium_ok:
            print("2. 安装Selenium: pip install selenium")
        if not wdm_ok:
            print("3. 安装WebDriver Manager: pip install webdriver-manager")

if __name__ == "__main__":
    main()
