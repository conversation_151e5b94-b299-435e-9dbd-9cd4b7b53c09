# 通达信扫雷模块使用文档

## 📋 概述

`tdx_bxb_module.py` 是一个独立的Python模块，用于获取和计算股票的通达信扫雷风险评估分数。该模块已经过实际验证，计算结果准确可靠。

## ✨ 主要特性

- ✅ **已验证的计算算法** - 通过实际对比验证，确保分数计算准确
- 🚀 **高性能缓存机制** - 避免重复请求，提升响应速度
- 🔒 **线程安全设计** - 支持多线程环境使用
- 📦 **批量处理支持** - 可同时处理多只股票
- 🛡️ **完善的错误处理** - 网络异常和数据异常的优雅处理
- 🎨 **丰富的输出格式** - 提供分数、等级、颜色等多种展示方式

## 🚀 快速开始

### 1. 基本使用

```python
from tdx_bxb_module import TdxBxbClient

# 初始化客户端
scanner = TdxBxbClient()

# 获取单只股票分数
result = scanner.get_stock_score('000001')
if result:
    print(f"股票: {result['stock_name']}")
    print(f"分数: {result['score']}分")
    print(f"风险等级: {result['risk_level']}")
    print(f"触发风险: {result['triggered_risks']}项")
```

### 2. 便捷函数使用

```python
from tdx_bxb_module import get_stock_bxb_score, get_batch_bxb_scores

# 单只股票
result = get_stock_bxb_score('000001')

# 批量获取
batch_results = get_batch_bxb_scores(['000001', '000002', '600036'])
```

## 📊 返回数据格式

```python
{
    'stock_code': '000001',           # 股票代码
    'stock_name': '平安银行',          # 股票名称
    'score': 97,                      # 扫雷分数 (1-100)
    'risk_level': '极低风险',          # 风险等级
    'risk_color': '#10b068',          # 风险颜色代码
    'triggered_risks': 1,             # 触发风险数量
    'total_risks': 40,                # 总风险项数量
    'trigger_rate': 2.5,              # 触发率 (%)
    'details': [                      # 触发风险详情
        {
            'category': '交易类风险',
            'name': '主力资金卖出',
            'score': 3,
            'reason': '近5个交易日内出现过：净卖额超过2亿...'
        }
    ],
    'update_time': '2025-01-22 10:30:00'  # 更新时间
}
```

## 🎯 风险等级说明

| 分数范围 | 风险等级 | 颜色代码 | 说明 |
|---------|---------|---------|------|
| 90-100分 | 极低风险 | #10b068 | 风险很小，相对安全 |
| 80-89分  | 低风险   | #52c41a | 风险较小，需适度关注 |
| 70-79分  | 中低风险 | #faad14 | 风险一般，需要关注 |
| 60-69分  | 中等风险 | #fa8c16 | 风险较高，需要警惕 |
| 50-59分  | 中高风险 | #fa541c | 风险很高，需要谨慎 |
| 40-49分  | 高风险   | #f5222d | 风险极高，建议回避 |
| 1-39分   | 极高风险 | #a8071a | 风险巨大，强烈建议回避 |

## 🔧 集成到持仓系统

### 1. 文件放置
将 `tdx_bxb_module.py` 放在您的持仓系统根目录下。

### 2. 导入模块
在 `持仓系统_v8_饼图版.py` 文件顶部添加：

```python
from tdx_bxb_module import TdxBxbClient
```

### 3. 初始化客户端
在全局变量区域添加：

```python
# 初始化扫雷客户端 (缓存30分钟)
bxb_client = TdxBxbClient(cache_duration=1800)
```

### 4. 添加API路由
在Flask应用初始化后添加：

```python
@app.route('/api/bxb_score/<stock_code>')
def get_bxb_score_api(stock_code):
    """获取扫雷分数API"""
    result = bxb_client.get_stock_score(stock_code)
    if result:
        return jsonify(result)
    else:
        return jsonify({'error': f'无法获取 {stock_code} 的数据'}), 404
```

### 5. 增强股票数据
在股票数据更新函数中添加：

```python
def update_stock_data():
    # ... 原有逻辑 ...
    
    # 为每只股票添加扫雷数据
    for code, data in stock_data.items():
        bxb_result = bxb_client.get_stock_score(code)
        if bxb_result:
            data.update({
                'bxb_score': bxb_result['score'],
                'bxb_risk_level': bxb_result['risk_level'],
                'bxb_risk_color': bxb_result['risk_color'],
                'bxb_display_text': f"{bxb_result['score']}分 ({bxb_result['risk_level']})"
            })
```

### 6. 前端显示
在HTML模板中添加扫雷信息显示：

```html
<div class="bxb-info">
    <span class="bxb-score" style="color: {{stock.bxb_risk_color}}">
        扫雷: {{stock.bxb_display_text}}
    </span>
</div>
```

## ⚙️ 配置选项

```python
# 自定义配置
bxb = TdxBxbClient(
    cache_duration=3600,    # 缓存时间(秒), 默认1小时
    request_timeout=10      # 请求超时(秒), 默认10秒
)
```

## 🔍 高级功能

### 1. 批量更新 (异步)
```python
import threading

def batch_update_bxb_scores(stock_codes, callback=None):
    def update_worker():
        results = bxb_client.get_batch_scores(stock_codes)
        if callback:
            callback(results)
    
    thread = threading.Thread(target=update_worker)
    thread.daemon = True
    thread.start()
```

### 2. 缓存管理
```python
# 获取缓存信息
cache_info = bxb_client.get_cache_info()
print(f"已缓存股票数: {cache_info['cached_stocks']}")

# 清空缓存
bxb_client.clear_cache()
```

### 3. 风险统计分析
```python
def get_portfolio_risk_summary(stock_list):
    """获取投资组合风险摘要"""
    scores = []
    risk_distribution = {}
    
    for stock in stock_list:
        if 'bxb_score' in stock:
            scores.append(stock['bxb_score'])
            level = stock['bxb_risk_level']
            risk_distribution[level] = risk_distribution.get(level, 0) + 1
    
    return {
        'average_score': sum(scores) / len(scores) if scores else 0,
        'risk_distribution': risk_distribution,
        'high_risk_count': len([s for s in scores if s < 60])
    }
```

## 🚨 注意事项

1. **请求频率控制**: 建议批量请求时设置适当的延迟 (0.3-0.5秒)
2. **缓存策略**: 默认缓存1小时，可根据需要调整
3. **错误处理**: 网络异常时会返回None，请做好判断
4. **数据时效性**: 爆雷宝数据实时更新，建议定期刷新缓存

## 📝 示例代码

完整的集成示例请参考 `bxb_integration_example.py` 文件。

## 🐛 故障排除

### 常见问题

1. **获取数据失败**
   - 检查网络连接
   - 确认股票代码格式正确
   - 检查是否被服务器限制访问频率

2. **分数计算不准确**
   - 本模块已通过实际验证，如有疑问请对比官方页面

3. **性能问题**
   - 适当调整缓存时间
   - 使用批量接口而非单个请求
   - 考虑异步处理大量数据

## 📞 技术支持

如有问题，请检查：
1. 模块是否正确导入
2. 网络连接是否正常
3. 股票代码格式是否正确
4. 是否有足够的缓存空间

---

**版本**: 1.0  
**更新日期**: 2025-01-22  
**兼容性**: Python 3.6+, Flask 1.0+
