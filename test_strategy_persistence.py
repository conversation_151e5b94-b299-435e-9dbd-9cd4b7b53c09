#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略配置持久化测试工具
====================

用于测试持仓系统的卖出策略配置是否能正确持久化保存。

测试内容：
1. 配置文件读写测试
2. 策略启用/禁用状态测试
3. 参数修改持久化测试
4. 重启后配置保持测试

作者: AI Assistant
版本: 1.0
日期: 2025-07-24
"""

import json
import os
import time
import requests
from datetime import datetime

def test_config_file_exists():
    """测试配置文件是否存在"""
    print("🔍 测试1: 检查配置文件是否存在")
    
    config_file = 'strategy_config.json'
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"✅ 配置文件存在，包含 {len(config)} 个策略")
            return True, config
        except Exception as e:
            print(f"❌ 配置文件损坏: {e}")
            return False, {}
    else:
        print("❌ 配置文件不存在")
        return False, {}

def test_config_completeness(config):
    """测试配置完整性"""
    print("\n🔍 测试2: 检查配置完整性")
    
    expected_strategies = [
        'distance_from_low',
        'pe_abnormal', 
        'pb_overvalued',
        'profit_target',
        'dividend_yield_low',
        'scan_risk_high',
        'high_gain_sell',
        'high_ttm_medium_gain',
        'limit_up_high_pb_clearance',
        'extreme_ttm_profit_clearance'
    ]
    
    missing_strategies = []
    for strategy_id in expected_strategies:
        if strategy_id not in config:
            missing_strategies.append(strategy_id)
    
    if missing_strategies:
        print(f"❌ 缺失策略: {missing_strategies}")
        return False
    else:
        print(f"✅ 配置完整，包含所有 {len(expected_strategies)} 个策略")
        return True

def test_api_connection():
    """测试API连接"""
    print("\n🔍 测试3: 检查API连接")
    
    try:
        response = requests.get('http://localhost:5000/api/strategies', timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                strategies = data.get('data', {})
                print(f"✅ API连接正常，返回 {len(strategies)} 个策略")
                return True, strategies
            else:
                print(f"❌ API返回错误: {data.get('message', '未知错误')}")
                return False, {}
        else:
            print(f"❌ API请求失败: HTTP {response.status_code}")
            return False, {}
    except Exception as e:
        print(f"❌ API连接失败: {e}")
        print("💡 请确保持仓系统正在运行 (python \"持仓系统_v13 - 副本 - 副本.py\")")
        return False, {}

def test_strategy_modification():
    """测试策略修改"""
    print("\n🔍 测试4: 测试策略修改")
    
    try:
        # 获取当前配置
        response = requests.get('http://localhost:5000/api/strategies', timeout=5)
        if response.status_code != 200:
            print("❌ 无法获取当前策略配置")
            return False
        
        current_config = response.json()['data']
        
        # 修改一个策略的状态（选择一个当前禁用的策略来启用）
        test_strategy = 'profit_target'  # 盈利目标策略通常是禁用的
        
        if test_strategy not in current_config:
            print(f"❌ 测试策略 {test_strategy} 不存在")
            return False
        
        original_enabled = current_config[test_strategy].get('enabled', False)
        new_enabled = not original_enabled
        
        print(f"📝 修改策略 '{current_config[test_strategy]['name']}' 状态: {original_enabled} -> {new_enabled}")
        
        # 发送修改请求
        update_data = {
            test_strategy: {
                'enabled': new_enabled
            }
        }
        
        response = requests.post('http://localhost:5000/api/strategies', 
                               json=update_data, timeout=5)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 策略修改请求成功")
                
                # 验证修改是否生效
                time.sleep(1)  # 等待保存完成
                
                response = requests.get('http://localhost:5000/api/strategies', timeout=5)
                if response.status_code == 200:
                    updated_config = response.json()['data']
                    actual_enabled = updated_config[test_strategy].get('enabled', False)
                    
                    if actual_enabled == new_enabled:
                        print("✅ 策略修改已生效")
                        
                        # 恢复原始状态
                        restore_data = {test_strategy: {'enabled': original_enabled}}
                        requests.post('http://localhost:5000/api/strategies', 
                                    json=restore_data, timeout=5)
                        print(f"🔄 已恢复原始状态: {original_enabled}")
                        
                        return True
                    else:
                        print(f"❌ 策略修改未生效: 期望 {new_enabled}, 实际 {actual_enabled}")
                        return False
                else:
                    print("❌ 无法验证修改结果")
                    return False
            else:
                print(f"❌ 策略修改失败: {result.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ 策略修改请求失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 策略修改测试失败: {e}")
        return False

def test_config_persistence():
    """测试配置持久化"""
    print("\n🔍 测试5: 测试配置文件持久化")
    
    config_file = 'strategy_config.json'
    
    # 读取修改前的文件时间戳
    if os.path.exists(config_file):
        before_mtime = os.path.getmtime(config_file)
        print(f"📅 配置文件修改时间: {datetime.fromtimestamp(before_mtime)}")
        
        # 强制保存配置
        try:
            response = requests.post('http://localhost:5000/api/strategies/save', timeout=5)
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print("✅ 强制保存配置成功")
                    
                    # 检查文件是否更新
                    time.sleep(1)
                    after_mtime = os.path.getmtime(config_file)
                    
                    if after_mtime > before_mtime:
                        print(f"✅ 配置文件已更新: {datetime.fromtimestamp(after_mtime)}")
                        return True
                    else:
                        print("⚠️ 配置文件时间戳未变化（可能内容相同）")
                        return True
                else:
                    print(f"❌ 强制保存失败: {result.get('message', '未知错误')}")
                    return False
            else:
                print(f"❌ 强制保存请求失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 强制保存测试失败: {e}")
            return False
    else:
        print("❌ 配置文件不存在")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 策略配置持久化测试工具")
    print("=" * 60)
    
    test_results = []
    
    # 测试1: 配置文件存在性
    exists, config = test_config_file_exists()
    test_results.append(("配置文件存在", exists))
    
    if exists:
        # 测试2: 配置完整性
        complete = test_config_completeness(config)
        test_results.append(("配置完整性", complete))
        
        # 测试3: API连接
        api_ok, api_config = test_api_connection()
        test_results.append(("API连接", api_ok))
        
        if api_ok:
            # 测试4: 策略修改
            modify_ok = test_strategy_modification()
            test_results.append(("策略修改", modify_ok))
            
            # 测试5: 配置持久化
            persist_ok = test_config_persistence()
            test_results.append(("配置持久化", persist_ok))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！策略配置持久化工作正常。")
    else:
        print("⚠️ 部分测试失败，请检查配置或系统状态。")
        print("\n💡 建议操作:")
        print("1. 运行配置修复工具: python fix_strategy_config_enhanced.py")
        print("2. 重启持仓系统")
        print("3. 重新运行此测试")

if __name__ == '__main__':
    main()
