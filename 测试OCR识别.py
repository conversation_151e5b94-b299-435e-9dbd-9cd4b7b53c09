"""
测试OCR识别扫雷宝评分
"""

import os
import re
from PIL import Image
import pytesseract

def test_ocr_on_screenshot():
    """测试OCR识别截图"""
    
    screenshot_path = "test_screenshot.png"
    
    if not os.path.exists(screenshot_path):
        print("❌ 截图文件不存在，请先运行Chrome测试")
        return None
    
    print("🔍 测试OCR识别...")
    
    # 设置tesseract路径
    pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
    
    try:
        # 打开图片
        image = Image.open(screenshot_path)
        print(f"📸 图片尺寸: {image.size}")
        
        # 多种OCR配置测试
        ocr_configs = [
            ('基本配置', '--psm 6'),
            ('数字+分字', '--psm 6 -c tessedit_char_whitelist=0123456789分'),
            ('单词模式', '--psm 8 -c tessedit_char_whitelist=0123456789'),
            ('单行模式', '--psm 7 -c tessedit_char_whitelist=0123456789分'),
            ('原始行', '--psm 13 -c tessedit_char_whitelist=0123456789'),
        ]
        
        all_found_scores = []
        
        for name, config in ocr_configs:
            try:
                print(f"\n🔍 测试配置: {name}")
                text = pytesseract.image_to_string(image, lang='chi_sim+eng', config=config)
                print(f"   识别文本: '{text.strip()}'")
                
                # 提取分数
                scores = extract_scores_from_text(text)
                if scores:
                    print(f"   提取分数: {scores}")
                    all_found_scores.extend(scores)
                else:
                    print(f"   未找到分数")
                    
            except Exception as e:
                print(f"   ❌ 配置失败: {e}")
        
        # 分析结果
        if all_found_scores:
            from collections import Counter
            score_counts = Counter(all_found_scores)
            print(f"\n📊 所有找到的分数: {all_found_scores}")
            print(f"📊 分数统计: {dict(score_counts)}")
            
            # 选择最常出现的分数
            final_score = score_counts.most_common(1)[0][0]
            print(f"🎯 最终评分: {final_score}分")
            
            return final_score
        else:
            print("\n❌ 未能识别出任何有效分数")
            
            # 显示原始识别结果用于调试
            print("\n🔍 原始OCR结果（调试用）:")
            try:
                raw_text = pytesseract.image_to_string(image, lang='chi_sim+eng')
                print(f"原始文本: '{raw_text}'")
                
                # 查找所有数字
                all_numbers = re.findall(r'\d+', raw_text)
                print(f"所有数字: {all_numbers}")
                
            except Exception as e:
                print(f"原始OCR失败: {e}")
            
            return None
            
    except Exception as e:
        print(f"❌ OCR处理失败: {e}")
        return None

def extract_scores_from_text(text: str) -> list:
    """从文本中提取分数"""
    scores = []
    
    # 多种分数模式
    patterns = [
        r'(\d{1,3})分',           # "97分"
        r'(\d{1,3})\s*分',        # "97 分"
        r'分\s*(\d{1,3})',        # "分97"
        r'(\d{1,3})',             # 纯数字
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, text)
        for match in matches:
            try:
                score = int(match)
                if 0 <= score <= 100:  # 合理的分数范围
                    scores.append(score)
            except ValueError:
                continue
    
    return scores

def get_slb_score_with_ocr(stock_code: str) -> float:
    """完整的获取扫雷宝评分流程"""
    
    print(f"🎯 获取 {stock_code} 的扫雷宝评分...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        
        # 配置浏览器
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1200,800')
        
        # 启动浏览器
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        
        # 访问扫雷宝页面
        url = f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code={stock_code}&color=0"
        print(f"🌐 访问: {url}")
        driver.get(url)
        
        # 等待页面加载
        import time
        time.sleep(3)
        
        # 截图
        screenshot_path = f"slb_{stock_code}.png"
        driver.save_screenshot(screenshot_path)
        print(f"📸 截图保存: {screenshot_path}")
        
        # 关闭浏览器
        driver.quit()
        
        # OCR识别
        if os.path.exists(screenshot_path):
            file_size = os.path.getsize(screenshot_path)
            print(f"📸 截图大小: {file_size} 字节")
            
            # 设置tesseract路径
            pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
            
            # 识别评分
            image = Image.open(screenshot_path)
            
            # 尝试多种配置
            configs = [
                '--psm 6 -c tessedit_char_whitelist=0123456789分',
                '--psm 8 -c tessedit_char_whitelist=0123456789',
                '--psm 7 -c tessedit_char_whitelist=0123456789分',
            ]
            
            all_scores = []
            for config in configs:
                try:
                    text = pytesseract.image_to_string(image, lang='chi_sim+eng', config=config)
                    scores = extract_scores_from_text(text)
                    all_scores.extend(scores)
                except:
                    continue
            
            if all_scores:
                # 过滤合理分数
                valid_scores = [s for s in all_scores if 50 <= s <= 100]
                if valid_scores:
                    from collections import Counter
                    final_score = Counter(valid_scores).most_common(1)[0][0]
                    print(f"✅ 识别评分: {final_score}分")
                    return float(final_score)
            
            print("❌ 未能识别出有效评分")
            return None
        else:
            print("❌ 截图文件不存在")
            return None
            
    except Exception as e:
        print(f"❌ 获取评分失败: {e}")
        return None

def main():
    """主测试函数"""
    print("🔍 OCR识别测试")
    print("=" * 40)
    
    # 1. 测试现有截图
    print("1. 测试现有截图...")
    score = test_ocr_on_screenshot()
    
    # 2. 完整流程测试
    print("\n2. 完整流程测试...")
    test_codes = ["300479", "000001"]
    
    for code in test_codes:
        print(f"\n--- 测试股票: {code} ---")
        score = get_slb_score_with_ocr(code)
        
        if score is not None:
            print(f"🎉 {code}: {score}分")
            
            # 验证神思电子
            if code == "300479":
                if 90 <= score <= 100:
                    print("✅ 神思电子评分合理")
                else:
                    print("⚠️ 神思电子评分可能有误")
        else:
            print(f"❌ {code}: 获取失败")
        
        # 间隔
        import time
        time.sleep(2)

if __name__ == "__main__":
    main()
