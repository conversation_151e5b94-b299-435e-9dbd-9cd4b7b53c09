{"basic_gain_reduce": {"name": "基础涨幅减半策略", "description": "涨幅≥70%时减半", "enabled": true, "params": {"gain_threshold": 70, "warning_offset": 10}, "weight": 1, "category": "basic_gain", "action": "reduce"}, "basic_gain_clearance": {"name": "基础涨幅清仓策略", "description": "涨幅≥140%时清仓", "enabled": true, "params": {"gain_threshold": 140, "warning_offset": 10}, "weight": 2, "category": "basic_gain", "action": "clearance"}, "limit_up_high_pb_clearance": {"name": "涨停高PB清仓策略", "description": "涨停 且 PB≥1.75 且 涨幅≥70%时清仓", "enabled": true, "params": {"gain_threshold": 70, "limit_up_threshold": 9.8, "pb_threshold": 1.75, "warning_offset": 10}, "weight": 2, "category": "limit_up_pb", "action": "clearance"}, "limit_up_low_pb_reduce": {"name": "涨停低PB减半策略", "description": "涨停 且 PB≤1.75 且 涨幅≥70%时减半", "enabled": true, "params": {"gain_threshold": 70, "limit_up_threshold": 9.8, "pb_threshold": 1.75, "warning_offset": 10}, "weight": 1.5, "category": "limit_up_pb", "action": "reduce"}, "high_ttm_reduce": {"name": "高TTM减半策略", "description": "TTM≥30 且 涨幅≥50%时减半", "enabled": true, "params": {"gain_threshold": 50, "ttm_threshold": 30, "warning_offset": 10}, "weight": 1.2, "category": "high_ttm", "action": "reduce"}, "high_ttm_clearance": {"name": "高TTM清仓策略", "description": "TTM≥30 且 涨幅≥100%时清仓", "enabled": true, "params": {"gain_threshold": 100, "ttm_threshold": 30, "warning_offset": 10}, "weight": 2, "category": "high_ttm", "action": "clearance"}, "high_ttm_limit_up_high_pb_clearance": {"name": "高TTM涨停高PB清仓策略", "description": "TTM≥30 且 涨停 且 PB≥1.75 且 涨幅≥50%时清仓", "enabled": true, "params": {"gain_threshold": 50, "limit_up_threshold": 9.8, "pb_threshold": 1.75, "ttm_threshold": 30, "warning_offset": 10}, "weight": 2.5, "category": "high_ttm_limit_up", "action": "clearance"}, "high_ttm_limit_up_low_pb_reduce": {"name": "高TTM涨停低PB减半策略", "description": "TTM≥30 且 涨停 且 PB≤1.75 且 涨幅≥50%时减半", "enabled": true, "params": {"gain_threshold": 50, "limit_up_threshold": 9.8, "pb_threshold": 1.75, "ttm_threshold": 30, "warning_offset": 9}, "weight": 1.8, "category": "high_ttm_limit_up", "action": "reduce"}, "negative_ttm_low_dividend_low_scan_reduce": {"name": "负TTM低股息低扫雷减半策略", "description": "TTM≤0 且 股息≤2% 且 扫雷分数≤70 且 涨幅≥30%时减半", "enabled": true, "params": {"dividend_max": 2, "gain_threshold": 30, "scan_score_max": 70, "ttm_max": 0, "warning_offset": 9}, "weight": 1.3, "category": "negative_ttm_risk", "action": "reduce"}, "negative_ttm_low_dividend_low_scan_clearance": {"name": "负TTM低股息低扫雷清仓策略", "description": "TTM≤0 且 股息≤2% 且 扫雷分数≤70 且 涨幅≥50%时清仓", "enabled": true, "params": {"dividend_max": 2, "gain_threshold": 50, "scan_score_max": 70, "ttm_max": 0, "warning_offset": 10}, "weight": 2.2, "category": "negative_ttm_risk", "action": "clearance"}, "negative_ttm_profit_clearance": {"name": "负TTM回本清仓策略", "description": "TTM≤0 且 股息≤3% 且 接近/已回本时清仓", "enabled": true, "params": {"dividend_max": 3, "profit_threshold": 10, "require_profit": true, "ttm_max": 0}, "weight": 2, "category": "negative_ttm_profit", "action": "clearance"}}