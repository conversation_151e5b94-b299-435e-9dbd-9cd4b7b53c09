#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试东方财富API返回的字段
"""

import requests
import time
import json

def debug_eastmoney_fields(stock_code):
    """调试东方财富API返回的所有字段"""
    
    # 根据股票代码确定市场
    if stock_code.startswith('6'):
        secid = f'1.{stock_code}'
    else:
        secid = f'0.{stock_code}'
    
    url = 'http://push2.eastmoney.com/api/qt/stock/get'
    
    # 请求所有可能的字段
    params = {
        'fltt': '2',
        'invt': '2',
        'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13,f14,f15,f16,f17,f18,f19,f20,f21,f22,f23,f24,f25,f26,f27,f28,f29,f30,f31,f32,f33,f34,f35,f36,f37,f38,f39,f40,f41,f42,f43,f44,f45,f46,f47,f48,f49,f50,f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f62,f63,f64,f65,f66,f67,f68,f69,f70,f71,f72,f73,f74,f75,f76,f77,f78,f79,f80,f81,f82,f83,f84,f85,f86,f87,f88,f89,f90,f91,f92,f93,f94,f95,f96,f97,f98,f99,f100,f101,f102,f103,f104,f105,f106,f107,f108,f109,f110,f111,f112,f113,f114,f115,f116,f117,f118,f119,f120,f121,f122,f123,f124,f125,f126,f127,f128,f129,f130,f131,f132,f133,f134,f135,f136,f137,f138,f139,f140',
        'secid': secid,
        '_': str(int(time.time() * 1000))
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'http://quote.eastmoney.com/',
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = json.loads(response.text)['data']
            
            if data:
                print(f"=== 股票 {stock_code} 的所有字段 ===")
                
                # 按照F参数表的含义显示
                field_meanings = {
                    'f2': '最新价',
                    'f3': '涨跌幅',
                    'f4': '涨跌额',
                    'f9': '市盈率(动态)',
                    'f12': '股票代码',
                    'f14': '股票名称',
                    'f23': '市净率',
                    'f57': '代码',
                    'f58': '名称',
                    'f114': '市盈率(静态)',
                    'f115': '市盈率(TTM)',
                    'f130': '市销率TTM',
                    'f131': '市现率TTM',
                }
                
                print("重要字段:")
                for field, meaning in field_meanings.items():
                    if field in data:
                        print(f"  {field} ({meaning}): {data[field]}")
                    else:
                        print(f"  {field} ({meaning}): 不存在")
                
                print(f"\n所有返回的字段:")
                for key, value in data.items():
                    meaning = field_meanings.get(key, '未知')
                    print(f"  {key}: {value} ({meaning})")
                
                return data
        
        return None
        
    except Exception as e:
        print(f"调试失败: {e}")
        return None

def main():
    """主函数"""
    print("=== 调试东方财富API字段 ===")
    
    test_codes = ['000001', '600000', '600036']
    
    for code in test_codes:
        print(f"\n{'='*60}")
        debug_eastmoney_fields(code)
        time.sleep(1)

if __name__ == "__main__":
    main()
