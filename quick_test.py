#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 检查主程序是否有问题
"""

import sys
import traceback

def test_imports():
    """测试导入"""
    print("🔍 测试导入...")
    try:
        import json
        import os
        from datetime import datetime
        from flask import Flask, jsonify, request, render_template_string
        print("✅ 基本导入成功")

        # 将os设为全局变量
        globals()['os'] = os
        globals()['json'] = json
        
        # 测试自定义模块
        from A股交易时间监测_简化版 import AStockTradingTimeMonitor
        print("✅ 交易时间监测模块导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        traceback.print_exc()
        return False

def test_data_files():
    """测试数据文件"""
    print("\n📁 测试数据文件...")
    
    files_to_check = [
        'imported_stock_list.json',
        'stock_data_cache.json',
        'app_config.json'
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                print(f"✅ {file_path} 读取成功")
            except Exception as e:
                print(f"❌ {file_path} 读取失败: {e}")
        else:
            print(f"⚠️ {file_path} 不存在")

def test_main_script():
    """测试主脚本语法"""
    print("\n🔍 测试主脚本语法...")
    
    try:
        with open('持仓系统_v13 - 副本 - 副本.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 编译检查
        compile(content, '持仓系统_v13 - 副本 - 副本.py', 'exec')
        print("✅ 主脚本语法检查通过")
        return True
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        print(f"   行号: {e.lineno}")
        print(f"   位置: {e.offset}")
        print(f"   文本: {e.text}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_flask_basic():
    """测试基本Flask应用"""
    print("\n🌐 测试基本Flask应用...")
    
    try:
        from flask import Flask, jsonify
        
        app = Flask(__name__)
        
        @app.route('/test')
        def test():
            return jsonify({'status': 'ok', 'message': 'Flask测试成功'})
        
        print("✅ Flask应用创建成功")
        
        # 测试启动（不实际运行）
        print("✅ Flask路由配置成功")
        return True
        
    except Exception as e:
        print(f"❌ Flask测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 快速诊断测试")
    print("=" * 50)
    
    all_passed = True
    
    # 测试导入
    if not test_imports():
        all_passed = False
    
    # 测试数据文件
    test_data_files()
    
    # 测试主脚本
    if not test_main_script():
        all_passed = False
    
    # 测试Flask
    if not test_flask_basic():
        all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("✅ 所有基本测试通过")
        print("💡 问题可能在于运行时逻辑，建议检查:")
        print("   1. 数据加载逻辑")
        print("   2. 全局变量初始化")
        print("   3. 线程启动顺序")
    else:
        print("❌ 发现问题，请先修复上述错误")

if __name__ == "__main__":
    main()
