#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试东方财富K线API是否可用
"""

import requests
from urllib.parse import urlencode
from datetime import datetime, timedelta

def test_eastmoney_kline_api():
    """测试东方财富K线API"""
    
    print("=== 测试东方财富K线API ===")
    
    # 测试股票代码
    code = '000001'
    
    # 计算日期范围（最近30天）
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    start_date_str = start_date.strftime('%Y%m%d')
    end_date_str = end_date.strftime('%Y%m%d')
    
    print(f"测试股票: {code}")
    print(f"日期范围: {start_date_str} - {end_date_str}")
    
    # 构建请求参数
    secid = f'0.{code}'  # 深市股票
    
    params = {
        'fields1': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13',
        'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
        'beg': start_date_str,
        'end': end_date_str,
        'rtntype': '6',
        'secid': secid,
        'klt': '101',  # 日K
        'fqt': '1',    # 前复权
    }
    
    base_url = 'https://push2his.eastmoney.com/api/qt/stock/kline/get'
    url = base_url + '?' + urlencode(params)
    
    print(f"\n请求URL:")
    print(url)
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; WOW64; Trident/7.0; Touch; rv:11.0) like Gecko',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
        'Referer': 'http://quote.eastmoney.com/center/gridlist.html',
    }
    
    try:
        print(f"\n正在请求数据...")
        response = requests.get(url, headers=headers, timeout=15)
        
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            json_data = response.json()
            
            print(f"响应数据结构:")
            print(f"  - rc: {json_data.get('rc')}")
            print(f"  - rt: {json_data.get('rt')}")
            print(f"  - data存在: {'data' in json_data}")
            
            if 'data' in json_data and json_data['data']:
                data = json_data['data']
                print(f"  - 股票名称: {data.get('name', '未知')}")
                print(f"  - K线数据条数: {len(data.get('klines', []))}")
                
                if 'klines' in data and data['klines']:
                    # 显示前几条K线数据
                    print(f"\n前3条K线数据:")
                    for i, kline in enumerate(data['klines'][:3]):
                        kline_data = kline.split(',')
                        if len(kline_data) >= 6:
                            date = kline_data[0]
                            open_price = kline_data[1]
                            close_price = kline_data[2]
                            high_price = kline_data[3]
                            low_price = kline_data[4]
                            volume = kline_data[5]
                            
                            print(f"  {i+1}. 日期:{date} 开:{open_price} 收:{close_price} 高:{high_price} 低:{low_price}")
                    
                    # 计算最低价
                    all_lows = []
                    for kline in data['klines']:
                        kline_data = kline.split(',')
                        if len(kline_data) >= 5:
                            low_price = float(kline_data[4])
                            all_lows.append(low_price)
                    
                    if all_lows:
                        min_low = min(all_lows)
                        print(f"\n30天内最低价: {min_low}")
                        
                        # 当前价格（最后一天收盘价）
                        last_kline = data['klines'][-1].split(',')
                        current_price = float(last_kline[2])
                        
                        distance_pct = (current_price - min_low) / min_low * 100
                        print(f"当前价格: {current_price}")
                        print(f"距离最低价: {distance_pct:+.1f}%")
                        
                        return True
                else:
                    print("  - 无K线数据")
            else:
                print("  - 无data数据")
                print(f"完整响应: {json_data}")
        else:
            print(f"请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text[:500]}")
        
        return False
        
    except Exception as e:
        print(f"请求异常: {e}")
        return False

def test_different_markets():
    """测试不同市场的股票"""
    
    print("\n=== 测试不同市场股票 ===")
    
    test_stocks = [
        ('000001', '0.000001', '平安银行-深市'),
        ('600000', '1.600000', '浦发银行-沪市'),
        ('600036', '1.600036', '招商银行-沪市'),
    ]
    
    for code, secid, name in test_stocks:
        print(f"\n测试 {name} ({secid}):")
        
        # 简化的请求
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y%m%d')
        
        params = {
            'fields1': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13',
            'fields2': 'f51,f52,f53,f54,f55',
            'beg': start_date,
            'end': end_date,
            'rtntype': '6',
            'secid': secid,
            'klt': '101',
            'fqt': '1',
        }
        
        url = 'https://push2his.eastmoney.com/api/qt/stock/kline/get?' + urlencode(params)
        
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if 'data' in data and data['data'] and 'klines' in data['data']:
                    klines_count = len(data['data']['klines'])
                    print(f"  ✅ 成功获取 {klines_count} 条K线数据")
                else:
                    print(f"  ❌ 无K线数据")
            else:
                print(f"  ❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"  ❌ 请求异常: {e}")

def main():
    """主函数"""
    print("=== 东方财富K线API测试工具 ===")
    
    # 基础API测试
    success = test_eastmoney_kline_api()
    
    if success:
        print("\n✅ 基础API测试成功！")
    else:
        print("\n❌ 基础API测试失败！")
    
    # 测试不同市场
    test_different_markets()

if __name__ == "__main__":
    main()
