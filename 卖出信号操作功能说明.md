# 卖出信号操作功能说明

## 功能概述

在持仓系统中，当股票出现卖出信号（🚨卖出、🔥清仓、⚠️预警）时，您现在可以直接点击卖出信号来选择操作，并停止企业微信提醒。

## 使用方法

### 1. 点击卖出信号
- 在股票表格的"卖出信号"列中，当股票显示卖出信号时
- 直接点击卖出信号标签（如：🚨卖出、🔥清仓、⚠️预警）
- 系统会弹出操作菜单

### 2. 选择操作类型
操作菜单提供两个选项：

#### ✅ 已减半
- 表示您已经减持了一半的持仓
- 选择后会标记该股票为"已减半"状态
- 停止所有企业微信提醒

#### 🔥 已清仓  
- 表示您已经完全清仓该股票
- 选择后会标记该股票为"已清仓"状态
- 停止所有企业微信提醒

#### ❌ 取消
- 取消操作，关闭菜单

### 3. 确认操作
- 选择操作后，系统会弹出确认对话框
- 确认后，系统会：
  - 标记股票状态
  - 停止企业微信提醒
  - 在表格中显示操作状态
  - 记录操作时间

## 状态显示

### 卖出信号列
- **可点击状态**：当股票有卖出信号且未处理时，信号标签可点击，鼠标悬停会有放大效果
- **已减半状态**：显示"✅已减半"标记
- **已清仓状态**：显示"🔥已清仓"标记

### 操作列
- **未处理**：显示"✅"按钮（原有的标记已减半功能）
- **已减半**：显示"✅已减半"标记
- **已清仓**：显示"🔥已清仓"标记

## 企业微信提醒

一旦标记为"已减半"或"已清仓"：
- 该股票的所有企业微信提醒将永久停止
- 包括卖出信号、预警信号、高涨幅提醒等
- 系统会在企业微信提醒缓存中标记该股票为已处理

## 技术实现

### 后端API
- `POST /api/mark-action`：标记股票操作
  - 参数：`stock_code`（股票代码）、`action_type`（操作类型：reduced/cleared）
  - 返回：操作结果和详细信息

### 前端功能
- 点击卖出信号触发`showActionMenu()`函数
- 操作菜单使用绝对定位，跟随鼠标位置
- 自动处理菜单边界，防止超出屏幕
- 点击其他区域自动关闭菜单

### 数据存储
- `is_reduced`：是否已减半
- `reduced_time`：减半时间
- `is_cleared`：是否已清仓  
- `cleared_time`：清仓时间

## 注意事项

1. **操作不可逆**：一旦标记为已减半或已清仓，无法通过界面撤销
2. **提醒停止**：标记后将永久停止该股票的企业微信提醒
3. **状态优先级**：已清仓状态优先于已减半状态显示
4. **兼容性**：保留原有的"✅"按钮功能，用于快速标记已减半

## 使用建议

- 在实际减持或清仓后及时标记，避免无效提醒
- 根据实际操作选择合适的标记类型
- 定期检查已标记的股票状态，确保与实际持仓一致
