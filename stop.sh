#!/bin/bash
# 停止持仓系统

echo "🛑 停止持仓系统..."

# 查找并停止Gunicorn进程
PIDS=$(pgrep -f "gunicorn.*持仓系统")

if [ -z "$PIDS" ]; then
    echo "⚠️ 没有找到运行中的进程"
    exit 1
fi

# 优雅停止
echo "📊 正在停止进程..."
for PID in $PIDS; do
    echo "停止进程 $PID"
    kill -TERM $PID
done

# 等待进程停止
sleep 5

# 检查是否还有进程在运行
REMAINING=$(pgrep -f "gunicorn.*持仓系统")
if [ ! -z "$REMAINING" ]; then
    echo "⚠️ 强制停止剩余进程..."
    for PID in $REMAINING; do
        kill -KILL $PID
    done
fi

echo "✅ 持仓系统已停止"
