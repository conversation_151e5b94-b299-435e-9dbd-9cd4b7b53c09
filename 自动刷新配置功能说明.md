# 自动刷新配置功能说明

## 功能概述

在持仓系统中新增了一个配置开关，用于控制程序启动时是否立即刷新股票数据。这个功能可以帮助用户根据需要选择启动行为，提高系统的灵活性。

## 功能特性

### 🔧 配置选项
- **配置名称**: `auto_refresh_on_startup`
- **默认值**: `False` (关闭状态)
- **存储位置**: `app_config.json` 文件
- **持久化**: 重启后保持设置

### ⚙️ 功能行为

#### 开关开启时 (`True`)
- ✅ 程序启动后立即开始刷新所有股票数据
- ✅ 仅在交易时间内生效（休市时跳过）
- ✅ 显示详细的刷新进度和状态
- ✅ 自动检测交易时间和股票列表

#### 开关关闭时 (`False`)
- ⏸️ 程序启动后不自动刷新
- ⏸️ 等待用户手动触发或定时任务
- ⏸️ 减少启动时的系统负载
- ⏸️ 适合快速启动场景

### 📱 用户界面

#### 设置位置
1. 点击网页界面中的 "⚙️ 策略配置" 按钮
2. 在弹出的配置面板顶部找到 "🔧 应用设置" 区域
3. 切换 "启动时自动刷新股票数据" 复选框

#### 界面特性
- ✨ 实时保存设置变更
- ✨ 显示设置说明和提示
- ✨ 提供操作反馈通知
- ✨ 美观的界面设计

## 启动日志示例

### 开关开启时
```
⚙️ 启动配置检查...
🔄 自动刷新开关: 开启 (可在设置中修改)
📂 正在加载缓存数据...
⚙️ 正在初始化策略配置...
✅ 策略配置已保存并同步

🚀 启动时立即刷新数据...
✅ 当前为交易时间，开始立即刷新股票数据...
🔄 开始更新股票数据 (共 45 只股票)...
✅ 启动时数据刷新完成
```

### 开关关闭时
```
⚙️ 启动配置检查...
🔄 自动刷新开关: 关闭 (可在设置中修改)
📂 正在加载缓存数据...
⚙️ 正在初始化策略配置...
✅ 策略配置已保存并同步

ℹ️ 自动刷新已关闭，等待手动触发或定时任务
```

### 休市时间
```
⚙️ 启动配置检查...
🔄 自动刷新开关: 开启 (可在设置中修改)
📂 正在加载缓存数据...

🚀 启动时立即刷新数据...
⏸️ 当前为休市时间，跳过立即刷新
📅 当前时间: 2025-07-24 20:30:00, 下一个交易日: 2025-07-25
```

## 技术实现

### 配置管理
```python
# 配置结构
CONFIG = {
    'stock_interval': 0.2,
    'round_interval': 600,
    'request_timeout': 15,
    'alert_threshold': 70.0,
    'auto_refresh_on_startup': False,  # 新增配置项
}

# 配置文件: app_config.json
{
  "stock_interval": 0.2,
  "round_interval": 600,
  "request_timeout": 15,
  "alert_threshold": 70.0,
  "auto_refresh_on_startup": false,
  "save_time": "2025-07-24 10:30:00"
}
```

### API接口
```javascript
// 获取配置
GET /api/config
Response: {
  "success": true,
  "config": { ... }
}

// 更新配置
POST /api/config
Body: {
  "auto_refresh_on_startup": true
}
Response: {
  "success": true,
  "message": "配置已更新",
  "config": { ... }
}
```

### 启动流程
1. **加载配置** → 读取 `app_config.json`
2. **显示状态** → 输出开关状态到日志
3. **条件检查** → 验证开关状态和交易时间
4. **执行刷新** → 如果条件满足则立即刷新
5. **继续启动** → 正常启动其他服务

## 使用指南

### 首次使用
1. 启动持仓系统
2. 打开网页界面 (http://localhost:5000)
3. 点击 "⚙️ 策略配置" 按钮
4. 在应用设置中切换自动刷新开关
5. 重启系统验证功能

### 推荐设置

#### 适合开启的场景
- 🏢 **办公环境**: 需要最新数据进行决策
- 📊 **交易时间**: 市场开盘期间使用
- 🔄 **定期监控**: 需要实时跟踪股票变化
- 💻 **高性能设备**: 系统资源充足

#### 适合关闭的场景
- 🏠 **家庭环境**: 非交易时间使用
- 📱 **移动设备**: 网络或性能受限
- ⚡ **快速启动**: 需要快速查看历史数据
- 🔋 **节能模式**: 减少系统负载

### 故障排除

#### 配置不生效
1. 检查 `app_config.json` 文件是否存在
2. 验证文件内容格式是否正确
3. 查看启动日志中的配置状态
4. 重新在界面中设置一次

#### 自动刷新失败
1. 确认当前为交易时间
2. 检查网络连接状态
3. 验证股票列表是否已导入
4. 查看详细错误日志

#### 界面设置无效
1. 检查浏览器控制台错误
2. 确认API接口正常响应
3. 清除浏览器缓存重试
4. 检查服务器日志

## 测试验证

### 自动测试
```bash
# 运行测试脚本
python test_auto_refresh_config.py
```

### 手动测试
1. **配置切换测试**
   - 在界面中开启/关闭开关
   - 验证设置是否立即保存
   - 检查通知消息是否显示

2. **重启持久化测试**
   - 设置开关状态
   - 重启持仓系统
   - 验证设置是否保持

3. **功能行为测试**
   - 开启开关后重启（交易时间）
   - 观察是否立即刷新数据
   - 关闭开关后重启验证

## 注意事项

### ⚠️ 重要提醒
- 自动刷新仅在交易时间内生效
- 需要先导入股票列表才能刷新
- 刷新过程可能需要几分钟时间
- 网络异常时会自动跳过刷新

### 💡 使用建议
- 根据使用场景合理设置开关
- 定期检查配置文件完整性
- 关注启动日志中的状态信息
- 在交易时间前设置好配置

### 🔧 维护建议
- 定期备份 `app_config.json` 文件
- 监控启动时间和性能影响
- 根据需要调整其他配置参数
- 保持系统和依赖库更新

## 更新日志

### v1.0.0 (2025-07-24)
- ✨ 新增自动刷新配置开关
- ✨ 实现配置持久化存储
- ✨ 添加网页界面设置选项
- ✨ 完善启动流程和日志输出
- ✨ 提供API接口和测试工具

---

**🎯 总结**: 这个功能让用户可以根据需要控制程序启动行为，提高了系统的灵活性和用户体验。通过简单的界面操作，就能实现启动时数据刷新的个性化配置。
