"""
配置OCR环境
自动查找和配置Tesseract OCR路径
"""
import os
import sys

def find_tesseract():
    """查找Tesseract安装路径"""
    print("🔍 查找Tesseract OCR安装路径...")
    
    # 常见的Tesseract安装路径
    possible_paths = [
        r"C:\Program Files\Tesseract-OCR\tesseract.exe",
        r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
        r"C:\Tesseract-OCR\tesseract.exe",
        r"D:\Program Files\Tesseract-OCR\tesseract.exe",
        r"D:\Tesseract-OCR\tesseract.exe",
        # 便携版可能的路径
        r"C:\tesseract\tesseract.exe",
        r"D:\tesseract\tesseract.exe",
        r".\tesseract\tesseract.exe",
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            print(f"✅ 找到Tesseract: {path}")
            return path
    
    print("❌ 未找到Tesseract安装")
    return None

def test_ocr_with_path(tesseract_path):
    """使用指定路径测试OCR"""
    try:
        import pytesseract
        from PIL import Image
        
        # 设置Tesseract路径
        pytesseract.pytesseract.tesseract_cmd = tesseract_path
        
        # 测试版本
        version = pytesseract.get_tesseract_version()
        print(f"✅ OCR版本: {version}")
        
        # 创建一个简单的测试图片
        print("🧪 创建测试图片...")
        test_image = Image.new('RGB', (200, 100), color='white')
        
        # 这里应该添加一些文字，但为了简单起见，我们直接测试
        try:
            text = pytesseract.image_to_string(test_image, lang='chi_sim+eng')
            print("✅ OCR测试成功")
            return True
        except Exception as e:
            print(f"❌ OCR测试失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ OCR配置失败: {e}")
        return False

def create_ocr_config():
    """创建OCR配置文件"""
    tesseract_path = find_tesseract()
    
    if not tesseract_path:
        print("\n💡 请手动安装Tesseract OCR:")
        print("1. 下载: https://github.com/UB-Mannheim/tesseract/releases")
        print("2. 安装时选择中文语言包")
        print("3. 重新运行此脚本")
        return False
    
    # 测试OCR
    if test_ocr_with_path(tesseract_path):
        # 创建配置文件
        config_content = f'''"""
OCR配置文件
自动生成，请勿手动修改
"""
import pytesseract

# Tesseract路径配置
TESSERACT_PATH = r"{tesseract_path}"
pytesseract.pytesseract.tesseract_cmd = TESSERACT_PATH

# OCR配置
OCR_CONFIG = {{
    'lang': 'chi_sim+eng',  # 中英文混合
    'config': '--psm 6'     # 页面分割模式
}}

def setup_ocr():
    """设置OCR环境"""
    pytesseract.pytesseract.tesseract_cmd = TESSERACT_PATH
    return True
'''
        
        with open('ocr_config.py', 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        print("✅ OCR配置文件已创建: ocr_config.py")
        return True
    
    return False

def main():
    """主函数"""
    print("🎯 OCR环境配置")
    print("=" * 40)
    
    if create_ocr_config():
        print("\n🎉 OCR配置成功！")
        print("💡 现在可以使用OCR功能了")
        
        # 测试导入
        try:
            import ocr_config
            ocr_config.setup_ocr()
            print("✅ 配置文件测试通过")
        except Exception as e:
            print(f"❌ 配置文件测试失败: {e}")
    else:
        print("\n❌ OCR配置失败")

if __name__ == '__main__':
    main()
