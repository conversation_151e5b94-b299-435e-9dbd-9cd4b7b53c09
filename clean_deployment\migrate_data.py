#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据迁移脚本
============

从原系统迁移数据到新的模块化系统

使用方法:
python migrate_data.py --source /path/to/old/system

作者: AI Assistant
版本: 1.0
"""

import os
import shutil
import json
import argparse
from datetime import datetime


def find_files_in_directory(directory, filename_patterns):
    """在目录中查找匹配的文件"""
    found_files = []
    
    if not os.path.exists(directory):
        return found_files
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            for pattern in filename_patterns:
                if pattern in file:
                    found_files.append(os.path.join(root, file))
                    break
    
    return found_files


def migrate_data(source_dir, target_dir='.'):
    """迁移数据从源目录到目标目录"""
    
    print(f"🔄 开始数据迁移...")
    print(f"   源目录: {source_dir}")
    print(f"   目标目录: {target_dir}")
    
    # 确保目标目录存在
    os.makedirs(os.path.join(target_dir, 'data'), exist_ok=True)
    os.makedirs(os.path.join(target_dir, 'config'), exist_ok=True)
    os.makedirs(os.path.join(target_dir, 'cache'), exist_ok=True)
    os.makedirs(os.path.join(target_dir, 'uploads'), exist_ok=True)
    
    migration_count = 0
    
    # 1. 迁移股票数据缓存
    print("\n📊 迁移股票数据...")
    stock_data_files = find_files_in_directory(source_dir, ['stock_data_cache.json'])
    for file_path in stock_data_files:
        target_path = os.path.join(target_dir, 'data', 'stock_data_cache.json')
        try:
            shutil.copy2(file_path, target_path)
            print(f"   ✅ 复制: {os.path.basename(file_path)}")
            migration_count += 1
        except Exception as e:
            print(f"   ❌ 复制失败: {e}")
    
    # 2. 迁移导入列表
    print("\n📋 迁移导入列表...")
    import_files = find_files_in_directory(source_dir, ['imported_stock_list.json'])
    for file_path in import_files:
        target_path = os.path.join(target_dir, 'data', 'imported_stock_list.json')
        try:
            shutil.copy2(file_path, target_path)
            print(f"   ✅ 复制: {os.path.basename(file_path)}")
            migration_count += 1
        except Exception as e:
            print(f"   ❌ 复制失败: {e}")
    
    # 3. 迁移策略配置
    print("\n🔧 迁移策略配置...")
    strategy_files = find_files_in_directory(source_dir, ['strategy_config.json'])
    for file_path in strategy_files:
        target_path = os.path.join(target_dir, 'config', 'strategy_config.json')
        try:
            shutil.copy2(file_path, target_path)
            print(f"   ✅ 复制: {os.path.basename(file_path)}")
            migration_count += 1
        except Exception as e:
            print(f"   ❌ 复制失败: {e}")
    
    # 4. 迁移自动更新配置
    print("\n⚙️ 迁移自动更新配置...")
    auto_update_files = find_files_in_directory(source_dir, ['auto_update_config.json'])
    for file_path in auto_update_files:
        target_path = os.path.join(target_dir, 'data', 'auto_update_config.json')
        try:
            shutil.copy2(file_path, target_path)
            print(f"   ✅ 复制: {os.path.basename(file_path)}")
            migration_count += 1
        except Exception as e:
            print(f"   ❌ 复制失败: {e}")
    
    # 5. 迁移年内最低价缓存
    print("\n📈 迁移年内最低价缓存...")
    cache_files = find_files_in_directory(source_dir, ['yearly_low_cache'])
    for file_path in cache_files:
        if file_path.endswith('.csv'):
            target_path = os.path.join(target_dir, 'cache', os.path.basename(file_path))
            try:
                shutil.copy2(file_path, target_path)
                print(f"   ✅ 复制: {os.path.basename(file_path)}")
                migration_count += 1
            except Exception as e:
                print(f"   ❌ 复制失败: {e}")
    
    # 6. 迁移上传文件
    print("\n📁 迁移上传文件...")
    uploads_dir = os.path.join(source_dir, 'uploads')
    if os.path.exists(uploads_dir):
        target_uploads = os.path.join(target_dir, 'uploads')
        try:
            for file in os.listdir(uploads_dir):
                source_file = os.path.join(uploads_dir, file)
                target_file = os.path.join(target_uploads, file)
                if os.path.isfile(source_file):
                    shutil.copy2(source_file, target_file)
                    print(f"   ✅ 复制: {file}")
                    migration_count += 1
        except Exception as e:
            print(f"   ❌ 复制上传文件失败: {e}")
    
    # 7. 迁移企业微信缓存
    print("\n📱 迁移企业微信缓存...")
    wechat_files = find_files_in_directory(source_dir, ['wechat_alert_cache.pkl'])
    for file_path in wechat_files:
        target_path = os.path.join(target_dir, 'data', os.path.basename(file_path))
        try:
            shutil.copy2(file_path, target_path)
            print(f"   ✅ 复制: {os.path.basename(file_path)}")
            migration_count += 1
        except Exception as e:
            print(f"   ❌ 复制失败: {e}")
    
    # 8. 迁移节假日缓存
    print("\n📅 迁移节假日缓存...")
    holiday_files = find_files_in_directory(source_dir, ['节假日缓存.json'])
    for file_path in holiday_files:
        target_path = os.path.join(target_dir, 'data', os.path.basename(file_path))
        try:
            shutil.copy2(file_path, target_path)
            print(f"   ✅ 复制: {os.path.basename(file_path)}")
            migration_count += 1
        except Exception as e:
            print(f"   ❌ 复制失败: {e}")
    
    print(f"\n✅ 数据迁移完成！")
    print(f"   📊 总共迁移了 {migration_count} 个文件")
    
    return migration_count


def create_migration_report(source_dir, target_dir, migration_count):
    """创建迁移报告"""
    report = {
        'migration_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'source_directory': source_dir,
        'target_directory': target_dir,
        'migrated_files_count': migration_count,
        'status': 'completed'
    }
    
    report_file = os.path.join(target_dir, 'migration_report.json')
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"📋 迁移报告已保存到: {report_file}")


def main():
    parser = argparse.ArgumentParser(description='迁移原系统数据到新的模块化系统')
    parser.add_argument('--source', '-s', help='原系统目录路径')
    parser.add_argument('--target', '-t', default='.', help='目标目录路径（默认为当前目录）')

    args = parser.parse_args()

    # 如果没有提供source参数，使用交互式输入
    if not args.source:
        print("=== 🔄 持仓系统数据迁移工具 ===")
        print("请输入原系统目录路径：")
        source_dir = input("源目录路径: ").strip().strip('"')
        if not source_dir:
            print("❌ 必须提供源目录路径")
            return False
    else:
        source_dir = args.source

    target_dir = args.target
    
    # 检查源目录是否存在
    if not os.path.exists(source_dir):
        print(f"❌ 源目录不存在: {source_dir}")
        return False
    
    print("=== 🔄 持仓系统数据迁移工具 ===")
    print(f"源目录: {source_dir}")
    print(f"目标目录: {target_dir}")
    
    # 确认迁移
    confirm = input("\n是否开始迁移？(y/N): ")
    if confirm.lower() not in ['y', 'yes']:
        print("❌ 迁移已取消")
        return False
    
    # 执行迁移
    migration_count = migrate_data(source_dir, target_dir)
    
    # 创建迁移报告
    create_migration_report(source_dir, target_dir, migration_count)
    
    print("\n🎉 迁移完成！现在可以启动新系统了。")
    print("💡 建议运行测试脚本验证系统: python test_system.py")
    
    return True


if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
