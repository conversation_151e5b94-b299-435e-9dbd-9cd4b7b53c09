#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
从东方财富获取股票市净率(PB)、TTM市盈率(PE)和股息率的完整脚本
"""

import requests
import time
import json
import pandas as pd
from datetime import datetime

def get_stock_valuation_data(stock_codes):
    """
    批量获取股票估值数据：市净率、TTM市盈率、股息率等
    
    Args:
        stock_codes: 股票代码列表
    
    Returns:
        list: 包含估值数据的字典列表
    """
    
    # 构建股票代码列表
    secids = []
    for code in stock_codes:
        if code.startswith('6'):
            secids.append(f'1.{code}')  # 上海
        else:
            secids.append(f'0.{code}')  # 深圳
    
    secids_str = ','.join(secids)
    
    url = 'http://push2.eastmoney.com/api/qt/ulist.np/get'
    
    # 请求参数 - 包含所有估值相关字段
    params = {
        'fltt': '2',
        'invt': '2',
        'fields': 'f12,f14,f2,f3,f4,f23,f115,f114,f9,f130,f131,f133,f112,f113,f162,f163,f164,f165,f166,f167,f168',
        'secids': secids_str,
        '_': str(int(time.time() * 1000))
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'http://quote.eastmoney.com/',
    }
    
    try:
        print(f"正在获取 {len(stock_codes)} 只股票的估值数据...")
        response = requests.get(url, params=params, headers=headers, timeout=15)
        
        if response.status_code == 200:
            data = json.loads(response.text)
            
            if 'data' in data and 'diff' in data['data']:
                results = []
                
                for item in data['data']['diff']:
                    try:
                        # 处理股息率数据
                        dividend_yield = item.get('f133', None)
                        if dividend_yield == '-' or dividend_yield == 0:
                            dividend_yield = None
                        
                        # 处理市净率数据
                        pb_ratio = item.get('f23', None)
                        if pb_ratio == '-':
                            pb_ratio = None
                        
                        # 处理TTM市盈率数据
                        pe_ttm = item.get('f115', None)
                        if pe_ttm == '-':
                            pe_ttm = None
                        
                        # 处理其他市盈率数据
                        pe_static = item.get('f114', None)
                        if pe_static == '-':
                            pe_static = None
                            
                        pe_dynamic = item.get('f9', None)
                        if pe_dynamic == '-':
                            pe_dynamic = None
                        
                        # 处理市销率和市现率
                        ps_ttm = item.get('f130', None)
                        if ps_ttm == '-':
                            ps_ttm = None
                            
                        pcf_ttm = item.get('f131', None)
                        if pcf_ttm == '-':
                            pcf_ttm = None
                        
                        # 处理每股收益和每股净资产
                        eps = item.get('f112', None)
                        if eps == '-':
                            eps = None
                            
                        bps = item.get('f113', None)
                        if bps == '-':
                            bps = None
                        
                        result = {
                            'code': item.get('f12', ''),           # 股票代码
                            'name': item.get('f14', ''),           # 股票名称
                            'price': item.get('f2', 0),            # 最新价
                            'change_pct': item.get('f3', 0),       # 涨跌幅
                            'change': item.get('f4', 0),           # 涨跌额
                            'pb_ratio': pb_ratio,                  # 市净率
                            'pe_ttm': pe_ttm,                      # TTM市盈率
                            'pe_static': pe_static,                # 静态市盈率
                            'pe_dynamic': pe_dynamic,              # 动态市盈率
                            'ps_ttm': ps_ttm,                      # 市销率TTM
                            'pcf_ttm': pcf_ttm,                    # 市现率TTM
                            'dividend_yield': dividend_yield,      # 股息率
                            'eps': eps,                            # 每股收益
                            'bps': bps,                            # 每股净资产
                            'update_time': datetime.now().strftime('%H:%M:%S')
                        }
                        results.append(result)
                        
                    except Exception as e:
                        print(f"处理股票数据时出错: {e}")
                        continue
                
                return results
        
        return []
        
    except Exception as e:
        print(f"批量获取股票数据失败: {e}")
        return []

def load_stock_codes_from_csv():
    """从CSV文件加载股票代码"""
    try:
        df = pd.read_csv('stocks_list.csv', encoding='utf-8-sig')
        stock_codes = df['代码'].astype(str).str.zfill(6).tolist()
        print(f"从CSV文件加载了 {len(stock_codes)} 只股票代码")
        return stock_codes
    except Exception as e:
        print(f"加载股票代码失败: {e}")
        return []

def save_to_csv(data_list, filename=None):
    """保存数据到CSV文件"""
    if not data_list:
        print("没有数据可保存")
        return False
    
    if filename is None:
        filename = f"股票估值数据_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    df = pd.DataFrame(data_list)
    
    # 重新排列列的顺序
    columns_order = [
        'code', 'name', 'price', 'change', 'change_pct', 
        'pb_ratio', 'pe_ttm', 'pe_static', 'pe_dynamic', 
        'ps_ttm', 'pcf_ttm', 'dividend_yield', 
        'eps', 'bps', 'update_time'
    ]
    
    # 只保留存在的列
    existing_columns = [col for col in columns_order if col in df.columns]
    df = df[existing_columns]
    
    # 添加中文列名
    column_names = {
        'code': '股票代码',
        'name': '股票名称', 
        'price': '最新价',
        'change': '涨跌额',
        'change_pct': '涨跌幅(%)',
        'pb_ratio': '市净率(PB)',
        'pe_ttm': 'TTM市盈率',
        'pe_static': '静态市盈率',
        'pe_dynamic': '动态市盈率',
        'ps_ttm': '市销率TTM',
        'pcf_ttm': '市现率TTM',
        'dividend_yield': '股息率(%)',
        'eps': '每股收益',
        'bps': '每股净资产',
        'update_time': '更新时间'
    }
    
    df.columns = [column_names.get(col, col) for col in df.columns]
    
    df.to_csv(filename, index=False, encoding='utf-8-sig')
    print(f"✅ 数据已保存到 {filename}")
    return True

def analyze_valuation_data(data_list):
    """分析估值数据"""
    if not data_list:
        print("没有数据可分析")
        return
    
    print(f"\n=== 📊 估值数据分析 ===")
    print(f"总股票数: {len(data_list)}")
    
    # 统计有效数据
    valid_pb = [d for d in data_list if d['pb_ratio'] is not None and d['pb_ratio'] > 0]
    valid_pe = [d for d in data_list if d['pe_ttm'] is not None and d['pe_ttm'] > 0]
    valid_dividend = [d for d in data_list if d['dividend_yield'] is not None and d['dividend_yield'] > 0]
    
    print(f"有效市净率数据: {len(valid_pb)} 只 ({len(valid_pb)/len(data_list)*100:.1f}%)")
    print(f"有效TTM市盈率数据: {len(valid_pe)} 只 ({len(valid_pe)/len(data_list)*100:.1f}%)")
    print(f"有效股息率数据: {len(valid_dividend)} 只 ({len(valid_dividend)/len(data_list)*100:.1f}%)")
    
    # 市净率分析
    if valid_pb:
        pb_values = [d['pb_ratio'] for d in valid_pb]
        print(f"\n📈 市净率(PB)分析:")
        print(f"   范围: {min(pb_values):.2f} - {max(pb_values):.2f}")
        print(f"   平均值: {sum(pb_values)/len(pb_values):.2f}")
        print(f"   中位数: {sorted(pb_values)[len(pb_values)//2]:.2f}")
        
        # 低估值股票 (PB < 1)
        low_pb = [d for d in valid_pb if d['pb_ratio'] < 1]
        print(f"   破净股(PB<1): {len(low_pb)} 只")
        if low_pb:
            print("   破净股列表:")
            for stock in sorted(low_pb, key=lambda x: x['pb_ratio'])[:5]:
                print(f"     {stock['code']} {stock['name']} PB:{stock['pb_ratio']:.2f}")
    
    # TTM市盈率分析
    if valid_pe:
        pe_values = [d['pe_ttm'] for d in valid_pe]
        print(f"\n📈 TTM市盈率分析:")
        print(f"   范围: {min(pe_values):.2f} - {max(pe_values):.2f}")
        print(f"   平均值: {sum(pe_values)/len(pe_values):.2f}")
        print(f"   中位数: {sorted(pe_values)[len(pe_values)//2]:.2f}")
        
        # 低估值股票 (PE < 15)
        low_pe = [d for d in valid_pe if d['pe_ttm'] < 15]
        print(f"   低估值股票(PE<15): {len(low_pe)} 只")
    
    # 股息率分析
    if valid_dividend:
        dividend_values = [d['dividend_yield'] for d in valid_dividend]
        print(f"\n💰 股息率分析:")
        print(f"   范围: {min(dividend_values):.2f}% - {max(dividend_values):.2f}%")
        print(f"   平均值: {sum(dividend_values)/len(dividend_values):.2f}%")
        print(f"   中位数: {sorted(dividend_values)[len(dividend_values)//2]:.2f}%")
        
        # 高股息股票 (股息率 > 3%)
        high_dividend = [d for d in valid_dividend if d['dividend_yield'] > 3]
        print(f"   高股息股票(>3%): {len(high_dividend)} 只")
        if high_dividend:
            print("   高股息股票列表:")
            for stock in sorted(high_dividend, key=lambda x: x['dividend_yield'], reverse=True)[:5]:
                print(f"     {stock['code']} {stock['name']} 股息率:{stock['dividend_yield']:.2f}%")

def test_sample_stocks():
    """测试几只样本股票"""
    print("=== 测试样本股票 ===")
    
    test_codes = ['000001', '600000', '600036', '000002', '600519']
    results = get_stock_valuation_data(test_codes)
    
    if results:
        print(f"✅ 成功获取 {len(results)} 只股票数据")
        print("\n详细数据:")
        for stock in results:
            print(f"📊 {stock['code']} {stock['name']}")
            print(f"   最新价: {stock['price']}")
            print(f"   市净率: {stock['pb_ratio']}")
            print(f"   TTM市盈率: {stock['pe_ttm']}")
            print(f"   股息率: {stock['dividend_yield']}%")
            print()
    else:
        print("❌ 获取失败")
    
    return results

def main():
    """主函数"""
    print("=== 📈 东方财富股票估值数据获取工具 (含股息率) ===")
    
    # 选择运行模式
    print("\n请选择运行模式:")
    print("1. 测试模式（获取几只样本股票）")
    print("2. 批量模式（获取所有持仓股票的估值数据）")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == '1':
        test_sample_stocks()
    
    elif choice == '2':
        # 批量模式
        stock_codes = load_stock_codes_from_csv()
        
        if not stock_codes:
            print("未找到股票代码，退出程序")
            return
        
        # 获取数据
        data_list = get_stock_valuation_data(stock_codes)
        
        if data_list:
            print(f"✅ 成功获取 {len(data_list)} 只股票的估值数据")
            
            # 保存数据
            save_to_csv(data_list)
            
            # 分析数据
            analyze_valuation_data(data_list)
        else:
            print("❌ 未获取到任何数据")
    
    else:
        print("无效选择，退出程序")

if __name__ == "__main__":
    main()
