#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import time
import json
import pandas as pd
from datetime import datetime

def get_eastmoney_stock_data(stock_code, debug=False):
    """
    从东方财富网获取单只股票实时数据
    :param stock_code: 股票代码，如 '000001'
    :param debug: 是否打印调试信息
    :return: 股票数据字典
    """

    # 根据股票代码确定市场
    if stock_code.startswith('6'):
        # 上海股票
        secid = f'1.{stock_code}'
    else:
        # 深圳股票
        secid = f'0.{stock_code}'

    # 东方财富API URL
    url = 'http://push2.eastmoney.com/api/qt/stock/get'

    # 请求参数 - 包含更多字段用于探索
    params = {
        'fltt': '2',
        'invt': '2',
        'fields': 'f120,f121,f122,f174,f175,f59,f163,f43,f57,f58,f169,f170,f46,f44,f51,f168,f47,f164,f116,f60,f45,f52,f50,f48,f167,f117,f71,f161,f49,f530,f135,f136,f137,f138,f139,f141,f142,f144,f145,f147,f148,f140,f143,f146,f149,f55,f62,f162,f92,f173,f104,f105,f84,f85,f183,f184,f185,f186,f187,f188,f189,f190,f191,f192,f107,f111,f86,f177,f78,f110,f262,f263,f264,f267,f268,f255,f256,f257,f258,f127,f199,f128,f198,f259,f260,f261,f171,f277,f278,f279,f288,f152,f250,f251,f252,f253,f254,f269,f270,f271,f272,f273,f274,f275,f276,f265,f266,f289,f290,f286,f285,f292,f293,f294,f295,f133,f134,f131,f132,f129,f130',
        'secid': secid,
        '_': str(int(time.time() * 1000))
    }

    try:
        response = requests.get(url, params=params, timeout=10)

        if response.status_code == 200:
            data = json.loads(response.text)['data']

            if data:
                if debug:
                    print(f"\n=== {stock_code} 原始数据字段 ===")
                    for key, value in data.items():
                        print(f"{key}: {value}")

                # 解析数据
                stock_info = {
                    'code': data.get('f57', ''),           # 股票代码
                    'name': data.get('f58', ''),           # 股票名称
                    'price': data.get('f43', 0),           # 最新价
                    'open': data.get('f46', 0),            # 开盘价
                    'high': data.get('f44', 0),            # 最高价
                    'low': data.get('f45', 0),             # 最低价
                    'yesterday_close': data.get('f60', 0), # 昨收价
                    'volume': data.get('f47', 0),          # 成交量
                    'amount': data.get('f48', 0),          # 成交额
                    'change': data.get('f169', 0),         # 涨跌额
                    'change_pct': data.get('f170', 0),     # 涨跌幅
                    'pe_ratio': data.get('f162', 0),       # 市盈率
                    'pb_ratio': data.get('f173', 0),       # 市净率
                    'dividend_yield': data.get('f187', 0), # 股息率 (尝试f187)
                    'industry': data.get('f127', ''),      # 行业 (尝试f127)
                    'market_cap': data.get('f116', 0),     # 总市值
                    'update_time': datetime.now().strftime('%H:%M:%S')
                }

                # 尝试其他可能的股息率字段
                for field in ['f133', 'f134', 'f131', 'f132', 'f129', 'f130', 'f187', 'f188']:
                    if field in data and data[field] is not None:
                        if debug:
                            print(f"字段 {field}: {data[field]} (可能是股息率)")

                # 尝试其他可能的行业字段
                for field in ['f127', 'f128', 'f199', 'f198']:
                    if field in data and data[field] is not None:
                        if debug:
                            print(f"字段 {field}: {data[field]} (可能是行业)")

                return stock_info

    except Exception as e:
        print(f"获取股票 {stock_code} 数据失败: {e}")
        return None

def get_multiple_stocks_data(stock_codes):
    """
    批量获取多只股票数据
    :param stock_codes: 股票代码列表
    :return: 股票数据列表
    """
    
    results = []
    
    for code in stock_codes:
        print(f"正在获取 {code} 的数据...")
        stock_data = get_eastmoney_stock_data(code)
        
        if stock_data:
            results.append(stock_data)
            print(f"✅ {code} {stock_data['name']} 价格:{stock_data['price']} 涨跌幅:{stock_data['change_pct']}%")
        else:
            print(f"❌ {code} 获取失败")
        
        # 避免请求过于频繁
        time.sleep(0.2)
    
    return results

def load_stock_codes_from_excel():
    """
    从Excel文件中加载股票代码
    """
    try:
        df = pd.read_csv('stocks_list.csv', encoding='utf-8-sig')
        stock_codes = df['代码'].astype(str).str.zfill(6).tolist()
        print(f"从Excel加载了 {len(stock_codes)} 只股票代码")
        return stock_codes
    except Exception as e:
        print(f"加载股票代码失败: {e}")
        return []

def save_to_csv(stock_data_list, filename='eastmoney_realtime_data.csv'):
    """
    保存数据到CSV文件
    """
    if stock_data_list:
        df = pd.DataFrame(stock_data_list)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"✅ 数据已保存到 {filename}")
        return True
    return False

def test_api():
    """
    测试API功能
    """
    print("=== 测试东方财富API ===")
    
    # 测试几只股票
    test_codes = ['000001', '600000', '600036', '000002', '600519']
    
    print("测试单只股票获取...")
    for code in test_codes[:2]:
        data = get_eastmoney_stock_data(code)
        if data:
            print(f"✅ {code}: {data['name']} - {data['price']} ({data['change_pct']}%)")
        else:
            print(f"❌ {code}: 获取失败")
    
    print("\n测试批量获取...")
    batch_data = get_multiple_stocks_data(test_codes)
    
    if batch_data:
        print(f"\n成功获取 {len(batch_data)} 只股票数据")
        
        # 显示汇总
        df = pd.DataFrame(batch_data)
        print("\n数据汇总:")
        print(df[['code', 'name', 'price', 'change', 'change_pct']])
        
        # 保存测试数据
        save_to_csv(batch_data, 'test_eastmoney_data.csv')
    
    return batch_data

def main():
    """
    主函数：获取持仓股票的实时数据
    """
    print("=== 东方财富实时股票数据获取 ===")
    
    # 1. 加载股票代码
    stock_codes = load_stock_codes_from_excel()
    
    if not stock_codes:
        print("未找到股票代码，使用测试数据...")
        return test_api()
    
    # 2. 批量获取数据
    print(f"\n开始获取 {len(stock_codes)} 只股票的实时数据...")
    stock_data_list = get_multiple_stocks_data(stock_codes)
    
    # 3. 保存数据
    if stock_data_list:
        save_to_csv(stock_data_list)
        
        # 显示统计信息
        df = pd.DataFrame(stock_data_list)
        print(f"\n=== 数据统计 ===")
        print(f"成功获取: {len(stock_data_list)} 只股票")
        print(f"上涨股票: {len(df[df['change_pct'] > 0])} 只")
        print(f"下跌股票: {len(df[df['change_pct'] < 0])} 只")
        print(f"平盘股票: {len(df[df['change_pct'] == 0])} 只")
        
        # 显示涨幅前5和跌幅前5
        print("\n涨幅前5:")
        top_gainers = df.nlargest(5, 'change_pct')[['code', 'name', 'price', 'change_pct']]
        print(top_gainers.to_string(index=False))
        
        print("\n跌幅前5:")
        top_losers = df.nsmallest(5, 'change_pct')[['code', 'name', 'price', 'change_pct']]
        print(top_losers.to_string(index=False))
        
    else:
        print("❌ 未获取到任何数据")
    
    return stock_data_list

if __name__ == "__main__":
    # 可以选择运行测试或主程序
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'test':
        test_api()
    else:
        main()
