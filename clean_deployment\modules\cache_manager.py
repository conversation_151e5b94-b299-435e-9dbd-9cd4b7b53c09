#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存管理模块
============

负责管理系统的各种缓存，包括：
- 股票数据缓存
- 年内最低价缓存
- 扫雷数据缓存
- 行业信息缓存
- 交易状态缓存

作者: AI Assistant
版本: 1.0
"""

import time
from typing import Dict, Any, Optional


class CacheManager:
    """统一缓存管理器 - 管理所有类型的缓存"""

    def __init__(self):
        self.cache_configs = {
            'stock_data': {'timeout': 300, 'max_size': 1000},      # 股票数据缓存5分钟
            'yearly_low': {'timeout': 3600, 'max_size': 500},     # 年内最低价缓存1小时
            'scan_data': {'timeout': 1800, 'max_size': 200},      # 扫雷数据缓存30分钟
            'industry_info': {'timeout': 7200, 'max_size': 100},  # 行业信息缓存2小时
            'trading_status': {'timeout': 60, 'max_size': 1}      # 交易状态缓存1分钟
        }
        self.caches = {}
        self._initialize_caches()

    def _initialize_caches(self):
        """初始化各类缓存"""
        for cache_type, config in self.cache_configs.items():
            self.caches[cache_type] = {
                'data': {},
                'timestamps': {},
                'config': config
            }

    def get(self, cache_type: str, key: str) -> Optional[Any]:
        """获取缓存数据"""
        if cache_type not in self.caches:
            return None

        cache = self.caches[cache_type]
        current_time = time.time()

        # 检查是否存在且未过期
        if key in cache['data'] and key in cache['timestamps']:
            if current_time - cache['timestamps'][key] < cache['config']['timeout']:
                return cache['data'][key]
            else:
                # 过期则删除
                self._remove_from_cache(cache_type, key)

        return None

    def set(self, cache_type: str, key: str, value: Any) -> bool:
        """设置缓存数据"""
        if cache_type not in self.caches:
            return False

        cache = self.caches[cache_type]
        current_time = time.time()

        # 检查缓存大小限制
        if len(cache['data']) >= cache['config']['max_size']:
            self._cleanup_cache(cache_type)

        cache['data'][key] = value
        cache['timestamps'][key] = current_time
        return True

    def _remove_from_cache(self, cache_type: str, key: str):
        """从缓存中移除指定项"""
        cache = self.caches[cache_type]
        cache['data'].pop(key, None)
        cache['timestamps'].pop(key, None)

    def _cleanup_cache(self, cache_type: str):
        """清理过期缓存项"""
        cache = self.caches[cache_type]
        current_time = time.time()
        timeout = cache['config']['timeout']

        # 找出过期的键
        expired_keys = [
            key for key, timestamp in cache['timestamps'].items()
            if current_time - timestamp >= timeout
        ]

        # 删除过期项
        for key in expired_keys:
            self._remove_from_cache(cache_type, key)

        # 如果还是太多，删除最旧的项
        if len(cache['data']) >= cache['config']['max_size']:
            sorted_items = sorted(cache['timestamps'].items(), key=lambda x: x[1])
            items_to_remove = len(sorted_items) - cache['config']['max_size'] + 10

            for key, _ in sorted_items[:items_to_remove]:
                self._remove_from_cache(cache_type, key)

    def clear_cache(self, cache_type: Optional[str] = None):
        """清空指定类型或所有缓存"""
        if cache_type:
            if cache_type in self.caches:
                self.caches[cache_type]['data'].clear()
                self.caches[cache_type]['timestamps'].clear()
        else:
            for cache in self.caches.values():
                cache['data'].clear()
                cache['timestamps'].clear()

    def get_cache_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取缓存统计信息"""
        stats = {}
        current_time = time.time()

        for cache_type, cache in self.caches.items():
            valid_count = sum(
                1 for timestamp in cache['timestamps'].values()
                if current_time - timestamp < cache['config']['timeout']
            )

            stats[cache_type] = {
                'total_items': len(cache['data']),
                'valid_items': valid_count,
                'expired_items': len(cache['data']) - valid_count,
                'max_size': cache['config']['max_size'],
                'timeout': cache['config']['timeout']
            }

        return stats

    def is_cached(self, cache_type: str, key: str) -> bool:
        """检查数据是否在缓存中且有效"""
        if cache_type not in self.caches:
            return False

        cache = self.caches[cache_type]
        current_time = time.time()

        if key in cache['data'] and key in cache['timestamps']:
            return current_time - cache['timestamps'][key] < cache['config']['timeout']

        return False

    def get_cache_size(self, cache_type: str) -> int:
        """获取指定缓存的大小"""
        if cache_type in self.caches:
            return len(self.caches[cache_type]['data'])
        return 0

    def update_cache_config(self, cache_type: str, timeout: Optional[int] = None, max_size: Optional[int] = None):
        """更新缓存配置"""
        if cache_type in self.cache_configs:
            if timeout is not None:
                self.cache_configs[cache_type]['timeout'] = timeout
                self.caches[cache_type]['config']['timeout'] = timeout
            
            if max_size is not None:
                self.cache_configs[cache_type]['max_size'] = max_size
                self.caches[cache_type]['config']['max_size'] = max_size
                
                # 如果新的最大大小小于当前大小，清理缓存
                if len(self.caches[cache_type]['data']) > max_size:
                    self._cleanup_cache(cache_type)

    def get_cache_keys(self, cache_type: str) -> list:
        """获取指定缓存的所有键"""
        if cache_type in self.caches:
            return list(self.caches[cache_type]['data'].keys())
        return []

    def remove_expired_items(self):
        """移除所有过期的缓存项"""
        current_time = time.time()
        
        for cache_type, cache in self.caches.items():
            timeout = cache['config']['timeout']
            expired_keys = [
                key for key, timestamp in cache['timestamps'].items()
                if current_time - timestamp >= timeout
            ]
            
            for key in expired_keys:
                self._remove_from_cache(cache_type, key)
                
        return len(expired_keys) if 'expired_keys' in locals() else 0


# 创建全局缓存管理器实例
cache_manager = CacheManager()


# 便捷函数
def get_cache(cache_type: str, key: str) -> Optional[Any]:
    """获取缓存数据的便捷函数"""
    return cache_manager.get(cache_type, key)


def set_cache(cache_type: str, key: str, value: Any) -> bool:
    """设置缓存数据的便捷函数"""
    return cache_manager.set(cache_type, key, value)


def clear_cache(cache_type: Optional[str] = None):
    """清空缓存的便捷函数"""
    cache_manager.clear_cache(cache_type)


def get_cache_stats() -> Dict[str, Dict[str, Any]]:
    """获取缓存统计信息的便捷函数"""
    return cache_manager.get_cache_stats()


def is_cached(cache_type: str, key: str) -> bool:
    """检查是否缓存的便捷函数"""
    return cache_manager.is_cached(cache_type, key)
