import re

def analyze_html():
    try:
        with open('saolei_response_000001.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("🔍 分析HTML文件中的JavaScript变量...")
        
        # 查找所有JavaScript变量赋值
        js_vars = [
            'realvalue', 'showvalue', 'max', 'pieRar', 'isScan', 'ix', 'deg'
        ]
        
        for var in js_vars:
            pattern = f'{var}\\s*=\\s*(\\d+)'
            matches = re.findall(pattern, content)
            if matches:
                print(f"{var}: {matches}")
        
        # 查找Canvas绘制中的分数
        print("\n🎨 查找Canvas绘制分数:")
        filltext_pattern = r'fillText\(([^)]+)\)'
        filltext_matches = re.findall(filltext_pattern, content)
        for match in filltext_matches:
            if '分' in match:
                print(f"Canvas文本: {match}")
        
        # 查找所有包含97的地方
        print("\n🎯 查找所有97:")
        contexts = re.findall(r'.{0,50}97.{0,50}', content)
        for i, ctx in enumerate(contexts):
            # 排除CSS样式
            if not any(css in ctx.lower() for css in ['color', 'rgb', 'background', 'border', 'font', 'style']):
                print(f"上下文 {i+1}: {ctx.strip()}")
        
        # 查找特定的分数设置模式
        print("\n📊 查找分数设置:")
        score_patterns = [
            r'(\w+)\s*=\s*97',
            r'97\s*[,;]',
            r'"97"',
            r'97\+',
        ]
        
        for pattern in score_patterns:
            matches = re.findall(pattern, content)
            if matches:
                print(f"模式 {pattern}: {matches}")
                
    except Exception as e:
        print(f"错误: {e}")

if __name__ == '__main__':
    analyze_html()
