#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持仓系统 V14 显示逻辑优化版
==========================

基于V13版本的改进：
- V1-V1.2: 基础功能（股价、年内最低价、股息率、行业信息）
- V2: 智能交易时间监测
- V3: 持仓数量和市值计算
- V4: 数据管理功能（Excel上传、清空数据）
- V5: 分类筛选功能
- V6: 统计分析功能
- V7: 界面优化
- V8: 饼图可视化
- V9: 移动端适配
- V10-V11: 扫雷功能和年内最低价缓存
- V12: 完整整合版
- V13: 策略优化版
- V14: 显示逻辑优化版
  * 修改减半后的显示逻辑：不显示"已减半"标签
  * 将原来的"卖出"显示改为"已减半"
  * 实现持仓数量的直接点击编辑功能

作者: AI Assistant
版本: 14.0
日期: 2025-07-23
"""

from flask import Flask, render_template_string, jsonify, request
import requests
import time
import json
import pandas as pd
import os
from datetime import datetime, timedelta
import threading
from urllib.parse import urlencode
from werkzeug.utils import secure_filename
import hashlib
import pickle
import atexit
import signal
import sys
# from wechat_alert import WeChatAlert  # 暂时注释，我们将在代码中实现
from A股交易时间监测_简化版 import AStockTradingTimeMonitor
from yearly_low_cache_reader import get_yearly_low_from_cache, check_cache_status
from tdx_scan_module import TdxScanClient

app = Flask(__name__)

# 配置文件上传
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
ALLOWED_EXTENSIONS = {'xlsx', 'xls'}

# 确保上传目录存在
if not os.path.exists(app.config['UPLOAD_FOLDER']):
    os.makedirs(app.config['UPLOAD_FOLDER'])

# 企业微信提醒类
class WeChatAlert:
    def __init__(self, webhook_url):
        self.webhook_url = webhook_url
        self.sent_alerts = {}  # 存储已发送的提醒，避免重复发送
        self.alert_cache_file = 'wechat_alert_cache.pkl'
        self.load_alert_cache()

    def load_alert_cache(self):
        """加载提醒缓存"""
        try:
            if os.path.exists(self.alert_cache_file):
                with open(self.alert_cache_file, 'rb') as f:
                    self.sent_alerts = pickle.load(f)
                print(f"✅ 已加载企业微信提醒缓存: {len(self.sent_alerts)} 条记录")
        except Exception as e:
            print(f"⚠️ 加载企业微信提醒缓存失败: {e}")
            self.sent_alerts = {}

    def save_alert_cache(self):
        """保存提醒缓存"""
        try:
            with open(self.alert_cache_file, 'wb') as f:
                pickle.dump(self.sent_alerts, f)
        except Exception as e:
            print(f"⚠️ 保存企业微信提醒缓存失败: {e}")

    def generate_alert_key(self, stock_code, alert_type, date_str):
        """生成提醒唯一标识"""
        return f"{stock_code}_{alert_type}_{date_str}"

    def should_send_alert(self, stock_code, alert_type):
        """检查是否应该发送提醒（每只股票每种类型每天最多1次）"""
        today = datetime.now().strftime('%Y-%m-%d')
        alert_key = self.generate_alert_key(stock_code, alert_type, today)

        # 检查是否已标记为已处理（永久停止提醒）
        handled_key = f"{stock_code}_handled"
        if handled_key in self.sent_alerts:
            print(f"📱 跳过已处理股票的提醒: {stock_code} - {alert_type}")
            return False

        # 检查今天是否已经发送过相同类型的提醒
        if alert_key in self.sent_alerts:
            return False

        return True

    def mark_alert_sent(self, stock_code, alert_type):
        """标记提醒已发送"""
        today = datetime.now().strftime('%Y-%m-%d')
        alert_key = self.generate_alert_key(stock_code, alert_type, today)
        self.sent_alerts[alert_key] = {
            'timestamp': datetime.now().isoformat(),
            'stock_code': stock_code,
            'alert_type': alert_type
        }
        self.save_alert_cache()

    def mark_as_handled(self, stock_code, action_type):
        """标记股票为已处理，永久停止提醒"""
        handled_key = f"{stock_code}_handled"
        self.sent_alerts[handled_key] = {
            'timestamp': datetime.now().isoformat(),
            'stock_code': stock_code,
            'action_type': action_type,
            'handled': True
        }
        self.save_alert_cache()
        print(f"✅ 标记 {stock_code} 为已处理，将停止所有提醒 (操作: {action_type})")

    def clean_old_alerts(self):
        """清理7天前的提醒记录"""
        try:
            cutoff_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
            keys_to_remove = []

            for key in self.sent_alerts:
                if '_' in key:
                    date_part = key.split('_')[-1]
                    if date_part < cutoff_date:
                        keys_to_remove.append(key)

            for key in keys_to_remove:
                del self.sent_alerts[key]

            if keys_to_remove:
                self.save_alert_cache()
                print(f"🧹 已清理 {len(keys_to_remove)} 条过期提醒记录")
        except Exception as e:
            print(f"⚠️ 清理过期提醒记录失败: {e}")

    def send_message(self, message):
        """发送企业微信消息"""
        try:
            data = {
                "msgtype": "text",
                "text": {
                    "content": message
                }
            }

            response = requests.post(
                self.webhook_url,
                json=data,
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    return True
                else:
                    print(f"企业微信发送失败: {result}")
                    return False
            else:
                print(f"企业微信请求失败: HTTP {response.status_code}")
                return False

        except Exception as e:
            print(f"发送企业微信消息异常: {e}")
            return False

    def send_sell_alert(self, stock_code, stock_name, alert_type, reason, current_price, distance_from_low=None, profit_status=None, profit_margin=None):
        """发送卖出提醒"""
        # 检查是否应该发送提醒
        if not self.should_send_alert(stock_code, alert_type):
            print(f"📱 跳过重复提醒: {stock_name} ({stock_code}) - {alert_type}")
            return False

        # 构建消息内容
        if alert_type == 'sell':
            emoji = "🚨"
            title = "卖出信号"
        elif alert_type == 'warning':
            emoji = "⚠️"
            title = "卖出预警"
        else:
            emoji = "📊"
            title = "股票提醒"

        message = f"{emoji} {title}\n"
        message += f"股票: {stock_name} ({stock_code})\n"
        message += f"当前价格: {current_price:.2f}元\n"
        message += f"卖出原因: {reason}\n"

        if distance_from_low is not None:
            message += f"距最低点涨幅: {distance_from_low:.1f}%\n"

        if profit_status == 'profit' and profit_margin is not None:
            message += f"盈利状况: 已盈利 +{profit_margin:.1f}%\n"

        message += f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        # 发送消息
        if self.send_message(message):
            self.mark_alert_sent(stock_code, alert_type)
            print(f"📱 已发送{title}: {stock_name} - {reason}")
            return True
        else:
            print(f"📱 发送{title}失败: {stock_name}")
            return False

# 全局变量存储股票数据
stock_data = {}
last_update_time = None
imported_stock_list = []  # 存储导入的股票列表

# 数据持久化文件路径
STOCK_DATA_FILE = 'stock_data_cache.json'
IMPORTED_LIST_FILE = 'imported_stock_list.json'

def save_stock_data():
    """保存股票数据到文件"""
    try:
        data_to_save = {
            'stock_data': stock_data,
            'last_update_time': last_update_time,
            'save_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        with open(STOCK_DATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(data_to_save, f, ensure_ascii=False, indent=2)
        print(f"✅ 股票数据已保存到 {STOCK_DATA_FILE}")
    except Exception as e:
        print(f"❌ 保存股票数据失败: {e}")

def load_stock_data():
    """从文件加载股票数据"""
    global stock_data, last_update_time
    try:
        if os.path.exists(STOCK_DATA_FILE):
            with open(STOCK_DATA_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
            stock_data = data.get('stock_data', {})
            last_update_time = data.get('last_update_time')
            save_time = data.get('save_time', '未知')
            print(f"✅ 已加载股票数据缓存 (保存时间: {save_time})")
            print(f"   📊 包含 {len(stock_data)} 只股票数据")

            # 重新计算卖出信号（因为缓存数据可能没有卖出信号字段）
            print("🔄 重新计算卖出信号...")
            try:
                for code, info in stock_data.items():
                    sell_signal = calculate_sell_signal(info)
                    info['sell_signal'] = sell_signal['signal']
                    info['sell_reason'] = sell_signal['reason']
                    info['sell_color'] = sell_signal['color']
                    info['sell_priority'] = sell_signal['priority']
                print(f"✅ 已更新 {len(stock_data)} 只股票的卖出信号")
            except Exception as e:
                print(f"⚠️ 计算卖出信号时出错: {e}")
                # 如果计算失败，给所有股票设置默认值
                for code, info in stock_data.items():
                    info['sell_signal'] = 'hold'
                    info['sell_reason'] = '持有'
                    info['sell_color'] = '#2ed573'
                    info['sell_priority'] = 3

            return True
    except Exception as e:
        print(f"❌ 加载股票数据失败: {e}")
    return False

def save_imported_list():
    """保存导入的股票列表"""
    try:
        data_to_save = {
            'imported_stock_list': imported_stock_list,
            'save_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        with open(IMPORTED_LIST_FILE, 'w', encoding='utf-8') as f:
            json.dump(data_to_save, f, ensure_ascii=False, indent=2)
        print(f"✅ 导入列表已保存到 {IMPORTED_LIST_FILE}")
    except Exception as e:
        print(f"❌ 保存导入列表失败: {e}")

def load_imported_list():
    """从文件加载导入的股票列表"""
    global imported_stock_list
    try:
        if os.path.exists(IMPORTED_LIST_FILE):
            with open(IMPORTED_LIST_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
            imported_stock_list = data.get('imported_stock_list', [])
            save_time = data.get('save_time', '未知')
            print(f"✅ 已加载导入列表缓存 (保存时间: {save_time})")
            print(f"   📋 包含 {len(imported_stock_list)} 只股票")
            return True
    except Exception as e:
        print(f"❌ 加载导入列表失败: {e}")
    return False

# 配置
CONFIG = {
    'stock_interval': 0.2,      # 每只股票间隔时间（秒）
    'round_interval': 600,      # 每轮更新间隔时间（秒）
    'request_timeout': 15,      # 请求超时时间（秒）
    'alert_threshold': 70.0,    # 企业微信提醒阈值（距离最低点涨幅%）
}

# 企业微信提醒配置
WECHAT_WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7bddd706-ef0f-4d6c-9e1f-3cc8a0a18d9e"

# 初始化企业微信提醒
wechat_alert = WeChatAlert(WECHAT_WEBHOOK_URL)

# 初始化A股交易时间监测器
trading_monitor = AStockTradingTimeMonitor()

# 初始化扫雷客户端
scan_client = TdxScanClient(cache_duration=1800)  # 缓存30分钟

def get_security_type(code):
    """根据代码判断证券类型"""
    code_str = str(code)

    # 只保留股票，过滤其他类型
    if code_str.startswith('1'):  # ETF
        return 'ETF'
    elif code_str.startswith('5'):  # 基金
        return '基金'
    elif code_str.startswith('11') or code_str.startswith('12') or code_str.startswith('13'):  # 可转债
        return '可转债'
    elif code_str.startswith('4'):  # 新三板等
        return '其他'
    else:
        return '股票'

def is_stock_only(code):
    """判断是否为股票（用于筛选）"""
    code_str = str(code).zfill(6)

    # 只保留6位数字的A股股票
    if len(code_str) != 6 or not code_str.isdigit():
        return False

    # 特殊排除列表（港股、三板等）
    excluded_codes = {
        '700000',  # 腾讯控股（港股）
        '000700',  # 如果是这个代码的腾讯
        # 添加其他需要排除的特定代码
    }

    if code_str in excluded_codes:
        return False

    # 排除ETF、基金、可转债等
    if (code_str.startswith('1') or  # ETF和基金
        code_str.startswith('5') or  # 基金
        code_str.startswith('11') or  # 可转债
        code_str.startswith('12') or  # 可转债
        code_str.startswith('13') or  # 可转债
        code_str.startswith('4') or   # 新三板等
        code_str.startswith('8') or   # 三板股票
        code_str.startswith('9')):    # 其他非A股
        return False

    # 排除港股通等（通常以特定数字开头）
    if (code_str.startswith('02') or  # 港股
        code_str.startswith('03') and len(code_str) > 6):  # 港股（如果超过6位）
        return False

    # 只保留沪深A股
    if code_str.startswith('6'):  # 沪市A股
        return True
    elif code_str.startswith('0') or code_str.startswith('3'):  # 深市A股和创业板
        return True

    return False

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def gen_secid(rawcode: str) -> str:
    """生成东方财富专用的secid"""
    if rawcode[0] == '6':
        return f'1.{rawcode}'  # 沪市
    return f'0.{rawcode}'      # 深市

def get_yearly_low_price_original(code: str) -> dict:
    """获取股票一年内的最低价（前复权）- 原始实现"""
    
    # 计算一年前的日期
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365)
    
    start_date_str = start_date.strftime('%Y%m%d')
    end_date_str = end_date.strftime('%Y%m%d')
    
    secid = gen_secid(code)
    
    params = {
        'fields1': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13',
        'fields2': 'f51,f52,f53,f54,f55',
        'beg': start_date_str,
        'end': end_date_str,
        'rtntype': '6',
        'secid': secid,
        'klt': '101',  # 日K
        'fqt': '1',    # 前复权
    }
    
    base_url = 'https://push2his.eastmoney.com/api/qt/stock/kline/get'
    url = base_url + '?' + urlencode(params)
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; WOW64; Trident/7.0; Touch; rv:11.0) like Gecko',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.8',
        'Referer': 'http://quote.eastmoney.com/',
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=CONFIG['request_timeout'])
        
        if response.status_code == 200:
            json_data = response.json()
            
            if 'data' in json_data and json_data['data'] and 'klines' in json_data['data']:
                klines = json_data['data']['klines']
                
                if klines:
                    # 找到最低价
                    min_low = float('inf')
                    low_date = None
                    
                    for kline in klines:
                        kline_data = kline.split(',')
                        if len(kline_data) >= 5:
                            low_price = float(kline_data[4])  # 最低价
                            if low_price < min_low:
                                min_low = low_price
                                low_date = kline_data[0]  # 日期
                    
                    return {
                        'yearly_low': min_low,
                        'low_date': low_date,
                        'data_points': len(klines)
                    }
        
        return {'yearly_low': None, 'low_date': None, 'data_points': 0}
        
    except Exception as e:
        print(f"获取股票 {code} 历史数据失败: {e}")
        return {'yearly_low': None, 'low_date': None, 'data_points': 0}

def get_yearly_low_price(code: str) -> dict:
    """获取股票一年内的最低价（前复权）- 优先使用缓存"""
    
    # 首先尝试从缓存获取
    try:
        cache_result = get_yearly_low_from_cache(code)
        if cache_result['success']:
            return {
                'yearly_low': cache_result['yearly_low'],
                'low_date': cache_result['low_date'],
                'data_points': cache_result['data_points']
            }
    except Exception as e:
        print(f"从缓存获取 {code} 年内最低价失败: {e}")
    
    # 缓存获取失败，使用原始方法
    print(f"⚠️ 缓存获取失败，使用实时API获取 {code} 年内最低价...")
    return get_yearly_low_price_original(code)

def get_xueqiu_dividend_yield_ttm(stock_code):
    """
    从雪球网页爬取股息率TTM数据 - 带防限制措施
    """
    try:
        import random
        import time

        # 构造雪球股票代码格式
        if stock_code.startswith('6'):
            symbol = f'SH{stock_code}'
        else:
            symbol = f'SZ{stock_code}'

        # 雪球股票详情页面URL
        url = f'https://xueqiu.com/S/{symbol}'

        # 更完整的请求头，模拟真实浏览器
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Referer': 'https://xueqiu.com/',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Cache-Control': 'max-age=0',
        }

        # 添加随机延迟，避免被限制
        delay = random.uniform(1, 3)
        time.sleep(delay)

        # 获取网页内容
        response = requests.get(url, headers=headers, timeout=20)

        if response.status_code == 200:
            html_content = response.text

            # 检查是否被限制
            if '验证码' in html_content or 'captcha' in html_content.lower():
                print(f"⚠️ 雪球访问受限 {stock_code}，跳过股息率获取")
                return 0

            # 使用正则表达式提取股息率TTM
            import re

            # 查找股息率TTM的多种可能模式
            patterns = [
                r'股息率\(TTM\)：(\d+\.?\d*)%',
                r'股息率\(TTM\).*?(\d+\.?\d*)%',
                r'股息率.*?TTM.*?(\d+\.?\d*)%',
            ]

            dividend_yield = 0
            for pattern in patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                if matches:
                    try:
                        dividend_yield = float(matches[0])
                        if dividend_yield > 0:
                            print(f"✅ 从雪球获取股息率TTM {stock_code}: {dividend_yield}%")
                            break
                    except:
                        continue

            if dividend_yield == 0:
                print(f"⚠️ 雪球未找到股息率TTM数据 {stock_code}")

            return dividend_yield

        print(f"❌ 雪球页面请求失败 {stock_code}: {response.status_code}")
        return 0

    except Exception as e:
        print(f'❌ 从雪球爬取股息率TTM失败 {stock_code}: {e}')
        return 0

def calculate_dividend_yield_ttm(stock_code, current_price=None):
    """
    计算股息率TTM - 从雪球获取
    current_price参数保留兼容性，但暂不使用
    """
    try:
        # 从雪球获取股息率TTM数据
        dividend_yield = get_xueqiu_dividend_yield_ttm(stock_code)

        if dividend_yield > 0:
            return round(dividend_yield, 2)

        return 0

    except Exception as e:
        print(f'计算股息率TTM失败 {stock_code}: {e}')
        return 0

# ==================== 多策略卖出决策系统 ====================

class SellStrategyManager:
    """卖出策略管理器"""

    def __init__(self):
        self.strategies = {}
        self.strategy_config = {}
        self.load_default_strategies()
        self.load_strategy_config()

    def load_default_strategies(self):
        """加载默认策略"""
        self.strategies = {
            'distance_from_low': {
                'name': '距最低点涨幅策略',
                'description': '基于距年内最低价的涨幅判断',
                'enabled': True,
                'params': {
                    'sell_threshold': 70.0,    # 卖出阈值
                    'warning_threshold': 60.0,  # 预警阈值
                },
                'weight': 1.0,
                'category': 'technical'
            },
            'pe_abnormal': {
                'name': 'PE异常策略',
                'description': 'PE异常且已回本时卖出',
                'enabled': True,
                'params': {
                    'pe_max': 25.0,           # PE上限
                    'pe_min': 0.0,            # PE下限
                    'require_profit': True,    # 需要盈利
                },
                'weight': 1.0,
                'category': 'fundamental'
            },
            'pb_overvalued': {
                'name': 'PB高估策略',
                'description': 'PB过高时卖出',
                'enabled': False,
                'params': {
                    'pb_threshold': 5.0,      # PB阈值
                    'require_profit': True,    # 需要盈利
                },
                'weight': 0.8,
                'category': 'fundamental'
            },
            'profit_target': {
                'name': '盈利目标策略',
                'description': '达到目标盈利率时卖出',
                'enabled': False,
                'params': {
                    'profit_threshold': 30.0,  # 盈利率阈值
                    'warning_threshold': 25.0, # 预警阈值
                },
                'weight': 0.9,
                'category': 'profit'
            },
            'dividend_yield_low': {
                'name': '股息率过低策略',
                'description': '股息率过低且高估时卖出',
                'enabled': False,
                'params': {
                    'dividend_max': 2.0,      # 股息率上限
                    'pe_min': 20.0,           # PE下限
                },
                'weight': 0.6,
                'category': 'fundamental'
            },
            'scan_risk_high': {
                'name': '扫雷高风险策略',
                'description': '扫雷评分过低时卖出',
                'enabled': False,
                'params': {
                    'score_threshold': 70,     # 评分阈值
                    'require_profit': False,   # 不需要盈利
                },
                'weight': 0.7,
                'category': 'risk'
            },
            'high_gain_sell': {
                'name': '高涨幅卖出策略',
                'description': '距最低点涨幅>=70%时卖出',
                'enabled': True,
                'params': {
                    'gain_threshold': 70.0     # 涨幅阈值
                },
                'weight': 1.2,
                'category': 'gain'
            },
            'high_ttm_medium_gain': {
                'name': '高估值中涨幅策略',
                'description': 'TTM市盈率>=30且距最低点涨幅>=50%时卖出',
                'enabled': True,
                'params': {
                    'ttm_threshold': 30.0,     # TTM市盈率阈值
                    'gain_threshold': 50.0     # 涨幅阈值
                },
                'weight': 1.3,
                'category': 'valuation'
            },
            'limit_up_high_pb_clearance': {
                'name': '涨停高估值清仓策略',
                'description': '涨停且PB>=1.75且距最低点涨幅>=70%时清仓',
                'enabled': True,
                'params': {
                    'limit_up_threshold': 9.8, # 涨停阈值（考虑误差）
                    'pb_threshold': 1.75,      # PB阈值
                    'gain_threshold': 70.0     # 涨幅阈值
                },
                'weight': 2.0,  # 清仓策略权重更高
                'category': 'clearance'
            },
            'extreme_ttm_profit_clearance': {
                'name': '极值TTM回本清仓策略',
                'description': 'TTM>=40或TTM<0且盈利时清仓',
                'enabled': True,
                'params': {
                    'high_ttm_threshold': 40.0, # 高TTM阈值
                    'require_profit': True       # 需要盈利
                },
                'weight': 2.0,  # 清仓策略权重更高
                'category': 'clearance'
            }
        }

    def load_strategy_config(self):
        """从文件加载策略配置"""
        try:
            if os.path.exists('strategy_config.json'):
                with open('strategy_config.json', 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    # 更新策略配置
                    for strategy_id, config in saved_config.items():
                        if strategy_id in self.strategies:
                            self.strategies[strategy_id].update(config)
        except Exception as e:
            print(f"加载策略配置失败: {e}")

    def save_strategy_config(self):
        """保存策略配置到文件"""
        try:
            with open('strategy_config.json', 'w', encoding='utf-8') as f:
                json.dump(self.strategies, f, ensure_ascii=False, indent=2)
            print(f"💾 策略配置已保存到文件")

            # 打印当前策略状态用于调试
            for strategy_id, strategy in self.strategies.items():
                print(f"  📋 {strategy['name']}: enabled={strategy['enabled']}")
        except Exception as e:
            print(f"❌ 保存策略配置失败: {e}")

    def evaluate_strategy(self, strategy_id, stock_data):
        """评估单个策略"""
        if strategy_id not in self.strategies:
            return None

        strategy = self.strategies[strategy_id]
        if not strategy['enabled']:
            return None

        params = strategy['params']

        try:
            if strategy_id == 'distance_from_low':
                return self._evaluate_distance_strategy(stock_data, params)
            elif strategy_id == 'pe_abnormal':
                return self._evaluate_pe_strategy(stock_data, params)
            elif strategy_id == 'pb_overvalued':
                return self._evaluate_pb_strategy(stock_data, params)
            elif strategy_id == 'profit_target':
                return self._evaluate_profit_strategy(stock_data, params)
            elif strategy_id == 'dividend_yield_low':
                return self._evaluate_dividend_strategy(stock_data, params)
            elif strategy_id == 'scan_risk_high':
                return self._evaluate_scan_strategy(stock_data, params)
            elif strategy_id == 'high_gain_sell':
                return self._evaluate_high_gain_strategy(stock_data, params)
            elif strategy_id == 'high_ttm_medium_gain':
                return self._evaluate_high_ttm_medium_gain_strategy(stock_data, params)
            elif strategy_id == 'limit_up_high_pb_clearance':
                return self._evaluate_limit_up_clearance_strategy(stock_data, params)
            elif strategy_id == 'extreme_ttm_profit_clearance':
                return self._evaluate_extreme_ttm_clearance_strategy(stock_data, params)
            elif strategy_id.startswith('custom_'):
                return self._evaluate_custom_strategy(stock_data, strategy)
        except Exception as e:
            print(f"评估策略 {strategy_id} 失败: {e}")

        return None

    def _evaluate_distance_strategy(self, stock_data, params):
        """评估距最低点涨幅策略"""
        distance = stock_data.get('distance_from_low_pct', 0)

        print(f"    📈 距最低点涨幅策略评估: distance={distance}")
        print(f"    📊 参数: sell_threshold={params['sell_threshold']}, warning_threshold={params['warning_threshold']}")

        if distance >= params['sell_threshold']:
            print(f"    ✅ 触发卖出信号: {distance} >= {params['sell_threshold']}")
            return {
                'signal': 'sell',
                'reason': f"距最低点已涨{distance:.1f}%",
                'score': min(100, distance),
                'strategy': 'distance_from_low'
            }
        elif distance >= params['warning_threshold']:
            print(f"    ⚠️ 触发预警信号: {distance} >= {params['warning_threshold']}")
            return {
                'signal': 'warning',
                'reason': f"距最低点{distance:.1f}%，接近卖出线",
                'score': distance,
                'strategy': 'distance_from_low'
            }

        print(f"    ❌ 未达到阈值: {distance} < {params['warning_threshold']}")
        return None

    def _evaluate_pe_strategy(self, stock_data, params):
        """评估PE异常策略"""
        pe = stock_data.get('pe_ratio', None)
        profit_status = stock_data.get('profit_status', '')

        if params['require_profit'] and profit_status != 'profit':
            return None

        pe_abnormal = pe is None or pe <= params['pe_min'] or pe > params['pe_max']

        if pe_abnormal:
            pe_desc = "负值" if pe is None or pe <= 0 else f"{pe:.1f}"
            reason = f"PE异常({pe_desc})"
            if params['require_profit']:
                reason += "且已回本"

            return {
                'signal': 'sell',
                'reason': reason,
                'score': 80,
                'strategy': 'pe_abnormal'
            }
        return None

    def _evaluate_pb_strategy(self, stock_data, params):
        """评估PB高估策略"""
        pb = stock_data.get('pb_ratio', 0)
        profit_status = stock_data.get('profit_status', '')

        if params['require_profit'] and profit_status != 'profit':
            return None

        if pb > params['pb_threshold']:
            return {
                'signal': 'sell',
                'reason': f"PB过高({pb:.1f})",
                'score': min(100, pb * 10),
                'strategy': 'pb_overvalued'
            }
        return None

    def _evaluate_profit_strategy(self, stock_data, params):
        """评估盈利目标策略"""
        profit_margin = stock_data.get('profit_margin', 0)
        profit_status = stock_data.get('profit_status', '')

        print(f"    💰 盈利目标策略评估: profit_margin={profit_margin}, profit_status={profit_status}")
        print(f"    📊 参数: profit_threshold={params['profit_threshold']}, warning_threshold={params['warning_threshold']}")

        # 只有在实际盈利状态下才评估
        if profit_status != 'profit':
            print(f"    ❌ 非盈利状态，跳过评估")
            return None

        if profit_margin >= params['profit_threshold']:
            print(f"    ✅ 触发卖出信号: {profit_margin} >= {params['profit_threshold']}")
            return {
                'signal': 'sell',
                'reason': f"达到盈利目标({profit_margin:.1f}%)",
                'score': min(100, profit_margin),
                'strategy': 'profit_target'
            }
        elif profit_margin >= params['warning_threshold']:
            print(f"    ⚠️ 触发预警信号: {profit_margin} >= {params['warning_threshold']}")
            return {
                'signal': 'warning',
                'reason': f"接近盈利目标({profit_margin:.1f}%)",
                'score': profit_margin,
                'strategy': 'profit_target'
            }

        print(f"    ❌ 未达到阈值: {profit_margin} < {params['warning_threshold']}")
        return None

    def _evaluate_dividend_strategy(self, stock_data, params):
        """评估股息率策略"""
        dividend_yield = stock_data.get('dividend_yield', 0)
        pe = stock_data.get('pe_ratio', 0)

        if dividend_yield < params['dividend_max'] and pe > params['pe_min']:
            return {
                'signal': 'warning',
                'reason': f"股息率过低({dividend_yield:.1f}%)且PE较高",
                'score': 60,
                'strategy': 'dividend_yield_low'
            }
        return None

    def _evaluate_scan_strategy(self, stock_data, params):
        """评估扫雷风险策略"""
        scan_score = stock_data.get('scan_score', 100)
        profit_status = stock_data.get('profit_status', '')

        if params['require_profit'] and profit_status != 'profit':
            return None

        if scan_score < params['score_threshold']:
            return {
                'signal': 'sell',
                'reason': f"扫雷评分过低({scan_score}分)",
                'score': 100 - scan_score,
                'strategy': 'scan_risk_high'
            }
        return None

    def _evaluate_high_gain_strategy(self, stock_data, params):
        """评估高涨幅卖出策略"""
        distance_pct = stock_data.get('distance_from_low_pct', 0)

        print(f"    🚀 高涨幅卖出策略评估: distance_pct={distance_pct}")
        print(f"    📊 参数: gain_threshold={params['gain_threshold']}")

        if distance_pct >= params['gain_threshold']:
            print(f"    ✅ 触发卖出信号: {distance_pct} >= {params['gain_threshold']}")
            return {
                'signal': 'sell',
                'reason': f"高涨幅卖出({distance_pct:.1f}%)",
                'score': min(100, distance_pct),
                'strategy': 'high_gain_sell'
            }

        print(f"    ❌ 未达到阈值: {distance_pct} < {params['gain_threshold']}")
        return None

    def _evaluate_high_ttm_medium_gain_strategy(self, stock_data, params):
        """评估高估值中涨幅策略"""
        ttm_pe = stock_data.get('pe_ratio', 0)
        distance_pct = stock_data.get('distance_from_low_pct', 0)

        print(f"    📈 高估值中涨幅策略评估: ttm_pe={ttm_pe}, distance_pct={distance_pct}")
        print(f"    📊 参数: ttm_threshold={params['ttm_threshold']}, gain_threshold={params['gain_threshold']}")

        # 检查TTM市盈率和涨幅条件
        if ttm_pe is not None and ttm_pe >= params['ttm_threshold'] and distance_pct >= params['gain_threshold']:
            print(f"    ✅ 触发卖出信号: TTM={ttm_pe} >= {params['ttm_threshold']} 且 涨幅={distance_pct} >= {params['gain_threshold']}")
            return {
                'signal': 'sell',
                'reason': f"高估值中涨幅(TTM={ttm_pe:.1f}, 涨幅={distance_pct:.1f}%)",
                'score': min(100, ttm_pe + distance_pct),
                'strategy': 'high_ttm_medium_gain'
            }

        print(f"    ❌ 条件不满足")
        return None

    def _evaluate_limit_up_clearance_strategy(self, stock_data, params):
        """评估涨停高估值清仓策略"""
        change_pct = abs(stock_data.get('change_pct', 0))  # 当日涨跌幅
        pb_ratio = stock_data.get('pb_ratio', 0)
        distance_pct = stock_data.get('distance_from_low_pct', 0)

        print(f"    🔥 涨停高估值清仓策略评估: change_pct={change_pct}, pb_ratio={pb_ratio}, distance_pct={distance_pct}")
        print(f"    📊 参数: limit_up_threshold={params['limit_up_threshold']}, pb_threshold={params['pb_threshold']}, gain_threshold={params['gain_threshold']}")

        # 检查涨停、PB和涨幅条件
        is_limit_up = change_pct >= params['limit_up_threshold']
        is_high_pb = pb_ratio >= params['pb_threshold']
        is_high_gain = distance_pct >= params['gain_threshold']

        if is_limit_up and is_high_pb and is_high_gain:
            print(f"    ✅ 触发清仓信号: 涨停={change_pct:.1f}% >= {params['limit_up_threshold']}%, PB={pb_ratio:.2f} >= {params['pb_threshold']}, 涨幅={distance_pct:.1f}% >= {params['gain_threshold']}%")
            return {
                'signal': 'sell',
                'reason': f"【清仓】涨停高估值(涨停={change_pct:.1f}%, PB={pb_ratio:.2f}, 涨幅={distance_pct:.1f}%)",
                'score': 100,  # 清仓策略最高优先级
                'strategy': 'limit_up_high_pb_clearance'
            }

        print(f"    ❌ 条件不满足: 涨停={is_limit_up}, 高PB={is_high_pb}, 高涨幅={is_high_gain}")
        return None

    def _evaluate_extreme_ttm_clearance_strategy(self, stock_data, params):
        """评估极值TTM回本清仓策略"""
        ttm_pe = stock_data.get('pe_ratio', 0)
        profit_status = stock_data.get('profit_status', '')

        print(f"    ⚡ 极值TTM回本清仓策略评估: ttm_pe={ttm_pe}, profit_status={profit_status}")
        print(f"    📊 参数: high_ttm_threshold={params['high_ttm_threshold']}, require_profit={params['require_profit']}")

        # 检查是否需要盈利状态
        if params['require_profit'] and profit_status != 'profit':
            print(f"    ❌ 非盈利状态，跳过评估")
            return None

        # 检查极值TTM条件（TTM >= 40 或 TTM < 0）
        is_extreme_ttm = False
        if ttm_pe is not None:
            is_extreme_ttm = ttm_pe >= params['high_ttm_threshold'] or ttm_pe < 0

        if is_extreme_ttm:
            ttm_desc = f"TTM={ttm_pe:.1f}" if ttm_pe >= params['high_ttm_threshold'] else f"负TTM={ttm_pe:.1f}"
            print(f"    ✅ 触发清仓信号: {ttm_desc}")
            return {
                'signal': 'sell',
                'reason': f"【清仓】极值TTM回本({ttm_desc})",
                'score': 100,  # 清仓策略最高优先级
                'strategy': 'extreme_ttm_profit_clearance'
            }

        print(f"    ❌ TTM不在极值范围: {ttm_pe}")
        return None

    def _evaluate_custom_strategy(self, stock_data, strategy):
        """评估自定义策略"""
        conditions = strategy.get('conditions', [])
        if not conditions:
            return None

        # 评估所有条件
        all_conditions_met = True
        triggered_reasons = []
        total_score = 0

        for condition in conditions:
            field = condition.get('field')
            operator = condition.get('operator')
            value = condition.get('value')

            if not all([field, operator, value is not None]):
                continue

            stock_value = stock_data.get(field, 0)
            if stock_value is None:
                stock_value = 0

            condition_met = False
            if operator == '>':
                condition_met = stock_value > value
            elif operator == '>=':
                condition_met = stock_value >= value
            elif operator == '<':
                condition_met = stock_value < value
            elif operator == '<=':
                condition_met = stock_value <= value
            elif operator == '==':
                condition_met = stock_value == value

            if condition_met:
                field_names = {
                    'distance_from_low_pct': '距最低点涨幅',
                    'pe_ratio': 'PE比率',
                    'pb_ratio': 'PB比率',
                    'profit_margin': '盈利率',
                    'dividend_yield': '股息率',
                    'scan_score': '扫雷评分'
                }
                field_name = field_names.get(field, field)
                triggered_reasons.append(f"{field_name}{operator}{value}")
                total_score += abs(stock_value - value)
            else:
                all_conditions_met = False

        if all_conditions_met and triggered_reasons:
            return {
                'signal': 'sell',
                'reason': f"自定义策略: {' & '.join(triggered_reasons)}",
                'score': min(100, total_score),
                'strategy': strategy.get('name', '自定义策略')
            }

        return None

# 全局策略管理器实例
strategy_manager = SellStrategyManager()

def calculate_sell_signal(stock_data):
    """
    多策略卖出信号计算
    返回: {
        'signal': 'sell'|'warning'|'hold',
        'reason': '卖出原因',
        'color': '显示颜色',
        'priority': 优先级数字,
        'strategies': [触发的策略列表],
        'details': '详细信息'
    }
    """
    try:
        triggered_strategies = []

        print(f"🔍 开始评估股票 {stock_data.get('name', 'Unknown')} 的卖出信号...")

        # 评估所有启用的策略
        for strategy_id, strategy_config in strategy_manager.strategies.items():
            if strategy_config.get('enabled', False):
                print(f"  📊 评估策略: {strategy_config['name']} (enabled: {strategy_config['enabled']})")
                result = strategy_manager.evaluate_strategy(strategy_id, stock_data)
                if result:
                    print(f"    ✅ 策略触发: {result['reason']}")
                    triggered_strategies.append(result)
                else:
                    print(f"    ❌ 策略未触发")
            else:
                print(f"  ⏸️ 跳过禁用策略: {strategy_config['name']}")

        print(f"📋 触发的策略数量: {len(triggered_strategies)}")

        if not triggered_strategies:
            return {
                'signal': 'hold',
                'reason': '持有',
                'color': '#2ed573',  # 绿色
                'priority': 3,
                'strategies': [],
                'details': '无触发策略'
            }

        # 按信号类型和评分排序
        sell_strategies = [s for s in triggered_strategies if s['signal'] == 'sell']
        warning_strategies = [s for s in triggered_strategies if s['signal'] == 'warning']

        if sell_strategies:
            # 有卖出信号，选择评分最高的
            best_strategy = max(sell_strategies, key=lambda x: x['score'])
            reasons = [s['reason'] for s in sell_strategies]

            # 检查是否有清仓策略
            clearance_strategies = [s for s in sell_strategies if '【清仓】' in s['reason']]
            is_clearance = len(clearance_strategies) > 0

            return {
                'signal': 'clearance' if is_clearance else 'sell',
                'reason': ' | '.join(reasons),
                'color': '#8b0000' if is_clearance else '#ff4757',  # 清仓用深红色
                'priority': 0 if is_clearance else 1,  # 清仓优先级最高
                'strategies': sell_strategies,
                'details': f"主要策略: {best_strategy.get('strategy', '未知策略')}"
            }

        elif warning_strategies:
            # 只有预警信号
            best_strategy = max(warning_strategies, key=lambda x: x['score'])
            reasons = [s['reason'] for s in warning_strategies]

            return {
                'signal': 'warning',
                'reason': ' | '.join(reasons),
                'color': '#ffa502',  # 橙色
                'priority': 2,
                'strategies': warning_strategies,
                'details': f"主要策略: {best_strategy.get('strategy', '未知策略')}"
            }

    except Exception as e:
        print(f"❌ 计算卖出信号失败: {e}")
        import traceback
        traceback.print_exc()
        return {
            'signal': 'hold',
            'reason': '数据异常',
            'color': '#747d8c',  # 灰色
            'priority': 4,
            'strategies': [],
            'details': f'计算错误: {str(e)}'
        }

def get_eastmoney_stock_data_v2(stock_code):
    """
    获取股票实时数据 - 东方财富 + 雪球股息率TTM
    """
    # 根据股票代码确定市场
    if stock_code.startswith('6'):
        secid = f'1.{stock_code}'
    else:
        secid = f'0.{stock_code}'

    # 使用批量接口获取更准确的数据
    url = 'http://push2.eastmoney.com/api/qt/ulist.np/get'

    # 请求参数
    params = {
        'fltt': '2',
        'invt': '2',
        'fields': 'f12,f14,f2,f3,f4,f23,f115,f114,f9,f133,f162,f173,f116,f127,f128,f129,f47,f48',
        'secids': secid,
        '_': str(int(time.time() * 1000))
    }

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Referer': 'http://quote.eastmoney.com/',
    }

    try:
        response = requests.get(url, params=params, headers=headers, timeout=CONFIG['request_timeout'])

        if response.status_code == 200:
            data = json.loads(response.text)

            if 'data' in data and 'diff' in data['data'] and len(data['data']['diff']) > 0:
                stock_info = data['data']['diff'][0]

                # 计算股息率TTM（过去12个月分红/当前股价）
                current_price = stock_info.get('f2', 0)
                dividend_yield = calculate_dividend_yield_ttm(stock_code, current_price)

                # 处理行业信息
                industry = ''  # 先设为空，后面单独获取

                return {
                    'code': stock_info.get('f12', ''),
                    'name': stock_info.get('f14', ''),
                    'price': stock_info.get('f2', 0),
                    'change': stock_info.get('f4', 0),
                    'change_pct': stock_info.get('f3', 0),
                    'dividend_yield': dividend_yield,
                    'pb_ratio': stock_info.get('f23', None) if stock_info.get('f23') != '-' else None,
                    'pe_ttm': stock_info.get('f115', None) if stock_info.get('f115') != '-' else None,
                    'industry': industry,
                    'volume': stock_info.get('f47', 0),
                    'turnover': stock_info.get('f48', 0),
                    'market_cap': stock_info.get('f116', 0) if stock_info.get('f116') != '-' else 0,
                }

    except Exception as e:
        print(f"获取股票 {stock_code} 数据失败: {e}")

    return None

def get_stock_industry_info(stock_code):
    """
    获取股票行业信息 - 使用单独的接口
    """
    # 根据股票代码确定市场
    if stock_code.startswith('6'):
        secid = f'1.{stock_code}'
    else:
        secid = f'0.{stock_code}'

    # 东方财富API URL - 获取详细信息
    url = 'http://push2.eastmoney.com/api/qt/stock/get'

    params = {
        'fltt': '2',
        'invt': '2',
        'fields': 'f127,f128,f129',  # 行业、板块、概念
        'secid': secid,
        '_': str(int(time.time() * 1000))
    }

    try:
        response = requests.get(url, params=params, timeout=CONFIG['request_timeout'])

        if response.status_code == 200:
            data = json.loads(response.text)['data']

            if data:
                return {
                    'industry': data.get('f127', ''),      # 行业
                    'sector': data.get('f128', ''),        # 板块
                    'concept': data.get('f129', ''),       # 概念
                }

    except Exception as e:
        print(f"获取股票 {stock_code} 行业信息失败: {e}")

    return {'industry': '', 'sector': '', 'concept': ''}

def get_stock_info(code: str) -> dict:
    """获取股票基本信息 - 整合版本"""

    # 获取实时数据（东方财富 + 雪球股息率TTM）
    real_data = get_eastmoney_stock_data_v2(code)
    if not real_data:
        return None

    # 获取年内最低价
    yearly_low_info = get_yearly_low_price(code)

    # 获取行业信息（如果主接口没有获取到）
    if not real_data.get('industry'):
        industry_data = get_stock_industry_info(code)
        real_data['industry'] = industry_data.get('industry', '')

    # 计算距离最低点的涨幅
    yearly_low = yearly_low_info.get('yearly_low')
    current_price = real_data['price']
    distance_from_low_pct = None

    if yearly_low and yearly_low > 0 and current_price > 0:
        distance_from_low_pct = ((current_price - yearly_low) / yearly_low) * 100

    return {
        'code': code,
        'name': real_data['name'],
        'price': current_price,
        'change': real_data['change'],
        'change_pct': real_data['change_pct'],
        'volume': real_data.get('volume', 0),
        'turnover': real_data.get('turnover', 0),
        'pe_ratio': real_data['pe_ttm'],
        'pb_ratio': real_data['pb_ratio'],
        'dividend_yield': real_data['dividend_yield'],
        'market_cap': real_data.get('market_cap', 0),
        'industry': real_data['industry'],
        'yearly_low': yearly_low,
        'low_date': yearly_low_info.get('low_date'),
        'distance_from_low_pct': distance_from_low_pct,
        'security_type': get_security_type(code),
        'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }

def load_stock_list():
    """加载股票列表 - 只加载股票，过滤其他证券类型"""
    global imported_stock_list

    # 优先使用导入的股票列表
    if imported_stock_list:
        # 过滤只保留股票
        filtered_list = []
        for stock in imported_stock_list:
            code = str(stock['代码']).zfill(6)
            if is_stock_only(code):
                filtered_list.append(stock)
            else:
                print(f"⚠️ 跳过非股票证券: {code} {stock.get('名称', '')}")
        print(f"📊 过滤后保留 {len(filtered_list)} 只股票")
        return filtered_list

    # 否则尝试从CSV文件加载
    csv_file = 'stocks_list.csv'
    if os.path.exists(csv_file):
        try:
            df = pd.read_csv(csv_file, encoding='utf-8-sig')
            print(f"📊 CSV文件列名: {list(df.columns)}")
            stock_list = []
            for _, row in df.iterrows():
                code = str(row['代码']).zfill(6)
                if is_stock_only(code):
                    # 支持多种持仓数量列名
                    holdings = 0
                    found_col = None
                    for col in ['持有数量', '持仓数量', '持仓', '数量', 'quantity']:
                        if col in row and pd.notna(row[col]):
                            holdings = int(float(row[col]))  # 先转float再转int，处理小数
                            found_col = col
                            break

                    if found_col and holdings > 0:
                        print(f"   📈 {code}: 从列'{found_col}'读取持仓 {holdings}")

                    stock_list.append({
                        '代码': code,
                        '名称': row.get('名称', ''),
                        '持仓数量': holdings
                    })
                else:
                    print(f"⚠️ 跳过非股票证券: {code} {row.get('名称', '')}")
            return stock_list
        except Exception as e:
            print(f"加载股票列表失败: {e}")

    return []

def update_single_stock(stock_info):
    """更新单只股票数据"""
    global stock_data

    code = stock_info['代码']
    name = stock_info.get('名称', '')
    holdings = stock_info.get('持仓数量', 0)
    unit_cost = stock_info.get('单位成本', 0)  # 获取单位成本

    print(f"正在处理 {code} {name}，持仓数量: {holdings}，单位成本: {unit_cost}...")

    # 获取股票信息
    info = get_stock_info(code)

    if info:
        # 添加持仓数量和成本信息
        info['holdings'] = int(holdings)  # 使用holdings作为键名
        info['quantity'] = int(holdings)  # 同时保留quantity以兼容前端
        info['unit_cost'] = float(unit_cost) if unit_cost != 0 else 0  # 修复：允许负成本显示
        info['name'] = name if name else info.get('name', '')

        # 计算市值
        current_price = info['price']
        if holdings > 0 and current_price > 0:
            info['market_value'] = round(current_price * holdings, 2)
            print(f"   💰 市值计算: {current_price} × {holdings} = {info['market_value']}")
        else:
            info['market_value'] = 0
            print(f"   ⚠️ 市值为0: 价格={current_price}, 持仓={holdings}")

        # 计算回本涨幅（更直观的显示方式）
        if unit_cost != 0 and current_price > 0:  # 修改：允许负成本
            if unit_cost > 0 and current_price < unit_cost:
                # 亏损：显示还需要涨多少才能回本
                info['profit_margin'] = round(((unit_cost - current_price) / current_price) * 100, 2)
                info['profit_status'] = 'need_gain'  # 标记为需要涨幅
                print(f"   📈 还需涨幅: ({unit_cost} - {current_price}) / {current_price} × 100% = +{info['profit_margin']}% 才能回本")
            elif unit_cost > 0:
                # 正成本盈利：显示已经盈利多少
                info['profit_margin'] = round(((current_price - unit_cost) / unit_cost) * 100, 2)
                info['profit_status'] = 'profit'  # 标记为已盈利
                print(f"   � 已盈利: ({current_price} - {unit_cost}) / {unit_cost} × 100% = +{info['profit_margin']}%")
            else:
                # 负成本的情况：显示为已盈利
                info['profit_margin'] = round(((current_price - abs(unit_cost)) / abs(unit_cost)) * 100, 2)
                info['profit_status'] = 'profit'  # 负成本标记为已盈利
                print(f"   💰 负成本盈利: ({current_price} - {abs(unit_cost)}) / {abs(unit_cost)} × 100% = +{info['profit_margin']}%")
        else:
            info['profit_margin'] = None
            info['profit_status'] = 'unknown'
            print(f"   ⚠️ 无法计算回本涨幅: 成本={unit_cost}, 价格={current_price}")

        # 获取扫雷数据
        try:
            scan_result = scan_client.get_stock_score(code)
            if scan_result:
                info['scan_score'] = scan_result['score']
                info['scan_risk_level'] = scan_result['risk_level']
                info['scan_risk_color'] = scan_result['risk_color']
                info['scan_triggered_risks'] = scan_result['triggered_risks']
                info['scan_display_text'] = f"{scan_result['score']}分"
                print(f"🔍 扫雷: {scan_result['score']}分 ({scan_result['risk_level']})")
            else:
                # 添加默认扫雷数据
                info['scan_score'] = 0
                info['scan_risk_level'] = '数据异常'
                info['scan_risk_color'] = '#cccccc'
                info['scan_triggered_risks'] = 0
                info['scan_display_text'] = '获取失败'
        except Exception as e:
            print(f"获取扫雷数据失败: {e}")
            # 添加默认扫雷数据
            info['scan_score'] = 0
            info['scan_risk_level'] = '数据异常'
            info['scan_risk_color'] = '#cccccc'
            info['scan_triggered_risks'] = 0
            info['scan_display_text'] = '获取失败'

        # 计算卖出信号
        sell_signal = calculate_sell_signal(info)
        info['sell_signal'] = sell_signal['signal']
        info['sell_reason'] = sell_signal['reason']
        info['sell_color'] = sell_signal['color']
        info['sell_priority'] = sell_signal['priority']

        # 存储到全局数据
        stock_data[code] = info

        print(f"✅ {code} {info['name']} 价格: {info['price']:.2f} 涨跌: {info['change_pct']:.2f}% 卖出信号: {sell_signal['signal']}")

        # 检查是否需要发送卖出提醒
        if sell_signal['signal'] in ['sell', 'warning']:
            try:
                wechat_alert.send_sell_alert(
                    stock_code=code,
                    stock_name=info['name'],
                    alert_type=sell_signal['signal'],
                    reason=sell_signal['reason'],
                    current_price=info['price'],
                    distance_from_low=info.get('distance_from_low_pct'),
                    profit_status=info.get('profit_status'),
                    profit_margin=info.get('profit_margin')
                )
            except Exception as e:
                print(f"发送卖出提醒失败: {e}")

        # 检查是否需要发送高涨幅提醒（保留原有功能，但也要避免重复）
        elif info.get('distance_from_low_pct') and info['distance_from_low_pct'] >= CONFIG['alert_threshold']:
            try:
                if wechat_alert.should_send_alert(code, 'high_gain'):
                    message = f"📈 高涨幅提醒\n股票: {info['name']} ({code})\n当前价格: {info['price']:.2f}元\n距最低点涨幅: {info['distance_from_low_pct']:.1f}%\n年内最低价: {info['yearly_low']:.2f}元 ({info['low_date']})\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                    if wechat_alert.send_message(message):
                        wechat_alert.mark_alert_sent(code, 'high_gain')
                        print(f"📱 已发送高涨幅提醒: {info['name']} 涨幅 {info['distance_from_low_pct']:.1f}%")
                else:
                    print(f"📱 跳过重复高涨幅提醒: {info['name']}")
            except Exception as e:
                print(f"发送高涨幅提醒失败: {e}")
    else:
        print(f"❌ {code} {name} 获取数据失败")

def update_all_stocks():
    """更新所有股票数据"""
    global last_update_time

    stock_list = load_stock_list()
    if not stock_list:
        print("❌ 没有找到股票列表")
        print(f"   导入列表状态: {len(imported_stock_list)} 只股票")
        return

    print(f"\n🚀 开始更新 {len(stock_list)} 只股票数据...")
    print(f"📋 股票列表预览:")
    for i, stock in enumerate(stock_list[:3]):  # 显示前3只股票
        print(f"   {i+1}. {stock['代码']} {stock.get('名称', '')} 持仓:{stock.get('持仓数量', 0)}股")
    start_time = time.time()
    success_count = 0

    for i, stock in enumerate(stock_list, 1):
        print(f"\n[{i}/{len(stock_list)}] ", end="")

        try:
            update_single_stock(stock)
            success_count += 1
        except Exception as e:
            print(f"❌ 更新失败: {e}")

        # 间隔时间
        if i < len(stock_list):
            time.sleep(CONFIG['stock_interval'])

    end_time = time.time()
    elapsed_time = end_time - start_time
    last_update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    success_rate = success_count / len(stock_list) * 100

    print(f"\n✅ 更新完成！")
    print(f"   📈 成功率: {success_rate:.1f}% ({success_count}/{len(stock_list)})")
    print(f"   ⏱️ 耗时: {elapsed_time:.1f}秒")
    print(f"   🕐 更新时间: {last_update_time}")

    # 保存更新后的数据
    save_stock_data()

def background_update():
    """后台智能更新股票数据 - 只在交易时间更新"""
    round_count = 0

    while True:
        round_count += 1
        current_time = datetime.now()

        # 获取交易状态
        trading_status = trading_monitor.get_trading_status()
        should_update = trading_monitor.should_update_data(include_auction=True)  # 包含集合竞价
        update_interval = trading_monitor.get_update_interval(CONFIG['round_interval'])

        print(f"\n{'='*60}")
        print(f"🔄 第 {round_count} 轮检查 - {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📊 交易状态: {trading_status['message']}")
        print(f"{'='*60}")

        if should_update:
            print(f"✅ 当前为交易时间，开始更新股票数据...")
            update_all_stocks()
            print(f"✅ 股票数据更新完成")
        else:
            print(f"⏸️  当前为休市时间，跳过数据更新")
            print(f"📅 {trading_status['message']}")

        print(f"\n⏰ 下一轮检查将在 {update_interval} 秒后开始")
        if update_interval >= 60:
            print(f"   (约 {update_interval/60:.1f} 分钟后)")

        time.sleep(update_interval)

# Flask路由
@app.route('/')
def index():
    """主页面"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/stocks')
def get_stocks():
    """获取股票数据API"""
    try:
        # 计算统计数据
        stats = calculate_statistics()

        # 清理股票数据，确保JSON可序列化
        clean_data = []
        for stock in stock_data.values():
            clean_stock = {}
            for key, value in stock.items():
                # 处理可能的NaN、None或无穷大值
                if value is None:
                    clean_stock[key] = None
                elif isinstance(value, (int, float)):
                    if str(value).lower() in ['nan', 'inf', '-inf']:
                        clean_stock[key] = 0
                    else:
                        clean_stock[key] = value
                else:
                    clean_stock[key] = str(value) if value is not None else ''
            clean_data.append(clean_stock)

        return jsonify({
            'success': True,
            'data': clean_data,
            'last_update': last_update_time,
            'count': len(clean_data),
            'stats': stats
        })
    except Exception as e:
        print(f"API错误: {e}")
        return jsonify({
            'success': False,
            'message': f'获取数据失败: {str(e)}'
        }), 500

@app.route('/api/trading-status')
def get_trading_status():
    """获取交易状态API"""
    try:
        status = trading_monitor.get_trading_status()
        return jsonify({
            'success': True,
            'data': status
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取交易状态失败: {str(e)}'
        }), 500

@app.route('/api/refresh-holidays', methods=['POST'])
def refresh_holidays():
    """刷新节假日数据API"""
    try:
        trading_monitor.refresh_holidays()
        return jsonify({
            'success': True,
            'message': '节假日数据已刷新'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'刷新失败: {str(e)}'
        }), 500

@app.route('/api/clear-data', methods=['POST'])
def clear_data():
    """清空数据API"""
    try:
        global stock_data, last_update_time
        stock_data = {}
        last_update_time = None

        return jsonify({
            'success': True,
            'message': '数据已清空'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'清空失败: {str(e)}'
        }), 500

@app.route('/api/upload-holdings', methods=['POST'])
def upload_holdings():
    """上传持仓表格API"""
    try:
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'message': '没有选择文件'
            }), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'success': False,
                'message': '没有选择文件'
            }), 400

        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{timestamp}_{filename}"
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)

            # 读取Excel文件
            try:
                df = pd.read_excel(filepath)

                # 验证必要的列 - 支持多种列名
                code_column = None
                quantity_column = None

                # 查找代码列
                for col in ['代码', 'code', '股票代码']:
                    if col in df.columns:
                        code_column = col
                        break

                # 查找数量列
                for col in ['持有数量', '持仓数量', '数量', 'quantity', '股数']:
                    if col in df.columns:
                        quantity_column = col
                        break

                if not code_column or not quantity_column:
                    return jsonify({
                        'success': False,
                        'message': f'缺少必要的列。需要包含代码列（代码/code/股票代码）和数量列（持有数量/持仓数量/数量/quantity/股数）'
                    }), 400

                # 处理数据 - 只处理股票，使用正确的列名
                global imported_stock_list
                imported_stock_list = []
                skipped_count = 0

                # 首先过滤掉代码列为空的行
                df = df.dropna(subset=[code_column])

                # 确保代码列转换为字符串
                df[code_column] = df[code_column].astype(str)

                # 处理持有数量，确保是数值类型
                df[quantity_column] = pd.to_numeric(df[quantity_column], errors='coerce').fillna(0).astype(int)

                # 过滤掉持有数量为0或负数的股票
                df = df[df[quantity_column] > 0]

                for _, row in df.iterrows():
                    code = str(row[code_column]).zfill(6)
                    quantity = int(row[quantity_column])
                    name = str(row.get('名称', '')) if pd.notna(row.get('名称', '')) else ''

                    # 读取单位成本
                    unit_cost = 0
                    for cost_col in ['单位成本', '成本价', '成本', 'unit_cost']:
                        if cost_col in row and pd.notna(row[cost_col]):
                            unit_cost = float(row[cost_col])
                            break

                    if len(code) == 6 and code.isdigit() and quantity > 0:
                        if is_stock_only(code):
                            imported_stock_list.append({
                                '代码': code,
                                '名称': name,
                                '持仓数量': quantity,
                                '单位成本': unit_cost
                            })
                        else:
                            skipped_count += 1
                            # 详细显示跳过的原因
                            skip_reason = "未知原因"
                            if code.startswith('1') or code.startswith('5'):
                                skip_reason = "ETF/基金"
                            elif code.startswith('11') or code.startswith('12') or code.startswith('13'):
                                skip_reason = "可转债"
                            elif code.startswith('4') or code.startswith('8'):
                                skip_reason = "三板股票"
                            elif code in ['700000', '000700']:
                                skip_reason = "港股"
                            elif "腾讯" in name or "艾" in name:
                                skip_reason = "港股/三板"

                            print(f"⚠️ 跳过非A股证券: {code} {name} ({skip_reason})")

                message = f'成功导入 {len(imported_stock_list)} 只股票'
                if skipped_count > 0:
                    message += f'，跳过 {skipped_count} 只非股票证券'

                # 保存导入的股票列表
                save_imported_list()

                # 上传成功后立即更新股票数据
                print(f"📈 开始更新股票数据...")
                update_all_stocks()

                return jsonify({
                    'success': True,
                    'message': message,
                    'count': len(imported_stock_list)
                })

            except Exception as e:
                return jsonify({
                    'success': False,
                    'message': f'读取文件失败: {str(e)}'
                }), 400

        return jsonify({
            'success': False,
            'message': '不支持的文件格式'
        }), 400

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'上传失败: {str(e)}'
        }), 500

@app.route('/api/refresh-data', methods=['POST'])
def refresh_data():
    """重新获取数据API"""
    try:
        if not imported_stock_list:
            return jsonify({
                'success': False,
                'message': '没有找到导入的股票列表，请先上传Excel文件'
            }), 400

        # 在后台线程中更新数据
        def update_in_background():
            update_all_stocks()

        thread = threading.Thread(target=update_in_background)
        thread.daemon = True
        thread.start()

        return jsonify({
            'success': True,
            'message': f'数据更新已开始，正在更新 {len(imported_stock_list)} 只股票，请稍后刷新页面查看结果'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'更新失败: {str(e)}'
        }), 500

def calculate_statistics():
    """计算统计数据 - 只统计股票"""
    if not stock_data:
        return {
            'total_market_value': 0,
            'stock_count': 0,
            'industry_distribution': {}
        }

    # 总市值（现在只有股票）
    total_market_value = sum(stock.get('market_value', 0) for stock in stock_data.values())
    stock_count = len(stock_data)

    # 行业分布统计
    industry_distribution = {}

    for stock in stock_data.values():
        industry = stock.get('industry', '未知行业')
        if not industry:
            industry = '未知行业'
        market_value = stock.get('market_value', 0)

        if industry not in industry_distribution:
            industry_distribution[industry] = {'market_value': 0, 'count': 0}

        industry_distribution[industry]['market_value'] += market_value
        industry_distribution[industry]['count'] += 1

    # 计算行业分布百分比
    for industry in industry_distribution:
        if total_market_value > 0:
            industry_distribution[industry]['percentage'] = (
                industry_distribution[industry]['market_value'] / total_market_value * 100
            )
        else:
            industry_distribution[industry]['percentage'] = 0

    # 按市值排序
    industry_distribution = dict(
        sorted(industry_distribution.items(),
               key=lambda x: x[1]['market_value'],
               reverse=True)
    )

    return {
        'total_market_value': total_market_value,
        'stock_count': stock_count,
        'industry_distribution': industry_distribution
    }

@app.route('/api/cache-status')
def get_cache_status():
    """获取缓存状态API"""
    try:
        cache_status = check_cache_status()
        return jsonify({
            'success': True,
            'data': cache_status
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取缓存状态失败: {str(e)}'
        }), 500

@app.route('/api/scan-cache-info')
def get_scan_cache_info():
    """获取扫雷缓存信息API"""
    try:
        cache_info = scan_client.get_cache_info()
        return jsonify({
            'success': True,
            'data': cache_info
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取扫雷缓存信息失败: {str(e)}'
        }), 500

@app.route('/api/stock-list-status')
def get_stock_list_status():
    """获取股票列表状态API"""
    try:
        stock_list = load_stock_list()
        return jsonify({
            'success': True,
            'data': {
                'imported_count': len(imported_stock_list),
                'filtered_count': len(stock_list),
                'sample_stocks': stock_list[:5] if stock_list else []
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取股票列表状态失败: {str(e)}'
        }), 500

@app.route('/api/wechat-alert-stats')
def get_wechat_alert_stats():
    """获取企业微信提醒统计API"""
    try:
        today = datetime.now().strftime('%Y-%m-%d')
        today_alerts = {}
        total_alerts = len(wechat_alert.sent_alerts)

        # 统计今天的提醒
        for key, alert_info in wechat_alert.sent_alerts.items():
            if key.endswith(today):
                alert_type = alert_info.get('alert_type', 'unknown')
                if alert_type not in today_alerts:
                    today_alerts[alert_type] = 0
                today_alerts[alert_type] += 1

        return jsonify({
            'success': True,
            'data': {
                'today_alerts': today_alerts,
                'total_alerts': total_alerts,
                'today_date': today
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取企业微信提醒统计失败: {str(e)}'
        }), 500

@app.route('/api/update-single-stock/<stock_code>', methods=['POST'])
def update_single_stock_api(stock_code):
    """更新单只股票数据API"""
    try:
        global stock_data

        # 验证股票代码
        if not stock_code or len(stock_code) != 6:
            return jsonify({
                'success': False,
                'message': '无效的股票代码'
            }), 400

        # 检查股票是否在持仓列表中
        if stock_code not in stock_data:
            return jsonify({
                'success': False,
                'message': f'股票 {stock_code} 不在持仓列表中'
            }), 404

        old_data = stock_data[stock_code].copy()

        # 获取持仓数量（保持不变）
        holdings = old_data.get('holdings', 0)

        print(f"🔄 开始更新单只股票: {stock_code} ({old_data.get('name', '未知')})")

        # 获取最新股票数据
        new_data = get_stock_info(stock_code)

        if new_data:
            # 保持持仓数量不变
            new_data['holdings'] = holdings

            # 重新计算市值
            new_data['market_value'] = new_data['price'] * holdings

            # 获取成本价（如果有的话）
            unit_cost = old_data.get('unit_cost', 0)
            new_data['unit_cost'] = unit_cost

            # 计算回本涨幅
            if unit_cost > 0 and new_data['price'] > 0:
                profit_margin = ((new_data['price'] - unit_cost) / unit_cost) * 100
                new_data['profit_margin'] = profit_margin
                new_data['profit_status'] = 'profit' if profit_margin > 0 else 'loss'
            else:
                new_data['profit_margin'] = None
                new_data['profit_status'] = 'unknown'

            # 计算卖出信号
            try:
                sell_signal_data = {
                    'distance_from_low_pct': new_data.get('distance_from_low_pct', 0),
                    'pe_ratio': new_data.get('pe_ratio'),
                    'profit_status': new_data.get('profit_status', 'unknown'),
                    'profit_margin': new_data.get('profit_margin', 0)
                }
                sell_signal = calculate_sell_signal(sell_signal_data)
                new_data['sell_signal'] = sell_signal['signal']
                new_data['sell_reason'] = sell_signal['reason']
                new_data['sell_color'] = sell_signal['color']
                new_data['sell_priority'] = sell_signal['priority']
            except Exception as e:
                print(f"⚠️ 计算卖出信号失败: {e}")
                new_data['sell_signal'] = 'hold'
                new_data['sell_reason'] = '持有'
                new_data['sell_color'] = '#2ed573'
                new_data['sell_priority'] = 3

            # 获取扫雷评分
            try:
                print(f"🔍 获取 {stock_code} 的扫雷评分...")
                scan_client = TdxScanClient()
                scan_result = scan_client.get_scan_score(stock_code)

                if scan_result['success']:
                    new_data['scan_score'] = scan_result['score']
                    new_data['scan_risk_level'] = scan_result['risk_level']
                    new_data['scan_risk_color'] = scan_result['risk_color']
                    new_data['scan_triggered_risks'] = scan_result['triggered_risks']
                    new_data['scan_display_text'] = scan_result['display_text']
                    print(f"✅ 扫雷评分获取成功: {scan_result['score']}分")
                else:
                    # 使用旧数据或默认值
                    new_data['scan_score'] = old_data.get('scan_score', 85)
                    new_data['scan_risk_level'] = old_data.get('scan_risk_level', '低风险')
                    new_data['scan_risk_color'] = old_data.get('scan_risk_color', '#2ed573')
                    new_data['scan_triggered_risks'] = old_data.get('scan_triggered_risks', 0)
                    new_data['scan_display_text'] = old_data.get('scan_display_text', '85分')
                    print(f"⚠️ 扫雷评分获取失败，使用缓存数据: {scan_result.get('message', '未知错误')}")
            except Exception as e:
                print(f"❌ 扫雷评分获取异常: {e}")
                # 使用旧数据或默认值
                new_data['scan_score'] = old_data.get('scan_score', 85)
                new_data['scan_risk_level'] = old_data.get('scan_risk_level', '低风险')
                new_data['scan_risk_color'] = old_data.get('scan_risk_color', '#2ed573')
                new_data['scan_triggered_risks'] = old_data.get('scan_triggered_risks', 0)
                new_data['scan_display_text'] = old_data.get('scan_display_text', '85分')

            # 更新股票数据
            stock_data[stock_code] = new_data

            # 保存数据
            save_stock_data()

            # 计算变化
            price_change = new_data['price'] - old_data.get('price', 0)
            price_change_pct = (price_change / old_data.get('price', 1)) * 100 if old_data.get('price', 0) > 0 else 0

            print(f"✅ 股票 {stock_code} 更新成功")
            print(f"   价格: {old_data.get('price', 0):.2f} → {new_data['price']:.2f} ({price_change:+.2f}, {price_change_pct:+.1f}%)")

            return jsonify({
                'success': True,
                'message': f'股票 {new_data["name"]} 数据更新成功',
                'data': {
                    'code': stock_code,
                    'name': new_data['name'],
                    'old_price': old_data.get('price', 0),
                    'new_price': new_data['price'],
                    'price_change': price_change,
                    'price_change_pct': price_change_pct,
                    'update_time': new_data['update_time']
                }
            })
        else:
            print(f"❌ 股票 {stock_code} 数据获取失败")
            return jsonify({
                'success': False,
                'message': f'获取股票 {stock_code} 数据失败，请稍后重试'
            }), 500

    except Exception as e:
        print(f"❌ 更新单只股票失败: {e}")
        return jsonify({
            'success': False,
            'message': f'更新失败: {str(e)}'
        }), 500

@app.route('/api/test-wechat-alerts', methods=['POST'])
def test_wechat_alerts():
    """手动测试企业微信提醒API"""
    try:
        import random

        # 18支随机测试股票数据
        test_stocks = [
            {'code': '000001', 'name': '平安银行', 'price': 12.85, 'signal': 'sell', 'reason': '距最低点已涨72.3%', 'distance': 72.3},
            {'code': '000002', 'name': '万科A', 'price': 8.92, 'signal': 'warning', 'reason': '距卖出条件还差6.8%', 'distance': 63.2},
            {'code': '000858', 'name': '五粮液', 'price': 128.50, 'signal': 'sell', 'reason': 'TTM市盈率-15.2且已回本', 'distance': 45.6},
            {'code': '000876', 'name': '新希望', 'price': 15.67, 'signal': 'warning', 'reason': '距卖出条件还差8.2%', 'distance': 61.8},
            {'code': '002415', 'name': '海康威视', 'price': 32.45, 'signal': 'sell', 'reason': '距最低点已涨75.8%', 'distance': 75.8},
            {'code': '002594', 'name': '比亚迪', 'price': 245.60, 'signal': 'warning', 'reason': '距卖出条件还差5.5%', 'distance': 64.5},
            {'code': '600036', 'name': '招商银行', 'price': 35.20, 'signal': 'sell', 'reason': 'TTM市盈率28.5且已回本', 'distance': 52.3},
            {'code': '600519', 'name': '贵州茅台', 'price': 1680.00, 'signal': 'warning', 'reason': '距卖出条件还差7.3%', 'distance': 62.7},
            {'code': '600887', 'name': '伊利股份', 'price': 28.90, 'signal': 'sell', 'reason': '距最低点已涨71.2%', 'distance': 71.2},
            {'code': '000568', 'name': '泸州老窖', 'price': 142.30, 'signal': 'warning', 'reason': '距卖出条件还差9.1%', 'distance': 60.9},
            {'code': '002304', 'name': '洋河股份', 'price': 98.75, 'signal': 'sell', 'reason': 'TTM市盈率-8.7且已回本', 'distance': 48.2},
            {'code': '000895', 'name': '双汇发展', 'price': 25.80, 'signal': 'warning', 'reason': '距卖出条件还差6.2%', 'distance': 63.8},
            {'code': '600276', 'name': '恒瑞医药', 'price': 45.60, 'signal': 'sell', 'reason': '距最低点已涨73.5%', 'distance': 73.5},
            {'code': '000661', 'name': '长春高新', 'price': 156.40, 'signal': 'warning', 'reason': '距卖出条件还差8.7%', 'distance': 61.3},
            {'code': '002142', 'name': '宁波银行', 'price': 22.15, 'signal': 'sell', 'reason': 'TTM市盈率26.8且已回本', 'distance': 55.9},
            {'code': '600309', 'name': '万华化学', 'price': 78.90, 'signal': 'warning', 'reason': '距卖出条件还差5.8%', 'distance': 64.2},
            {'code': '000338', 'name': '潍柴动力', 'price': 13.45, 'signal': 'sell', 'reason': '距最低点已涨74.1%', 'distance': 74.1},
            {'code': '002475', 'name': '立讯精密', 'price': 28.30, 'signal': 'warning', 'reason': '距卖出条件还差7.5%', 'distance': 62.5}
        ]

        sent_count = 0
        skipped_count = 0
        results = []

        for stock in test_stocks:
            try:
                # 随机生成一些额外数据
                profit_margin = random.uniform(5.0, 25.0) if random.choice([True, False]) else None
                profit_status = 'profit' if profit_margin else 'loss'

                # 发送提醒
                success = wechat_alert.send_sell_alert(
                    stock_code=stock['code'],
                    stock_name=stock['name'],
                    alert_type=stock['signal'],
                    reason=stock['reason'],
                    current_price=stock['price'],
                    distance_from_low=stock['distance'],
                    profit_status=profit_status,
                    profit_margin=profit_margin
                )

                if success:
                    sent_count += 1
                    status = '✅ 已发送'
                else:
                    skipped_count += 1
                    status = '⏭️ 已跳过(重复)'

                results.append({
                    'code': stock['code'],
                    'name': stock['name'],
                    'signal': stock['signal'],
                    'status': status
                })

                # 添加小延迟避免频率限制
                time.sleep(0.1)

            except Exception as e:
                results.append({
                    'code': stock['code'],
                    'name': stock['name'],
                    'signal': stock['signal'],
                    'status': f'❌ 失败: {str(e)}'
                })

        return jsonify({
            'success': True,
            'message': f'测试完成！发送 {sent_count} 条，跳过 {skipped_count} 条',
            'data': {
                'sent_count': sent_count,
                'skipped_count': skipped_count,
                'total_count': len(test_stocks),
                'results': results
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'测试失败: {str(e)}'
        }), 500

@app.route('/api/strategies', methods=['GET'])
def get_strategies():
    """获取所有策略配置"""
    try:
        return jsonify({
            'success': True,
            'data': strategy_manager.strategies
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取策略配置失败: {str(e)}'
        }), 500

@app.route('/api/strategies', methods=['POST'])
def update_strategies():
    """更新策略配置"""
    try:
        data = request.get_json()

        print(f"📝 收到策略配置更新: {data}")

        # 更新策略配置
        for strategy_id, config in data.items():
            if strategy_id in strategy_manager.strategies:
                strategy_manager.strategies[strategy_id].update(config)
                print(f"✅ 更新策略 {strategy_id}: enabled={config.get('enabled', 'N/A')}")

        # 保存配置
        strategy_manager.save_strategy_config()

        # 强制重新加载配置以确保生效
        strategy_manager.load_strategy_config()

        print(f"💾 策略配置已保存并重新加载")

        return jsonify({
            'success': True,
            'message': '策略配置已更新'
        })
    except Exception as e:
        print(f"❌ 更新策略配置失败: {e}")
        return jsonify({
            'success': False,
            'message': f'更新策略配置失败: {str(e)}'
        }), 500

@app.route('/api/strategies/presets/<preset_name>', methods=['POST'])
def apply_strategy_preset(preset_name):
    """应用策略预设"""
    try:
        presets = {
            'conservative': {  # 保守型
                'distance_from_low': {'enabled': True, 'params': {'sell_threshold': 50.0, 'warning_threshold': 40.0}},
                'pe_abnormal': {'enabled': True, 'params': {'pe_max': 20.0, 'require_profit': True}},
                'profit_target': {'enabled': True, 'params': {'profit_threshold': 20.0, 'warning_threshold': 15.0}},
                'scan_risk_high': {'enabled': True, 'params': {'score_threshold': 80, 'require_profit': False}},
                'high_gain_sell': {'enabled': True, 'params': {'gain_threshold': 60.0}},
                'high_ttm_medium_gain': {'enabled': True, 'params': {'ttm_threshold': 25.0, 'gain_threshold': 40.0}},
                'limit_up_high_pb_clearance': {'enabled': True, 'params': {'limit_up_threshold': 9.8, 'pb_threshold': 1.5, 'gain_threshold': 60.0}},
                'extreme_ttm_profit_clearance': {'enabled': True, 'params': {'high_ttm_threshold': 35.0, 'require_profit': True}},
            },
            'aggressive': {  # 激进型
                'distance_from_low': {'enabled': True, 'params': {'sell_threshold': 80.0, 'warning_threshold': 70.0}},
                'pe_abnormal': {'enabled': True, 'params': {'pe_max': 30.0, 'require_profit': False}},
                'pb_overvalued': {'enabled': True, 'params': {'pb_threshold': 6.0, 'require_profit': False}},
                'profit_target': {'enabled': True, 'params': {'profit_threshold': 50.0, 'warning_threshold': 40.0}},
                'high_gain_sell': {'enabled': True, 'params': {'gain_threshold': 80.0}},
                'high_ttm_medium_gain': {'enabled': True, 'params': {'ttm_threshold': 35.0, 'gain_threshold': 60.0}},
                'limit_up_high_pb_clearance': {'enabled': True, 'params': {'limit_up_threshold': 9.8, 'pb_threshold': 2.0, 'gain_threshold': 80.0}},
                'extreme_ttm_profit_clearance': {'enabled': True, 'params': {'high_ttm_threshold': 50.0, 'require_profit': True}},
            },
            'balanced': {  # 平衡型
                'distance_from_low': {'enabled': True, 'params': {'sell_threshold': 70.0, 'warning_threshold': 60.0}},
                'pe_abnormal': {'enabled': True, 'params': {'pe_max': 25.0, 'require_profit': True}},
                'pb_overvalued': {'enabled': True, 'params': {'pb_threshold': 5.0, 'require_profit': True}},
                'profit_target': {'enabled': True, 'params': {'profit_threshold': 30.0, 'warning_threshold': 25.0}},
                'scan_risk_high': {'enabled': True, 'params': {'score_threshold': 70, 'require_profit': False}},
                'high_gain_sell': {'enabled': True, 'params': {'gain_threshold': 70.0}},
                'high_ttm_medium_gain': {'enabled': True, 'params': {'ttm_threshold': 30.0, 'gain_threshold': 50.0}},
                'limit_up_high_pb_clearance': {'enabled': True, 'params': {'limit_up_threshold': 9.8, 'pb_threshold': 1.75, 'gain_threshold': 70.0}},
                'extreme_ttm_profit_clearance': {'enabled': True, 'params': {'high_ttm_threshold': 40.0, 'require_profit': True}},
            }
        }

        if preset_name not in presets:
            return jsonify({
                'success': False,
                'message': f'未知的预设类型: {preset_name}'
            }), 400

        preset_config = presets[preset_name]

        # 先禁用所有策略
        for strategy_id in strategy_manager.strategies:
            strategy_manager.strategies[strategy_id]['enabled'] = False

        # 应用预设配置
        for strategy_id, config in preset_config.items():
            if strategy_id in strategy_manager.strategies:
                strategy_manager.strategies[strategy_id].update(config)

        # 保存配置
        strategy_manager.save_strategy_config()

        return jsonify({
            'success': True,
            'message': f'{preset_name}型策略预设已应用'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'应用策略预设失败: {str(e)}'
        }), 500

@app.route('/api/strategies/custom', methods=['POST'])
def create_custom_strategy():
    """创建自定义策略"""
    try:
        data = request.get_json()
        strategy_name = data.get('name', '')
        strategy_config = data.get('config', {})

        if not strategy_name:
            return jsonify({
                'success': False,
                'message': '策略名称不能为空'
            }), 400

        # 生成策略ID
        strategy_id = f"custom_{strategy_name.lower().replace(' ', '_')}"

        # 添加自定义策略
        strategy_manager.strategies[strategy_id] = {
            'name': strategy_name,
            'description': data.get('description', '用户自定义策略'),
            'enabled': True,
            'params': strategy_config.get('params', {}),
            'weight': strategy_config.get('weight', 1.0),
            'category': 'custom',
            'conditions': strategy_config.get('conditions', [])
        }

        # 保存配置
        strategy_manager.save_strategy_config()

        return jsonify({
            'success': True,
            'message': f'自定义策略 "{strategy_name}" 已创建',
            'strategy_id': strategy_id
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'创建自定义策略失败: {str(e)}'
        }), 500

@app.route('/api/refresh-signals', methods=['POST'])
def refresh_signals():
    """强制刷新所有股票的卖出信号"""
    try:
        global stock_data

        print("🔄 开始强制刷新所有股票的卖出信号...")

        # 重新计算所有股票的卖出信号
        for stock_code, stock_info in stock_data.items():
            try:
                # 重新计算卖出信号
                sell_signal_result = calculate_sell_signal(stock_info)

                # 更新股票信息
                stock_info['sell_signal'] = sell_signal_result['signal']
                stock_info['sell_reason'] = sell_signal_result['reason']
                stock_info['sell_color'] = sell_signal_result['color']
                stock_info['sell_priority'] = sell_signal_result['priority']
                stock_info['sell_details'] = sell_signal_result.get('details', '')

                print(f"✅ 更新 {stock_code} 卖出信号: {sell_signal_result['signal']}")

            except Exception as e:
                print(f"❌ 更新 {stock_code} 卖出信号失败: {e}")

        # 保存更新后的数据
        save_stock_data()

        print("✅ 所有股票卖出信号刷新完成")

        return jsonify({
            'success': True,
            'message': '卖出信号已刷新'
        })
    except Exception as e:
        print(f"❌ 刷新卖出信号失败: {e}")
        return jsonify({
            'success': False,
            'message': f'刷新失败: {str(e)}'
        }), 500

@app.route('/api/update-holdings', methods=['POST'])
def update_holdings():
    """更新持仓数量"""
    try:
        data = request.get_json()
        stock_code = data.get('stock_code')
        new_holdings = data.get('holdings')

        if not stock_code or new_holdings is None:
            return jsonify({
                'success': False,
                'message': '股票代码和持仓数量不能为空'
            }), 400

        if new_holdings < 0:
            return jsonify({
                'success': False,
                'message': '持仓数量不能为负数'
            }), 400

        global stock_data

        if stock_code not in stock_data:
            return jsonify({
                'success': False,
                'message': f'未找到股票 {stock_code}'
            }), 404

        old_holdings = stock_data[stock_code].get('holdings', 0)
        stock_data[stock_code]['holdings'] = new_holdings

        # 重新计算市值
        current_price = stock_data[stock_code].get('price', 0)
        new_market_value = current_price * new_holdings
        stock_data[stock_code]['market_value'] = round(new_market_value, 2)

        # 保存数据
        save_stock_data()

        print(f"📝 更新持仓: {stock_data[stock_code]['name']} ({stock_code}) 持仓从 {old_holdings} 更新为 {new_holdings}")

        return jsonify({
            'success': True,
            'message': f'持仓数量已更新',
            'data': {
                'stock_code': stock_code,
                'stock_name': stock_data[stock_code]['name'],
                'old_holdings': old_holdings,
                'new_holdings': new_holdings,
                'new_market_value': new_market_value
            }
        })
    except Exception as e:
        print(f"❌ 更新持仓失败: {e}")
        return jsonify({
            'success': False,
            'message': f'更新失败: {str(e)}'
        }), 500

@app.route('/api/mark-reduced', methods=['POST'])
def mark_reduced():
    """标记股票已减半，停止提醒"""
    try:
        data = request.get_json()
        stock_code = data.get('stock_code')

        if not stock_code:
            return jsonify({
                'success': False,
                'message': '股票代码不能为空'
            }), 400

        global stock_data

        if stock_code not in stock_data:
            return jsonify({
                'success': False,
                'message': f'未找到股票 {stock_code}'
            }), 404

        # 标记为已减半
        stock_data[stock_code]['is_reduced'] = True
        stock_data[stock_code]['reduced_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 在企业微信提醒系统中标记为已处理
        stock_name = stock_data[stock_code]['name']
        wechat_alert.mark_as_handled(stock_code, 'reduced')

        # 保存数据
        save_stock_data()

        print(f"✅ 标记已减半: {stock_name} ({stock_code})")

        return jsonify({
            'success': True,
            'message': f'{stock_name} 已标记为减半，将停止提醒',
            'data': {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'reduced_time': stock_data[stock_code]['reduced_time']
            }
        })
    except Exception as e:
        print(f"❌ 标记减半失败: {e}")
        return jsonify({
            'success': False,
            'message': f'操作失败: {str(e)}'
        }), 500

@app.route('/api/mark-action', methods=['POST'])
def mark_action():
    """标记股票操作（已减半或已清仓），停止提醒"""
    try:
        data = request.get_json()
        stock_code = data.get('stock_code')
        action_type = data.get('action_type')  # 'reduced' 或 'cleared'

        if not stock_code or not action_type:
            return jsonify({
                'success': False,
                'message': '缺少股票代码或操作类型'
            }), 400

        global stock_data

        if stock_code not in stock_data:
            return jsonify({
                'success': False,
                'message': f'未找到股票 {stock_code}'
            }), 404

        if action_type not in ['reduced', 'cleared']:
            return jsonify({
                'success': False,
                'message': '无效的操作类型，只支持 reduced 或 cleared'
            }), 400

        # 标记操作
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        stock_name = stock_data[stock_code]['name']

        if action_type == 'reduced':
            stock_data[stock_code]['is_reduced'] = True
            stock_data[stock_code]['reduced_time'] = current_time
            action_text = '已减半'
        else:  # cleared
            stock_data[stock_code]['is_cleared'] = True
            stock_data[stock_code]['cleared_time'] = current_time
            action_text = '已清仓'

        # 在企业微信提醒系统中标记为已处理
        wechat_alert.mark_as_handled(stock_code, action_type)

        # 保存数据
        save_stock_data()

        print(f"✅ 标记{action_text}: {stock_name} ({stock_code})")

        return jsonify({
            'success': True,
            'message': f'{stock_name} 已标记为{action_text}，将停止提醒',
            'data': {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'action_type': action_type,
                'action_text': action_text,
                'action_time': current_time
            }
        })

    except Exception as e:
        print(f"❌ 标记操作失败: {e}")
        return jsonify({
            'success': False,
            'message': f'操作失败: {str(e)}'
        }), 500

@app.route('/api/set-custom-status', methods=['POST'])
def set_custom_status():
    """设置股票自定义状态"""
    try:
        data = request.get_json()
        stock_code = data.get('stock_code')
        status_type = data.get('status_type')  # 状态类型
        status_text = data.get('status_text')  # 显示文本
        status_emoji = data.get('status_emoji')  # 表情符号

        if not stock_code or not status_type or not status_text:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400

        global stock_data

        if stock_code not in stock_data:
            return jsonify({
                'success': False,
                'message': f'未找到股票 {stock_code}'
            }), 404

        # 设置自定义状态
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        stock_name = stock_data[stock_code]['name']

        # 覆盖原有的卖出信号
        stock_data[stock_code]['custom_status'] = True
        stock_data[stock_code]['custom_status_type'] = status_type
        stock_data[stock_code]['custom_status_text'] = status_text
        stock_data[stock_code]['custom_status_emoji'] = status_emoji or ''
        stock_data[stock_code]['custom_status_time'] = current_time

        # 根据状态类型设置相应的标记
        if status_type == 'reduced':
            stock_data[stock_code]['is_reduced'] = True
            stock_data[stock_code]['reduced_time'] = current_time
        elif status_type == 'cleared':
            stock_data[stock_code]['is_cleared'] = True
            stock_data[stock_code]['cleared_time'] = current_time

        # 在企业微信提醒系统中标记为已处理
        wechat_alert.mark_as_handled(stock_code, status_type)

        # 保存数据
        save_stock_data()

        print(f"✅ 设置自定义状态: {stock_name} ({stock_code}) -> {status_emoji}{status_text}")

        return jsonify({
            'success': True,
            'message': f'{stock_name} 已设置为{status_emoji}{status_text}，将停止提醒',
            'data': {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'status_type': status_type,
                'status_text': status_text,
                'status_emoji': status_emoji,
                'status_time': current_time
            }
        })

    except Exception as e:
        print(f"❌ 设置自定义状态失败: {e}")
        return jsonify({
            'success': False,
            'message': f'操作失败: {str(e)}'
        }), 500

@app.route('/api/generate-demo-data', methods=['POST'])
def generate_demo_data():
    """生成演示数据API"""
    try:
        import random

        # 清空现有数据
        global stock_data, last_update_time, imported_stock_list
        stock_data = {}
        imported_stock_list = []

        # 30支演示股票数据
        demo_stocks = [
            {'code': '000001', 'name': '平安银行', 'industry': '银行', 'holdings': 1000},
            {'code': '000002', 'name': '万科A', 'industry': '房地产开发', 'holdings': 800},
            {'code': '000858', 'name': '五粮液', 'industry': '白酒', 'holdings': 200},
            {'code': '000876', 'name': '新希望', 'industry': '农牧饲渔', 'holdings': 1500},
            {'code': '002415', 'name': '海康威视', 'industry': '安防设备', 'holdings': 600},
            {'code': '002594', 'name': '比亚迪', 'industry': '汽车整车', 'holdings': 300},
            {'code': '600036', 'name': '招商银行', 'industry': '银行', 'holdings': 1200},
            {'code': '600519', 'name': '贵州茅台', 'industry': '白酒', 'holdings': 50},
            {'code': '600887', 'name': '伊利股份', 'industry': '乳品', 'holdings': 900},
            {'code': '000568', 'name': '泸州老窖', 'industry': '白酒', 'holdings': 400},
            {'code': '002304', 'name': '洋河股份', 'industry': '白酒', 'holdings': 350},
            {'code': '000895', 'name': '双汇发展', 'industry': '肉制品', 'holdings': 700},
            {'code': '600276', 'name': '恒瑞医药', 'industry': '化学制药', 'holdings': 500},
            {'code': '000661', 'name': '长春高新', 'industry': '生物制药', 'holdings': 250},
            {'code': '002142', 'name': '宁波银行', 'industry': '银行', 'holdings': 1100},
            {'code': '600309', 'name': '万华化学', 'industry': '化工', 'holdings': 450},
            {'code': '000338', 'name': '潍柴动力', 'industry': '机械设备', 'holdings': 800},
            {'code': '002475', 'name': '立讯精密', 'industry': '电子制造', 'holdings': 600},
            {'code': '600030', 'name': '中信证券', 'industry': '证券', 'holdings': 550},
            {'code': '000725', 'name': '京东方A', 'industry': '面板', 'holdings': 2000},
            {'code': '002230', 'name': '科大讯飞', 'industry': '软件开发', 'holdings': 400},
            {'code': '000063', 'name': '中兴通讯', 'industry': '通信设备', 'holdings': 700},
            {'code': '600741', 'name': '华域汽车', 'industry': '汽车零部件', 'holdings': 650},
            {'code': '002027', 'name': '分众传媒', 'industry': '广告', 'holdings': 800},
            {'code': '600585', 'name': '海螺水泥', 'industry': '水泥制造', 'holdings': 500},
            {'code': '000166', 'name': '申万宏源', 'industry': '证券', 'holdings': 900},
            {'code': '600104', 'name': '上汽集团', 'industry': '汽车整车', 'holdings': 750},
            {'code': '002008', 'name': '大族激光', 'industry': '激光设备', 'holdings': 300},
            {'code': '000977', 'name': '浪潮信息', 'industry': '计算机设备', 'holdings': 400},
            {'code': '600660', 'name': '福耀玻璃', 'industry': '汽车零部件', 'holdings': 600}
        ]

        # 为每支股票生成随机数据
        for stock in demo_stocks:
            # 基础价格范围
            if '茅台' in stock['name']:
                base_price = random.uniform(1600, 1800)
            elif '银行' in stock['industry']:
                base_price = random.uniform(15, 45)
            elif '白酒' in stock['industry']:
                base_price = random.uniform(80, 200)
            elif stock['name'] in ['比亚迪', '海康威视']:
                base_price = random.uniform(200, 300)
            else:
                base_price = random.uniform(8, 80)

            # 生成年内最低价（当前价格的60%-85%）
            yearly_low = base_price * random.uniform(0.60, 0.85)

            # 当前价格（基于最低价的涨幅）
            gain_pct = random.uniform(15, 85)  # 15%-85%的涨幅
            current_price = yearly_low * (1 + gain_pct / 100)

            # 计算距离最低点涨幅
            distance_from_low = ((current_price - yearly_low) / yearly_low) * 100

            # 随机生成其他数据
            pe_ratio = random.choice([None, random.uniform(-20, 35)]) if random.random() < 0.9 else None
            pb_ratio = random.uniform(0.8, 8.5)
            dividend_yield = random.uniform(0.5, 6.8)
            change_pct = random.uniform(-3.5, 4.2)

            # 计算市值
            market_value = current_price * stock['holdings']

            # 计算回本涨幅（随机生成成本价）
            cost_price = current_price * random.uniform(0.85, 1.15)
            profit_margin = ((current_price - cost_price) / cost_price) * 100
            profit_status = 'profit' if profit_margin > 0 else 'loss'

            # 生成卖出信号
            sell_signal_data = {
                'distance_from_low_pct': distance_from_low,
                'pe_ratio': pe_ratio,
                'pb_ratio': pb_ratio,
                'profit_margin': profit_margin,
                'profit_status': profit_status,
                'dividend_yield': dividend_yield,
                'scan_score': random.randint(60, 95)  # 提前生成扫雷评分
            }
            sell_signal = calculate_sell_signal(sell_signal_data)

            # 生成扫雷数据（使用已生成的评分）
            scan_score = sell_signal_data['scan_score']
            risk_count = random.randint(0, 3)

            # 生成成本价和单位成本
            unit_cost = cost_price

            # 构建股票信息 - 确保包含所有表格列的数据
            info = {
                'code': stock['code'],  # 添加代码字段
                'name': stock['name'],
                'price': round(current_price, 2),
                'change_pct': round(change_pct, 2),
                'yearly_low': round(yearly_low, 2),
                'distance_from_low_pct': round(distance_from_low, 1),
                'low_date': f"2024-{random.randint(1,12):02d}-{random.randint(1,28):02d}",
                'pe_ratio': round(pe_ratio, 2) if pe_ratio else None,
                'pb_ratio': round(pb_ratio, 2),
                'dividend_yield': round(dividend_yield, 2),
                'industry': stock['industry'],
                'holdings': stock['holdings'],
                'market_value': round(market_value, 2),
                'profit_margin': round(profit_margin, 1),
                'profit_status': profit_status,
                'unit_cost': round(unit_cost, 3),  # 添加单位成本
                'sell_signal': sell_signal['signal'],
                'sell_reason': sell_signal['reason'],
                'sell_color': sell_signal['color'],
                'sell_priority': sell_signal['priority'],
                'scan_score': scan_score,
                'scan_risk_level': '低风险' if scan_score > 80 else '中风险' if scan_score > 70 else '高风险',
                'scan_risk_color': '#2ed573' if scan_score > 80 else '#ffa502' if scan_score > 70 else '#ff4757',
                'scan_triggered_risks': risk_count,
                'scan_display_text': f'{scan_score}分',
                'update_time': datetime.now().strftime('%H:%M:%S'),
                'is_reduced': False,  # 默认未减半
                'reduced_time': None  # 减半时间
            }

            stock_data[stock['code']] = info
            imported_stock_list.append({
                'code': stock['code'],
                'name': stock['name'],
                'holdings': stock['holdings']
            })

        # 更新时间
        last_update_time = datetime.now()

        # 保存数据
        save_stock_data()
        save_imported_list()

        return jsonify({
            'success': True,
            'message': f'演示数据生成成功！已生成 {len(demo_stocks)} 支股票的模拟数据',
            'data': {
                'stock_count': len(demo_stocks),
                'total_market_value': sum(info['market_value'] for info in stock_data.values()),
                'sell_signals': len([s for s in stock_data.values() if s['sell_signal'] == 'sell']),
                'warning_signals': len([s for s in stock_data.values() if s['sell_signal'] == 'warning'])
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'生成演示数据失败: {str(e)}'
        }), 500

# HTML模板
HTML_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>持仓系统 V12 完整整合版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
        }

        .header .subtitle {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .update-info {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 5px;
            margin-top: 15px;
        }

        .controls {
            background: white;
            padding: 20px;
            margin: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-box {
            flex: 1;
            max-width: 300px;
            margin-right: 20px;
        }

        .search-box input {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }

        .stats {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            font-size: 0.9em;
            color: #666;
        }

        /* 分类筛选按钮 */
        .filter-buttons {
            background: white;
            padding: 15px 20px;
            margin: 0 20px 20px 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .filter-btn {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            color: #495057;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
        }

        .filter-btn:hover {
            background: #e9ecef;
            border-color: #adb5bd;
            transform: translateY(-2px);
        }

        .filter-btn.active {
            background: #667eea;
            border-color: #667eea;
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        /* 统计面板 */
        .stats-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 0 20px 20px 20px;
        }

        .stats-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .stats-card h3 {
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .total-assets {
            border-left: 4px solid #28a745;
        }

        .asset-allocation {
            border-left: 4px solid #17a2b8;
        }



        .stats-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .stats-item:last-child {
            border-bottom: none;
        }

        .stats-value {
            font-weight: bold;
            color: #667eea;
        }

        /* 数据管理按钮 */
        .management-buttons {
            background: white;
            padding: 15px 20px;
            margin: 0 20px 20px 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .management-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .management-btn:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .management-btn.danger {
            background: #dc3545;
        }

        .management-btn.danger:hover {
            background: #c82333;
        }

        .management-btn.success {
            background: #28a745;
        }

        .management-btn.success:hover {
            background: #218838;
        }

        .management-btn.primary {
            background: #007bff;
        }

        .management-btn.primary:hover {
            background: #0056b3;
        }

        .management-btn.warning {
            background: #ffc107;
            color: #212529;
        }

        .management-btn.warning:hover {
            background: #e0a800;
        }

        .management-btn.info {
            background: #17a2b8;
            color: white;
        }

        .management-btn.info:hover {
            background: #138496;
        }

        .management-btn.strategy {
            background: #6f42c1;
            color: white;
        }

        .management-btn.strategy:hover {
            background: #5a2d91;
        }

        /* 策略配置面板样式 */
        .strategy-panel {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .strategy-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        /* 策略配置面板移动端优化 */
        @media (max-width: 768px) {
            .strategy-card {
                max-width: 95vw;
                margin: 10px;
                padding: 15px;
                max-height: 85vh;
            }

            .preset-buttons {
                flex-direction: column;
                gap: 8px;
            }

            .preset-btn {
                width: 100%;
                padding: 12px 16px;
                font-size: 14px;
            }

            .strategy-params {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .strategy-actions {
                flex-direction: column;
                gap: 10px;
            }

            .action-btn {
                width: 100%;
                padding: 12px 20px;
                font-size: 16px;
            }
        }

        .strategy-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }

        .close-btn {
            background: #ff4757;
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            cursor: pointer;
            font-size: 18px;
            line-height: 1;
        }

        .strategy-presets {
            margin-bottom: 20px;
        }

        .preset-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .preset-btn {
            background: #74b9ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .preset-btn:hover {
            background: #0984e3;
            transform: translateY(-2px);
        }

        .strategy-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }

        .strategy-item.enabled {
            border-color: #28a745;
            background: #f8fff9;
        }

        .strategy-item.disabled {
            border-color: #dc3545;
            background: #fff8f8;
            opacity: 0.7;
        }

        .strategy-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .strategy-toggle {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .strategy-toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: #28a745;
        }

        input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }

        .strategy-params {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }

        .param-group {
            display: flex;
            flex-direction: column;
        }

        .param-group label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .param-group input {
            padding: 5px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .strategy-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 2px solid #eee;
        }

        .action-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .action-btn.primary {
            background: #007bff;
            color: white;
        }

        .action-btn.primary:hover {
            background: #0056b3;
        }

        .action-btn.secondary {
            background: #6c757d;
            color: white;
        }

        .action-btn.secondary:hover {
            background: #545b62;
        }

        .action-btn.info {
            background: #17a2b8;
            color: white;
        }

        .action-btn.info:hover {
            background: #138496;
        }

        .action-btn.success {
            background: #28a745;
            color: white;
        }

        .action-btn.success:hover {
            background: #218838;
        }

        /* 自定义策略对话框 */
        .custom-strategy-dialog {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1100;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .dialog-content {
            background: white;
            border-radius: 10px;
            padding: 20px;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .dialog-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }

        .dialog-body {
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-group textarea {
            height: 80px;
            resize: vertical;
        }

        .add-condition-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }

        .condition-item {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
            background: #f8f9fa;
        }

        .dialog-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            padding-top: 20px;
            border-top: 2px solid #eee;
        }

        /* 单只股票更新按钮样式 */
        .update-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
            min-width: 30px;
        }

        .update-btn:hover {
            background: #218838;
            transform: scale(1.1);
        }

        .update-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .update-btn.updating {
            background: #ffc107;
            color: #212529;
            animation: pulse 1s infinite;
        }

        /* 操作按钮容器 */
        .action-buttons {
            display: flex;
            gap: 4px;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* 编辑持仓按钮 */
        .edit-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
            min-width: 30px;
        }

        .edit-btn:hover {
            background: #0056b3;
            transform: scale(1.1);
        }

        /* 已减半按钮 */
        .reduce-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
            min-width: 30px;
        }

        .reduce-btn:hover {
            background: #218838;
            transform: scale(1.1);
        }

        /* 已减半标记 */
        .reduced-mark {
            background: #6c757d;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            white-space: nowrap;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        /* 操作列样式 */
        .no-sort {
            cursor: default !important;
        }

        .no-sort:hover {
            background: transparent !important;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 持仓系统 V12 股票专版</h1>
        <div class="subtitle">A股股票数据监控 | 智能交易时间检测 | 扫雷风险评估 | 自动过滤非股票证券</div>
        <div class="update-info">
            <span id="updateTime">等待数据加载...</span> |
            <span id="stockCount">0</span> 只A股股票 |
            <span id="tradingStatus">检查交易状态中...</span>
        </div>
    </div>

    <!-- 数据管理按钮 -->
    <div class="management-buttons">
        <button class="management-btn danger" onclick="clearData()">🗑️ 清空数据</button>
        <input type="file" id="fileInput" accept=".xlsx,.xls" style="display: none;" onchange="uploadFile()">
        <button class="management-btn primary" onclick="document.getElementById('fileInput').click()">📁 导入持仓表格</button>
        <button class="management-btn info" onclick="generateDemoData()">🎭 生成演示数据</button>
        <button class="management-btn success" onclick="refreshData()">🔄 重新获取数据</button>
        <button class="management-btn" onclick="checkCacheStatus()">📊 缓存状态</button>
        <button class="management-btn warning" onclick="testWeChatAlerts()">📱 测试企业微信提醒</button>
        <button class="management-btn strategy" onclick="openStrategyPanel()">⚙️ 策略配置</button>
    </div>

    <!-- 股票资产统计面板 -->
    <div class="stats-panel">
        <div class="stats-card total-assets">
            <h3>💰 股票资产统计</h3>
            <div class="stats-item">
                <span>总市值</span>
                <span class="stats-value" id="totalMarketValue">¥0</span>
            </div>
            <div class="stats-item">
                <span>股票数量</span>
                <span class="stats-value" id="stockCountDisplay">0只</span>
            </div>
        </div>
    </div>

    <!-- 卖出策略配置面板 -->
    <div class="strategy-panel" id="strategyPanel" style="display: none;">
        <div class="strategy-card">
            <div class="strategy-header">
                <h3>⚙️ 卖出策略配置</h3>
                <button class="close-btn" onclick="closeStrategyPanel()">×</button>
            </div>

            <!-- 策略预设 -->
            <div class="strategy-presets">
                <h4>📋 策略预设</h4>
                <div class="preset-buttons">
                    <button class="preset-btn" onclick="applyPreset('conservative')">🛡️ 保守型</button>
                    <button class="preset-btn" onclick="applyPreset('balanced')">⚖️ 平衡型</button>
                    <button class="preset-btn" onclick="applyPreset('aggressive')">🚀 激进型</button>
                </div>
            </div>

            <!-- 策略列表 -->
            <div class="strategy-list" id="strategyList">
                <!-- 策略项将通过JavaScript动态生成 -->
            </div>

            <!-- 操作按钮 -->
            <div class="strategy-actions">
                <button class="action-btn primary" onclick="saveStrategies()">💾 保存配置</button>
                <button class="action-btn secondary" onclick="resetStrategies()">🔄 重置默认</button>
                <button class="action-btn info" onclick="showCustomStrategyDialog()">➕ 自定义策略</button>
                <button class="action-btn success" onclick="testStrategies()">🧪 测试策略</button>
            </div>
        </div>
    </div>

    <!-- 自定义策略对话框 -->
    <div class="custom-strategy-dialog" id="customStrategyDialog" style="display: none;">
        <div class="dialog-content">
            <div class="dialog-header">
                <h3>➕ 创建自定义策略</h3>
                <button class="close-btn" onclick="closeCustomStrategyDialog()">×</button>
            </div>
            <div class="dialog-body">
                <div class="form-group">
                    <label>策略名称:</label>
                    <input type="text" id="customStrategyName" placeholder="输入策略名称">
                </div>
                <div class="form-group">
                    <label>策略描述:</label>
                    <textarea id="customStrategyDesc" placeholder="描述策略的作用和逻辑"></textarea>
                </div>
                <div class="form-group">
                    <label>条件设置:</label>
                    <div id="customConditions">
                        <!-- 条件将通过JavaScript动态生成 -->
                    </div>
                    <button class="add-condition-btn" onclick="addCustomCondition()">+ 添加条件</button>
                </div>
            </div>
            <div class="dialog-actions">
                <button class="action-btn primary" onclick="saveCustomStrategy()">创建策略</button>
                <button class="action-btn secondary" onclick="closeCustomStrategyDialog()">取消</button>
            </div>
        </div>
    </div>

    <!-- 搜索框 -->
    <div class="controls">
        <div class="search-box">
            <input type="text" id="searchInput" placeholder="搜索股票代码或名称...">
        </div>
    </div>

    <!-- 卖出信号筛选按钮 -->
    <div class="filter-buttons">
        <div style="text-align: center; margin-bottom: 15px;">
            <button class="filter-btn" onclick="filterBySellSignal('all')" id="filterAll">全部股票</button>
            <button class="filter-btn" onclick="filterBySellSignal('clearance')" id="filterClearance" style="background: #8b0000;">🔥 清仓信号</button>
            <button class="filter-btn" onclick="filterBySellSignal('sell')" id="filterSell" style="background: #ff4757;">🚨 卖出信号</button>
            <button class="filter-btn" onclick="filterBySellSignal('warning')" id="filterWarning" style="background: #ffa502;">⚠️ 卖出预警</button>
            <button class="filter-btn" onclick="filterBySellSignal('hold')" id="filterHold" style="background: #2ed573;">✅ 持有</button>
        </div>
        <div style="text-align: center; color: #666; font-size: 14px; padding: 10px;">
            📊 当前显示：仅A股股票（已自动过滤ETF、基金、可转债等其他证券）
        </div>
    </div>

    <!-- 表格容器 -->
    <div class="table-container">
        <table id="stockTable">
            <thead>
                <tr>
                    <th onclick="sortTable('code')" class="sticky-left-1">代码 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('name')" class="sticky-left-2">名称 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('price')">最新价 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('change_pct')">涨跌幅 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('yearly_low')" class="mobile-hidden">年内最低价 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('low_date')" class="mobile-hidden">最低价日期 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('distance_from_low_pct')" class="mobile-hidden">距最低点涨幅 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('pb_ratio')" class="mobile-hidden">市净率 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('pe_ratio')" class="mobile-hidden">TTM市盈率 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('dividend_yield')" class="mobile-hidden">股息率 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('industry')" class="extra-mobile-hidden">行业 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('sell_priority')">卖出信号 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('scan_score')">扫雷 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('holdings')">持仓 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('profit_margin')" class="mobile-hidden">回本涨幅 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('market_value')">市值 <span class="sort-indicator"></span></th>
                    <th onclick="sortTable('update_time')" class="mobile-hidden">更新时间 <span class="sort-indicator"></span></th>
                    <th class="no-sort">操作</th>
                </tr>
            </thead>
            <tbody id="stockTableBody">
                <tr>
                    <td colspan="18" style="text-align: center; padding: 40px;">
                        <div style="color: #666;">
                            <div style="font-size: 48px; margin-bottom: 20px;">📊</div>
                            <div style="font-size: 18px; margin-bottom: 10px;">暂无股票数据</div>
                            <div style="font-size: 14px;">请导入持仓表格或等待数据加载</div>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <style>
        /* 表格样式 */
        .table-container {
            background: white;
            margin: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: auto;
            max-height: 80vh; /* 限制表格最大高度为视窗高度的80% */
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
            border-spacing: 0;
        }

        th, td {
            padding: 12px 8px;
            text-align: left;
            white-space: nowrap;
            margin: 0;
            border: none;
        }

        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #495057;
            cursor: pointer;
            user-select: none;
            position: sticky;
            top: 0;
            z-index: 10;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* 固定左侧列样式 */
        .sticky-left-1 {
            position: sticky;
            left: 0;
            min-width: 80px;
        }

        .sticky-left-2 {
            position: sticky;
            left: 80px; /* 第一列的宽度 */
            min-width: 100px;
        }

        /* 表头固定列样式 - 最高优先级 */
        th.sticky-left-1 {
            z-index: 1000;
            background: #f8f9fa;
            box-shadow: 2px 0 4px rgba(0,0,0,0.08);
        }

        th.sticky-left-2 {
            z-index: 1000;
            background: #f8f9fa;
            box-shadow: 2px 0 4px rgba(0,0,0,0.08);
        }

        /* 数据行的固定列样式 */
        td.sticky-left-1 {
            z-index: 100;
            background: white;
            box-shadow: 2px 0 4px rgba(0,0,0,0.05);
        }

        td.sticky-left-2 {
            z-index: 100;
            background: white;
            box-shadow: 2px 0 4px rgba(0,0,0,0.05);
        }

        th:hover {
            background: #e9ecef;
        }

        .sort-indicator {
            margin-left: 5px;
            opacity: 0.5;
        }

        .sort-indicator.asc::after {
            content: '↑';
            opacity: 1;
        }

        .sort-indicator.desc::after {
            content: '↓';
            opacity: 1;
        }

        /* 表格行样式 - 统一背景色 */
        tbody tr {
            background-color: white;
        }

        tbody tr:hover {
            background-color: #e3f2fd !important;
            transform: translateY(-1px);
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        tbody tr:hover td {
            background-color: #e3f2fd !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .positive {
            color: #dc3545;
        }

        .negative {
            color: #28a745;
        }

        .scan-score {
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
        }

        /* 卖出信号样式 */
        .sell-signal {
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            font-size: 12px;
            text-align: center;
            min-width: 60px;
            display: inline-block;
        }

        .sell-signal.sell {
            background-color: #ff4757;
            animation: pulse 2s infinite;
        }

        /* 清仓信号特殊样式 */
        .sell-signal.clearance {
            background-color: #8b0000 !important;
            color: white !important;
            font-weight: bold !important;
            border: 2px solid #ff0000 !important;
            animation: clearance-blink 2s infinite !important;
        }

        @keyframes clearance-blink {
            0%, 50% { opacity: 1; }
            25%, 75% { opacity: 0.7; }
        }

        .sell-signal.warning {
            background-color: #ffa502;
        }

        .sell-signal.hold {
            background-color: #2ed573;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        /* 可点击的卖出信号样式 */
        .sell-signal.clickable {
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .sell-signal.clickable:hover {
            transform: scale(1.05);
            border: 2px solid #fff;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        /* 操作标记样式 */
        .action-mark {
            color: #666;
            font-size: 10px;
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            margin-top: 2px;
            display: inline-block;
        }

        /* 状态菜单样式 */
        .status-menu {
            position: fixed;
            background: white;
            border: 1px solid #ddd;
            border-radius: 12px;
            box-shadow: 0 12px 32px rgba(0,0,0,0.2);
            z-index: 10000;
            min-width: 280px;
            max-height: 400px;
            overflow-y: auto;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .status-menu-header {
            padding: 16px 20px 12px;
            font-weight: 600;
            font-size: 16px;
            color: #333;
            border-bottom: 1px solid #eee;
            background: #f8f9fa;
            border-radius: 12px 12px 0 0;
        }

        .status-menu-item {
            padding: 14px 20px;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 12px;
            position: relative;
        }

        .status-menu-item:hover {
            background-color: #f8f9fa;
            transform: translateX(2px);
        }

        .status-menu-item.cancel {
            color: #6c757d;
            border-top: 1px solid #eee;
            margin-top: 8px;
            background: #f8f9fa;
        }

        .status-menu-item.cancel:hover {
            background-color: #e9ecef;
        }

        .status-emoji {
            font-size: 18px;
            width: 24px;
            text-align: center;
        }

        .status-text {
            flex: 1;
            font-weight: 500;
            color: #333;
        }

        .status-hint {
            color: #666;
            font-size: 12px;
            opacity: 0.8;
        }

        /* 自定义状态样式 */
        .custom-status {
            border: 2px solid #6c757d !important;
            background: linear-gradient(135deg, #6c757d, #5a6268) !important;
        }

        .custom-status:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                gap: 15px;
            }

            .search-box {
                max-width: none;
                margin-right: 0;
            }

            .stats {
                justify-content: center;
            }

            .stats-panel {
                grid-template-columns: 1fr;
                margin: 0 10px 20px 10px;
            }

            .filter-buttons, .management-buttons {
                margin: 0 10px 20px 10px;
            }

            /* 移动端管理按钮优化 */
            .management-buttons {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
                justify-content: center;
            }

            .management-btn {
                font-size: 12px !important;
                padding: 8px 12px !important;
                min-width: auto !important;
                flex: 0 0 auto;
            }

            .management-btn.strategy {
                background: #6f42c1 !important;
                color: white !important;
                font-weight: bold !important;
            }

            /* 移动端筛选按钮优化 */
            .filter-buttons div {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
                justify-content: center;
            }

            .filter-btn {
                font-size: 12px !important;
                padding: 8px 12px !important;
                min-width: auto !important;
                flex: 0 0 auto;
            }

            /* 清仓信号按钮在移动端的特殊样式 */
            #filterClearance {
                background: #8b0000 !important;
                color: white !important;
                font-weight: bold !important;
                border: 1px solid #ff0000 !important;
            }

            .table-container {
                margin: 10px;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }

            table {
                min-width: 1200px; /* 增加最小宽度以显示所有列 */
                font-size: 13px;
            }

            th, td {
                padding: 6px 3px;
                font-size: 12px;
                white-space: nowrap;
            }

            /* 移动端卖出信号优化 */
            .sell-signal {
                font-size: 11px !important;
                padding: 4px 6px !important;
                min-width: 50px !important;
                text-align: center !important;
            }

            /* 移动端清仓信号特殊样式 */
            .sell-signal.clearance {
                background-color: #8b0000 !important;
                color: white !important;
                font-weight: bold !important;
                border: 1px solid #ff0000 !important;
                animation: clearance-blink 2s infinite !important;
            }

            /* 移动端调整固定列位置 */
            .sticky-left-1 {
                left: 0;
                min-width: 60px;
            }

            .sticky-left-2 {
                left: 60px; /* 调整为移动端第一列的宽度 */
                min-width: 80px;
            }

            /* 移动端显示所有列，通过横向滚动查看 */
            /* .mobile-hidden { display: none; } 注释掉，显示所有列 */
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.6em;
                padding: 10px;
            }

            /* 超小屏幕筛选按钮优化 */
            .filter-btn {
                font-size: 10px !important;
                padding: 6px 8px !important;
                margin: 2px !important;
            }

            /* 超小屏幕卖出信号优化 */
            .sell-signal {
                font-size: 9px !important;
                padding: 3px 4px !important;
                min-width: 40px !important;
            }

            /* 超小屏幕表格优化 */
            table {
                min-width: 1000px;
                font-size: 11px;
            }

            th, td {
                padding: 4px 2px;
                font-size: 10px;
            }

            /* 移动端触摸优化 */
            .filter-btn, .management-btn, .action-btn {
                -webkit-tap-highlight-color: transparent;
                touch-action: manipulation;
                min-height: 44px; /* iOS推荐的最小触摸目标 */
            }

            /* 移动端策略配置按钮优化 */
            .management-btn.strategy {
                background: #6f42c1 !important;
                border: 2px solid #5a2d91 !important;
            }

            /* 移动端操作按钮优化 */
            .action-buttons {
                flex-direction: column;
                gap: 2px;
            }

            .update-btn, .edit-btn, .reduce-btn {
                font-size: 10px !important;
                padding: 3px 6px !important;
                min-width: 25px !important;
            }

            .reduced-mark {
                font-size: 8px !important;
                padding: 1px 4px !important;
            }

            .header .subtitle {
                font-size: 0.85em;
                padding: 0 10px;
            }

            .filter-btn, .management-btn {
                padding: 6px 10px;
                font-size: 11px;
                margin: 2px;
            }

            table {
                min-width: 1000px; /* 超小屏幕也保持足够宽度显示所有列 */
                font-size: 11px;
            }

            th, td {
                padding: 4px 2px;
                font-size: 10px;
            }

            /* 超小屏幕固定列调整 */
            .sticky-left-1 {
                left: 0;
                min-width: 50px;
            }

            .sticky-left-2 {
                left: 50px; /* 调整为超小屏幕第一列的宽度 */
                min-width: 70px;
            }

            /* 超小屏幕也显示所有列 */
            /* .extra-mobile-hidden { display: none; } 注释掉，显示所有列 */
        }
    </style>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        let stockData = [];
        let filteredData = [];
        let sortColumn = 'sell_priority';  // 默认按卖出信号优先级排序
        let sortDirection = 'asc';         // 默认从小到大（卖出信号在前）
        let currentSellFilter = 'all';     // 当前卖出信号筛选

        // ==================== 策略配置功能 ====================

        let currentStrategies = {};

        // 打开策略配置面板
        async function openStrategyPanel() {
            try {
                const response = await fetch('/api/strategies');
                const result = await response.json();

                if (result.success) {
                    currentStrategies = result.data;
                    renderStrategyPanel();
                    document.getElementById('strategyPanel').style.display = 'flex';
                } else {
                    alert('加载策略配置失败: ' + result.message);
                }
            } catch (error) {
                alert('加载策略配置失败: ' + error.message);
            }
        }

        // 关闭策略配置面板
        function closeStrategyPanel() {
            document.getElementById('strategyPanel').style.display = 'none';
        }

        // 渲染策略配置面板
        function renderStrategyPanel() {
            const strategyList = document.getElementById('strategyList');
            strategyList.innerHTML = '';

            for (const [strategyId, strategy] of Object.entries(currentStrategies)) {
                const strategyItem = document.createElement('div');
                strategyItem.className = `strategy-item ${strategy.enabled ? 'enabled' : 'disabled'}`;

                let paramsHtml = '';
                for (const [paramKey, paramValue] of Object.entries(strategy.params || {})) {
                    const paramLabel = getParamLabel(paramKey);
                    const inputType = typeof paramValue === 'boolean' ? 'checkbox' : 'number';
                    const inputValue = typeof paramValue === 'boolean' ? (paramValue ? 'checked' : '') : paramValue;

                    paramsHtml += `
                        <div class="param-group">
                            <label>${paramLabel}</label>
                            <input type="${inputType}"
                                   value="${inputType === 'checkbox' ? '' : inputValue}"
                                   ${inputType === 'checkbox' ? inputValue : ''}
                                   onchange="updateStrategyParam('${strategyId}', '${paramKey}', this.${inputType === 'checkbox' ? 'checked' : 'value'})">
                        </div>
                    `;
                }

                strategyItem.innerHTML = `
                    <div class="strategy-title">
                        <div>
                            <h4>${strategy.name}</h4>
                            <p style="font-size: 12px; color: #666; margin: 5px 0;">${strategy.description}</p>
                        </div>
                        <label class="strategy-toggle">
                            <input type="checkbox" ${strategy.enabled ? 'checked' : ''}
                                   onchange="toggleStrategy('${strategyId}', this.checked)">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    <div class="strategy-params">
                        ${paramsHtml}
                    </div>
                `;

                strategyList.appendChild(strategyItem);
            }
        }

        // 获取参数标签
        function getParamLabel(paramKey) {
            const labels = {
                'sell_threshold': '卖出阈值',
                'warning_threshold': '预警阈值',
                'pe_max': 'PE上限',
                'pe_min': 'PE下限',
                'pb_threshold': 'PB阈值',
                'profit_threshold': '盈利阈值(%)',
                'dividend_max': '股息率上限(%)',
                'score_threshold': '评分阈值',
                'require_profit': '需要盈利',
                'gain_threshold': '涨幅阈值(%)',
                'ttm_threshold': 'TTM市盈率阈值',
                'limit_up_threshold': '涨停阈值(%)',
                'high_ttm_threshold': '高TTM阈值'
            };
            return labels[paramKey] || paramKey;
        }

        // 切换策略启用状态
        function toggleStrategy(strategyId, enabled) {
            if (currentStrategies[strategyId]) {
                currentStrategies[strategyId].enabled = enabled;
                renderStrategyPanel();

                // 实时预览：自动保存并刷新数据
                debounceAutoSave();
            }
        }

        // 更新策略参数
        function updateStrategyParam(strategyId, paramKey, value) {
            if (currentStrategies[strategyId] && currentStrategies[strategyId].params) {
                // 转换数据类型
                if (typeof currentStrategies[strategyId].params[paramKey] === 'number') {
                    value = parseFloat(value);
                } else if (typeof currentStrategies[strategyId].params[paramKey] === 'boolean') {
                    value = Boolean(value);
                }
                currentStrategies[strategyId].params[paramKey] = value;

                // 实时预览：自动保存并刷新数据
                debounceAutoSave();
            }
        }

        // 防抖自动保存
        let autoSaveTimeout;
        function debounceAutoSave() {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = setTimeout(async () => {
                try {
                    console.log('🔄 开始自动保存策略配置...');

                    // 1. 保存策略配置
                    const saveResponse = await fetch('/api/strategies', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(currentStrategies)
                    });
                    const saveResult = await saveResponse.json();

                    if (saveResult.success) {
                        console.log('✅ 策略配置保存成功');

                        // 2. 强制刷新卖出信号
                        const refreshResponse = await fetch('/api/refresh-signals', {
                            method: 'POST'
                        });
                        const refreshResult = await refreshResponse.json();

                        if (refreshResult.success) {
                            console.log('✅ 卖出信号刷新成功');

                            // 3. 重新加载股票数据以显示更新
                            await loadStockData();
                            console.log('✅ 策略配置已自动保存并应用');
                        } else {
                            console.error('❌ 刷新卖出信号失败:', refreshResult.message);
                        }
                    } else {
                        console.error('❌ 保存策略配置失败:', saveResult.message);
                    }
                } catch (error) {
                    console.error('❌ 自动保存失败:', error);
                }
            }, 1500); // 1.5秒后自动保存
        }

        // 应用策略预设
        async function applyPreset(presetName) {
            try {
                const response = await fetch(`/api/strategies/presets/${presetName}`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    alert(result.message);
                    // 重新加载策略配置
                    openStrategyPanel();
                } else {
                    alert('应用预设失败: ' + result.message);
                }
            } catch (error) {
                alert('应用预设失败: ' + error.message);
            }
        }

        // 保存策略配置
        async function saveStrategies() {
            try {
                const response = await fetch('/api/strategies', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(currentStrategies)
                });
                const result = await response.json();

                if (result.success) {
                    alert('策略配置已保存');
                    closeStrategyPanel();
                    // 重新加载股票数据以应用新策略
                    await loadStockData();
                } else {
                    alert('保存失败: ' + result.message);
                }
            } catch (error) {
                alert('保存失败: ' + error.message);
            }
        }

        // 重置策略配置
        function resetStrategies() {
            if (confirm('确定要重置为默认策略配置吗？')) {
                // 重新加载默认配置
                openStrategyPanel();
            }
        }

        // 显示自定义策略对话框
        function showCustomStrategyDialog() {
            document.getElementById('customStrategyDialog').style.display = 'flex';
            renderCustomConditions();
        }

        // 关闭自定义策略对话框
        function closeCustomStrategyDialog() {
            document.getElementById('customStrategyDialog').style.display = 'none';
            // 清空表单
            document.getElementById('customStrategyName').value = '';
            document.getElementById('customStrategyDesc').value = '';
            document.getElementById('customConditions').innerHTML = '';
        }

        // 渲染自定义条件
        function renderCustomConditions() {
            const container = document.getElementById('customConditions');
            container.innerHTML = `
                <div class="condition-item">
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr auto; gap: 10px; align-items: center;">
                        <select class="condition-field">
                            <option value="distance_from_low_pct">距最低点涨幅</option>
                            <option value="pe_ratio">PE比率</option>
                            <option value="pb_ratio">PB比率</option>
                            <option value="profit_margin">盈利率</option>
                            <option value="dividend_yield">股息率</option>
                            <option value="scan_score">扫雷评分</option>
                        </select>
                        <select class="condition-operator">
                            <option value=">">&gt;</option>
                            <option value=">=">&gt;=</option>
                            <option value="<">&lt;</option>
                            <option value="<=">&lt;=</option>
                            <option value="==">=</option>
                        </select>
                        <input type="number" class="condition-value" placeholder="数值" step="0.1">
                        <button type="button" onclick="removeCondition(this)" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px;">删除</button>
                    </div>
                </div>
            `;
        }

        // 添加自定义条件
        function addCustomCondition() {
            const container = document.getElementById('customConditions');
            const conditionItem = document.createElement('div');
            conditionItem.className = 'condition-item';
            conditionItem.innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr auto; gap: 10px; align-items: center;">
                    <select class="condition-field">
                        <option value="distance_from_low_pct">距最低点涨幅</option>
                        <option value="pe_ratio">PE比率</option>
                        <option value="pb_ratio">PB比率</option>
                        <option value="profit_margin">盈利率</option>
                        <option value="dividend_yield">股息率</option>
                        <option value="scan_score">扫雷评分</option>
                    </select>
                    <select class="condition-operator">
                        <option value=">">&gt;</option>
                        <option value=">=">&gt;=</option>
                        <option value="<">&lt;</option>
                        <option value="<=">&lt;=</option>
                        <option value="==">=</option>
                    </select>
                    <input type="number" class="condition-value" placeholder="数值" step="0.1">
                    <button type="button" onclick="removeCondition(this)" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px;">删除</button>
                </div>
            `;
            container.appendChild(conditionItem);
        }

        // 删除条件
        function removeCondition(button) {
            button.closest('.condition-item').remove();
        }

        // 保存自定义策略
        async function saveCustomStrategy() {
            const name = document.getElementById('customStrategyName').value.trim();
            const description = document.getElementById('customStrategyDesc').value.trim();

            if (!name) {
                alert('请输入策略名称');
                return;
            }

            // 收集条件
            const conditions = [];
            const conditionItems = document.querySelectorAll('.condition-item');

            conditionItems.forEach(item => {
                const field = item.querySelector('.condition-field').value;
                const operator = item.querySelector('.condition-operator').value;
                const value = parseFloat(item.querySelector('.condition-value').value);

                if (!isNaN(value)) {
                    conditions.push({ field, operator, value });
                }
            });

            if (conditions.length === 0) {
                alert('请至少添加一个有效条件');
                return;
            }

            try {
                const response = await fetch('/api/strategies/custom', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: name,
                        description: description,
                        config: {
                            conditions: conditions,
                            weight: 1.0
                        }
                    })
                });

                const result = await response.json();

                if (result.success) {
                    alert(result.message);
                    closeCustomStrategyDialog();
                    // 重新加载策略配置
                    openStrategyPanel();
                } else {
                    alert('创建失败: ' + result.message);
                }
            } catch (error) {
                alert('创建失败: ' + error.message);
            }
        }

        // 测试策略配置
        async function testStrategies() {
            try {
                console.log('🧪 开始测试策略配置...');

                // 1. 先保存当前配置
                const saveResponse = await fetch('/api/strategies', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(currentStrategies)
                });
                const saveResult = await saveResponse.json();

                if (saveResult.success) {
                    console.log('✅ 策略配置保存成功');

                    // 2. 强制刷新卖出信号
                    const refreshResponse = await fetch('/api/refresh-signals', {
                        method: 'POST'
                    });
                    const refreshResult = await refreshResponse.json();

                    if (refreshResult.success) {
                        console.log('✅ 卖出信号刷新成功');

                        // 3. 重新加载股票数据
                        await loadStockData();

                        // 4. 统计策略效果
                        let clearanceCount = 0;
                        let sellCount = 0;
                        let warningCount = 0;
                        let holdCount = 0;

                        stockData.forEach(stock => {
                            if (stock.sell_signal === 'clearance') clearanceCount++;
                            else if (stock.sell_signal === 'sell') sellCount++;
                            else if (stock.sell_signal === 'warning') warningCount++;
                            else holdCount++;
                        });

                        alert(`🧪 策略测试结果:\\n\\n📊 信号统计:\\n• 🔥 清仓信号: ${clearanceCount}只\\n• 🚨 卖出信号: ${sellCount}只\\n• ⚠️ 预警信号: ${warningCount}只\\n• ✅ 持有信号: ${holdCount}只\\n\\n✅ 策略配置已应用到表格中，请查看卖出信号列的变化！`);
                    } else {
                        alert('刷新信号失败: ' + refreshResult.message);
                    }
                } else {
                    alert('保存配置失败: ' + saveResult.message);
                }
            } catch (error) {
                console.error('❌ 测试失败:', error);
                alert('测试失败: ' + error.message);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStockData();
            loadTradingStatus();

            // 设置默认排序指示器
            updateSortIndicator();

            // 设置定时刷新
            setInterval(loadStockData, 30000); // 30秒刷新一次
            setInterval(loadTradingStatus, 60000); // 1分钟刷新一次交易状态

            // 搜索功能
            document.getElementById('searchInput').addEventListener('input', function() {
                filterAndDisplayData();
            });
        });

        // 编辑持仓数量
        async function editHoldings(stockCode, currentHoldings) {
            const newHoldings = prompt(`请输入新的持仓数量:`, currentHoldings);

            if (newHoldings === null) return; // 用户取消

            const holdings = parseFloat(newHoldings);
            if (isNaN(holdings) || holdings < 0) {
                alert('请输入有效的持仓数量（不能为负数）');
                return;
            }

            try {
                const response = await fetch('/api/update-holdings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        stock_code: stockCode,
                        holdings: holdings
                    })
                });

                const result = await response.json();

                if (result.success) {
                    const data = result.data;
                    alert(`✅ ${result.message}\\n\\n📊 持仓变化:\\n• 股票: ${data.stock_name} (${data.stock_code})\\n• 原持仓: ${data.old_holdings}\\n• 新持仓: ${data.new_holdings}\\n• 新市值: ¥${formatNumber(data.new_market_value, 2)}`);

                    // 重新加载数据
                    await loadStockData();
                } else {
                    alert(`❌ 更新失败: ${result.message}`);
                }
            } catch (error) {
                alert(`❌ 更新失败: ${error.message}`);
            }
        }

        // 标记已减半
        async function markReduced(stockCode) {
            if (!confirm('确定要标记这只股票为已减半吗？\\n\\n标记后将停止所有企业微信提醒。')) {
                return;
            }

            try {
                const response = await fetch('/api/mark-reduced', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        stock_code: stockCode
                    })
                });

                const result = await response.json();

                if (result.success) {
                    const data = result.data;
                    alert(`✅ ${result.message}\\n\\n📊 操作详情:\\n• 股票: ${data.stock_name} (${data.stock_code})\\n• 操作时间: ${data.reduced_time}\\n\\n📱 该股票的所有企业微信提醒已停止。`);

                    // 重新加载数据
                    await loadStockData();
                } else {
                    alert(`❌ 操作失败: ${result.message}`);
                }
            } catch (error) {
                alert(`❌ 操作失败: ${error.message}`);
            }
        }

        // 显示状态选择菜单
        function showStatusMenu(stockCode, stockName) {
            // 移除已存在的菜单
            const existingMenu = document.querySelector('.status-menu');
            if (existingMenu) {
                existingMenu.remove();
            }

            // 创建菜单
            const menu = document.createElement('div');
            menu.className = 'status-menu';

            // 预定义状态选项
            const statusOptions = [
                { type: 'reduced', emoji: '✅', text: '已减半', color: '#28a745' },
                { type: 'cleared', emoji: '🔥', text: '已清仓', color: '#dc3545' },
                { type: 'custom1', emoji: '📈', text: '已加仓', color: '#007bff' },
                { type: 'custom2', emoji: '⏳', text: '观察中', color: '#ffc107' },
                { type: 'custom3', emoji: '🎯', text: '目标价', color: '#6f42c1' },
                { type: 'custom4', emoji: '💎', text: '长期持有', color: '#20c997' },
                { type: 'custom5', emoji: '🚫', text: '暂停交易', color: '#6c757d' },
                { type: 'custom6', emoji: '📊', text: '分析中', color: '#fd7e14' }
            ];

            // 菜单内容
            let menuHTML = '<div class="status-menu-header">选择股票状态</div>';

            statusOptions.forEach(option => {
                menuHTML += `
                    <button class="status-menu-item"
                            style="border-left: 4px solid ${option.color};"
                            onclick="setCustomStatus('${stockCode}', '${option.type}', '${option.text}', '${option.emoji}')">
                        <span class="status-emoji">${option.emoji}</span>
                        <span class="status-text">${option.text}</span>
                        <small class="status-hint">停止提醒</small>
                    </button>
                `;
            });

            menuHTML += `
                <button class="status-menu-item cancel" onclick="closeStatusMenu()">
                    <span class="status-emoji">❌</span>
                    <span class="status-text">取消操作</span>
                </button>
            `;

            menu.innerHTML = menuHTML;

            // 添加到页面
            document.body.appendChild(menu);

            // 定位菜单（跟随鼠标）
            const rect = event.target.getBoundingClientRect();
            menu.style.left = rect.left + 'px';
            menu.style.top = (rect.bottom + 5) + 'px';

            // 确保菜单不超出屏幕
            const menuRect = menu.getBoundingClientRect();
            if (menuRect.right > window.innerWidth) {
                menu.style.left = (window.innerWidth - menuRect.width - 10) + 'px';
            }
            if (menuRect.bottom > window.innerHeight) {
                menu.style.top = (rect.top - menuRect.height - 5) + 'px';
            }

            // 点击其他地方关闭菜单
            setTimeout(() => {
                document.addEventListener('click', function closeMenuHandler(e) {
                    if (!menu.contains(e.target)) {
                        menu.remove();
                        document.removeEventListener('click', closeMenuHandler);
                    }
                });
            }, 100);
        }

        // 关闭状态菜单
        function closeStatusMenu() {
            const menu = document.querySelector('.status-menu');
            if (menu) {
                menu.remove();
            }
        }

        // 设置自定义状态
        async function setCustomStatus(stockCode, statusType, statusText, statusEmoji) {
            // 关闭菜单
            closeStatusMenu();

            if (!confirm(`确定要将这只股票标记为"${statusEmoji}${statusText}"吗？\\n\\n📱 标记后将停止该股票的企业微信提醒。`)) {
                return;
            }

            try {
                const response = await fetch('/api/set-custom-status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        stock_code: stockCode,
                        status_type: statusType,
                        status_text: statusText,
                        status_emoji: statusEmoji
                    })
                });

                const result = await response.json();

                if (result.success) {
                    const data = result.data;
                    alert(`${statusEmoji} ${result.message}\\n\\n📊 状态详情:\\n• 股票: ${data.stock_name} (${data.stock_code})\\n• 状态: ${data.status_emoji}${data.status_text}\\n• 设置时间: ${data.status_time}\\n\\n📱 该股票的企业微信提醒已停止。`);

                    // 重新加载数据
                    await loadStockData();
                } else {
                    alert(`❌ 操作失败: ${result.message}`);
                }
            } catch (error) {
                alert(`❌ 操作失败: ${error.message}`);
            }
        }

        // 标记操作（已减半或已清仓）
        async function markAction(stockCode, actionType) {
            // 关闭菜单
            closeActionMenu();

            const actionText = actionType === 'reduced' ? '已减半' : '已清仓';
            const actionEmoji = actionType === 'reduced' ? '✅' : '🔥';

            if (!confirm(`确定要标记这只股票为${actionText}吗？\\n\\n${actionEmoji} 标记后将停止所有企业微信提醒。`)) {
                return;
            }

            try {
                const response = await fetch('/api/mark-action', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        stock_code: stockCode,
                        action_type: actionType
                    })
                });

                const result = await response.json();

                if (result.success) {
                    const data = result.data;
                    alert(`${actionEmoji} ${result.message}\\n\\n📊 操作详情:\\n• 股票: ${data.stock_name} (${data.stock_code})\\n• 操作: ${data.action_text}\\n• 操作时间: ${data.action_time}\\n\\n📱 该股票的所有企业微信提醒已停止。`);

                    // 重新加载数据
                    await loadStockData();
                } else {
                    alert(`❌ 操作失败: ${result.message}`);
                }
            } catch (error) {
                alert(`❌ 操作失败: ${error.message}`);
            }
        }

        // 更新单只股票数据
        async function updateSingleStock(stockCode) {
            const button = document.querySelector(`button[onclick="updateSingleStock('${stockCode}')"]`);
            if (!button) return;

            // 禁用按钮并显示更新状态
            button.disabled = true;
            button.classList.add('updating');
            button.textContent = '⏳';

            try {
                const response = await fetch(`/api/update-single-stock/${stockCode}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    // 显示更新成功信息
                    const data = result.data;
                    const changeText = data.price_change >= 0 ?
                        `+${data.price_change.toFixed(2)}` :
                        `${data.price_change.toFixed(2)}`;
                    const changePctText = data.price_change_pct >= 0 ?
                        `+${data.price_change_pct.toFixed(1)}%` :
                        `${data.price_change_pct.toFixed(1)}%`;

                    alert(`✅ ${result.message}\\n\\n📊 价格变化:\\n• 原价格: ${data.old_price.toFixed(2)}元\\n• 新价格: ${data.new_price.toFixed(2)}元\\n• 变化: ${changeText}元 (${changePctText})\\n• 更新时间: ${data.update_time}`);

                    // 重新加载数据以更新表格
                    await loadStockData();
                } else {
                    alert(`❌ 更新失败: ${result.message}`);
                }
            } catch (error) {
                console.error('更新单只股票失败:', error);
                alert(`❌ 更新失败: ${error.message}`);
            } finally {
                // 恢复按钮状态
                button.disabled = false;
                button.classList.remove('updating');
                button.textContent = '🔄';
            }
        }

        // 加载股票数据
        async function loadStockData() {
            try {
                const response = await fetch('/api/stocks');
                const result = await response.json();

                if (result.success) {
                    stockData = result.data;
                    updateHeader(result);
                    updateStatistics(result.stats);
                    filterAndDisplayData();
                } else {
                    console.error('加载数据失败:', result.message);
                }
            } catch (error) {
                console.error('请求失败:', error);
            }
        }

        // 加载交易状态
        async function loadTradingStatus() {
            try {
                const response = await fetch('/api/trading-status');
                const result = await response.json();

                if (result.success) {
                    document.getElementById('tradingStatus').textContent = result.data.message;
                }
            } catch (error) {
                console.error('获取交易状态失败:', error);
            }
        }

        // 更新页面头部信息
        function updateHeader(result) {
            document.getElementById('updateTime').textContent =
                result.last_update ? `最后更新: ${result.last_update}` : '暂未更新';
            document.getElementById('stockCount').textContent = result.count;
        }

        // 更新统计信息
        function updateStatistics(stats) {
            if (!stats) return;

            // 更新总市值和股票数量
            const totalValue = formatCurrency(stats.total_market_value);
            document.getElementById('totalValue').textContent = totalValue;
            document.getElementById('totalMarketValue').textContent = totalValue;
            document.getElementById('stockCountStat').textContent = stockData.length;
            document.getElementById('stockCountDisplay').textContent = stats.stock_count + '只';


        }

        // 筛选和显示数据
        function filterAndDisplayData() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();

            // 应用筛选条件
            filteredData = stockData.filter(stock => {
                // 搜索筛选
                if (searchTerm &&
                    !stock.code.toLowerCase().includes(searchTerm) &&
                    !stock.name.toLowerCase().includes(searchTerm)) {
                    return false;
                }

                // 卖出信号筛选
                if (currentSellFilter !== 'all') {
                    if (stock.sell_signal !== currentSellFilter) {
                        return false;
                    }
                }

                return true;
            });

            // 应用排序
            if (sortColumn) {
                filteredData.sort((a, b) => {
                    let aVal = a[sortColumn];
                    let bVal = b[sortColumn];

                    // 特殊处理回本涨幅排序
                    if (sortColumn === 'profit_margin') {
                        // 处理null值
                        if (aVal === null || aVal === undefined) aVal = -999999;
                        if (bVal === null || bVal === undefined) bVal = -999999;

                        // 排序逻辑：亏损多的在前，盈利多的在后
                        let aWeight, bWeight;

                        if (a.profit_status === 'need_gain') {
                            // 亏损状态：数值越大（亏损越多）排越前面
                            aWeight = 1000 + aVal;  // 亏损50% -> 1050, 亏损10% -> 1010
                        } else {
                            // 盈利状态：数值越小（盈利越少）排越前面
                            aWeight = -aVal;  // 盈利10% -> -10, 盈利50% -> -50
                        }

                        if (b.profit_status === 'need_gain') {
                            bWeight = 1000 + bVal;
                        } else {
                            bWeight = -bVal;
                        }

                        return sortDirection === 'asc' ? aWeight - bWeight : bWeight - aWeight;
                    }

                    // 处理其他数值类型
                    if (typeof aVal === 'number' && typeof bVal === 'number') {
                        return sortDirection === 'asc' ? aVal - bVal : bVal - aVal;
                    }

                    // 处理字符串类型
                    aVal = String(aVal || '').toLowerCase();
                    bVal = String(bVal || '').toLowerCase();

                    if (sortDirection === 'asc') {
                        return aVal.localeCompare(bVal);
                    } else {
                        return bVal.localeCompare(aVal);
                    }
                });
            }

            displayStockTable();
        }

        // 卖出信号筛选
        function filterBySellSignal(signal) {
            currentSellFilter = signal;

            // 更新按钮样式
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.style.opacity = '0.6';
                btn.style.transform = 'none';
            });

            const activeBtn = document.getElementById(`filter${signal.charAt(0).toUpperCase() + signal.slice(1)}`);
            if (activeBtn) {
                activeBtn.style.opacity = '1';
                activeBtn.style.transform = 'translateY(-2px)';
            }

            // 重新筛选和显示数据
            filterAndDisplayData();

            // 显示筛选结果统计
            const sellCount = stockData.filter(s => s.sell_signal === 'sell').length;
            const warningCount = stockData.filter(s => s.sell_signal === 'warning').length;
            const holdCount = stockData.filter(s => s.sell_signal === 'hold').length;

            console.log(`卖出信号统计: 卖出${sellCount}只, 预警${warningCount}只, 持有${holdCount}只`);
        }

        // 显示股票表格
        function displayStockTable() {
            const tbody = document.getElementById('stockTableBody');

            if (filteredData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="18" style="text-align: center; padding: 40px;">
                            <div style="color: #666;">
                                <div style="font-size: 48px; margin-bottom: 20px;">📊</div>
                                <div style="font-size: 18px; margin-bottom: 10px;">暂无匹配的股票数据</div>
                                <div style="font-size: 14px;">请调整筛选条件或导入持仓表格</div>
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = filteredData.map(stock => `
                <tr>
                    <td class="sticky-left-1">${stock.code}</td>
                    <td class="sticky-left-2">${stock.name}</td>
                    <td>${formatNumber(stock.price, 2)}</td>
                    <td class="${stock.change_pct >= 0 ? 'positive' : 'negative'}">
                        ${formatPercent(stock.change_pct)}
                    </td>
                    <td class="mobile-hidden">${formatNumber(stock.yearly_low, 2)}</td>
                    <td class="mobile-hidden">${stock.low_date || '-'}</td>
                    <td class="mobile-hidden ${stock.distance_from_low_pct >= 0 ? 'positive' : 'negative'}">
                        ${stock.distance_from_low_pct ? formatPercent(stock.distance_from_low_pct) : '-'}
                    </td>
                    <td class="mobile-hidden">${formatNumber(stock.pb_ratio, 2)}</td>
                    <td class="mobile-hidden">${formatNumber(stock.pe_ratio, 2)}</td>
                    <td class="mobile-hidden">${formatPercent(stock.dividend_yield)}</td>
                    <td class="extra-mobile-hidden">${stock.industry || '-'}</td>
                    <td>
                        ${stock.custom_status ?
                            `<span class="sell-signal custom-status"
                                  style="background-color: #6c757d; cursor: pointer;"
                                  title="自定义状态：${stock.custom_status_text} (${stock.custom_status_time})"
                                  onclick="showStatusMenu('${stock.code}', '${stock.name}')">
                                ${stock.custom_status_emoji}${stock.custom_status_text}
                            </span>` :
                            (stock.sell_signal === 'sell' || stock.sell_signal === 'clearance' || stock.sell_signal === 'warning') ?
                                `<span class="sell-signal clickable ${stock.sell_signal || 'hold'}"
                                      style="background-color: ${stock.sell_color || '#2ed573'}; cursor: pointer;"
                                      title="点击选择状态：${stock.sell_reason || '持有'}"
                                      onclick="showStatusMenu('${stock.code}', '${stock.name}')">
                                    ${stock.sell_signal === 'clearance' ? '🔥清仓' : stock.sell_signal === 'sell' ? '🚨卖出' : '⚠️预警'}
                                </span>` :
                                `<span class="sell-signal ${stock.sell_signal || 'hold'}"
                                      style="background-color: ${stock.sell_color || '#2ed573'}; cursor: pointer;"
                                      title="点击选择状态：${stock.sell_reason || '持有'}"
                                      onclick="showStatusMenu('${stock.code}', '${stock.name}')">
                                    ✅持有
                                </span>`
                        }
                    </td>
                    <td>
                        <span class="scan-score" style="background-color: ${stock.scan_risk_color || '#cccccc'}">
                            ${stock.scan_display_text || '-'}
                        </span>
                    </td>
                    <td>${formatNumber(stock.holdings || stock.quantity || 0, 0)}</td>
                    <td class="mobile-hidden">
                        ${stock.profit_margin !== null && stock.profit_margin !== undefined ?
                            stock.profit_status === 'need_gain' ?
                                `<span class="negative">还需+${formatPercent(stock.profit_margin)}</span>` :
                            stock.profit_status === 'profit' ?
                                `<span class="positive">已盈利+${formatPercent(stock.profit_margin)}</span>` :
                                `<span>${formatPercent(stock.profit_margin)}</span>`
                            : '-'
                        }
                        ${stock.unit_cost !== 0 ? `<br><small>(成本:${formatNumber(stock.unit_cost, 3)})</small>` : ''}
                    </td>
                    <td>${formatCurrency(stock.market_value)}</td>
                    <td class="mobile-hidden">${stock.update_time || '-'}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="update-btn" onclick="updateSingleStock('${stock.code}')" title="更新${stock.name}数据">
                                🔄
                            </button>
                            <button class="edit-btn" onclick="editHoldings('${stock.code}', ${stock.holdings || 0})" title="修改持仓数量">
                                ✏️
                            </button>
                            ${stock.custom_status ?
                                `<span class="custom-status-mark"
                                      title="${stock.custom_status_emoji}${stock.custom_status_text} (${stock.custom_status_time})"
                                      style="background: #6c757d; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                                    ${stock.custom_status_emoji}${stock.custom_status_text}
                                </span>` :
                                (stock.sell_signal === 'sell' || stock.sell_signal === 'clearance' || stock.sell_signal === 'warning') && !stock.is_reduced && !stock.is_cleared ?
                                    `<button class="reduce-btn" onclick="markReduced('${stock.code}')" title="标记已减半，停止提醒">
                                        ✅
                                    </button>` : ''
                            }
                            ${!stock.custom_status && stock.is_reduced ?
                                `<span class="reduced-mark" title="已减半 ${stock.reduced_time || ''}">
                                    ✅已减半
                                </span>` : ''
                            }
                            ${!stock.custom_status && stock.is_cleared ?
                                `<span class="reduced-mark" title="已清仓 ${stock.cleared_time || ''}" style="background: #dc3545;">
                                    🔥已清仓
                                </span>` : ''
                            }
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // 更新排序指示器
        function updateSortIndicator() {
            document.querySelectorAll('.sort-indicator').forEach(indicator => {
                indicator.className = 'sort-indicator';
            });

            const currentIndicator = document.querySelector(`th[onclick="sortTable('${sortColumn}')"] .sort-indicator`);
            if (currentIndicator) {
                currentIndicator.className = `sort-indicator ${sortDirection}`;
            }
        }

        // 表格排序
        function sortTable(column) {
            if (sortColumn === column) {
                sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                sortColumn = column;
                sortDirection = 'asc';
            }

            updateSortIndicator();
            filterAndDisplayData();
        }

        // 数据管理功能
        async function clearData() {
            if (!confirm('确定要清空所有数据吗？此操作不可恢复。')) {
                return;
            }

            try {
                const response = await fetch('/api/clear-data', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    alert('数据已清空');
                    stockData = [];
                    filterAndDisplayData();
                    updateStatistics({});
                } else {
                    alert('清空失败: ' + result.message);
                }
            } catch (error) {
                alert('清空失败: ' + error.message);
            }
        }

        async function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];

            if (!file) {
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            try {
                const response = await fetch('/api/upload-holdings', {
                    method: 'POST',
                    body: formData
                });
                const result = await response.json();

                if (result.success) {
                    alert(`导入成功: ${result.message}`);
                } else {
                    alert('导入失败: ' + result.message);
                }
            } catch (error) {
                alert('导入失败: ' + error.message);
            }

            fileInput.value = '';
        }

        async function refreshData() {
            if (!confirm('确定要重新获取所有股票数据吗？这可能需要几分钟时间。')) {
                return;
            }

            try {
                const response = await fetch('/api/refresh-data', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    alert(result.message);
                } else {
                    alert('更新失败: ' + result.message);
                }
            } catch (error) {
                alert('更新失败: ' + error.message);
            }
        }

        async function checkCacheStatus() {
            try {
                const [cacheResponse, scanResponse] = await Promise.all([
                    fetch('/api/cache-status'),
                    fetch('/api/scan-cache-info')
                ]);

                const cacheResult = await cacheResponse.json();
                const scanResult = await scanResponse.json();

                let message = '缓存状态信息:\\n\\n';

                if (cacheResult.success) {
                    const cache = cacheResult.data;
                    message += `年内最低价缓存:\\n`;
                    message += `- 状态: ${cache.exists ? '存在' : '不存在'}\\n`;
                    message += `- 有效: ${cache.valid ? '是' : '否'}\\n`;
                    if (cache.success_rate) {
                        message += `- 成功率: ${cache.success_rate.toFixed(1)}%\\n`;
                    }
                } else {
                    message += `年内最低价缓存: 获取失败\\n`;
                }

                message += '\\n';

                if (scanResult.success) {
                    const scan = scanResult.data;
                    message += `扫雷数据缓存:\\n`;
                    message += `- 缓存股票数: ${scan.cached_stocks}\\n`;
                    message += `- 缓存时长: ${scan.cache_duration}秒\\n`;
                } else {
                    message += `扫雷数据缓存: 获取失败\\n`;
                }

                alert(message);
            } catch (error) {
                alert('获取缓存状态失败: ' + error.message);
            }
        }

        async function generateDemoData() {
            if (!confirm('确定要生成演示数据吗？这将清空现有数据并生成30支随机股票的模拟持仓数据，仅供演示使用。')) {
                return;
            }

            try {
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = '🎭 生成中...';
                button.disabled = true;

                const response = await fetch('/api/generate-demo-data', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    alert(`🎉 ${result.message}\\n\\n📊 数据概览:\\n• 股票数量: ${result.data.stock_count}只\\n• 总市值: ${(result.data.total_market_value / 10000).toFixed(1)}万元\\n• 卖出信号: ${result.data.sell_signals}只\\n• 卖出预警: ${result.data.warning_signals}只\\n\\n💡 这是模拟数据，仅供演示使用！`);

                    // 刷新页面数据
                    await loadStockData();
                } else {
                    alert('生成演示数据失败: ' + result.message);
                }
            } catch (error) {
                alert('生成演示数据失败: ' + error.message);
            } finally {
                const button = document.querySelector('.management-btn.info');
                if (button) {
                    button.textContent = '🎭 生成演示数据';
                    button.disabled = false;
                }
            }
        }

        async function testWeChatAlerts() {
            if (!confirm('确定要发送企业微信测试提醒吗？将发送18支随机股票的卖出信号演示。')) {
                return;
            }

            try {
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = '📱 发送中...';
                button.disabled = true;

                const response = await fetch('/api/test-wechat-alerts', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    let message = `🎉 ${result.message}\\n\\n`;
                    message += `📊 详细结果:\\n`;

                    // 按信号类型分组显示
                    const sellSignals = result.data.results.filter(r => r.signal === 'sell');
                    const warningSignals = result.data.results.filter(r => r.signal === 'warning');

                    if (sellSignals.length > 0) {
                        message += `\\n🚨 卖出信号 (${sellSignals.length}只):\\n`;
                        sellSignals.forEach(stock => {
                            message += `  • ${stock.name} (${stock.code}) - ${stock.status}\\n`;
                        });
                    }

                    if (warningSignals.length > 0) {
                        message += `\\n⚠️ 卖出预警 (${warningSignals.length}只):\\n`;
                        warningSignals.forEach(stock => {
                            message += `  • ${stock.name} (${stock.code}) - ${stock.status}\\n`;
                        });
                    }

                    message += `\\n💡 提示: 每只股票每种提醒类型每天最多发送1次，重复的提醒会被自动跳过。`;

                    alert(message);
                } else {
                    alert('测试失败: ' + result.message);
                }
            } catch (error) {
                alert('测试失败: ' + error.message);
            } finally {
                const button = document.querySelector('.management-btn.warning');
                if (button) {
                    button.textContent = '📱 测试企业微信提醒';
                    button.disabled = false;
                }
            }
        }

        // 工具函数
        function formatNumber(value, decimals = 2) {
            if (value === null || value === undefined || value === '') return '-';
            return Number(value).toFixed(decimals);
        }

        function formatPercent(value) {
            if (value === null || value === undefined || value === '') return '-';
            return Number(value).toFixed(2) + '%';
        }

        function formatCurrency(value) {
            if (value === null || value === undefined || value === '' || value === 0) return '¥0';
            return '¥' + Number(value).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
        }
    </script>
</body>
</html>
'''

if __name__ == '__main__':
    import sys
    sys.stdout.flush()

    print("=== 🚀 启动持仓系统 V12 股票专版 ===", flush=True)
    print("📊 功能特性:", flush=True)
    print("   • 只显示A股股票（自动过滤ETF、基金、可转债等）", flush=True)
    print("   • 实时股价、年内最低价、股息率、行业信息", flush=True)
    print("   • 智能交易时间监测", flush=True)
    print("   • 持仓数量和市值计算", flush=True)
    print("   • Excel数据管理（上传、清空、刷新）", flush=True)
    print("   • 行业分布统计分析", flush=True)
    print("   • 扫雷风险评估", flush=True)
    print("   • 移动端响应式设计", flush=True)
    print("   • 年内最低价缓存系统", flush=True)
    print(flush=True)
    print("⚠️  注意：本版本只处理A股股票，会自动跳过其他证券类型", flush=True)

    # 检查缓存状态
    try:
        cache_status = check_cache_status()
        if cache_status['exists'] and cache_status['valid']:
            print("✅ 年内最低价缓存可用")
            if 'success_rate' in cache_status:
                print(f"   📊 缓存统计: {cache_status['success_count']}/{cache_status['total_count']} ({cache_status['success_rate']:.1f}%)")
        else:
            print("⚠️ 年内最低价缓存不可用")
            print("   建议运行: python yearly_low_cache_system.py")
    except Exception as e:
        print(f"⚠️ 缓存检查失败: {e}")

    # 显示交易状态
    try:
        status = trading_monitor.get_trading_status()
        print(f"📈 当前交易状态: {status['message']}")
        print(f"⏰ 更新间隔: {trading_monitor.get_update_interval()}秒")
    except Exception as e:
        print(f"⚠️ 获取交易状态失败: {e}")

    # 启动时加载缓存数据
    print("\n📂 正在加载缓存数据...")
    load_imported_list()  # 加载导入的股票列表
    load_stock_data()     # 加载股票数据缓存

    # 清理过期的企业微信提醒记录
    print("\n🧹 正在清理过期提醒记录...")
    try:
        wechat_alert.clean_old_alerts()
    except Exception as e:
        print(f"⚠️ 清理过期提醒记录失败: {e}")

    print(f"\n🌐 访问地址: http://localhost:5000")
    print("📱 支持移动端访问，响应式设计")
    print("🔄 智能更新：只在交易时间更新数据")
    print("📊 完整功能：数据管理、统计分析、风险评估")
    print("💾 数据持久化：自动保存和恢复上次数据")

    # 启动后台更新线程
    update_thread = threading.Thread(target=background_update)
    update_thread.daemon = True
    update_thread.start()

    # 启动Flask应用
    # 生产环境使用Gunicorn，开发环境使用内置服务器
    import os
    if os.environ.get('FLASK_ENV') == 'production':
        # 生产环境由Gunicorn启动，这里不需要app.run()
        pass
    else:
        # 开发环境
        app.run(host='0.0.0.0', port=5000, debug=False)

