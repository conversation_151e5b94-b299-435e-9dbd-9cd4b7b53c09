<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>持仓系统 V2 - 股息率+行业分析</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .controls {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        
        .btn-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }
        
        .btn-info {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #666;
            font-size: 1.1em;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section-title {
            font-size: 1.8em;
            margin-bottom: 20px;
            color: #333;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }
        
        .table-container {
            overflow-x: auto;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }
        
        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .positive {
            color: #e74c3c;
            font-weight: 600;
        }
        
        .negative {
            color: #27ae60;
            font-weight: 600;
        }
        
        .neutral {
            color: #7f8c8d;
        }
        
        .industry-tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.9em;
            font-weight: 500;
        }
        
        .dividend-badge {
            background: #fff3e0;
            color: #f57c00;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.9em;
            font-weight: 600;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
            font-size: 1.2em;
        }
        
        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 10px;
            font-weight: 500;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .last-update {
            text-align: center;
            color: #666;
            font-style: italic;
            margin-top: 20px;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .btn-group {
                flex-direction: column;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                margin: 10px;
                border-radius: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 持仓系统 V2</h1>
            <p>📊 股息率分析 + 🏭 行业分布 + 💰 价值发现</p>
        </div>
        
        <div class="controls">
            <div class="btn-group">
                <button class="btn btn-primary" onclick="updateData()">🔄 更新数据</button>
                <button class="btn btn-success" onclick="sendSummary()">📱 发送摘要</button>
                <button class="btn btn-info" onclick="testWechat()">🧪 测试微信</button>
            </div>
        </div>
        
        <div class="stats-grid" id="statsGrid">
            <!-- 统计卡片将在这里动态生成 -->
        </div>
        
        <div class="content">
            <div id="alertContainer"></div>
            
            <div class="section">
                <h2 class="section-title">📈 持仓股票列表</h2>
                <div class="table-container">
                    <table id="stockTable">
                        <thead>
                            <tr>
                                <th>代码</th>
                                <th>名称</th>
                                <th>价格</th>
                                <th>涨跌幅</th>
                                <th>行业</th>
                                <th>股息率</th>
                                <th>距最低点</th>
                                <th>市盈率</th>
                                <th>市净率</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="stockTableBody">
                            <tr>
                                <td colspan="10" class="loading">正在加载数据...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <div class="last-update" id="lastUpdate">
                最后更新时间: 未更新
            </div>
        </div>
    </div>

    <script>
        let stockData = [];
        let isUpdating = false;

        // 页面加载时获取数据
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
            // 每30秒自动刷新一次
            setInterval(loadData, 30000);
        });

        // 加载数据
        async function loadData() {
            try {
                const response = await fetch('/api/stocks');
                const data = await response.json();
                
                stockData = data.stocks;
                updateStatsGrid(data.stats, data.industry_stats, data.dividend_stats);
                updateStockTable(stockData);
                updateLastUpdateTime(data.stats.last_update);
                isUpdating = data.is_updating;
                
            } catch (error) {
                console.error('加载数据失败:', error);
                showAlert('加载数据失败: ' + error.message, 'error');
            }
        }

        // 更新统计卡片
        function updateStatsGrid(stats, industryStats, dividendStats) {
            const grid = document.getElementById('statsGrid');
            grid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number" style="color: #667eea;">${stats.total}</div>
                    <div class="stat-label">总股票数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #e74c3c;">${stats.up}</div>
                    <div class="stat-label">上涨</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #27ae60;">${stats.down}</div>
                    <div class="stat-label">下跌</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #f39c12;">${stats.high_gain}</div>
                    <div class="stat-label">高涨幅(>50%)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #9b59b6;">${dividendStats.count}</div>
                    <div class="stat-label">有股息股票</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #1abc9c;">${dividendStats.avg.toFixed(2)}%</div>
                    <div class="stat-label">平均股息率</div>
                </div>
            `;
        }

        // 更新股票表格
        function updateStockTable(stocks) {
            const tbody = document.getElementById('stockTableBody');
            
            if (stocks.length === 0) {
                tbody.innerHTML = '<tr><td colspan="10" class="loading">暂无数据</td></tr>';
                return;
            }

            tbody.innerHTML = stocks.map(stock => `
                <tr>
                    <td>${stock.code}</td>
                    <td>${stock.name}</td>
                    <td>¥${stock.price}</td>
                    <td class="${stock.change_pct > 0 ? 'positive' : stock.change_pct < 0 ? 'negative' : 'neutral'}">
                        ${stock.change_pct > 0 ? '+' : ''}${stock.change_pct.toFixed(2)}%
                    </td>
                    <td><span class="industry-tag">${stock.industry || '未知'}</span></td>
                    <td>
                        ${stock.dividend_yield > 0 ? 
                            `<span class="dividend-badge">${stock.dividend_yield.toFixed(2)}%</span>` : 
                            '<span style="color: #999;">无</span>'
                        }
                    </td>
                    <td class="${stock.distance_from_low_pct > 50 ? 'positive' : stock.distance_from_low_pct > 20 ? 'neutral' : 'negative'}">
                        +${stock.distance_from_low_pct.toFixed(1)}%
                    </td>
                    <td>${stock.pe_ratio > 0 ? stock.pe_ratio.toFixed(2) : '-'}</td>
                    <td>${stock.pb_ratio > 0 ? stock.pb_ratio.toFixed(2) : '-'}</td>
                    <td>
                        <button class="btn btn-info" style="padding: 5px 10px; font-size: 12px;" 
                                onclick="sendAlert('${stock.code}')">📱 提醒</button>
                    </td>
                </tr>
            `).join('');
        }

        // 更新数据
        async function updateData() {
            if (isUpdating) {
                showAlert('正在更新中，请稍候...', 'error');
                return;
            }

            try {
                showAlert('开始更新数据，将实时显示进度...', 'success');
                const response = await fetch('/api/update', { method: 'POST' });
                const result = await response.json();

                if (result.status === 'started') {
                    showAlert('数据更新已开始，实时显示中...', 'success');
                    // 每1秒检查一次更新状态，实时显示数据
                    const checkInterval = setInterval(() => {
                        loadData();
                        if (!isUpdating) {
                            clearInterval(checkInterval);
                            showAlert('数据更新完成！', 'success');
                        }
                    }, 1000);  // 改为1秒检查一次
                }
            } catch (error) {
                showAlert('更新失败: ' + error.message, 'error');
            }
        }

        // 发送摘要
        async function sendSummary() {
            try {
                const response = await fetch('/api/send_summary', { method: 'POST' });
                const result = await response.json();
                showAlert(result.message, result.status === 'success' ? 'success' : 'error');
            } catch (error) {
                showAlert('发送失败: ' + error.message, 'error');
            }
        }

        // 发送股票提醒
        async function sendAlert(code) {
            try {
                const response = await fetch('/api/send_alert', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ code: code })
                });
                const result = await response.json();
                showAlert(result.message, result.status === 'success' ? 'success' : 'error');
            } catch (error) {
                showAlert('发送失败: ' + error.message, 'error');
            }
        }

        // 测试微信
        async function testWechat() {
            try {
                const response = await fetch('/api/test_wechat', { method: 'POST' });
                const result = await response.json();
                showAlert(result.message, result.status === 'success' ? 'success' : 'error');
            } catch (error) {
                showAlert('测试失败: ' + error.message, 'error');
            }
        }

        // 显示提示信息
        function showAlert(message, type) {
            const container = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            
            container.appendChild(alert);
            
            // 3秒后自动移除
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 3000);
        }

        // 更新最后更新时间
        function updateLastUpdateTime(time) {
            document.getElementById('lastUpdate').textContent = `最后更新时间: ${time}`;
        }
    </script>
</body>
</html>
