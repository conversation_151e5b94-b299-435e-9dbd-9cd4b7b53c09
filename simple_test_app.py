#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试应用 - 验证基本功能
"""

from flask import Flask, jsonify
import json
import os

app = Flask(__name__)

@app.route('/')
def index():
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>测试页面</title>
        <meta charset="UTF-8">
    </head>
    <body>
        <h1>🧪 Flask测试页面</h1>
        <p>如果你看到这个页面，说明Flask正常工作</p>
        <button onclick="testAPI()">测试API</button>
        <div id="result"></div>
        
        <script>
        async function testAPI() {
            try {
                const response = await fetch('/api/test');
                const data = await response.json();
                document.getElementById('result').innerHTML = 
                    '<h3>API测试结果:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    '<h3>API测试失败:</h3><p>' + error.message + '</p>';
            }
        }
        </script>
    </body>
    </html>
    '''

@app.route('/api/test')
def api_test():
    """测试API"""
    try:
        # 检查数据文件
        files_status = {}
        
        test_files = [
            'imported_stock_list.json',
            'stock_data_cache.json',
            'app_config.json'
        ]
        
        for file_path in test_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    if file_path == 'imported_stock_list.json':
                        stock_count = len(data.get('imported_stock_list', []))
                        files_status[file_path] = f"存在，包含 {stock_count} 只股票"
                    elif file_path == 'stock_data_cache.json':
                        cache_count = len(data.get('stock_data', {}))
                        files_status[file_path] = f"存在，缓存 {cache_count} 只股票"
                    else:
                        files_status[file_path] = "存在且可读取"
                        
                except Exception as e:
                    files_status[file_path] = f"存在但读取失败: {e}"
            else:
                files_status[file_path] = "不存在"
        
        return jsonify({
            'success': True,
            'message': 'API测试成功',
            'files_status': files_status,
            'flask_working': True
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'API测试失败: {e}',
            'flask_working': True
        }), 500

if __name__ == '__main__':
    print("🚀 启动简单测试应用...")
    print("🌐 访问地址: http://localhost:5000")
    app.run(host='0.0.0.0', port=5000, debug=True)
