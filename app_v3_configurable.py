from flask import Flask, render_template, jsonify
import requests
import time
import json
import pandas as pd
from datetime import datetime
import threading

app = Flask(__name__)

# 全局变量存储股票数据
stock_data = {}
last_update_time = None

# ===== 可配置的间隔时间设置 =====
# 您可以在这里修改间隔时间
CONFIG = {
    'stock_interval': 2.0,      # 每只股票间隔时间（秒）- 您可以修改这个值
    'round_interval': 600,      # 每轮更新间隔时间（秒）- 您可以修改这个值 (600秒=10分钟)
    'request_timeout': 10,      # 请求超时时间（秒）
}

def get_eastmoney_stock_data(stock_code):
    """从东方财富获取单只股票实时数据"""
    
    # 根据股票代码确定市场
    if stock_code.startswith('6'):
        secid = f'1.{stock_code}'  # 上海
    else:
        secid = f'0.{stock_code}'  # 深圳
    
    url = 'http://push2.eastmoney.com/api/qt/stock/get'
    params = {
        'fltt': '2',
        'invt': '2',
        'fields': 'f43,f44,f45,f46,f47,f48,f57,f58,f60,f169,f170',
        'secid': secid,
        '_': str(int(time.time() * 1000))
    }
    
    try:
        response = requests.get(url, params=params, timeout=CONFIG['request_timeout'])
        if response.status_code == 200:
            data = json.loads(response.text)['data']
            if data:
                return {
                    'code': data['f57'],
                    'name': data['f58'],
                    'price': data['f43'],
                    'open': data['f46'],
                    'high': data['f44'],
                    'low': data['f45'],
                    'yesterday_close': data['f60'],
                    'volume': data['f47'],
                    'amount': data['f48'],
                    'change': data['f169'],
                    'change_pct': data['f170'],
                    'update_time': datetime.now().strftime('%H:%M:%S')
                }
    except Exception as e:
        print(f"获取股票 {stock_code} 数据失败: {e}")
        return None

def load_stock_list():
    """加载股票列表"""
    try:
        df = pd.read_csv('stocks_list.csv', encoding='utf-8-sig')
        return df.to_dict('records')
    except Exception as e:
        print(f"加载股票列表失败: {e}")
        return []

def update_all_stocks():
    """更新所有股票数据"""
    global stock_data, last_update_time
    
    stock_list = load_stock_list()
    print(f"\n🚀 开始更新 {len(stock_list)} 只股票数据...")
    print(f"⚙️  当前配置:")
    print(f"   📊 每只股票间隔: {CONFIG['stock_interval']} 秒")
    print(f"   🔄 每轮更新间隔: {CONFIG['round_interval']} 秒 ({CONFIG['round_interval']/60:.1f} 分钟)")
    print(f"   ⏱️  请求超时: {CONFIG['request_timeout']} 秒")
    
    # 清空旧数据
    stock_data.clear()
    
    success_count = 0
    start_time = time.time()
    
    for i, stock in enumerate(stock_list):
        code = str(stock['代码']).zfill(6)
        
        print(f"📊 [{i+1:3d}/{len(stock_list)}] 正在获取 {code}...", end=' ')
        
        # 获取实时数据
        real_data = get_eastmoney_stock_data(code)
        
        if real_data:
            stock_data[code] = real_data
            success_count += 1
            print(f"✅ {real_data['name']} {real_data['price']} ({real_data['change_pct']:+.2f}%)")
        else:
            # 如果获取失败，使用基本信息
            stock_data[code] = {
                'code': code,
                'name': stock['名称'],
                'price': 0, 'change': 0, 'change_pct': 0,
                'volume': 0, 'amount': 0, 'high': 0, 'low': 0,
                'open': 0, 'yesterday_close': 0,
                'update_time': datetime.now().strftime('%H:%M:%S'),
                'error': '暂无数据'
            }
            print(f"❌ 获取失败")
        
        # 每只股票间隔时间
        if i < len(stock_list) - 1:  # 最后一只股票不需要等待
            if CONFIG['stock_interval'] >= 1:
                print(f"   ⏱️  等待 {CONFIG['stock_interval']} 秒...")
            time.sleep(CONFIG['stock_interval'])
    
    end_time = time.time()
    total_time = end_time - start_time
    
    last_update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    success_rate = success_count / len(stock_list) * 100
    
    print(f"\n✅ 本轮更新完成！")
    print(f"   📈 成功率: {success_rate:.1f}% ({success_count}/{len(stock_list)})")
    print(f"   ⏱️  总耗时: {total_time:.1f} 秒")
    print(f"   🕐 更新时间: {last_update_time}")

def background_update():
    """后台定时更新股票数据"""
    round_count = 0
    
    while True:
        round_count += 1
        print(f"\n{'='*60}")
        print(f"🔄 第 {round_count} 轮更新开始 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*60}")
        
        update_all_stocks()
        
        print(f"\n⏰ 下一轮更新将在 {CONFIG['round_interval']} 秒后开始")
        print(f"   (约 {CONFIG['round_interval']/60:.1f} 分钟后)")
        print(f"   预计下次更新时间: {datetime.fromtimestamp(time.time() + CONFIG['round_interval']).strftime('%H:%M:%S')}")
        
        # 显示倒计时（每30秒显示一次）
        remaining = CONFIG['round_interval']
        while remaining > 0:
            if remaining % 30 == 0 or remaining <= 10:
                print(f"⏳ 距离下次更新还有 {remaining} 秒...")
            time.sleep(1)
            remaining -= 1

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/stocks')
def get_stocks():
    """获取所有股票数据API"""
    return jsonify({
        'stocks': list(stock_data.values()),
        'last_update': last_update_time,
        'total_count': len(stock_data),
        'config': CONFIG
    })

@app.route('/api/stock/<stock_code>')
def get_stock(stock_code):
    """获取单只股票数据API"""
    if stock_code in stock_data:
        return jsonify(stock_data[stock_code])
    else:
        return jsonify({'error': '股票代码不存在'}), 404

@app.route('/api/config')
def get_config():
    """获取当前配置"""
    return jsonify({
        'config': CONFIG,
        'description': {
            'stock_interval': '每只股票间隔时间（秒）',
            'round_interval': '每轮更新间隔时间（秒）',
            'request_timeout': '请求超时时间（秒）'
        }
    })

@app.route('/api/refresh')
def refresh_data():
    """手动刷新数据"""
    try:
        threading.Thread(target=update_all_stocks, daemon=True).start()
        return jsonify({
            'success': True,
            'message': '数据刷新已启动',
            'last_update': last_update_time,
            'total_count': len(stock_data)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'刷新失败: {str(e)}'
        }), 500

def print_startup_info():
    """打印启动信息"""
    print("⚙️  当前间隔时间配置:")
    print(f"   📊 每只股票间隔: {CONFIG['stock_interval']} 秒")
    print(f"   🔄 每轮更新间隔: {CONFIG['round_interval']} 秒 ({CONFIG['round_interval']/60:.1f} 分钟)")
    print(f"   ⏱️  请求超时时间: {CONFIG['request_timeout']} 秒")
    print()
    print("💡 如需修改间隔时间:")
    print("   1. 停止程序 (Ctrl+C)")
    print("   2. 编辑 app_v3_configurable.py 文件中的 CONFIG 字典")
    print("   3. 重新启动程序")
    print()
    
    # 计算预估时间
    stock_count = len(load_stock_list())
    if stock_count > 0:
        estimated_time = stock_count * CONFIG['stock_interval']
        print(f"📊 预估单轮更新时间: {estimated_time:.1f} 秒 ({estimated_time/60:.1f} 分钟)")
        print(f"   (基于 {stock_count} 只股票 × {CONFIG['stock_interval']} 秒间隔)")

if __name__ == '__main__':
    try:
        print("=== 🚀 启动东方财富股票实时行情系统 V3 (可配置版) ===")
        print_startup_info()

        # 初始加载数据
        print("\n📊 正在初始化数据...")
        update_all_stocks()

        # 启动后台更新线程
        print("\n🔄 启动后台更新线程...")
        update_thread = threading.Thread(target=background_update, daemon=True)
        update_thread.start()

        print("\n🌐 启动Flask Web服务...")
        print("🔗 访问 http://localhost:5000 查看股票行情")
        print("📈 数据来源：东方财富网")

        # 启动Flask应用
        app.run(debug=False, host='0.0.0.0', port=5000)

    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
