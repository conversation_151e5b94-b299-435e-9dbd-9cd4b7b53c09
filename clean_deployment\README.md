# 持仓系统 V2.0 模块化版本

## 📋 项目简介

这是基于原V14版本重构的模块化持仓系统，将原来9924行的单文件拆分为多个功能模块，便于维护和部署。

### 🎯 主要功能

- **股票数据获取**: 实时获取股价、PE/PB、股息率等数据
- **多策略卖出信号**: 支持多种自定义策略的卖出信号计算
- **交易时间监测**: 智能识别A股交易时间，自动调整更新频率
- **年内最低价缓存**: 高效的年内最低价数据缓存系统
- **扫雷功能集成**: 集成通达信扫雷风险评估
- **企业微信提醒**: 支持企业微信消息推送
- **数据持久化**: 自动保存和恢复数据
- **Web界面**: 响应式Web界面，支持移动端

### 🏗️ 系统架构

```
clean_deployment/
├── app.py                 # 主启动文件
├── requirements.txt       # Python依赖
├── Dockerfile            # Docker配置
├── gunicorn_config.py    # Gunicorn配置
├── start.sh              # 启动脚本
├── stop.sh               # 停止脚本
├── README.md             # 说明文档
├── modules/              # 功能模块
│   ├── data_fetcher.py       # 数据获取模块
│   ├── strategy_manager.py   # 策略管理模块
│   ├── sell_signal.py        # 卖出信号计算模块
│   ├── cache_manager.py      # 缓存管理模块
│   ├── data_persistence.py   # 数据持久化模块
│   ├── api_routes.py         # API路由模块
│   ├── trading_time_monitor.py # 交易时间监测模块
│   ├── yearly_low_cache.py   # 年内最低价缓存模块
│   └── tdx_scan_module.py    # 扫雷模块
├── templates/            # HTML模板
│   └── index.html
├── config/              # 配置文件
│   └── strategy_config.json
├── data/                # 数据文件
├── cache/               # 缓存目录
├── uploads/             # 上传文件目录
└── logs/                # 日志目录
```

## 🚀 快速开始

### 方式一：直接运行

1. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **启动应用**
   ```bash
   # 开发模式
   python app.py
   
   # 或使用启动脚本
   chmod +x start.sh
   ./start.sh dev
   ```

3. **访问系统**
   ```
   http://localhost:5000
   ```

### 方式二：生产部署

1. **使用Gunicorn**
   ```bash
   chmod +x start.sh stop.sh
   ./start.sh
   ```

2. **使用Docker**
   ```bash
   # 构建镜像
   docker build -t stock-portfolio-system .
   
   # 运行容器
   docker run -d -p 5000:5000 \
     -v $(pwd)/data:/app/data \
     -v $(pwd)/cache:/app/cache \
     -v $(pwd)/uploads:/app/uploads \
     stock-portfolio-system
   ```

### 方式三：Docker Compose

创建 `docker-compose.yml`:

```yaml
version: '3.8'
services:
  stock-system:
    build: .
    ports:
      - "5000:5000"
    volumes:
      - ./data:/app/data
      - ./cache:/app/cache
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    restart: unless-stopped
    environment:
      - FLASK_ENV=production
```

运行：
```bash
docker-compose up -d
```

## 📖 使用说明

### 1. 导入持仓数据

1. 准备Excel文件，包含以下列：
   - 股票代码（必需）
   - 股票名称（可选）
   - 持仓数量（可选）

2. 在Web界面点击"上传持仓表格"

3. 系统会自动识别列名并导入数据

### 2. 策略配置

系统支持多种卖出策略：

- **基础涨幅策略**: 根据涨幅百分比触发
- **涨停+PB策略**: 涨停配合PB比率
- **高TTM策略**: 高市盈率配合涨幅
- **负TTM策略**: 负市盈率风险控制

可在Web界面的"策略配置"中调整参数。

### 3. 自动更新

- 系统会在交易时间自动更新股票数据
- 非交易时间暂停更新以节省资源
- 可在设置中开启/关闭自动更新

### 4. 数据持久化

- 股票数据自动保存到 `data/stock_data_cache.json`
- 导入列表保存到 `data/imported_stock_list.json`
- 策略配置保存到 `config/strategy_config.json`

## 🔧 配置说明

### 环境变量

- `FLASK_ENV`: Flask运行环境 (development/production)
- `PYTHONPATH`: Python模块路径

### 配置文件

- `config/strategy_config.json`: 策略配置
- `gunicorn_config.py`: Gunicorn服务器配置
- `requirements.txt`: Python依赖包

## 📊 API接口

### 主要接口

- `GET /api/stocks` - 获取股票数据
- `POST /api/upload-holdings` - 上传持仓表格
- `POST /api/refresh-data` - 刷新股票数据
- `GET /api/strategies` - 获取策略配置
- `POST /api/strategies` - 更新策略配置
- `GET /api/trading-status` - 获取交易状态

### 响应格式

```json
{
  "success": true,
  "message": "操作成功",
  "data": { ... }
}
```

## 🛠️ 开发说明

### 模块说明

1. **data_fetcher.py**: 负责从各种数据源获取股票信息
2. **strategy_manager.py**: 管理卖出策略的配置和执行
3. **sell_signal.py**: 计算卖出信号和优先级
4. **cache_manager.py**: 统一管理各种缓存
5. **data_persistence.py**: 处理数据的保存和加载
6. **api_routes.py**: 定义所有API路由
7. **trading_time_monitor.py**: 监测A股交易时间
8. **yearly_low_cache.py**: 年内最低价缓存系统
9. **tdx_scan_module.py**: 通达信扫雷功能

### 添加新功能

1. 在相应模块中添加功能函数
2. 在 `api_routes.py` 中添加API接口
3. 在 `app.py` 中注册新组件
4. 更新配置文件和文档

## 🔍 故障排除

### 常见问题

1. **端口占用**
   ```bash
   # 查找占用端口的进程
   lsof -i :5000
   # 或
   netstat -tulpn | grep :5000
   ```

2. **权限问题**
   ```bash
   chmod +x start.sh stop.sh
   ```

3. **依赖安装失败**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

4. **数据文件损坏**
   ```bash
   # 删除缓存文件重新开始
   rm -rf data/* cache/*
   ```

### 日志查看

- 应用日志: `logs/error.log`
- 访问日志: `logs/access.log`
- 控制台输出: 直接查看终端

## 📝 更新日志

### V2.0 (2025-07-25)
- 🔄 完全模块化重构
- 📦 拆分9924行单文件为多个模块
- 🏗️ 改进系统架构和可维护性
- 🐳 优化Docker部署配置
- 📚 完善文档和部署说明

### V1.4 (原版本)
- ✅ 基础功能完整实现
- 🎯 多策略卖出信号
- 📊 数据获取和处理
- 🌐 Web界面和API

## 📞 技术支持

如有问题，请检查：
1. 日志文件中的错误信息
2. 网络连接是否正常
3. 依赖包是否正确安装
4. 配置文件是否正确

## 📄 许可证

本项目仅供学习和研究使用。
