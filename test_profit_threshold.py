#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的回本阈值逻辑
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入策略管理器
from 持仓系统_v14 import calculate_sell_signal

def test_profit_threshold():
    """测试回本阈值逻辑"""
    print("🧪 测试新的回本阈值逻辑...")
    
    # 测试场景：成本价10元的股票
    base_data = {
        'name': '测试股票',
        'code': '000001',
        'yearly_low': 8.00,
        'unit_cost': 10.00,  # 成本价10元
        'distance_from_low_pct': 20.0,
        'pe_ratio': -15.0,   # 负TTM
        'pb_ratio': 0.8,
        'dividend_yield': 2.5,  # ≤3%
        'scan_score': 75,
        'profit_status': 'need_gain',
        'profit_margin': -10.0
    }
    
    test_cases = [
        {
            'name': '距离回本5%（应该预警）',
            'current_price': 9.52,  # 距离10元还需5%涨幅
            'expected_signal': 'warning',
            'expected_distance': 5.0
        },
        {
            'name': '距离回本10%（刚好预警）',
            'current_price': 9.09,  # 距离10元还需10%涨幅
            'expected_signal': 'warning',
            'expected_distance': 10.0
        },
        {
            'name': '距离回本15%（不预警）',
            'current_price': 8.70,  # 距离10元还需15%涨幅
            'expected_signal': 'hold',
            'expected_distance': 15.0
        },
        {
            'name': '刚好回本（清仓）',
            'current_price': 10.00,  # 刚好回本
            'expected_signal': 'clearance',
            'expected_distance': 0.0
        },
        {
            'name': '已盈利5%（清仓）',
            'current_price': 10.50,  # 已盈利5%
            'expected_signal': 'clearance',
            'expected_distance': -5.0
        }
    ]
    
    print(f"\n🧪 开始测试 {len(test_cases)} 个测试用例...")
    print(f"📊 基础条件: 成本价={base_data['unit_cost']}元, TTM={base_data['pe_ratio']}, 股息={base_data['dividend_yield']}%")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试用例 {i}: {test_case['name']} ---")
        
        # 准备测试数据
        test_data = base_data.copy()
        test_data['price'] = test_case['current_price']
        
        # 计算实际距离回本百分比
        actual_distance = ((test_data['unit_cost'] - test_data['price']) / test_data['price']) * 100
        
        print(f"📊 测试数据:")
        print(f"   当前价格: {test_data['price']}元")
        print(f"   成本价格: {test_data['unit_cost']}元")
        print(f"   距离回本: {actual_distance:.1f}%")
        print(f"   预期距离: {test_case['expected_distance']:.1f}%")
        
        # 计算卖出信号
        result = calculate_sell_signal(test_data)
        
        print(f"🎯 策略结果:")
        print(f"   信号: {result['signal']}")
        print(f"   原因: {result['reason']}")
        
        # 验证结果
        expected_signal = test_case['expected_signal']
        signal_correct = result['signal'] == expected_signal
        
        if signal_correct:
            print(f"   ✅ 测试通过")
        else:
            print(f"   ❌ 测试失败:")
            print(f"     期望信号: {expected_signal}")
            print(f"     实际信号: {result['signal']}")
        
        # 显示前端格式预览
        if result['signal'] == 'warning' and '负TTM回本预警' in result['reason']:
            print(f"📱 前端显示预览:")
            print(f"   信号：预警（接近回本）")
            print(f"   策略：负TTM回本预警(TTM≤0, 股息≤3%, 接近回本)")
            print(f"   说明：距离负TTM回本清仓还需{actual_distance:.1f}%涨幅")
            print(f"   卖出价: {test_data['unit_cost']:.3f}元 (回本价)")
        elif result['signal'] == 'clearance' and '负TTM回本' in result['reason']:
            print(f"📱 前端显示预览:")
            print(f"   信号：清仓")
            print(f"   策略：负TTM回本清仓(TTM≤0, 股息≤3%, 已回本)")
            print(f"   说明：负TTM股票已回本，建议清仓")
            print(f"   卖出价: {test_data['unit_cost']:.3f}元 (回本价)")

def test_jinhui_wine():
    """测试金徽酒的具体情况"""
    print(f"\n🍷 测试金徽酒的具体情况...")
    
    # 根据图片信息推测的金徽酒数据
    jinhui_data = {
        'name': '金徽酒',
        'code': '601992',
        'price': 1.19,
        'yearly_low': 1.00,  # 推测
        'unit_cost': 1.35,   # 推测成本价，使当前亏损约12%
        'distance_from_low_pct': 19.0,
        'pe_ratio': -19.06,  # 负TTM
        'pb_ratio': 0.45,
        'dividend_yield': 2.86,  # ≤3%
        'scan_score': 75,
        'profit_status': 'need_gain',
        'profit_margin': -12.0  # 推测亏损12%
    }
    
    print(f"📊 金徽酒数据:")
    print(f"   当前价格: {jinhui_data['price']}元")
    print(f"   推测成本价: {jinhui_data['unit_cost']}元")
    print(f"   TTM: {jinhui_data['pe_ratio']}")
    print(f"   股息率: {jinhui_data['dividend_yield']:.1f}%")
    
    # 计算距离回本百分比
    distance_to_profit = ((jinhui_data['unit_cost'] - jinhui_data['price']) / jinhui_data['price']) * 100
    print(f"   距离回本: {distance_to_profit:.1f}%")
    
    # 计算卖出信号
    result = calculate_sell_signal(jinhui_data)
    
    print(f"🎯 策略结果:")
    print(f"   信号: {result['signal']}")
    print(f"   原因: {result['reason']}")
    
    # 分析结果
    if distance_to_profit <= 10:
        print(f"✅ 应该触发预警（距离回本{distance_to_profit:.1f}% ≤ 10%）")
    else:
        print(f"❌ 不应该触发预警（距离回本{distance_to_profit:.1f}% > 10%）")

def main():
    """主函数"""
    try:
        test_profit_threshold()
        test_jinhui_wine()
        print("\n✅ 回本阈值逻辑测试完成!")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
