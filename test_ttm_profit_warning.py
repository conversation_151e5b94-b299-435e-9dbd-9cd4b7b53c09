#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试负TTM回本预警功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入策略管理器
from 持仓系统_v14 import calculate_sell_signal

def test_ttm_profit_warning():
    """测试负TTM回本预警功能"""
    print("🧪 测试负TTM回本预警功能...")
    
    # 测试场景
    test_cases = [
        {
            'name': '接近回本预警(-5%)',
            'data': {
                'name': '测试股票A',
                'code': '000001',
                'price': 9.50,
                'yearly_low': 8.00,
                'unit_cost': 10.00,  # 成本价10元，当前9.5元，还需5%回本
                'distance_from_low_pct': 18.75,
                'pe_ratio': -15.0,   # 负TTM
                'pb_ratio': 0.8,
                'dividend_yield': 2.5,  # ≤3%
                'scan_score': 75,
                'profit_status': 'need_gain',  # 还未回本
                'profit_margin': -5.0  # 还需5%涨幅回本
            },
            'expected_signal': 'warning',
            'expected_reason': '负TTM回本预警'
        },
        {
            'name': '刚好回本清仓(0%)',
            'data': {
                'name': '测试股票B',
                'code': '000002',
                'price': 10.00,
                'yearly_low': 8.00,
                'unit_cost': 10.00,  # 成本价10元，当前10元，刚好回本
                'distance_from_low_pct': 25.0,
                'pe_ratio': -20.0,   # 负TTM
                'pb_ratio': 0.9,
                'dividend_yield': 2.0,  # ≤3%
                'scan_score': 80,
                'profit_status': 'profit',  # 已回本
                'profit_margin': 0.0
            },
            'expected_signal': 'clearance',
            'expected_reason': '负TTM回本'
        },
        {
            'name': '距离回本太远(-15%)',
            'data': {
                'name': '测试股票C',
                'code': '000003',
                'price': 8.50,
                'yearly_low': 7.00,
                'unit_cost': 10.00,  # 成本价10元，当前8.5元，还需15%回本
                'distance_from_low_pct': 21.4,
                'pe_ratio': -25.0,   # 负TTM
                'pb_ratio': 0.7,
                'dividend_yield': 1.8,  # ≤3%
                'scan_score': 65,
                'profit_status': 'need_gain',  # 还未回本
                'profit_margin': -15.0  # 还需15%涨幅回本，超过10%预警阈值
            },
            'expected_signal': 'hold',
            'expected_reason': None
        },
        {
            'name': '已盈利清仓(+10%)',
            'data': {
                'name': '测试股票D',
                'code': '000004',
                'price': 11.00,
                'yearly_low': 8.00,
                'unit_cost': 10.00,  # 成本价10元，当前11元，已盈利10%
                'distance_from_low_pct': 37.5,
                'pe_ratio': -18.0,   # 负TTM
                'pb_ratio': 1.1,
                'dividend_yield': 2.8,  # ≤3%
                'scan_score': 85,
                'profit_status': 'profit',  # 已盈利
                'profit_margin': 10.0
            },
            'expected_signal': 'clearance',
            'expected_reason': '负TTM回本'
        }
    ]
    
    print(f"\n🧪 开始测试 {len(test_cases)} 个测试用例...")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试用例 {i}: {test_case['name']} ---")
        
        data = test_case['data']
        print(f"📊 测试数据:")
        print(f"   当前价格: {data['price']}元")
        print(f"   成本价格: {data['unit_cost']}元")
        print(f"   盈利状态: {data['profit_status']}")
        print(f"   盈利幅度: {data['profit_margin']:.1f}%")
        print(f"   TTM: {data['pe_ratio']}")
        print(f"   股息率: {data['dividend_yield']:.1f}%")
        
        # 计算卖出信号
        result = calculate_sell_signal(data)
        
        print(f"🎯 策略结果:")
        print(f"   信号: {result['signal']}")
        print(f"   原因: {result['reason']}")
        
        # 验证结果
        expected_signal = test_case['expected_signal']
        expected_reason = test_case['expected_reason']
        
        signal_correct = result['signal'] == expected_signal
        reason_correct = expected_reason is None or expected_reason in result['reason']
        
        if signal_correct and reason_correct:
            print(f"   ✅ 测试通过")
        else:
            print(f"   ❌ 测试失败:")
            print(f"     期望信号: {expected_signal}")
            print(f"     实际信号: {result['signal']}")
            if expected_reason:
                print(f"     期望原因包含: {expected_reason}")
                print(f"     实际原因: {result['reason']}")
        
        # 显示前端格式预览
        if result['signal'] == 'warning' and '负TTM回本预警' in result['reason']:
            print(f"📱 前端显示预览:")
            print(f"   信号：预警（接近回本）")
            print(f"   策略：负TTM回本预警(TTM≤0, 股息≤3%, 接近回本)")
            print(f"   说明：距离负TTM回本清仓还需{abs(data['profit_margin']):.1f}%涨幅")
            print(f"   卖出价: {data['unit_cost']:.3f}元 (回本价)")

def main():
    """主函数"""
    try:
        test_ttm_profit_warning()
        print("\n✅ 负TTM回本预警功能测试完成!")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
