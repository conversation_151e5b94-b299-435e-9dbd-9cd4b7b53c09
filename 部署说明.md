# 持仓系统服务器部署指南

## 📋 部署准备

### 1. 服务器要求
- **操作系统**: Ubuntu 20.04+ / CentOS 7+ / Debian 10+
- **内存**: 最低 1GB，推荐 2GB+
- **存储**: 最低 10GB 可用空间
- **网络**: 公网IP或域名

### 2. 软件依赖
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3 python3-pip nginx supervisor

# CentOS/RHEL
sudo yum install python3 python3-pip nginx supervisor
```

## 🚀 快速部署

### 1. 上传文件
将以下文件上传到服务器（如 `/var/www/stock-portfolio/`）：
```
持仓系统_v12_完整整合版.py
A股交易时间监测_简化版.py
yearly_low_cache_reader.py
tdx_scan_module.py
wechat_alert_v2.py
requirements.txt
gunicorn_config.py
nginx_config.conf
deploy.sh
start.sh
stop.sh
```

### 2. 执行部署脚本
```bash
cd /var/www/stock-portfolio/
chmod +x deploy.sh
./deploy.sh
```

### 3. 配置Nginx
```bash
# 编辑nginx配置
sudo nano nginx_config.conf
# 修改域名和路径

# 复制配置文件
sudo cp nginx_config.conf /etc/nginx/sites-available/stock-portfolio
sudo ln -s /etc/nginx/sites-available/stock-portfolio /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启nginx
sudo systemctl restart nginx
```

### 4. 启动服务
```bash
# 使用systemd启动
sudo systemctl start stock-portfolio
sudo systemctl status stock-portfolio

# 或使用脚本启动
./start.sh
```

## 🔧 配置说明

### 1. 修改配置文件

#### gunicorn_config.py
```python
# 根据服务器配置调整
workers = 4  # CPU核心数 * 2 + 1
bind = "127.0.0.1:8000"  # 内网绑定
```

#### nginx_config.conf
```nginx
server_name your-domain.com;  # 替换为您的域名
# 或者使用IP地址
server_name 123.456.789.123;
```

### 2. 环境变量设置
```bash
# 设置生产环境
export FLASK_ENV=production

# 可选：设置其他配置
export STOCK_DATA_PATH=/var/www/stock-portfolio/data
export LOG_LEVEL=INFO
```

## 🔒 安全配置

### 1. 防火墙设置
```bash
# Ubuntu/Debian
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 2. SSL证书（推荐）
```bash
# 使用Let's Encrypt免费证书
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 3. 访问控制
在nginx配置中添加IP白名单：
```nginx
location / {
    allow ***********/24;  # 允许内网
    allow 123.456.789.123; # 允许特定IP
    deny all;              # 拒绝其他
    
    proxy_pass http://127.0.0.1:8000;
    # ... 其他配置
}
```

## 📊 监控和维护

### 1. 查看日志
```bash
# 应用日志
tail -f logs/error.log
tail -f logs/access.log

# 系统日志
sudo journalctl -u stock-portfolio -f

# Nginx日志
sudo tail -f /var/log/nginx/stock_portfolio_error.log
```

### 2. 重启服务
```bash
# 重启应用
sudo systemctl restart stock-portfolio

# 重启nginx
sudo systemctl restart nginx

# 或使用脚本
./stop.sh && ./start.sh
```

### 3. 更新代码
```bash
# 停止服务
./stop.sh

# 更新代码文件
# ... 上传新文件 ...

# 重启服务
./start.sh
```

## 🌐 访问方式

### 1. 通过域名访问
```
http://your-domain.com
https://your-domain.com  # 如果配置了SSL
```

### 2. 通过IP访问
```
http://123.456.789.123
```

### 3. 移动端访问
系统已适配移动端，可直接在手机浏览器中访问。

## ❗ 常见问题

### 1. 端口被占用
```bash
# 查看端口占用
sudo netstat -tlnp | grep :8000
# 或
sudo lsof -i :8000

# 杀死占用进程
sudo kill -9 PID
```

### 2. 权限问题
```bash
# 设置正确的文件权限
sudo chown -R www-data:www-data /var/www/stock-portfolio/
sudo chmod -R 755 /var/www/stock-portfolio/
```

### 3. 依赖安装失败
```bash
# 升级pip
pip3 install --upgrade pip

# 使用国内镜像
pip3 install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

## 📞 技术支持

如果遇到部署问题，请检查：
1. 服务器系统版本和配置
2. Python版本（需要3.7+）
3. 网络连接和防火墙设置
4. 日志文件中的错误信息
