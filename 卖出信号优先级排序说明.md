# 卖出信号优先级排序说明

## 🎯 设计理念

按照完整的交易流程设计优先级排序，确保信号显示符合实际操作逻辑：

```
系统发出信号 → 用户挂单 → 成交处理 → 后续监控 → 完全退出
```

## 📋 完整优先级排序（从高到低）

### 🚨 最高优先级 - 需要立即行动 (1-6)

| 优先级 | 信号类型 | 显示文本 | 颜色 | 说明 |
|--------|----------|----------|------|------|
| 1 | clearance | 🔥 立即清仓 | 深红色 #8b0000 | 需要全部卖出 |
| 2 | reduce | 📉 立即减半 | 橙红色 #ff6b35 | 需要减半卖出 |
| 3 | prepare_clearance | ⚠️ 准备清仓 | 橙红色 #ff8c00 | 接近清仓条件 |
| 4 | prepare_reduce | 📊 准备减半 | 橙色 #ffa500 | 接近减半条件 |
| 5 | sell | 🚨 卖出信号 | 红色 #ff4757 | 一般卖出信号 |
| 6 | warning | ⚠️ 预警信号 | 橙色 #ffa502 | 预警提醒 |

### 📋 中等优先级 - 已操作待成交 (10)

| 优先级 | 状态类型 | 显示文本 | 颜色 | 说明 |
|--------|----------|----------|------|------|
| 10 | 已挂单 | ⏳ 已挂单 | 黄色 #ffc107 | 已经挂单等待成交 |

### ✅ 低优先级 - 已完成操作但需继续监控 (15-20)

| 优先级 | 状态类型 | 显示文本 | 颜色 | 说明 |
|--------|----------|----------|------|------|
| 15 | 已减半(负TTM) | ⚠️ 已减半(负TTM) | 红色 #dc3545 | 基本面严重恶化 |
| 16 | 已减半(高TTM) | 📊 已减半(高TTM) | 橙色 #fd7e14 | 基本面恶化 |
| 17 | 已减半(冷却期) | 🧊 已减半(冷却期) | 蓝色 #17a2b8 | 正常冷却期 |
| 18 | 已减半(正常) | ✅ 已减半(正常) | 绿色 #28a745 | 正常监控状态 |
| 19 | 已减半(其他) | 📋 已减半 | 灰色 #6c757d | 其他减半状态 |
| 20 | 其他自定义 | 🔧 自定义状态 | 灰色 #6c757d | 其他自定义状态 |

### 💚 正常持有 (50)

| 优先级 | 状态类型 | 显示文本 | 颜色 | 说明 |
|--------|----------|----------|------|------|
| 50 | hold | 💚 持有 | 绿色 #2ed573 | 继续持有 |

### 🔧 异常状态 (60)

| 优先级 | 状态类型 | 显示文本 | 颜色 | 说明 |
|--------|----------|----------|------|------|
| 60 | 异常 | ❓ 数据异常 | 灰色 #747d8c | 计算错误等异常 |

### 🛑 最低优先级 - 已完全退出 (999)

| 优先级 | 状态类型 | 显示文本 | 颜色 | 说明 |
|--------|----------|----------|------|------|
| 999 | 已清仓 | 🛑 已清仓 | 红色 #dc3545 | 已完全退出，不再监控 |

## 🔄 排序逻辑说明

### 1. 紧急程度优先
- **立即行动** > **准备行动** > **已处理** > **正常持有** > **已退出**

### 2. 同级别内部排序
- 立即清仓 > 立即减半（风险程度）
- 准备清仓 > 准备减半（风险程度）
- 负TTM > 高TTM > 冷却期 > 正常（监控紧急程度）

### 3. 特殊处理
- **已清仓**始终排在最后（完全退出）
- **已挂单**在中间位置（正在处理中）
- **已减半**根据监控状态细分优先级

## 📊 实际应用场景

### 场景1：多种信号混合
```
股票A: 🔥 立即清仓 (优先级1)
股票B: ⏳ 已挂单 (优先级10)  
股票C: 🧊 已减半(冷却期) (优先级17)
股票D: 💚 持有 (优先级50)
股票E: 🛑 已清仓 (优先级999)

排序结果: A → B → C → D → E
```

### 场景2：同类型不同状态
```
股票A: ⚠️ 已减半(负TTM) (优先级15)
股票B: 📊 已减半(高TTM) (优先级16)
股票C: 🧊 已减半(冷却期) (优先级17)
股票D: ✅ 已减半(正常) (优先级18)

排序结果: A → B → C → D
```

## 🎯 设计优势

### 1. 符合交易流程
- 按照实际操作顺序排列
- 紧急的排在前面，已处理的排在后面

### 2. 直观易懂
- 颜色和图标直观表示紧急程度
- 优先级数字清晰表示排序逻辑

### 3. 便于管理
- 需要关注的股票排在前面
- 已完成操作的股票排在中间
- 已退出的股票排在最后

### 4. 灵活扩展
- 可以轻松添加新的信号类型
- 优先级数字留有扩展空间

## 🔧 技术实现

### 前端排序函数
```javascript
function getSellSignalPriority(stock) {
    // 根据股票状态返回优先级数字
    // 数字越小优先级越高
}
```

### 后端优先级计算
```python
def calculate_sell_signal(stock_data):
    # 返回包含priority字段的信号对象
    return {
        'signal': 'clearance',
        'priority': 1,
        # ... 其他字段
    }
```

### 排序应用
- 默认按优先级升序排序（紧急的在前）
- 同优先级按市值降序排序
- 支持用户自定义排序规则

## 📝 使用建议

### 1. 日常监控
- 重点关注优先级1-6的股票（需要行动）
- 定期检查优先级10的股票（挂单状态）
- 监控优先级15-16的股票（基本面恶化）

### 2. 操作流程
- 优先处理紧急信号（1-6）
- 跟进挂单状态（10）
- 关注减半股票变化（15-20）
- 定期清理已清仓股票（999）

### 3. 系统维护
- 定期检查异常状态股票（60）
- 确保优先级计算正确
- 根据需要调整优先级规则
