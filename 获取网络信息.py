#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取网络信息脚本
显示本机IP地址，方便局域网访问
"""

import socket
import subprocess
import platform

def get_local_ip():
    """获取本机IP地址"""
    try:
        # 创建一个UDP socket
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        # 连接到一个远程地址（不会真正发送数据）
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception:
        return "127.0.0.1"

def get_all_ips():
    """获取所有网络接口的IP地址"""
    ips = []
    try:
        if platform.system() == "Windows":
            result = subprocess.run(['ipconfig'], capture_output=True, text=True, encoding='gbk')
            lines = result.stdout.split('\n')
            for line in lines:
                if 'IPv4' in line and '地址' in line:
                    ip = line.split(':')[-1].strip()
                    if ip and ip != '127.0.0.1':
                        ips.append(ip)
        else:
            result = subprocess.run(['hostname', '-I'], capture_output=True, text=True)
            ips = result.stdout.strip().split()
    except Exception as e:
        print(f"获取IP地址失败: {e}")
    
    return ips

def main():
    print("=" * 60)
    print("🌐 持仓系统网络访问信息")
    print("=" * 60)
    
    # 获取主要IP地址
    main_ip = get_local_ip()
    print(f"📍 主要IP地址: {main_ip}")
    
    # 获取所有IP地址
    all_ips = get_all_ips()
    if all_ips:
        print(f"📋 所有可用IP地址:")
        for i, ip in enumerate(all_ips, 1):
            print(f"   {i}. {ip}")
    
    print("\n" + "=" * 60)
    print("🔗 访问方式:")
    print("=" * 60)
    
    print("1. 本机访问:")
    print("   http://localhost:5000")
    print("   http://127.0.0.1:5000")
    
    print("\n2. 局域网访问:")
    if all_ips:
        for ip in all_ips:
            print(f"   http://{ip}:5000")
    else:
        print(f"   http://{main_ip}:5000")
    
    print("\n" + "=" * 60)
    print("📱 移动设备访问:")
    print("=" * 60)
    print("• 确保手机/平板连接同一WiFi网络")
    print("• 在浏览器中输入上述局域网地址")
    print("• 系统已适配移动端，可正常使用")
    
    print("\n" + "=" * 60)
    print("🔒 安全提醒:")
    print("=" * 60)
    print("• 仅在可信任的局域网环境中使用")
    print("• 不要在公共网络中开放访问")
    print("• 建议设置防火墙规则限制访问")
    
    print("\n" + "=" * 60)
    print("🚀 启动持仓系统:")
    print("=" * 60)
    print("python 持仓系统_v12_完整整合版.py")
    print("=" * 60)

if __name__ == "__main__":
    main()
