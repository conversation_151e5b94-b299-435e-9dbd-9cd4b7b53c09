import requests
import re

def debug_slb_page(code: str):
    """调试扫雷宝页面内容"""
    try:
        print(f"🔍 调试扫雷宝页面: {code}")
        
        # 扫雷宝页面URL
        url = f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code={code}&color=0"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Referer': 'http://page3.tdx.com.cn:7615/',
        }
        
        response = requests.get(url, headers=headers, timeout=15)
        response.encoding = 'utf-8'
        
        if response.status_code == 200:
            content = response.text
            
            print(f"页面长度: {len(content)} 字符")
            print(f"URL: {url}")
            print("-" * 80)
            
            # 查找所有包含数字的行
            lines = content.split('\n')
            relevant_lines = []
            
            for i, line in enumerate(lines):
                # 查找包含分数相关关键词的行
                if any(keyword in line.lower() for keyword in ['score', 'value', '分', 'showvalue', 'realvalue']):
                    relevant_lines.append(f"第{i+1}行: {line.strip()}")
                
                # 查找包含两位数或三位数的行
                if re.search(r'\b\d{2,3}\b', line):
                    if any(keyword in line for keyword in ['var', '=', 'function', 'fillText']):
                        relevant_lines.append(f"第{i+1}行: {line.strip()}")
            
            print("相关代码行:")
            for line in relevant_lines[:20]:  # 只显示前20行
                print(line)
            
            print("\n" + "-" * 80)
            
            # 查找特定模式
            patterns_to_check = [
                (r'var\s+showvalue\s*=\s*(\d{1,3})', "showvalue变量"),
                (r'showvalue\s*=\s*(\d{1,3})', "showvalue赋值"),
                (r'var\s+realvalue\s*=\s*(\d{1,3})', "realvalue变量"),
                (r'realvalue\s*=\s*(\d{1,3})', "realvalue赋值"),
                (r'fillText\([^,]*?(\d{1,3})[^,]*?"分"', "Canvas绘制分数"),
                (r'(\d{1,3})分', "直接分数文本"),
            ]
            
            print("模式匹配结果:")
            for pattern, description in patterns_to_check:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    print(f"  {description}: {matches}")
                else:
                    print(f"  {description}: 未找到")
            
            # 查找所有两位数和三位数
            all_numbers = re.findall(r'\b(\d{2,3})\b', content)
            number_counts = {}
            for num in all_numbers:
                number_counts[num] = number_counts.get(num, 0) + 1
            
            print(f"\n所有2-3位数字出现频率 (前10个):")
            sorted_numbers = sorted(number_counts.items(), key=lambda x: x[1], reverse=True)
            for num, count in sorted_numbers[:10]:
                print(f"  {num}: {count}次")
            
            return content
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        return None

if __name__ == "__main__":
    # 调试神思电子的页面
    debug_slb_page("300479")
    
    print("\n" + "=" * 100 + "\n")
    
    # 调试平安银行的页面
    debug_slb_page("000001")
