#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通达信爆雷宝集成模块
用于集成到持仓系统中
"""

import requests
import json
import time
from typing import Dict, Optional, List

class BXBIntegration:
    """通达信爆雷宝集成类"""
    
    def __init__(self):
        self.base_url = "http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/json/"
        self.cache = {}  # 缓存数据，避免重复请求
        
    def get_bxb_score(self, stock_code: str) -> Optional[Dict]:
        """
        获取股票的爆雷宝分数
        
        Args:
            stock_code: 股票代码，如 '000001'
            
        Returns:
            包含分数和风险信息的字典，如果失败返回None
        """
        # 检查缓存
        if stock_code in self.cache:
            cache_time = self.cache[stock_code].get('cache_time', 0)
            if time.time() - cache_time < 3600:  # 缓存1小时
                return self.cache[stock_code]['data']
        
        try:
            # 获取数据
            url = f"{self.base_url}{stock_code}.json"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            stock_data = response.json()
            
            # 计算分数
            result = self._calculate_score(stock_data)
            
            # 缓存结果
            self.cache[stock_code] = {
                'data': result,
                'cache_time': time.time()
            }
            
            return result
            
        except Exception as e:
            print(f"获取 {stock_code} 爆雷宝数据失败: {e}")
            return None
    
    def _calculate_score(self, stock_data: Dict) -> Dict:
        """
        计算爆雷宝分数

        根据实际验证，正确的计算逻辑：
        1. 初始分数100分
        2. 只计算主风险项，不计算关联风险
        3. 如果 trig=1 且 fs>0，则扣分
        """
        if not stock_data or 'data' not in stock_data:
            return {
                'score': 0,
                'risk_level': '数据异常',
                'triggered_risks': 0,
                'total_risks': 0,
                'error': '数据格式错误'
            }

        # 初始分数100分
        realvalue = 100
        total_risks = 0
        triggered_risks = 0
        triggered_details = []

        # 遍历所有风险类别
        for category in stock_data['data']:
            category_name = category.get('name', '未知类别')

            # 遍历风险项
            for risk_item in category.get('rows', []):
                total_risks += 1

                # 只计算主风险项（这是关键！）
                if risk_item.get('trig') == 1 and risk_item.get('fs', 0) > 0:
                    fs = risk_item.get('fs', 0)
                    triggered_risks += 1
                    realvalue -= fs
                    triggered_details.append({
                        'category': category_name,
                        'name': risk_item.get('lx', '未知风险'),
                        'score': fs,
                        'reason': risk_item.get('trigyy', '')
                    })

                # 注意：不计算关联风险项！这是通过实际验证得出的结论
                # 关联风险只用于页面显示，不参与分数计算

        # 确保分数不小于1
        if realvalue <= 1:
            realvalue = 1
        
        return {
            'stock_name': stock_data.get('name', '未知股票'),
            'score': realvalue,
            'risk_level': self._get_risk_level(realvalue),
            'triggered_risks': triggered_risks,
            'total_risks': total_risks,
            'trigger_rate': round(triggered_risks/total_risks*100, 1) if total_risks > 0 else 0,
            'details': triggered_details
        }
    
    def _get_risk_level(self, score: float) -> str:
        """根据分数确定风险等级"""
        if score >= 90:
            return "极低风险"
        elif score >= 80:
            return "低风险"
        elif score >= 70:
            return "中低风险"
        elif score >= 60:
            return "中等风险"
        elif score >= 50:
            return "中高风险"
        elif score >= 40:
            return "高风险"
        else:
            return "极高风险"
    
    def get_risk_color(self, score: float) -> str:
        """根据分数返回颜色代码"""
        if score >= 80:
            return "#10b068"  # 绿色
        elif score >= 60:
            return "#faba02"  # 黄色
        elif score >= 40:
            return "#fa7602"  # 橙色
        else:
            return "#e93030"  # 红色
    
    def batch_get_scores(self, stock_codes: List[str]) -> Dict[str, Dict]:
        """批量获取多只股票的爆雷宝分数"""
        results = {}
        
        for i, code in enumerate(stock_codes):
            print(f"正在获取 {code} 的爆雷宝数据... ({i+1}/{len(stock_codes)})")
            
            result = self.get_bxb_score(code)
            if result:
                results[code] = result
            
            # 避免请求过于频繁
            if i < len(stock_codes) - 1:
                time.sleep(0.5)
        
        return results
    
    def format_for_display(self, bxb_data: Dict) -> str:
        """格式化显示信息"""
        if not bxb_data or 'error' in bxb_data:
            return "数据获取失败"
        
        score = bxb_data['score']
        risk_level = bxb_data['risk_level']
        triggered = bxb_data['triggered_risks']
        
        return f"{score}分 ({risk_level}) 触发{triggered}项"

# 测试函数
def test_bxb_integration():
    """测试爆雷宝集成功能"""
    bxb = BXBIntegration()
    
    # 测试单只股票
    print("=== 测试单只股票 ===")
    result = bxb.get_bxb_score("000001")
    if result:
        print(f"股票: {result['stock_name']}")
        print(f"分数: {result['score']}")
        print(f"风险等级: {result['risk_level']}")
        print(f"触发风险: {result['triggered_risks']}/{result['total_risks']}")
        print(f"显示格式: {bxb.format_for_display(result)}")
    
    # 测试批量获取
    print("\n=== 测试批量获取 ===")
    codes = ["000001", "000002"]
    batch_results = bxb.batch_get_scores(codes)
    
    for code, data in batch_results.items():
        print(f"{code}: {bxb.format_for_display(data)}")

if __name__ == "__main__":
    test_bxb_integration()
