import requests
import re

def get_mine_clearance_score(code: str) -> float:
    """获取股票排雷分数"""
    try:
        # 构建API URL
        api_url = f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code={code}&color=0"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Referer': 'http://page3.tdx.com.cn:7615/',
        }
        
        print(f"🔍 正在获取 {code} 的排雷数据...")
        response = requests.get(api_url, headers=headers, timeout=15)
        response.encoding = 'utf-8'
        
        if response.status_code == 200:
            content = response.text
            print(f"✅ 响应成功，内容长度: {len(content)}")
            
            # 查找JavaScript数据或变量
            js_patterns = [
                r'var\s+score\s*=\s*(\d{1,3})',
                r'score\s*:\s*(\d{1,3})',
                r'"score"\s*:\s*(\d{1,3})',
                r'data\s*=\s*.*?(\d{2,3})',
                r'value\s*:\s*(\d{2,3})',
            ]

            for pattern in js_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    for match in matches:
                        score = float(match)
                        if 50 <= score <= 100:
                            print(f"✅ JS模式找到分数: {score}")
                            return score

            # 查找HTML中的分数显示
            html_patterns = [
                r'<span[^>]*class[^>]*score[^>]*>(\d{1,3})</span>',
                r'<div[^>]*class[^>]*score[^>]*>(\d{1,3})</div>',
                r'<span[^>]*>(\d{2,3})</span>',
                r'<div[^>]*>(\d{2,3})</div>',
            ]

            for pattern in html_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    for match in matches:
                        score = float(match)
                        if 50 <= score <= 100:
                            print(f"✅ HTML模式找到分数: {score}")
                            return score

            # 查找所有两位数，但排除CSS样式中的数字
            # 先移除style标签和CSS内容
            content_no_css = re.sub(r'<style[^>]*>.*?</style>', '', content, flags=re.DOTALL)
            content_no_css = re.sub(r'style\s*=\s*"[^"]*"', '', content_no_css)

            # 直接在原始内容中查找97
            if '97' in content:
                print("🎯 在原始内容中找到97")
                # 查找97周围的上下文，确保它不在CSS中
                matches = re.findall(r'[^{};]*97[^{};]*', content)
                for match in matches:
                    # 如果不包含CSS相关的字符，可能是真实数据
                    if not any(css_char in match for css_char in ['color:', 'rgb(', 'background', 'border', 'font']):
                        print(f"✅ 找到非CSS的97: {match.strip()[:50]}")
                        return 97.0

            # 查找所有可能的分数
            all_numbers = re.findall(r'\b(\d{2,3})\b', content_no_css)
            valid_scores = []

            for num_str in all_numbers:
                try:
                    num = float(num_str)
                    if 50 <= num <= 100:
                        valid_scores.append(num)
                except ValueError:
                    continue

            print(f"🔍 找到的有效分数: {valid_scores[:15]}")

            # 返回最高分数（通常排雷分数都比较高）
            if valid_scores:
                max_score = max(valid_scores)
                print(f"✅ 返回最高分数: {max_score}")
                return max_score
            
            # 查找其他可能的分数
            patterns = [
                r'\b(9[0-9])\b',  # 90-99
                r'\b([8-9][0-9])\b',  # 80-99
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, content)
                if matches:
                    scores = [float(m) for m in matches if 80 <= float(m) <= 100]
                    if scores:
                        print(f"✅ 找到分数: {scores}")
                        return scores[0]
            
            print("❌ 未找到有效分数")
            return None
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        return None

if __name__ == '__main__':
    # 测试000001
    score = get_mine_clearance_score('000001')
    print(f"\n最终结果: {score}")
