#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取通达信爆雷宝页面源代码并分析计算公式
"""

import requests
import re
import json

def get_page_source():
    """获取页面源代码"""
    url = 'http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code=000001&color=0'
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    try:
        print("正在获取页面源代码...")
        response = requests.get(url, headers=headers, timeout=15)
        response.encoding = 'utf-8'
        
        if response.status_code == 200:
            print(f"成功获取页面，长度: {len(response.text)} 字符")
            return response.text
        else:
            print(f"获取页面失败，状态码: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"获取页面异常: {e}")
        return None

def analyze_calculation_logic(html_content):
    """分析计算逻辑"""
    if not html_content:
        return

    print("\n=== 分析页面中的计算逻辑 ===")

    # 直接使用正则表达式查找script标签
    script_pattern = r'<script[^>]*>(.*?)</script>'
    scripts = re.findall(script_pattern, html_content, re.DOTALL | re.IGNORECASE)
    
    calculation_patterns = [
        r'function\s+\w*[Cc]alculate\w*\s*\([^)]*\)\s*\{[^}]*\}',
        r'function\s+\w*[Ss]core\w*\s*\([^)]*\)\s*\{[^}]*\}',
        r'function\s+\w*[Cc]ompute\w*\s*\([^)]*\)\s*\{[^}]*\}',
        r'var\s+\w*[Ss]core\w*\s*=.*?;',
        r'let\s+\w*[Ss]core\w*\s*=.*?;',
        r'const\s+\w*[Ss]core\w*\s*=.*?;',
        r'\w*[Ss]core\w*\s*\+=.*?;',
        r'\w*[Ss]core\w*\s*=\s*\w*[Ss]core\w*\s*\+.*?;',
        r'\.fs\s*\*.*?[;\n]',
        r'Math\.(round|floor|ceil)\([^)]*[Ss]core[^)]*\)',
    ]
    
    found_calculations = []

    for i, script_content in enumerate(scripts):
        if script_content:
            # 检查是否包含计算相关的关键词
            if any(keyword in script_content.lower() for keyword in ['score', 'calculate', 'compute', 'fs', '分数', '计算']):
                print(f"\n--- Script {i+1} (包含计算逻辑) ---")
                
                # 查找匹配的计算模式
                for pattern in calculation_patterns:
                    matches = re.findall(pattern, script_content, re.IGNORECASE | re.DOTALL)
                    if matches:
                        print(f"\n模式匹配 '{pattern[:30]}...':")
                        for match in matches:
                            clean_match = re.sub(r'\s+', ' ', match.strip())
                            if len(clean_match) > 100:
                                print(f"  {clean_match[:100]}...")
                            else:
                                print(f"  {clean_match}")
                            found_calculations.append(clean_match)
                
                # 查找包含fs的行（风险分数相关）
                lines = script_content.split('\n')
                fs_lines = [line.strip() for line in lines if 'fs' in line and ('*' in line or '+' in line or '=' in line)]
                if fs_lines:
                    print(f"\n包含fs的计算行:")
                    for line in fs_lines[:10]:  # 只显示前10行
                        print(f"  {line}")
    
    # 查找HTML中的数据结构
    print(f"\n=== 查找数据结构 ===")
    
    # 查找可能的JSON数据
    json_patterns = [
        r'var\s+\w+\s*=\s*(\{.*?\});',
        r'let\s+\w+\s*=\s*(\{.*?\});',
        r'const\s+\w+\s*=\s*(\{.*?\});',
        r'data\s*:\s*(\{.*?\})',
        r'(\{[^{}]*"fs"[^{}]*\})',
    ]
    
    for pattern in json_patterns:
        matches = re.findall(pattern, html_content, re.DOTALL)
        if matches:
            print(f"\n找到JSON数据结构:")
            for match in matches[:3]:  # 只显示前3个
                try:
                    # 尝试解析JSON
                    if match.strip().startswith('{'):
                        parsed = json.loads(match)
                        print(f"  解析成功的JSON: {str(parsed)[:200]}...")
                except:
                    print(f"  原始数据: {match[:200]}...")
    
    return found_calculations

def save_source_code(html_content):
    """保存源代码到文件"""
    if html_content:
        try:
            with open('bxb_source.html', 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"\n源代码已保存到 bxb_source.html")
        except Exception as e:
            print(f"保存文件失败: {e}")

def main():
    """主函数"""
    print("通达信爆雷宝计算公式分析器")
    print("=" * 50)
    
    # 获取页面源代码
    html_content = get_page_source()
    
    if html_content:
        # 保存源代码
        save_source_code(html_content)
        
        # 分析计算逻辑
        calculations = analyze_calculation_logic(html_content)
        
        if calculations:
            print(f"\n=== 总结 ===")
            print(f"找到 {len(calculations)} 个计算相关的代码片段")
            print("主要计算逻辑:")
            for i, calc in enumerate(calculations[:5], 1):
                print(f"{i}. {calc}")
        else:
            print("\n未找到明显的计算公式，可能需要进一步分析JavaScript代码")
    else:
        print("无法获取页面源代码")

if __name__ == "__main__":
    main()
