#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统测试脚本
============

用于测试模块化系统的各个组件是否正常工作

作者: AI Assistant
版本: 1.0
"""

import sys
import os

# 添加模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        from modules.data_fetcher import stock_data_fetcher, stock_info_processor
        print("  ✅ data_fetcher 模块导入成功")
    except Exception as e:
        print(f"  ❌ data_fetcher 模块导入失败: {e}")
        return False
    
    try:
        from modules.strategy_manager import SellStrategyManager
        print("  ✅ strategy_manager 模块导入成功")
    except Exception as e:
        print(f"  ❌ strategy_manager 模块导入失败: {e}")
        return False
    
    try:
        from modules.sell_signal import SellSignalPriorityManager, calculate_sell_signal
        print("  ✅ sell_signal 模块导入成功")
    except Exception as e:
        print(f"  ❌ sell_signal 模块导入失败: {e}")
        return False
    
    try:
        from modules.cache_manager import cache_manager
        print("  ✅ cache_manager 模块导入成功")
    except Exception as e:
        print(f"  ❌ cache_manager 模块导入失败: {e}")
        return False
    
    try:
        from modules.data_persistence import data_persistence
        print("  ✅ data_persistence 模块导入成功")
    except Exception as e:
        print(f"  ❌ data_persistence 模块导入失败: {e}")
        return False
    
    return True

def test_strategy_manager():
    """测试策略管理器"""
    print("\n🔧 测试策略管理器...")
    
    try:
        from modules.strategy_manager import SellStrategyManager
        
        # 创建策略管理器实例
        strategy_manager = SellStrategyManager()
        
        # 检查默认策略
        strategies = strategy_manager.strategies
        print(f"  📋 加载了 {len(strategies)} 个策略")
        
        # 检查启用的策略
        enabled_strategies = strategy_manager.get_enabled_strategies()
        print(f"  ✅ 启用了 {len(enabled_strategies)} 个策略")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 策略管理器测试失败: {e}")
        return False

def test_data_persistence():
    """测试数据持久化"""
    print("\n💾 测试数据持久化...")
    
    try:
        from modules.data_persistence import data_persistence
        
        # 测试保存和加载
        test_data = {"000001": {"name": "测试股票", "price": 10.0}}
        
        # 保存测试数据
        success = data_persistence.save_stock_data(test_data, "2025-07-25 12:00:00")
        if success:
            print("  ✅ 数据保存成功")
        else:
            print("  ❌ 数据保存失败")
            return False
        
        # 加载测试数据
        loaded_data, last_time, success = data_persistence.load_stock_data()
        if success and loaded_data:
            print("  ✅ 数据加载成功")
            print(f"  📊 加载了 {len(loaded_data)} 条数据")
        else:
            print("  ❌ 数据加载失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 数据持久化测试失败: {e}")
        return False

def test_cache_manager():
    """测试缓存管理器"""
    print("\n🗄️ 测试缓存管理器...")
    
    try:
        from modules.cache_manager import cache_manager
        
        # 测试缓存设置和获取
        cache_manager.set('test_cache', 'test_key', 'test_value')
        value = cache_manager.get('test_cache', 'test_key')
        
        if value == 'test_value':
            print("  ✅ 缓存设置和获取成功")
        else:
            print("  ❌ 缓存设置和获取失败")
            return False
        
        # 测试缓存统计
        stats = cache_manager.get_cache_stats()
        print(f"  📊 缓存统计: {len(stats)} 个缓存类型")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 缓存管理器测试失败: {e}")
        return False

def test_flask_app():
    """测试Flask应用"""
    print("\n🌐 测试Flask应用...")
    
    try:
        # 导入Flask应用
        from app import app
        
        # 测试应用配置
        if app:
            print("  ✅ Flask应用创建成功")
            print(f"  📁 上传目录: {app.config.get('UPLOAD_FOLDER')}")
            print(f"  📏 最大文件大小: {app.config.get('MAX_CONTENT_LENGTH')}")
        else:
            print("  ❌ Flask应用创建失败")
            return False
        
        # 测试应用上下文
        with app.app_context():
            print("  ✅ Flask应用上下文正常")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Flask应用测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 🧪 持仓系统模块化测试 ===\n")
    
    # 创建必要目录
    for directory in ['data', 'config', 'cache', 'uploads', 'logs']:
        os.makedirs(directory, exist_ok=True)
    
    tests = [
        ("模块导入", test_imports),
        ("策略管理器", test_strategy_manager),
        ("数据持久化", test_data_persistence),
        ("缓存管理器", test_cache_manager),
        ("Flask应用", test_flask_app),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print(f"\n=== 📊 测试结果 ===")
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！系统准备就绪。")
        return True
    else:
        print(f"\n⚠️ 有 {total - passed} 个测试失败，请检查相关模块。")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
