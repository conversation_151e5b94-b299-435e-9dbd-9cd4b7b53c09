#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复冷却期设置问题
"""

import sys
import os
import json
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_cooling_period_in_file():
    """修复数据文件中的冷却期设置"""
    print("🔧 修复数据文件中的冷却期设置...")
    
    try:
        # 读取数据文件
        with open('stock_data_cache.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        stock_data = data.get('stock_data', {})
        print(f"📊 文件中包含 {len(stock_data)} 只股票")
        
        # 查找需要修复的股票
        fixed_count = 0
        for code, info in stock_data.items():
            if info.get('is_reduced', False):
                needs_fix = False
                
                # 检查是否缺少冷却期设置
                if not info.get('cooling_down_until'):
                    needs_fix = True
                    print(f"🔍 发现需要修复的股票: {code} {info.get('name', '')} - 缺少冷却期")
                
                # 检查是否缺少策略模式
                if not info.get('strategy_mode') or info.get('strategy_mode') == 'normal':
                    # 如果有冷却期但策略模式不对，也需要修复
                    if info.get('cooling_down_until'):
                        try:
                            cooling_date = datetime.strptime(info['cooling_down_until'], '%Y-%m-%d')
                            if datetime.now() < cooling_date:
                                needs_fix = True
                                print(f"🔍 发现需要修复的股票: {code} {info.get('name', '')} - 策略模式错误")
                        except:
                            pass
                
                if needs_fix:
                    # 设置冷却期（如果没有的话）
                    if not info.get('cooling_down_until'):
                        # 使用减半时间作为起点，如果没有减半时间则使用当前时间
                        base_time = info.get('reduced_time', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                        try:
                            base_date = datetime.strptime(base_time, '%Y-%m-%d %H:%M:%S')
                        except:
                            base_date = datetime.now()
                        
                        cooling_until = base_date + timedelta(days=365)
                        info['cooling_down_until'] = cooling_until.strftime('%Y-%m-%d')
                        print(f"  ✅ 设置冷却期至: {info['cooling_down_until']}")
                    
                    # 设置监控状态
                    info['monitoring_status'] = 'cooling_down'
                    info['strategy_mode'] = 'cooling_down'
                    
                    # 初始化减半历史记录（如果没有的话）
                    if 'reduction_history' not in info:
                        info['reduction_history'] = []
                        # 添加一个基本的减半记录
                        reduction_record = {
                            'reduction_date': info.get('reduced_time', datetime.now().strftime('%Y-%m-%d')).split(' ')[0],
                            'reduction_time': info.get('reduced_time', datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
                            'reduction_price': info.get('price', 0),
                            'ttm_at_reduction': info.get('pe_ratio', 0),
                            'distance_from_low_pct': info.get('distance_from_low_pct', 0),
                            'reason': '手动减半',
                            'strategy_at_reduction': 'manual'
                        }
                        info['reduction_history'].append(reduction_record)
                        print(f"  ✅ 添加减半历史记录")
                    
                    fixed_count += 1
                    print(f"  ✅ 修复完成: {code} {info.get('name', '')}")
        
        if fixed_count > 0:
            # 保存修复后的数据
            with open('stock_data_cache.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"\n✅ 已修复 {fixed_count} 只股票的冷却期设置")
            print("💾 数据已保存到文件")
        else:
            print("\n✅ 所有已减半股票的冷却期设置都正确，无需修复")
        
        return fixed_count
        
    except FileNotFoundError:
        print("❌ 未找到股票数据文件 stock_data_cache.json")
        return 0
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return 0

def verify_fix():
    """验证修复结果"""
    print("\n🔍 验证修复结果...")
    
    try:
        with open('stock_data_cache.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        stock_data = data.get('stock_data', {})
        
        # 检查所有已减半股票
        reduced_stocks = []
        for code, info in stock_data.items():
            if info.get('is_reduced', False):
                reduced_stocks.append({
                    'code': code,
                    'name': info.get('name', ''),
                    'strategy_mode': info.get('strategy_mode', 'unknown'),
                    'cooling_down_until': info.get('cooling_down_until', ''),
                    'has_reduction_history': len(info.get('reduction_history', [])) > 0
                })
        
        print(f"📊 验证结果 - 共 {len(reduced_stocks)} 只已减半股票:")
        
        correct_count = 0
        for stock in reduced_stocks:
            is_correct = (
                stock['strategy_mode'] == 'cooling_down' and
                stock['cooling_down_until'] and
                stock['has_reduction_history']
            )
            
            status = "✅" if is_correct else "❌"
            print(f"  {status} {stock['code']} {stock['name']}")
            print(f"      策略模式: {stock['strategy_mode']}")
            print(f"      冷却期至: {stock['cooling_down_until']}")
            print(f"      有减半历史: {stock['has_reduction_history']}")
            
            if is_correct:
                correct_count += 1
        
        print(f"\n📊 验证总结:")
        print(f"  ✅ 正确设置: {correct_count}/{len(reduced_stocks)}")
        print(f"  ❌ 仍有问题: {len(reduced_stocks) - correct_count}/{len(reduced_stocks)}")
        
        return correct_count == len(reduced_stocks)
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    print("🔧 开始修复冷却期设置问题...")
    print("=" * 50)
    
    # 1. 修复数据文件
    fixed_count = fix_cooling_period_in_file()
    
    # 2. 验证修复结果
    if fixed_count > 0:
        success = verify_fix()
        
        if success:
            print("\n🎉 修复成功！所有已减半股票的冷却期设置都正确了。")
            print("\n💡 现在重启持仓系统，冷却器应该能正确显示了。")
        else:
            print("\n⚠️ 修复后仍有问题，请检查日志。")
    
    print("\n📋 修复说明:")
    print("1. 为缺少冷却期的已减半股票设置365天冷却期")
    print("2. 将策略模式设置为 'cooling_down'")
    print("3. 添加基本的减半历史记录")
    print("4. 设置监控状态为 'cooling_down'")

if __name__ == '__main__':
    main()
