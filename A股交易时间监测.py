# A股交易时间监测模块
# 功能：智能判断当前是否为A股交易时间，自动获取节假日信息

import datetime
import requests
import json
from typing import Tuple, List

class AStockTradingTimeMonitor:
    """A股交易时间监测器 - 智能版本"""

    def __init__(self):
        # A股交易时间配置（包含集合竞价）
        self.trading_sessions = [
            # 集合竞价时间
            (datetime.time(9, 15), datetime.time(9, 25), "集合竞价"),
            # 连续竞价时间
            (datetime.time(9, 30), datetime.time(11, 30), "上午交易"),
            (datetime.time(13, 0), datetime.time(15, 0), "下午交易"),
            # 尾盘集合竞价
            (datetime.time(14, 57), datetime.time(15, 0), "尾盘集合竞价")
        ]

        # 缓存节假日数据
        self.holiday_cache = {}
        self.cache_date = None

        # 节假日API列表（多个备用）
        self.holiday_apis = [
            "http://timor.tech/api/holiday/year/",  # 免费节假日API
            "https://api.apihubs.cn/holiday/get"    # 备用API
        ]

    def get_holidays_from_api(self, year: int) -> List[str]:
        """
        从API获取指定年份的节假日
        :param year: 年份
        :return: 节假日列表 ['2025-01-01', '2025-01-28', ...]
        """
        holidays = []

        # 尝试第一个API
        try:
            url = f"{self.holiday_apis[0]}{year}"
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                data = response.json()
                if 'holiday' in data:
                    for date_str, info in data['holiday'].items():
                        if info.get('holiday', False):  # 是节假日
                            holidays.append(date_str)
                    print(f"✅ 成功从API获取{year}年节假日信息，共{len(holidays)}天")
                    return holidays
        except Exception as e:
            print(f"⚠️ 第一个节假日API失败: {e}")

        # 如果API失败，使用固定的主要节假日规则
        print(f"⚠️ API获取失败，使用固定节假日规则")
        return self.get_fixed_holidays(year)

    def get_fixed_holidays(self, year: int) -> List[str]:
        """
        获取固定的主要节假日（当API不可用时使用）
        :param year: 年份
        :return: 节假日列表
        """
        holidays = []

        # 元旦（1月1日）
        holidays.append(f"{year}-01-01")

        # 春节（农历正月初一前后，通常1月底或2月）
        # 这里使用近似规则，实际应该查农历
        if year == 2025:
            holidays.extend([
                "2025-01-28", "2025-01-29", "2025-01-30", "2025-01-31",
                "2025-02-01", "2025-02-02", "2025-02-03", "2025-02-04"
            ])

        # 清明节（4月4日或5日）
        holidays.extend([f"{year}-04-04", f"{year}-04-05", f"{year}-04-06"])

        # 劳动节（5月1日）
        holidays.extend([f"{year}-05-01", f"{year}-05-02", f"{year}-05-03"])

        # 国庆节（10月1日-7日）
        for day in range(1, 8):
            holidays.append(f"{year}-10-{day:02d}")

        return holidays

    def get_holidays(self, year: int) -> List[datetime.date]:
        """
        获取指定年份的节假日列表
        :param year: 年份
        :return: 节假日日期对象列表
        """
        # 检查缓存
        cache_key = year
        today = datetime.date.today()

        if (cache_key in self.holiday_cache and
            self.cache_date and
            (today - self.cache_date).days < 30):  # 缓存30天
            return self.holiday_cache[cache_key]

        # 从API获取节假日
        holiday_strings = self.get_holidays_from_api(year)

        # 转换为日期对象
        holiday_dates = []
        for date_str in holiday_strings:
            try:
                date_obj = datetime.datetime.strptime(date_str, "%Y-%m-%d").date()
                holiday_dates.append(date_obj)
            except:
                continue

        # 缓存结果
        self.holiday_cache[cache_key] = holiday_dates
        self.cache_date = today

        return holiday_dates
    
    def is_trading_day(self, check_date: datetime.date = None) -> bool:
        """
        判断是否为交易日
        :param check_date: 要检查的日期，默认为今天
        :return: True表示交易日，False表示非交易日
        """
        if check_date is None:
            check_date = datetime.date.today()

        # 检查是否为周末
        if check_date.weekday() >= 5:  # 周六=5, 周日=6
            return False

        # 获取当年的节假日
        holidays = self.get_holidays(check_date.year)

        # 检查是否为节假日
        if check_date in holidays:
            return False

        return True
    
    def is_trading_time(self, check_time: datetime.datetime = None, include_auction: bool = True) -> bool:
        """
        判断是否为交易时间
        :param check_time: 要检查的时间，默认为当前时间
        :param include_auction: 是否包含集合竞价时间
        :return: True表示交易时间，False表示非交易时间
        """
        if check_time is None:
            check_time = datetime.datetime.now()

        # 首先检查是否为交易日
        if not self.is_trading_day(check_time.date()):
            return False

        # 检查是否在交易时间段内
        current_time = check_time.time()

        for start_time, end_time, session_type in self.trading_sessions:
            if start_time <= current_time <= end_time:
                # 如果不包含集合竞价，跳过集合竞价时间
                if not include_auction and "集合竞价" in session_type:
                    continue
                return True

        return False

    def get_current_session(self, check_time: datetime.datetime = None) -> str:
        """
        获取当前交易时段
        :param check_time: 要检查的时间，默认为当前时间
        :return: 交易时段描述
        """
        if check_time is None:
            check_time = datetime.datetime.now()

        if not self.is_trading_day(check_time.date()):
            return "休市"

        current_time = check_time.time()

        for start_time, end_time, session_type in self.trading_sessions:
            if start_time <= current_time <= end_time:
                return session_type

        return "休市"
    
    def get_next_trading_time(self) -> Tuple[datetime.datetime, str]:
        """
        获取下一个交易时间
        :return: (下一个交易开始时间, 描述信息)
        """
        now = datetime.datetime.now()
        today = now.date()
        current_time = now.time()

        # 如果今天是交易日
        if self.is_trading_day(today):
            # 检查今天的交易时间段
            for start_time, end_time, session_type in self.trading_sessions:
                if current_time < start_time:
                    # 还没到这个交易时间段
                    next_trading = datetime.datetime.combine(today, start_time)
                    return next_trading, f"今日{session_type}"
                elif start_time <= current_time <= end_time:
                    # 正在交易时间内
                    return now, f"正在{session_type}"

        # 寻找下一个交易日
        check_date = today + datetime.timedelta(days=1)
        while not self.is_trading_day(check_date):
            check_date += datetime.timedelta(days=1)

        # 下一个交易日的集合竞价时间
        next_trading = datetime.datetime.combine(check_date, datetime.time(9, 15))
        return next_trading, f"{check_date.strftime('%m月%d日')}集合竞价"
    
    def get_trading_status(self) -> dict:
        """
        获取当前交易状态信息
        :return: 包含交易状态的字典
        """
        now = datetime.datetime.now()
        is_trading = self.is_trading_time(now)
        current_session = self.get_current_session(now)
        next_time, next_desc = self.get_next_trading_time()

        if is_trading:
            # 计算距离当前时段结束的时间
            current_time = now.time()

            # 找到当前时段的结束时间
            end_time = None
            for start_time, session_end_time, _ in self.trading_sessions:
                if start_time <= current_time <= session_end_time:
                    end_time = session_end_time
                    break

            if end_time:
                close_time = datetime.datetime.combine(now.date(), end_time)
                time_to_close = close_time - now
            else:
                time_to_close = datetime.timedelta(0)

            return {
                'is_trading': True,
                'status': '交易中',
                'session': current_session,
                'time_to_close': str(time_to_close).split('.')[0],  # 去掉微秒
                'next_trading_time': None,
                'message': f'正在{current_session}，距离结束还有{str(time_to_close).split(".")[0]}'
            }
        else:
            time_to_next = next_time - now
            return {
                'is_trading': False,
                'status': '休市中',
                'session': current_session,
                'time_to_close': None,
                'next_trading_time': next_time.strftime('%Y-%m-%d %H:%M:%S'),
                'message': f'休市中，距离{next_desc}还有{str(time_to_next).split(".")[0]}'
            }
    
    def should_update_data(self, include_auction: bool = False) -> bool:
        """
        判断是否应该更新股票数据
        :param include_auction: 是否在集合竞价时间也更新数据
        :return: True表示应该更新，False表示不应该更新
        """
        return self.is_trading_time(include_auction=include_auction)

    def get_update_interval(self) -> int:
        """
        根据交易状态获取建议的更新间隔（秒）
        :return: 更新间隔秒数
        """
        current_session = self.get_current_session()

        if "集合竞价" in current_session:
            return 10  # 集合竞价时间10秒更新一次
        elif self.is_trading_time(include_auction=False):
            return 30  # 连续竞价时间30秒更新一次
        else:
            return 300  # 休市时间5分钟检查一次（而不是1小时）

# 测试函数
def test_trading_time_monitor():
    """测试交易时间监测功能"""
    monitor = AStockTradingTimeMonitor()
    
    print("=== A股交易时间监测测试 ===")
    print(f"当前时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 获取交易状态
    status = monitor.get_trading_status()
    print("交易状态信息:")
    for key, value in status.items():
        print(f"  {key}: {value}")
    print()
    
    # 测试几个特定时间
    test_times = [
        datetime.datetime(2025, 7, 21, 9, 0),   # 开盘前
        datetime.datetime(2025, 7, 21, 9, 20),  # 集合竞价时间
        datetime.datetime(2025, 7, 21, 10, 0),  # 上午交易时间
        datetime.datetime(2025, 7, 21, 12, 0),  # 午休时间
        datetime.datetime(2025, 7, 21, 14, 0),  # 下午交易时间
        datetime.datetime(2025, 7, 21, 14, 58), # 尾盘集合竞价
        datetime.datetime(2025, 7, 21, 16, 0),  # 收盘后
        datetime.datetime(2025, 7, 19, 10, 0),  # 周六
    ]

    print("特定时间测试:")
    for test_time in test_times:
        is_trading = monitor.is_trading_time(test_time)
        session = monitor.get_current_session(test_time)
        weekday = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'][test_time.weekday()]
        print(f"  {test_time.strftime('%Y-%m-%d %H:%M')} ({weekday}): {session} {'✅' if is_trading else '❌'}")

    print()
    print(f"是否应该更新数据: {'是' if monitor.should_update_data() else '否'}")
    print(f"是否应该更新数据(含集合竞价): {'是' if monitor.should_update_data(include_auction=True) else '否'}")
    print(f"建议更新间隔: {monitor.get_update_interval()}秒")

if __name__ == "__main__":
    test_trading_time_monitor()
