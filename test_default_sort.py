#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试默认排序设置
"""

import requests
import json
import time

def test_default_sort():
    """测试默认排序是否将已清仓股票放在最下面"""
    print("🧪 测试默认排序设置...")
    
    # 等待服务器启动
    for i in range(5):
        try:
            response = requests.get('http://localhost:5000/api/stocks', timeout=5)
            break
        except requests.exceptions.ConnectionError:
            print(f"等待服务器启动... ({i+1}/5)")
            time.sleep(2)
    else:
        print("❌ 无法连接到服务器")
        return False
    
    if response.status_code == 200:
        data = response.json()
        
        if isinstance(data.get('data'), list):
            stocks = data['data']
            print(f"✅ 获取到 {len(stocks)} 只股票")
            
            # 分析股票状态分布
            cleared_stocks = []
            reduced_stocks = []
            normal_stocks = []
            
            for stock in stocks:
                if stock.get('is_cleared', False):
                    cleared_stocks.append({
                        'code': stock.get('code'),
                        'name': stock.get('name', ''),
                        'sell_signal': stock.get('sell_signal', ''),
                        'market_value': stock.get('market_value', 0)
                    })
                elif stock.get('is_reduced', False):
                    reduced_stocks.append({
                        'code': stock.get('code'),
                        'name': stock.get('name', ''),
                        'strategy_mode': stock.get('strategy_mode', ''),
                        'sell_signal': stock.get('sell_signal', ''),
                        'market_value': stock.get('market_value', 0)
                    })
                else:
                    normal_stocks.append({
                        'code': stock.get('code'),
                        'name': stock.get('name', ''),
                        'sell_signal': stock.get('sell_signal', ''),
                        'market_value': stock.get('market_value', 0)
                    })
            
            print(f"\n📊 股票状态分布:")
            print(f"  - 已清仓股票: {len(cleared_stocks)}只")
            print(f"  - 已减半股票: {len(reduced_stocks)}只")
            print(f"  - 正常持仓股票: {len(normal_stocks)}只")
            
            # 显示已清仓股票信息
            if cleared_stocks:
                print(f"\n🛑 已清仓股票列表:")
                for stock in cleared_stocks:
                    print(f"    {stock['code']} {stock['name']} - 信号: {stock['sell_signal']}")
            
            # 显示已减半股票信息（冷却期状态）
            if reduced_stocks:
                print(f"\n🧊 已减半股票列表:")
                for stock in reduced_stocks:
                    print(f"    {stock['code']} {stock['name']} - 策略: {stock['strategy_mode']}")
            
            # 模拟前端排序逻辑
            print(f"\n🔧 模拟前端排序逻辑...")
            
            def get_sell_signal_priority(stock):
                """模拟前端的getSellSignalPriority函数（修复后版本）"""
                # 🛑 最低优先级 - 已完全退出（放在最后）
                if stock.get('is_cleared', False):
                    return 999  # 已清仓 - 最低优先级

                # 🧊 已减半股票 - 根据策略模式设置优先级
                if stock.get('is_reduced', False):
                    strategy_mode = stock.get('strategy_mode', 'normal')
                    if strategy_mode == 'negative_ttm':
                        return 15  # ⚠️ 负TTM策略 - 需要关注
                    elif strategy_mode == 'high_ttm':
                        return 16  # 📊 高TTM策略 - 需要关注
                    elif strategy_mode == 'cooling_down':
                        return 17  # 🧊 冷却期 - 正常监控
                    elif strategy_mode == 'normal':
                        return 18  # ✅ 正常监控
                    else:
                        return 19  # 🔄 其他状态

                # 🚨 系统卖出信号
                sell_signal = stock.get('sell_signal', '')
                if sell_signal == 'clearance':
                    return 1
                elif sell_signal == 'reduce':
                    return 2
                elif sell_signal == 'prepare_clearance':
                    return 3
                elif sell_signal == 'prepare_reduce':
                    return 4
                elif sell_signal == 'sell':
                    return 5
                elif sell_signal == 'warning':
                    return 6
                elif sell_signal == 'hold':
                    return 50
                else:
                    return 60
            
            # 按默认排序规则排序
            sorted_stocks = sorted(stocks, key=lambda s: (
                get_sell_signal_priority(s),  # 第一优先级：卖出信号优先级
                -s.get('market_value', 0)     # 第二优先级：市值降序
            ))
            
            print(f"\n📋 排序后前10只股票:")
            for i, stock in enumerate(sorted_stocks[:10]):
                priority = get_sell_signal_priority(stock)
                status = "🛑已清仓" if stock.get('is_cleared') else "🧊已减半" if stock.get('is_reduced') else "📈正常"
                print(f"  {i+1:2d}. {stock.get('code')} {stock.get('name', '')[:8]:8s} - {status} (优先级:{priority})")
            
            print(f"\n📋 排序后最后10只股票:")
            for i, stock in enumerate(sorted_stocks[-10:], len(sorted_stocks)-9):
                priority = get_sell_signal_priority(stock)
                status = "🛑已清仓" if stock.get('is_cleared') else "🧊已减半" if stock.get('is_reduced') else "📈正常"
                print(f"  {i:2d}. {stock.get('code')} {stock.get('name', '')[:8]:8s} - {status} (优先级:{priority})")
            
            # 验证已清仓股票是否在最后
            last_10_cleared_count = sum(1 for stock in sorted_stocks[-10:] if stock.get('is_cleared', False))
            total_cleared_count = len(cleared_stocks)
            
            print(f"\n✅ 排序验证结果:")
            print(f"  - 总共已清仓股票: {total_cleared_count}只")
            print(f"  - 最后10只中已清仓: {last_10_cleared_count}只")
            
            if total_cleared_count > 0:
                # 检查所有已清仓股票是否都在后面
                cleared_positions = []
                for i, stock in enumerate(sorted_stocks):
                    if stock.get('is_cleared', False):
                        cleared_positions.append(i + 1)
                
                if cleared_positions:
                    min_cleared_pos = min(cleared_positions)
                    max_cleared_pos = max(cleared_positions)
                    print(f"  - 已清仓股票位置范围: {min_cleared_pos} - {max_cleared_pos}")
                    
                    # 检查是否所有已清仓股票都在最后
                    expected_start = len(sorted_stocks) - total_cleared_count + 1
                    if min_cleared_pos >= expected_start:
                        print(f"  ✅ 已清仓股票正确排在最后")
                        return True
                    else:
                        print(f"  ❌ 已清仓股票没有完全排在最后")
                        return False
            else:
                print(f"  ✅ 没有已清仓股票，排序正常")
                return True
        else:
            print(f"❌ API返回格式错误")
            return False
    else:
        print(f"❌ API调用失败: HTTP {response.status_code}")
        return False

def main():
    print("🔧 测试默认排序设置...")
    print("=" * 60)
    
    success = test_default_sort()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 默认排序设置正确！")
        print("✅ 已清仓股票会自动排在最下面")
        print("\n💡 现在刷新页面，应该能看到:")
        print("  1. 需要立即行动的股票在最上面")
        print("  2. 已减半股票（冷却期）在中间")
        print("  3. 已清仓股票在最下面")
    else:
        print("⚠️ 默认排序可能有问题，需要进一步检查。")

if __name__ == '__main__':
    main()
