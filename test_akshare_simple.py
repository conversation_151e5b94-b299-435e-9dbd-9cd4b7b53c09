#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import akshare as ak
import pandas as pd

def test_akshare():
    """测试您提供的akshare调用"""
    
    try:
        print("🔍 测试akshare获取000001历史数据...")
        print("📅 参数: symbol='000001', start_date='20170301', end_date='20240528'")
        
        # 使用您提供的完全相同的参数
        df = ak.stock_zh_a_hist(
            symbol="000001", 
            period="daily", 
            start_date="20170301", 
            end_date='20240528', 
            adjust="qfq"
        )
        
        if df is not None and not df.empty:
            print(f"✅ 成功获取数据！")
            print(f"📊 数据形状: {df.shape}")
            print(f"📋 列名: {list(df.columns)}")
            print(f"📈 前5行数据:")
            print(df.head())
            print(f"📈 后5行数据:")
            print(df.tail())
            
            # 查找最低价
            if '最低' in df.columns:
                min_low_idx = df['最低'].idxmin()
                yearly_low = df.loc[min_low_idx, '最低']
                low_date = df.loc[min_low_idx, '日期']
                print(f"💰 历史最低价: {yearly_low}, 日期: {low_date}")
            
            return True
        else:
            print("❌ 返回空数据")
            return False
            
    except Exception as e:
        print(f"❌ akshare测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_akshare()
