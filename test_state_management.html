<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>状态管理功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .test-button {
            background: #2196F3;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            display: inline-block;
        }
        .test-button:hover {
            background: #1976D2;
        }
        .test-button.success {
            background: #4CAF50;
        }
        .test-button.success:hover {
            background: #45a049;
        }
        .test-button.warning {
            background: #ff9800;
        }
        .test-button.warning:hover {
            background: #f57c00;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            display: none;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .result.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 状态管理功能测试</h1>
        
        <div class="instructions">
            <strong>📋 测试说明：</strong>
            <ol>
                <li>确保持仓系统正在运行（http://localhost:5000）</li>
                <li>按顺序点击下面的测试按钮</li>
                <li>观察每个功能是否正常工作</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🔍 1. 测试API连接</h3>
            <p>首先测试与持仓系统的连接是否正常</p>
            <button class="test-button" onclick="testConnection()">测试连接</button>
            <div id="connectionResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>💾 2. 测试导出状态功能</h3>
            <p>测试是否能正常导出股票状态</p>
            <button class="test-button success" onclick="testExportStates()">测试导出状态</button>
            <div id="exportResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔄 3. 测试自动恢复功能</h3>
            <p>测试自动恢复功能是否正常</p>
            <button class="test-button warning" onclick="testAutoRestore()">测试自动恢复</button>
            <div id="restoreResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📊 4. 检查当前股票数据</h3>
            <p>查看当前系统中的股票数据状态</p>
            <button class="test-button" onclick="checkStockData()">检查股票数据</button>
            <div id="stockDataResult" class="result"></div>
        </div>
    </div>

    <script>
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        async function testConnection() {
            try {
                showResult('connectionResult', '正在测试连接...', 'info');
                
                const response = await fetch('http://localhost:5000/api/stock-data');
                if (response.ok) {
                    showResult('connectionResult', '✅ 连接成功！持仓系统运行正常', 'success');
                } else {
                    showResult('connectionResult', `❌ 连接失败：HTTP ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('connectionResult', `❌ 连接失败：${error.message}`, 'error');
            }
        }

        async function testExportStates() {
            try {
                showResult('exportResult', '正在测试导出功能...', 'info');
                
                const response = await fetch('http://localhost:5000/api/export_stock_states');
                const result = await response.json();
                
                if (result.success) {
                    showResult('exportResult', `✅ 导出功能正常！${result.message}`, 'success');
                } else {
                    showResult('exportResult', `❌ 导出失败：${result.message}`, 'error');
                }
            } catch (error) {
                showResult('exportResult', `❌ 导出测试失败：${error.message}`, 'error');
            }
        }

        async function testAutoRestore() {
            try {
                showResult('restoreResult', '正在测试自动恢复功能...', 'info');
                
                const response = await fetch('http://localhost:5000/api/auto_restore_states');
                const result = await response.json();
                
                if (result.success) {
                    showResult('restoreResult', `✅ 自动恢复功能正常！${result.message}`, 'success');
                } else {
                    showResult('restoreResult', `⚠️ 自动恢复结果：${result.message}`, 'info');
                }
            } catch (error) {
                showResult('restoreResult', `❌ 自动恢复测试失败：${error.message}`, 'error');
            }
        }

        async function checkStockData() {
            try {
                showResult('stockDataResult', '正在检查股票数据...', 'info');
                
                const response = await fetch('http://localhost:5000/api/stock-data');
                const data = await response.json();
                
                if (data && typeof data === 'object') {
                    const stockCount = Object.keys(data).length;
                    const specialStates = Object.values(data).filter(stock => 
                        stock.sell_signal === 'halved' || stock.sell_signal === 'cleared' || stock.halved_quantity > 0
                    ).length;
                    
                    showResult('stockDataResult', 
                        `✅ 股票数据正常！共 ${stockCount} 只股票，其中 ${specialStates} 只有特殊状态`, 
                        'success'
                    );
                } else {
                    showResult('stockDataResult', '⚠️ 股票数据为空或格式异常', 'info');
                }
            } catch (error) {
                showResult('stockDataResult', `❌ 检查股票数据失败：${error.message}`, 'error');
            }
        }

        // 页面加载时自动测试连接
        window.onload = function() {
            setTimeout(testConnection, 1000);
        };
    </script>
</body>
</html>
