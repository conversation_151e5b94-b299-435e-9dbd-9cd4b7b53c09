import requests
import re
import json

def get_saolei_score(code):
    """获取扫雷宝真实分数"""
    try:
        # 扫雷宝API接口
        api_url = f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code={code}&color=0"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Referer': 'http://page3.tdx.com.cn:7615/',
        }
        
        print(f"🔍 获取股票 {code} 的扫雷宝分数...")
        response = requests.get(api_url, headers=headers, timeout=15)
        response.encoding = 'utf-8'
        
        if response.status_code == 200:
            content = response.text
            print(f"✅ 响应成功，内容长度: {len(content)}")
            
            # 方法1: 查找JSON数据接口
            # 从HTML中提取JSON数据URL模式
            json_pattern = r'getJSON\("([^"]+)"'
            json_match = re.search(json_pattern, content)

            if json_match:
                json_url_pattern = json_match.group(1)
                print(f"🎯 找到JSON接口模式: {json_url_pattern}")

                # 构建完整的JSON URL，替换股票代码
                if 'json/' in json_url_pattern:
                    base_url = "http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/"
                    # 替换模式中的股票代码占位符
                    json_url = json_url_pattern.replace('"+e.code+"', code)
                    full_json_url = base_url + f"json/{code}.json"

                    print(f"📡 请求JSON数据: {full_json_url}")
                    json_response = requests.get(full_json_url, headers=headers, timeout=10)

                    if json_response.status_code == 200:
                        try:
                            json_data = json_response.json()
                            print(f"📊 JSON数据: {json.dumps(json_data, ensure_ascii=False, indent=2)}")

                            # 从JSON数据中提取分数
                            if 'score' in json_data:
                                return float(json_data['score'])
                            elif 'total' in json_data and 'num' in json_data:
                                # 计算安全分数：(总检查项 - 风险项) / 总检查项 * 100
                                total = json_data['total']
                                risk_num = json_data['num']
                                if total > 0:
                                    score = ((total - risk_num) / total) * 100
                                    print(f"✅ 计算得出分数: {score:.1f} (总检查:{total}, 风险:{risk_num})")
                                    return score
                        except json.JSONDecodeError as e:
                            print(f"❌ JSON解析失败: {e}")
                    else:
                        print(f"❌ JSON请求失败: {json_response.status_code}")
            
            # 方法2: 从HTML中直接提取分数
            # 查找JavaScript中的分数变量
            score_patterns = [
                r'realvalue\s*=\s*(\d{1,3})',
                r'showvalue\s*=\s*(\d{1,3})',
                r'max\s*=\s*(\d{1,3})',
                r'score["\']?\s*[:=]\s*(\d{1,3})',
                r'安全分["\']?\s*[:=]\s*(\d{1,3})',
            ]
            
            for pattern in score_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    for match in matches:
                        score = float(match)
                        if 50 <= score <= 100:
                            print(f"✅ 从HTML提取分数: {score}")
                            return score
            
            # 方法3: 查找所有可能的分数
            all_numbers = re.findall(r'\b(\d{2,3})\b', content)
            valid_scores = []
            for num_str in all_numbers:
                try:
                    num = float(num_str)
                    if 50 <= num <= 100:
                        valid_scores.append(num)
                except ValueError:
                    continue
            
            if valid_scores:
                # 返回最常见的分数或最高分数
                from collections import Counter
                score_counts = Counter(valid_scores)
                most_common_score = score_counts.most_common(1)[0][0]
                print(f"✅ 最常见分数: {most_common_score} (出现{score_counts[most_common_score]}次)")
                return most_common_score
            
            print("❌ 未找到有效分数")
            return None
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        return None

if __name__ == '__main__':
    # 测试几个股票代码
    test_codes = ['000001', '600000', '000002']
    for code in test_codes:
        print(f"\n{'='*60}")
        score = get_saolei_score(code)
        print(f"股票 {code} 的扫雷宝分数: {score}")
        print(f"{'='*60}")
