# 卖出信号优先级控制功能说明

## 🎯 功能概述

卖出信号优先级控制功能允许用户设置手动标记的卖出信号是否优先于系统自动生成的卖出信号。这个功能解决了用户手动设置的状态被系统自动更新覆盖的问题。

## ✨ 主要特性

### 1. 优先级控制开关
- **启用手动优先**：手动标记的卖出信号（如"已清仓"、"已减半"）优先于自动生成的信号
- **禁用手动优先**：系统始终使用最新的自动计算结果，可能覆盖手动标记
- **默认设置**：启用手动优先（推荐设置）

### 2. 状态保护机制
- 当启用手动优先时，系统会保护以下手动设置的状态：
  - 自定义状态（custom_status）
  - 已清仓状态（is_cleared）
  - 已减半状态（is_reduced）
  - 相关的时间戳和显示信息

### 3. 全面保护范围
- **批量数据更新**：定时自动更新所有股票数据时保护手动状态
- **单个股票更新**：点击单个股票更新按钮时保护手动状态
- **系统重启**：系统重启后手动状态保持不变
- **配置切换**：可随时启用或禁用保护机制

### 4. 智能判断逻辑
- 系统会检查是否存在手动状态设置
- 根据优先级配置决定是保护手动状态还是使用自动计算结果
- 确保在冲突时按照用户配置的优先级执行

## 🖥️ 用户界面

### 1. 优先级控制按钮
- 位置：数据管理按钮区域
- 显示：🎯 优先级控制
- 状态指示：
  - ✅ 表示手动优先级已启用
  - ⚠️ 表示手动优先级已禁用

### 2. 优先级控制面板
- 功能说明：详细解释优先级控制的作用
- 开关控制：直观的切换开关
- 状态显示：实时显示当前设置状态
- 保存按钮：应用设置更改

## 🔧 技术实现

### 1. 后端实现

#### 配置管理
```python
# 优先级配置结构
SELL_SIGNAL_PRIORITY_CONFIG = {
    'manual_priority_over_auto': True,  # 手动优先级开关
    'config_file': 'sell_signal_priority_config.json',
    'last_updated': None,
    'version': '1.0'
}
```

#### 优先级管理器
- `SellSignalPriorityManager` 类负责配置管理
- 支持配置的加载、保存和更新
- 提供优先级判断接口

#### 卖出信号计算
- 修改 `calculate_sell_signal()` 函数支持优先级判断
- 在自动策略计算前检查手动状态
- 根据优先级配置决定返回结果

#### 状态保护机制
- 在 `update_single_stock()` 函数中实现状态保护
- 检查是否存在手动状态设置
- 保护手动设置的字段不被覆盖

### 2. API接口

#### 获取优先级配置
```
GET /api/sell-signal-priority
```

#### 设置优先级配置
```
POST /api/sell-signal-priority
Content-Type: application/json

{
    "manual_priority_over_auto": true
}
```

### 3. 前端实现

#### 优先级控制面板
- 响应式设计，支持移动端
- 直观的开关控件
- 实时状态反馈
- 详细的功能说明

#### JavaScript功能
- `openPriorityPanel()` - 打开控制面板
- `savePrioritySettings()` - 保存设置
- `updatePriorityButtonStatus()` - 更新按钮状态
- `initializePriorityButton()` - 初始化按钮

## 📋 使用方法

### 1. 打开优先级控制面板
1. 在持仓系统主页面找到"🎯 优先级控制"按钮
2. 点击按钮打开优先级控制面板

### 2. 配置优先级设置
1. 查看当前设置状态（启用/禁用）
2. 根据需要切换开关状态
3. 阅读功能说明了解影响
4. 点击"保存设置"应用更改

### 3. 验证设置效果
1. 设置保存后会显示确认消息
2. 按钮状态会实时更新（✅/⚠️）
3. 新的优先级规则立即生效

## ⚠️ 注意事项

### 1. 推荐设置
- **建议启用手动优先级**：避免手动操作被意外覆盖
- 只有在需要系统始终使用最新计算结果时才禁用

### 2. 影响范围
- 只影响卖出信号的优先级判断
- 不影响其他股票数据的更新
- 不影响企业微信提醒功能

### 3. 配置持久化
- 设置会自动保存到配置文件
- 系统重启后设置保持不变
- 支持配置备份和恢复

## 🧪 测试验证

### 1. 功能测试
运行测试脚本验证功能：

**基础功能测试**：
```bash
python test_priority_control.py
```

**单个股票更新保护测试**：
```bash
python test_single_stock_update_protection.py
```

### 2. 测试场景
- 优先级配置的获取和设置
- 配置更改的持久化
- 手动状态的保护机制
- 单个股票更新时的状态保护
- 与现有功能的兼容性

## 🔄 版本信息

- **版本**: 1.0
- **发布日期**: 2025-07-24
- **兼容性**: 持仓系统 V14+
- **依赖**: 无额外依赖

## 📞 技术支持

如果在使用过程中遇到问题：
1. 检查浏览器控制台是否有错误信息
2. 确认系统版本是否支持此功能
3. 运行测试脚本验证功能状态
4. 查看系统日志获取详细错误信息

---

**注意**: 此功能是持仓系统的重要增强功能，建议在生产环境使用前进行充分测试。
