#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试企业微信机器人功能
"""

import requests
import json
from datetime import datetime

def test_wechat_bot():
    """测试企业微信机器人"""
    
    # 您的企业微信机器人webhook地址
    webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7bddd706-ef0f-4d6c-9e1f-3cc8a0a18d9e"
    
    print("=== 测试企业微信机器人 ===")
    print(f"Webhook URL: {webhook_url}")
    
    # 测试消息内容
    test_message = f"""🤖 股票监控系统测试消息

⏰ 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
📈 功能: 企业微信提醒测试
🚀 状态: 连接正常

💡 当股票距离最低点涨幅超过70%时，系统将自动发送提醒！

📱 数据来源: 东方财富网"""
    
    # 构建请求数据
    headers = {
        "Content-Type": "application/json"
    }
    
    data = {
        "msgtype": "text",
        "text": {
            "content": test_message,
            "mentioned_list": ["@all"]  # @所有人
        }
    }
    
    try:
        print("正在发送测试消息...")
        response = requests.post(webhook_url, headers=headers, json=data, timeout=10)
        
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应结果: {result}")
            
            if result.get('errcode') == 0:
                print("✅ 企业微信消息发送成功！")
                return True
            else:
                print(f"❌ 企业微信消息发送失败: {result}")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 发送消息异常: {e}")
        return False

def test_markdown_message():
    """测试Markdown格式消息"""
    
    webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7bddd706-ef0f-4d6c-9e1f-3cc8a0a18d9e"
    
    print("\n=== 测试Markdown消息 ===")
    
    # Markdown消息内容
    markdown_content = f"""# 📈 股票监控系统测试

**⏰ 测试时间:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 🚀 高涨幅股票模拟提醒

### 📊 发现高涨幅股票:
1. **000001 平安银行** +75.2%
2. **600519 贵州茅台** +82.1%

### 💰 详细信息:
- 当前价: 15.50 | 年内最低: 9.00
- 距离最低点涨幅: **+75.2%**

---
💡 **提醒:** 这是测试消息，实际数据以系统推送为准！
"""
    
    headers = {
        "Content-Type": "application/json"
    }
    
    data = {
        "msgtype": "markdown",
        "markdown": {
            "content": markdown_content
        }
    }
    
    try:
        print("正在发送Markdown测试消息...")
        response = requests.post(webhook_url, headers=headers, json=data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应结果: {result}")
            
            if result.get('errcode') == 0:
                print("✅ Markdown消息发送成功！")
                return True
            else:
                print(f"❌ Markdown消息发送失败: {result}")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 发送Markdown消息异常: {e}")
        return False

def test_high_gain_alert():
    """测试高涨幅提醒消息"""
    
    webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7bddd706-ef0f-4d6c-9e1f-3cc8a0a18d9e"
    
    print("\n=== 测试高涨幅提醒消息 ===")
    
    # 模拟高涨幅股票数据
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    message = f"""🚀 股票高涨幅提醒 🚀

⏰ 时间: {current_time}
📈 阈值: 距离最低点涨幅 > 70%
📊 发现 2 只高涨幅股票:

1. 000001 平安银行
   💰 当前价: 15.50
   📉 年内最低: 9.00 (2024-02-15)
   🚀 距最低点涨幅: +72.2%

2. 600519 贵州茅台
   💰 当前价: 1850.00
   📉 年内最低: 1500.00 (2024-01-10)
   🚀 距最低点涨幅: +23.3%

📱 数据来源: 东方财富网"""
    
    headers = {
        "Content-Type": "application/json"
    }
    
    data = {
        "msgtype": "text",
        "text": {
            "content": message,
            "mentioned_list": ["@all"]
        }
    }
    
    try:
        print("正在发送高涨幅提醒测试消息...")
        response = requests.post(webhook_url, headers=headers, json=data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应结果: {result}")
            
            if result.get('errcode') == 0:
                print("✅ 高涨幅提醒消息发送成功！")
                return True
            else:
                print(f"❌ 高涨幅提醒消息发送失败: {result}")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 发送高涨幅提醒消息异常: {e}")
        return False

def main():
    """主函数"""
    print("=== Zz.大A机器人功能测试 ===")
    
    # 测试基本文本消息
    success1 = test_wechat_bot()
    
    # 等待一下
    import time
    time.sleep(2)
    
    # 测试Markdown消息
    success2 = test_markdown_message()
    
    # 等待一下
    time.sleep(2)
    
    # 测试高涨幅提醒消息
    success3 = test_high_gain_alert()
    
    print(f"\n=== 测试结果汇总 ===")
    print(f"基本文本消息: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"Markdown消息: {'✅ 成功' if success2 else '❌ 失败'}")
    print(f"高涨幅提醒消息: {'✅ 成功' if success3 else '❌ 失败'}")
    
    if success1 and success2 and success3:
        print("\n🎉 所有测试通过！企业微信机器人功能正常！")
    else:
        print("\n⚠️  部分测试失败，请检查配置！")

if __name__ == "__main__":
    main()
