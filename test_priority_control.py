#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
卖出信号优先级控制功能测试脚本
测试各种场景下的优先级控制功能，确保不影响现有逻辑
"""

import requests
import time
import json

def test_priority_control():
    """测试卖出信号优先级控制功能"""
    base_url = 'http://localhost:5000'
    
    print("🧪 开始测试卖出信号优先级控制功能")
    print("=" * 60)
    
    # 测试1: 获取当前优先级配置
    print("\n📋 测试1: 获取当前优先级配置")
    try:
        response = requests.get(f'{base_url}/api/sell-signal-priority')
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                config = result['data']
                print(f"✅ 获取配置成功:")
                print(f"   手动优先级: {config.get('manual_priority_over_auto', 'unknown')}")
                print(f"   配置版本: {config.get('version', 'unknown')}")
                print(f"   最后更新: {config.get('last_updated', 'unknown')}")
                original_setting = config.get('manual_priority_over_auto', True)
            else:
                print(f"❌ 获取配置失败: {result.get('message')}")
                return False
        else:
            print(f"❌ API请求失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取配置异常: {e}")
        return False
    
    # 测试2: 设置优先级配置（启用）
    print("\n📋 测试2: 设置优先级配置（启用手动优先）")
    try:
        response = requests.post(f'{base_url}/api/sell-signal-priority', 
                               json={'manual_priority_over_auto': True})
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 设置成功: {result['message']}")
                print(f"   更新时间: {result['data']['updated_time']}")
            else:
                print(f"❌ 设置失败: {result.get('message')}")
                return False
        else:
            print(f"❌ API请求失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 设置配置异常: {e}")
        return False
    
    # 测试3: 验证配置是否生效
    print("\n📋 测试3: 验证配置是否生效")
    time.sleep(1)  # 等待配置生效
    try:
        response = requests.get(f'{base_url}/api/sell-signal-priority')
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                config = result['data']
                if config.get('manual_priority_over_auto') == True:
                    print("✅ 配置验证成功: 手动优先级已启用")
                else:
                    print("❌ 配置验证失败: 手动优先级未启用")
                    return False
            else:
                print(f"❌ 验证失败: {result.get('message')}")
                return False
        else:
            print(f"❌ API请求失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 验证配置异常: {e}")
        return False
    
    # 测试4: 设置优先级配置（禁用）
    print("\n📋 测试4: 设置优先级配置（禁用手动优先）")
    try:
        response = requests.post(f'{base_url}/api/sell-signal-priority', 
                               json={'manual_priority_over_auto': False})
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 设置成功: {result['message']}")
            else:
                print(f"❌ 设置失败: {result.get('message')}")
                return False
        else:
            print(f"❌ API请求失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 设置配置异常: {e}")
        return False
    
    # 测试5: 验证禁用配置
    print("\n📋 测试5: 验证禁用配置")
    time.sleep(1)
    try:
        response = requests.get(f'{base_url}/api/sell-signal-priority')
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                config = result['data']
                if config.get('manual_priority_over_auto') == False:
                    print("✅ 配置验证成功: 手动优先级已禁用")
                else:
                    print("❌ 配置验证失败: 手动优先级未禁用")
                    return False
            else:
                print(f"❌ 验证失败: {result.get('message')}")
                return False
        else:
            print(f"❌ API请求失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 验证配置异常: {e}")
        return False
    
    # 测试6: 恢复原始设置
    print("\n📋 测试6: 恢复原始设置")
    try:
        response = requests.post(f'{base_url}/api/sell-signal-priority', 
                               json={'manual_priority_over_auto': original_setting})
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 恢复原始设置成功: 手动优先级={'启用' if original_setting else '禁用'}")
            else:
                print(f"❌ 恢复设置失败: {result.get('message')}")
                return False
        else:
            print(f"❌ API请求失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 恢复设置异常: {e}")
        return False
    
    # 测试7: 测试股票数据获取（确保不影响现有功能）
    print("\n📋 测试7: 测试股票数据获取（确保不影响现有功能）")
    try:
        response = requests.get(f'{base_url}/api/stocks')
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                stocks = result['data']
                print(f"✅ 股票数据获取成功: {len(stocks)} 只股票")
                
                # 检查是否有卖出信号相关字段
                if stocks:
                    sample_stock = stocks[0]
                    required_fields = ['sell_signal', 'sell_reason', 'sell_color', 'sell_priority']
                    missing_fields = [field for field in required_fields if field not in sample_stock]
                    
                    if not missing_fields:
                        print("✅ 卖出信号字段完整")
                    else:
                        print(f"⚠️ 缺少卖出信号字段: {missing_fields}")
                else:
                    print("⚠️ 没有股票数据")
            else:
                print(f"❌ 获取股票数据失败: {result.get('message')}")
                return False
        else:
            print(f"❌ API请求失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取股票数据异常: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 所有测试通过！卖出信号优先级控制功能正常工作")
    return True

if __name__ == '__main__':
    success = test_priority_control()
    if success:
        print("\n✅ 测试结果: 成功")
        exit(0)
    else:
        print("\n❌ 测试结果: 失败")
        exit(1)
