#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析爆雷宝数据，找出正确的计算公式
"""

import requests
import json

def analyze_stock(stock_code, expected_score=None):
    """分析单只股票的数据"""
    url = f'http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/json/{stock_code}.json'
    
    try:
        response = requests.get(url, timeout=10)
        data = response.json()
        
        print(f'=== {stock_code} 数据分析 ===')
        print(f'股票名称: {data.get("name")}')
        print(f'总检查项: {data.get("total")}')
        print(f'风险项数: {data.get("num")}')
        if expected_score:
            print(f'期望分数: {expected_score}')
        
        # 分析触发的风险
        triggered_risks = []
        total_deduction = 0
        
        for category in data['data']:
            category_name = category['name']
            for risk in category['rows']:
                if risk.get('trig') == 1:
                    fs = risk.get('fs', 0)
                    triggered_risks.append({
                        'category': category_name,
                        'name': risk.get('lx'),
                        'fs': fs,
                        'trigyy': risk.get('trigyy', ''),
                        'type': 'main'
                    })
                    total_deduction += fs
                    
                # 检查关联风险
                for common in risk.get('commonlxid', []):
                    if common.get('trig') == 1:
                        fs = common.get('fs', 0)
                        triggered_risks.append({
                            'category': category_name,
                            'name': f'关联: {common.get("lx")}',
                            'fs': fs,
                            'trigyy': common.get('trigyy', ''),
                            'type': 'common'
                        })
                        total_deduction += fs
        
        print(f'\n触发的风险项 ({len(triggered_risks)}项):')
        for risk in triggered_risks:
            trigyy_info = f" - {risk['trigyy']}" if risk['trigyy'] else ""
            print(f'  {risk["name"]} (扣分: {risk["fs"]}){trigyy_info}')
        
        print(f'\n总扣分: {total_deduction}')
        calculated_score = 100 - total_deduction
        print(f'计算分数: {calculated_score}')
        
        if expected_score:
            diff = expected_score - calculated_score
            print(f'差异: {diff} (期望 {expected_score} vs 计算 {calculated_score})')
            
            if diff != 0:
                print(f'\n可能的原因分析:')
                print(f'1. 可能只计算主风险项，不计算关联风险')
                print(f'2. 可能有权重或其他计算规则')
                print(f'3. 可能有最小分数限制')
                
                # 尝试只计算主风险项
                main_deduction = sum(r['fs'] for r in triggered_risks if r['type'] == 'main')
                main_score = 100 - main_deduction
                print(f'只计算主风险项: {main_score} (扣分: {main_deduction})')
                
                # 尝试其他可能的计算方式
                if len(triggered_risks) > 0:
                    avg_fs = total_deduction / len(triggered_risks)
                    print(f'平均扣分: {avg_fs:.2f}')
        
        return {
            'stock_code': stock_code,
            'stock_name': data.get('name'),
            'total_risks': data.get('total'),
            'triggered_count': len(triggered_risks),
            'total_deduction': total_deduction,
            'calculated_score': calculated_score,
            'triggered_risks': triggered_risks
        }
        
    except Exception as e:
        print(f'分析 {stock_code} 失败: {e}')
        return None

def main():
    """主函数"""
    # 分析已知分数的股票
    test_cases = [
        ('000001', 97),  # 平安银行，期望97分
        ('000002', 69),  # 万科A，期望69分
    ]
    
    results = []
    for stock_code, expected_score in test_cases:
        result = analyze_stock(stock_code, expected_score)
        if result:
            results.append(result)
        print('\n' + '='*60 + '\n')
    
    # 对比分析
    print('=== 对比分析 ===')
    for result in results:
        print(f'{result["stock_code"]} ({result["stock_name"]}):')
        print(f'  触发风险: {result["triggered_count"]} 项')
        print(f'  总扣分: {result["total_deduction"]}')
        print(f'  计算分数: {result["calculated_score"]}')

if __name__ == "__main__":
    main()
