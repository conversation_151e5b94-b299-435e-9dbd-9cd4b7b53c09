#!/bin/bash

# 持仓系统停止脚本 - 模块化版本

echo "=== 🛑 停止持仓系统 V2.0 模块化版本 ==="

# 查找并停止gunicorn进程
echo "🔍 查找gunicorn进程..."
PIDS=$(pgrep -f "gunicorn.*app:app")

if [ -z "$PIDS" ]; then
    echo "⚠️ 没有找到运行中的gunicorn进程"
else
    echo "🛑 停止gunicorn进程: $PIDS"
    kill -TERM $PIDS
    
    # 等待进程优雅关闭
    sleep 5
    
    # 检查是否还有进程运行
    REMAINING=$(pgrep -f "gunicorn.*app:app")
    if [ ! -z "$REMAINING" ]; then
        echo "⚠️ 强制停止剩余进程: $REMAINING"
        kill -KILL $REMAINING
    fi
fi

# 查找并停止Python进程
echo "🔍 查找Python应用进程..."
PYTHON_PIDS=$(pgrep -f "python.*app.py")

if [ -z "$PYTHON_PIDS" ]; then
    echo "⚠️ 没有找到运行中的Python应用进程"
else
    echo "🛑 停止Python应用进程: $PYTHON_PIDS"
    kill -TERM $PYTHON_PIDS
    
    # 等待进程优雅关闭
    sleep 3
    
    # 检查是否还有进程运行
    REMAINING_PYTHON=$(pgrep -f "python.*app.py")
    if [ ! -z "$REMAINING_PYTHON" ]; then
        echo "⚠️ 强制停止剩余Python进程: $REMAINING_PYTHON"
        kill -KILL $REMAINING_PYTHON
    fi
fi

echo "✅ 持仓系统已停止"
