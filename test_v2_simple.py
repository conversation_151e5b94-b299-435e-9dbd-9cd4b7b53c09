#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("=== 测试第二版系统 ===")

try:
    print("1. 测试基础导入...")
    import requests
    import json
    import pandas as pd
    from datetime import datetime
    print("✅ 基础模块导入成功")
    
    print("2. 测试股息率API...")
    
    # 简化的股息率获取函数
    def get_dividend_yield(stock_code):
        if stock_code.startswith('6'):
            secid = f'1.{stock_code}'
        else:
            secid = f'0.{stock_code}'
        
        url = 'http://push2.eastmoney.com/api/qt/ulist.np/get'
        params = {
            'fltt': '2',
            'invt': '2',
            'fields': 'f12,f14,f2,f133',  # 代码、名称、价格、股息率
            'secids': secid,
            '_': str(int(datetime.now().timestamp() * 1000))
        }
        
        try:
            response = requests.get(url, params=params, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if 'data' in data and 'diff' in data['data'] and data['data']['diff']:
                    item = data['data']['diff'][0]
                    dividend_yield = item.get('f133', 0)
                    if dividend_yield == '-':
                        dividend_yield = 0
                    
                    return {
                        'code': item.get('f12', ''),
                        'name': item.get('f14', ''),
                        'price': item.get('f2', 0),
                        'dividend_yield': dividend_yield
                    }
        except Exception as e:
            print(f"获取 {stock_code} 失败: {e}")
        
        return None
    
    # 测试几只股票
    test_codes = ['600036', '000001', '600519']
    print("3. 测试股票数据获取...")
    
    for code in test_codes:
        data = get_dividend_yield(code)
        if data:
            print(f"✅ {code} {data['name']} 价格:{data['price']} 股息率:{data['dividend_yield']}")
        else:
            print(f"❌ {code} 获取失败")
    
    print("4. 测试微信推送...")
    
    # 简化的微信推送
    def test_wechat():
        webhook_url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7bddd706-ef0f-4d6c-9e1f-3cc8a0a18d9e'
        
        data = {
            "msgtype": "text",
            "text": {
                "content": f"🤖 第二版系统测试\n⏰ 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n✅ 股息率功能正常！"
            }
        }
        
        try:
            response = requests.post(webhook_url, json=data, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    print("✅ 微信推送测试成功")
                    return True
        except Exception as e:
            print(f"❌ 微信推送失败: {e}")
        
        return False
    
    test_wechat()
    
    print("\n=== 第二版系统测试完成 ===")
    print("✅ 股息率获取功能正常")
    print("✅ 微信推送功能正常")
    print("🎉 可以启动完整系统了！")

except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
