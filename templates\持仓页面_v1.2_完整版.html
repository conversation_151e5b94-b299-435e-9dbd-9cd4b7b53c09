<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>持仓股票综合数据实时监控</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
        }
        
        .header .subtitle {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .update-info {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 5px;
            margin-top: 15px;
        }
        
        .controls {
            background: white;
            padding: 20px;
            margin: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .search-box {
            flex: 1;
            max-width: 300px;
            margin-right: 20px;
        }
        
        .search-box input {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .stats {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 0.9em;
            color: #666;
        }
        
        .table-container {
            background: white;
            margin: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th {
            background: #f8f9fa;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            border-bottom: 2px solid #dee2e6;
            position: sticky;
            top: 0;
            z-index: 10;
            font-size: 0.9em;
        }
        
        td {
            padding: 10px 8px;
            text-align: center;
            border-bottom: 1px solid #eee;
            font-size: 0.9em;
        }
        
        tr:hover {
            background-color: #f8f9fa;
        }
        
        .positive {
            color: #dc3545;
            font-weight: bold;
        }
        
        .negative {
            color: #28a745;
            font-weight: bold;
        }
        
        .neutral {
            color: #6c757d;
        }
        
        .highlight-low {
            background-color: #fff3cd;
            color: #856404;
            font-weight: bold;
        }
        
        .highlight-high {
            background-color: #f8d7da;
            color: #721c24;
            font-weight: bold;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            font-size: 1.2em;
            color: #666;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            margin: 20px;
            border-radius: 5px;
            border: 1px solid #f5c6cb;
        }
        
        .refresh-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }
        
        .refresh-btn:hover {
            background: #5a6fd8;
        }
        
        .refresh-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .sort-btn {
            background: none;
            border: none;
            cursor: pointer;
            color: #667eea;
            font-weight: bold;
        }
        
        .sort-btn:hover {
            color: #5a6fd8;
        }
        
        @media (max-width: 1200px) {
            .controls {
                flex-direction: column;
                gap: 15px;
            }
            
            .search-box {
                max-width: 100%;
                margin-right: 0;
            }
            
            .stats {
                justify-content: center;
            }
            
            table {
                font-size: 12px;
            }
            
            th, td {
                padding: 6px 4px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📈 持仓股票综合数据监控</h1>
        <div class="subtitle">最新价 | 一年内最低价 | 距最低点涨幅 | 市净率 | TTM市盈率</div>
        <div class="update-info">
            <div>最后更新时间: <span id="lastUpdate">加载中...</span></div>
            <div>数据来源: 东方财富网</div>
        </div>
    </div>
    
    <div class="controls">
        <div class="search-box">
            <input type="text" id="searchInput" placeholder="搜索股票代码或名称...">
        </div>
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number" id="totalCount">-</div>
                <div class="stat-label">总股票数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="nearLowCount">-</div>
                <div class="stat-label">接近最低价</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="lowPbCount">-</div>
                <div class="stat-label">低市净率</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="lowPeCount">-</div>
                <div class="stat-label">低市盈率</div>
            </div>
        </div>
        <div style="display: flex; gap: 10px;">
            <button class="refresh-btn" id="refreshBtn" onclick="refreshData()">刷新数据</button>
            <button class="refresh-btn" onclick="testWechat()">测试微信</button>
            <button class="refresh-btn" onclick="sendSummary()">发送摘要</button>
            <button class="refresh-btn" onclick="checkHighGain()">检查高涨幅</button>
        </div>
    </div>
    
    <div class="table-container">
        <div id="loading" class="loading">正在加载综合股票数据...</div>
        <div id="error" class="error" style="display: none;"></div>
        <table id="stockTable" style="display: none;">
            <thead>
                <tr>
                    <th>股票代码</th>
                    <th>股票名称</th>
                    <th>最新价</th>
                    <th>涨跌额</th>
                    <th>涨跌幅</th>
                    <th>
                        <button class="sort-btn" onclick="sortTable('yearly_low')">一年内最低价 ↕</button>
                    </th>
                    <th>最低价日期</th>
                    <th>
                        <button class="sort-btn" onclick="sortTable('distance_from_low_pct')">距最低点涨幅 ↕</button>
                    </th>
                    <th>
                        <button class="sort-btn" onclick="sortTable('pb_ratio')">市净率(PB) ↕</button>
                    </th>
                    <th>
                        <button class="sort-btn" onclick="sortTable('pe_ttm')">TTM市盈率 ↕</button>
                    </th>
                    <th>
                        <button class="sort-btn" onclick="sortTable('dividend_yield')">股息率 ↕</button>
                    </th>
                    <th>
                        <button class="sort-btn" onclick="sortTable('industry')">行业 ↕</button>
                    </th>
                    <th>更新时间</th>
                </tr>
            </thead>
            <tbody id="stockTableBody">
            </tbody>
        </table>
    </div>

    <script>
        let stocksData = [];
        let filteredData = [];
        let sortColumn = null;
        let sortDirection = 'asc';
        
        // 格式化数字
        function formatNumber(num, decimals = 2) {
            if (num === 0 || num === null || num === undefined) return '-';
            return parseFloat(num).toFixed(decimals);
        }
        
        // 获取涨跌颜色类
        function getChangeClass(change) {
            if (change > 0) return 'positive';
            if (change < 0) return 'negative';
            return 'neutral';
        }
        
        // 获取距离最低价的颜色类
        function getDistanceClass(distance) {
            if (distance === null || distance === undefined) return '';
            if (distance < 10) return 'highlight-low';  // 接近最低价
            if (distance > 100) return 'highlight-high'; // 远离最低价
            return '';
        }
        
        // 渲染股票表格
        function renderTable(data) {
            const tbody = document.getElementById('stockTableBody');
            tbody.innerHTML = '';
            
            data.forEach(stock => {
                const row = document.createElement('tr');
                const changeClass = getChangeClass(stock.change);
                const distanceClass = getDistanceClass(stock.distance_from_low_pct);
                
                row.innerHTML = `
                    <td>${stock.code}</td>
                    <td>${stock.name}</td>
                    <td class="${changeClass}">${formatNumber(stock.price)}</td>
                    <td class="${changeClass}">${stock.change > 0 ? '+' : ''}${formatNumber(stock.change)}</td>
                    <td class="${changeClass}">${stock.change_pct > 0 ? '+' : ''}${formatNumber(stock.change_pct)}%</td>
                    <td>${formatNumber(stock.yearly_low)}</td>
                    <td>${stock.low_date || '-'}</td>
                    <td class="${distanceClass}">${stock.distance_from_low_pct !== null ? formatNumber(stock.distance_from_low_pct) + '%' : '-'}</td>
                    <td>${formatNumber(stock.pb_ratio)}</td>
                    <td>${formatNumber(stock.pe_ttm)}</td>
                    <td>${stock.dividend_yield > 0 ? formatNumber(stock.dividend_yield) + '%' : '-'}</td>
                    <td>${stock.industry || '-'}</td>
                    <td>${stock.update_time || '-'}</td>
                `;
                tbody.appendChild(row);
            });
        }
        
        // 更新统计信息
        function updateStats(data) {
            const totalCount = data.length;
            
            // 接近最低价的股票（距离最低价 < 20%）
            const nearLowCount = data.filter(stock => 
                stock.distance_from_low_pct !== null && stock.distance_from_low_pct < 20
            ).length;
            
            // 低市净率股票（PB < 1.5）
            const lowPbCount = data.filter(stock => 
                stock.pb_ratio !== null && stock.pb_ratio < 1.5
            ).length;
            
            // 低市盈率股票（PE < 20）
            const lowPeCount = data.filter(stock => 
                stock.pe_ttm !== null && stock.pe_ttm < 20
            ).length;
            
            document.getElementById('totalCount').textContent = totalCount;
            document.getElementById('nearLowCount').textContent = nearLowCount;
            document.getElementById('lowPbCount').textContent = lowPbCount;
            document.getElementById('lowPeCount').textContent = lowPeCount;
        }
        
        // 排序功能
        function sortTable(column) {
            if (sortColumn === column) {
                sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                sortColumn = column;
                sortDirection = 'asc';
            }
            
            filteredData.sort((a, b) => {
                let aVal = a[column];
                let bVal = b[column];

                // 处理null值
                if (aVal === null || aVal === undefined) aVal = '';
                if (bVal === null || bVal === undefined) bVal = '';

                // 判断是数值还是字符串
                if (column === 'industry' || column === 'name' || column === 'code') {
                    // 字符串排序
                    aVal = String(aVal).toLowerCase();
                    bVal = String(bVal).toLowerCase();

                    if (sortDirection === 'asc') {
                        return aVal.localeCompare(bVal);
                    } else {
                        return bVal.localeCompare(aVal);
                    }
                } else {
                    // 数值排序
                    if (aVal === '') aVal = sortDirection === 'asc' ? Infinity : -Infinity;
                    if (bVal === '') bVal = sortDirection === 'asc' ? Infinity : -Infinity;

                    if (sortDirection === 'asc') {
                        return aVal - bVal;
                    } else {
                        return bVal - aVal;
                    }
                }
            });
            
            renderTable(filteredData);
        }
        
        // 搜索功能
        function filterStocks() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            filteredData = stocksData.filter(stock => 
                stock.code.toLowerCase().includes(searchTerm) || 
                stock.name.toLowerCase().includes(searchTerm)
            );
            renderTable(filteredData);
            updateStats(filteredData);
        }
        
        // 获取股票数据
        async function fetchStocks() {
            try {
                const response = await fetch('/api/stocks');
                const data = await response.json();
                
                stocksData = data.stocks || [];
                filteredData = stocksData;
                
                document.getElementById('lastUpdate').textContent = data.last_update || '未知';
                
                renderTable(filteredData);
                updateStats(filteredData);
                
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error').style.display = 'none';
                document.getElementById('stockTable').style.display = 'table';
                
            } catch (error) {
                console.error('获取股票数据失败:', error);
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error').style.display = 'block';
                document.getElementById('error').textContent = '获取股票数据失败: ' + error.message;
            }
        }
        
        // 刷新数据
        function refreshData() {
            const btn = document.getElementById('refreshBtn');
            btn.disabled = true;
            btn.textContent = '刷新中...';

            fetch('/api/refresh')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        setTimeout(fetchStocks, 2000); // 2秒后获取新数据
                    } else {
                        alert('刷新失败: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('刷新失败: ' + error.message);
                })
                .finally(() => {
                    btn.disabled = false;
                    btn.textContent = '刷新数据';
                });
        }

        // 测试企业微信连接
        function testWechat() {
            fetch('/api/wechat/test')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('✅ 企业微信连接测试成功！');
                    } else {
                        alert('❌ 企业微信连接测试失败: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('❌ 测试失败: ' + error.message);
                });
        }

        // 发送每日摘要
        function sendSummary() {
            fetch('/api/wechat/summary')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('✅ 每日摘要已发送到企业微信！');
                    } else {
                        alert('❌ 发送失败: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('❌ 发送失败: ' + error.message);
                });
        }

        // 检查高涨幅股票
        function checkHighGain() {
            const threshold = prompt('请输入涨幅阈值（默认70%）:', '70');
            if (threshold === null) return;

            const thresholdValue = parseFloat(threshold) || 70;

            fetch(`/api/wechat/alert/${thresholdValue}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('✅ ' + data.message);
                    } else {
                        alert('❌ ' + data.message);
                    }
                })
                .catch(error => {
                    alert('❌ 检查失败: ' + error.message);
                });
        }
        
        // 搜索事件监听
        document.getElementById('searchInput').addEventListener('input', filterStocks);
        
        // 页面加载完成后获取数据
        document.addEventListener('DOMContentLoaded', function() {
            fetchStocks();
            
            // 每5分钟自动刷新一次
            setInterval(fetchStocks, 300000);
        });
    </script>
</body>
</html>
