#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的排序逻辑
验证"已挂单""已减半"状态的排序位置
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入策略管理器
from 持仓系统_v14 import calculate_sell_signal

def test_sorting_priority():
    """测试排序优先级"""
    print("🧪 测试新的排序优先级...")
    
    # 创建不同状态的测试股票
    test_stocks = [
        {
            'name': '已清仓股票',
            'data': {
                'name': '已清仓股票',
                'code': '000001',
                'price': 10.00,
                'yearly_low': 8.00,
                'unit_cost': 9.00,
                'distance_from_low_pct': 25.0,
                'pe_ratio': 15.0,
                'pb_ratio': 1.5,
                'dividend_yield': 3.0,
                'scan_score': 80,
                'profit_status': 'profit',
                'profit_margin': 10.0,
                'is_cleared': True,
                'cleared_time': '2025-01-01 10:00:00'
            },
            'expected_priority': 0
        },
        {
            'name': '清仓策略股票',
            'data': {
                'name': '清仓策略股票',
                'code': '000002',
                'price': 24.00,
                'yearly_low': 10.00,
                'unit_cost': 15.00,
                'distance_from_low_pct': 145.0,  # 触发基础清仓
                'pe_ratio': 15.0,
                'pb_ratio': 1.5,
                'dividend_yield': 3.0,
                'scan_score': 80,
                'profit_status': 'profit',
                'profit_margin': 60.0
            },
            'expected_priority': 1
        },
        {
            'name': '减半策略股票',
            'data': {
                'name': '减半策略股票',
                'code': '000003',
                'price': 17.00,
                'yearly_low': 10.00,
                'unit_cost': 12.00,
                'distance_from_low_pct': 75.0,  # 触发基础减半
                'pe_ratio': 15.0,
                'pb_ratio': 1.5,
                'dividend_yield': 3.0,
                'scan_score': 80,
                'profit_status': 'profit',
                'profit_margin': 40.0
            },
            'expected_priority': 2
        },
        {
            'name': '预警策略股票',
            'data': {
                'name': '预警策略股票',
                'code': '000004',
                'price': 20.00,
                'yearly_low': 12.00,
                'unit_cost': 15.00,
                'distance_from_low_pct': 67.0,  # 接近70%减半
                'pe_ratio': 15.0,
                'pb_ratio': 1.5,
                'dividend_yield': 3.0,
                'scan_score': 80,
                'profit_status': 'profit',
                'profit_margin': 30.0
            },
            'expected_priority': 3
        },
        {
            'name': '已挂单股票',
            'data': {
                'name': '已挂单股票',
                'code': '000005',
                'price': 15.00,
                'yearly_low': 10.00,
                'unit_cost': 12.00,
                'distance_from_low_pct': 50.0,
                'pe_ratio': 15.0,
                'pb_ratio': 1.5,
                'dividend_yield': 3.0,
                'scan_score': 80,
                'profit_status': 'profit',
                'profit_margin': 25.0,
                'custom_status': True,
                'custom_status_type': 'warning',
                'custom_status_text': '已挂单',
                'custom_status_emoji': '📋',
                'custom_status_time': '2025-01-01 12:00:00'
            },
            'expected_priority': 4
        },
        {
            'name': '已减半股票',
            'data': {
                'name': '已减半股票',
                'code': '000006',
                'price': 18.00,
                'yearly_low': 10.00,
                'unit_cost': 14.00,
                'distance_from_low_pct': 80.0,
                'pe_ratio': 15.0,
                'pb_ratio': 1.5,
                'dividend_yield': 3.0,
                'scan_score': 80,
                'profit_status': 'profit',
                'profit_margin': 28.0,
                'is_reduced': True,
                'reduced_time': '2025-01-01 11:00:00'
            },
            'expected_priority': 4
        },
        {
            'name': '持有股票',
            'data': {
                'name': '持有股票',
                'code': '000007',
                'price': 12.00,
                'yearly_low': 10.00,
                'unit_cost': 11.00,
                'distance_from_low_pct': 20.0,  # 没有触发任何策略
                'pe_ratio': 15.0,
                'pb_ratio': 1.5,
                'dividend_yield': 3.0,
                'scan_score': 80,
                'profit_status': 'profit',
                'profit_margin': 9.0
            },
            'expected_priority': 5
        }
    ]
    
    print(f"\n🧪 开始测试 {len(test_stocks)} 个股票的排序优先级...")
    
    results = []
    
    for stock in test_stocks:
        print(f"\n--- 测试: {stock['name']} ---")
        
        # 计算卖出信号
        result = calculate_sell_signal(stock['data'])
        
        print(f"📊 结果:")
        print(f"   信号: {result['signal']}")
        print(f"   原因: {result['reason']}")
        print(f"   优先级: {result['priority']}")
        print(f"   预期优先级: {stock['expected_priority']}")
        
        # 验证优先级
        if result['priority'] == stock['expected_priority']:
            print(f"   ✅ 优先级正确")
        else:
            print(f"   ❌ 优先级错误")
        
        results.append({
            'name': stock['name'],
            'signal': result['signal'],
            'reason': result['reason'],
            'priority': result['priority'],
            'expected_priority': stock['expected_priority'],
            'correct': result['priority'] == stock['expected_priority']
        })
    
    # 显示排序结果
    print(f"\n📊 排序结果预览:")
    print("=" * 60)
    
    # 按优先级排序
    sorted_results = sorted(results, key=lambda x: x['priority'])
    
    for i, result in enumerate(sorted_results, 1):
        status = "✅" if result['correct'] else "❌"
        print(f"{i}. {status} {result['name']} (优先级: {result['priority']})")
        print(f"   信号: {result['signal']} | 原因: {result['reason']}")
    
    print("=" * 60)
    
    # 验证排序逻辑
    print(f"\n🎯 排序逻辑验证:")
    expected_order = [
        "已清仓股票",      # Priority 0
        "清仓策略股票",    # Priority 1  
        "减半策略股票",    # Priority 2
        "预警策略股票",    # Priority 3
        "已挂单股票",      # Priority 4
        "已减半股票",      # Priority 4
        "持有股票"         # Priority 5
    ]
    
    actual_order = [r['name'] for r in sorted_results]
    
    print(f"预期顺序: {expected_order}")
    print(f"实际顺序: {actual_order}")
    
    if actual_order == expected_order:
        print(f"✅ 排序逻辑完全正确！")
    else:
        print(f"❌ 排序逻辑有问题")
        for i, (expected, actual) in enumerate(zip(expected_order, actual_order)):
            if expected != actual:
                print(f"   位置 {i+1}: 期望 {expected}, 实际 {actual}")

def main():
    """主函数"""
    try:
        test_sorting_priority()
        print("\n✅ 排序逻辑测试完成!")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
