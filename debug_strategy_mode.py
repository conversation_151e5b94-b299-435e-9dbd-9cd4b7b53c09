#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试策略模式问题
"""

import sys
import os
import json
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_stock_data_file():
    """检查股票数据文件中的策略模式"""
    print("🔍 检查股票数据文件...")
    
    try:
        with open('stock_data_cache.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        stock_data = data.get('stock_data', {})
        print(f"📊 文件中包含 {len(stock_data)} 只股票")
        
        # 查找已减半的股票
        reduced_stocks = []
        for code, info in stock_data.items():
            if info.get('is_reduced', False):
                reduced_stocks.append({
                    'code': code,
                    'name': info.get('name', ''),
                    'is_reduced': info.get('is_reduced', False),
                    'strategy_mode': info.get('strategy_mode', 'unknown'),
                    'cooling_down_until': info.get('cooling_down_until', ''),
                    'pe_ratio': info.get('pe_ratio', 0),
                    'reduced_time': info.get('reduced_time', '')
                })
        
        print(f"\n📉 找到 {len(reduced_stocks)} 只已减半股票:")
        for stock in reduced_stocks:
            print(f"  {stock['code']} {stock['name']}:")
            print(f"    - 策略模式: {stock['strategy_mode']}")
            print(f"    - 冷却期至: {stock['cooling_down_until']}")
            print(f"    - TTM: {stock['pe_ratio']}")
            print(f"    - 减半时间: {stock['reduced_time']}")
            
            # 检查冷却期状态
            if stock['cooling_down_until']:
                try:
                    cooling_date = datetime.strptime(stock['cooling_down_until'], '%Y-%m-%d')
                    is_in_cooling = datetime.now() < cooling_date
                    days_remaining = (cooling_date - datetime.now()).days
                    print(f"    - 是否在冷却期: {is_in_cooling} (剩余{days_remaining}天)")
                except:
                    print(f"    - 冷却期日期格式错误")
            
            print()
        
        return reduced_stocks
        
    except FileNotFoundError:
        print("❌ 未找到股票数据文件 stock_data_cache.json")
        return []
    except Exception as e:
        print(f"❌ 读取股票数据文件失败: {e}")
        return []

def simulate_strategy_mode_update():
    """模拟策略模式更新过程"""
    print("🧪 模拟策略模式更新过程...")
    
    # 导入必要的模块
    try:
        from 持仓系统_v14 import reduction_monitor, stock_data
        
        print(f"📊 当前内存中有 {len(stock_data)} 只股票")
        
        # 查找已减半的股票并更新策略模式
        updated_count = 0
        for stock_code, stock_info in stock_data.items():
            if stock_info.get('is_reduced', False):
                print(f"\n🔄 处理已减半股票: {stock_code} {stock_info.get('name', '')}")
                print(f"  更新前策略模式: {stock_info.get('strategy_mode', 'unknown')}")
                
                try:
                    # 检查冷却期状态
                    is_cooling = reduction_monitor.is_in_cooling_period(stock_info)
                    print(f"  是否在冷却期: {is_cooling}")
                    
                    # 更新策略模式
                    strategy_mode, reason = reduction_monitor.update_strategy_mode(stock_code, stock_info)
                    print(f"  更新后策略模式: {strategy_mode} ({reason})")
                    print(f"  数据中的策略模式: {stock_info.get('strategy_mode', 'unknown')}")
                    
                    updated_count += 1
                    
                except Exception as e:
                    print(f"  ❌ 更新失败: {e}")
        
        print(f"\n✅ 已更新 {updated_count} 只已减半股票的策略模式")
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
    except Exception as e:
        print(f"❌ 模拟更新失败: {e}")

def check_api_response():
    """检查API响应中的策略模式"""
    print("🌐 检查API响应...")
    
    try:
        import requests
        
        # 调用本地API
        response = requests.get('http://localhost:5000/api/stocks', timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            stocks = data.get('data', {})
            
            print(f"📊 API返回 {len(stocks)} 只股票")
            
            # 查找已减半的股票
            reduced_count = 0
            for code, info in stocks.items():
                if info.get('is_reduced', False):
                    reduced_count += 1
                    print(f"  {code} {info.get('name', '')}:")
                    print(f"    - 策略模式: {info.get('strategy_mode', 'unknown')}")
                    print(f"    - 冷却期至: {info.get('cooling_down_until', '')}")
            
            print(f"\n📉 API中找到 {reduced_count} 只已减半股票")
            
        else:
            print(f"❌ API调用失败: HTTP {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到本地服务器，请确保服务器正在运行")
    except Exception as e:
        print(f"❌ API调用失败: {e}")

def main():
    print("🔧 开始调试策略模式问题...")
    print("=" * 50)
    
    # 1. 检查文件中的数据
    print("1️⃣ 检查数据文件")
    reduced_stocks = check_stock_data_file()
    
    print("\n" + "=" * 50)
    
    # 2. 模拟策略模式更新
    print("2️⃣ 模拟策略模式更新")
    simulate_strategy_mode_update()
    
    print("\n" + "=" * 50)
    
    # 3. 检查API响应
    print("3️⃣ 检查API响应")
    check_api_response()
    
    print("\n" + "=" * 50)
    print("🎯 调试总结:")
    print("1. 检查数据文件中已减半股票的策略模式是否正确")
    print("2. 验证策略模式更新逻辑是否正常工作")
    print("3. 确认API返回的数据中策略模式是否正确")
    print("\n💡 如果发现问题:")
    print("- 文件中策略模式错误 → 数据持久化问题")
    print("- 更新逻辑失败 → 策略模式计算问题")
    print("- API返回错误 → 服务器端问题")

if __name__ == '__main__':
    main()
