#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的显示格式
验证山鹰国际的显示是否符合要求
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入策略管理器
from 持仓系统_v14 import calculate_sell_signal

def test_shanying_display_format():
    """测试山鹰国际的显示格式"""
    print("🧪 测试山鹰国际新的显示格式...")
    
    # 山鹰国际实际数据
    shanying_data = {
        'name': '山鹰国际',
        'code': '600567',
        'price': 1.97,
        'yearly_low': 1.34,
        'unit_cost': 2.936,
        'distance_from_low_pct': 47.01,  # 47%涨幅
        'pe_ratio': -23.59,              # 负TTM
        'pb_ratio': 0.71,
        'dividend_yield': 0.46,          # 低股息 ≤2%
        'scan_score': 64,                # 低扫雷 ≤70
        'profit_status': 'need_gain',
        'profit_margin': 49.04
    }
    
    print(f"\n📊 山鹰国际数据:")
    print(f"   当前价格: {shanying_data['price']}元")
    print(f"   年内最低价: {shanying_data['yearly_low']}元")
    print(f"   涨幅: {shanying_data['distance_from_low_pct']:.1f}%")
    print(f"   TTM: {shanying_data['pe_ratio']}")
    print(f"   股息率: {shanying_data['dividend_yield']:.1f}%")
    print(f"   扫雷分数: {shanying_data['scan_score']}")
    
    # 计算卖出信号
    result = calculate_sell_signal(shanying_data)
    
    print(f"\n🎯 策略评估结果:")
    print(f"   信号: {result['signal']}")
    print(f"   原因: {result['reason']}")
    
    # 模拟前端显示格式
    print(f"\n📱 前端显示格式预览:")
    print("=" * 50)
    
    if result['signal'] == 'warning' and '紧急预警：距离负TTM清仓策略触发' in result['reason']:
        # 提取剩余涨幅
        import re
        match = re.search(r'还有(\d+\.\d+)%涨幅', result['reason'])
        remaining = match.group(1) if match else '3.0'
        
        # 计算目标价格
        target_price = shanying_data['yearly_low'] * 1.5
        
        print(f"信号：预警（接近清仓）")
        print(f"策略：负TTM清仓预警(TTM≤0, 股息≤2%, 扫雷≤70, 涨幅≥50%)")
        print(f"说明：距离TTM策略50%清仓触发还有{remaining}%涨幅")
        print(f"卖出价: {target_price:.3f}元 ({shanying_data['yearly_low']:.3f} × 1.5)")
        
        print("\n✅ 显示格式符合要求！")
        
        # 验证价格计算
        expected_price = 1.34 * 1.5  # 2.010
        if abs(target_price - expected_price) < 0.001:
            print(f"✅ 价格计算正确: {target_price:.3f}元")
        else:
            print(f"❌ 价格计算错误: 期望{expected_price:.3f}元，实际{target_price:.3f}元")
            
    else:
        print(f"❌ 信号类型不符合预期:")
        print(f"   期望: warning + 紧急预警")
        print(f"   实际: {result['signal']} + {result['reason']}")
    
    print("=" * 50)

def test_other_scenarios():
    """测试其他场景的显示格式"""
    print(f"\n🧪 测试其他场景的显示格式...")
    
    scenarios = [
        {
            'name': '华泰证券预警',
            'data': {
                'name': '华泰证券',
                'code': '601688',
                'price': 20.02,
                'yearly_low': 12.02,
                'unit_cost': 11.736,
                'distance_from_low_pct': 66.56,
                'pe_ratio': 10.82,
                'pb_ratio': 1.08,
                'dividend_yield': 2.9,
                'scan_score': 100,
                'profit_status': 'profit',
                'profit_margin': 70.59
            },
            'expected': '基础减半预警'
        },
        {
            'name': '基础清仓',
            'data': {
                'name': '测试股票',
                'code': '000001',
                'price': 24.0,
                'yearly_low': 10.0,
                'unit_cost': 15.0,
                'distance_from_low_pct': 145.0,
                'pe_ratio': 15.0,
                'pb_ratio': 1.5,
                'dividend_yield': 3.0,
                'scan_score': 80,
                'profit_status': 'profit',
                'profit_margin': 60.0
            },
            'expected': '基础清仓'
        }
    ]
    
    for scenario in scenarios:
        print(f"\n--- {scenario['name']} ---")
        result = calculate_sell_signal(scenario['data'])
        print(f"信号: {result['signal']}")
        print(f"原因: {result['reason']}")
        
        # 简化显示预期格式
        if 'basic_gain' in result.get('details', ''):
            if result['signal'] == 'clearance':
                target_price = scenario['data']['yearly_low'] * 2.4
                print(f"预期显示: 基础清仓(140%) - {target_price:.3f}元")
            elif result['signal'] == 'warning':
                target_price = scenario['data']['yearly_low'] * 1.7
                print(f"预期显示: 基础减半预警(70%) - {target_price:.3f}元")

def main():
    """主函数"""
    try:
        test_shanying_display_format()
        test_other_scenarios()
        print("\n✅ 显示格式测试完成!")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
