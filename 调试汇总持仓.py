#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np

def analyze_excel_file():
    """分析汇总持仓.xlsx文件"""
    try:
        print("🔍 开始分析汇总持仓.xlsx文件...")
        
        # 读取Excel文件
        df = pd.read_excel('汇总持仓.xlsx')
        
        print(f"📊 文件基本信息:")
        print(f"   行数: {len(df)}")
        print(f"   列数: {len(df.columns)}")
        print(f"   列名: {list(df.columns)}")
        print()
        
        # 显示前几行数据
        print("📋 前10行数据:")
        for i in range(min(10, len(df))):
            row_data = []
            for col in df.columns:
                value = df.iloc[i][col]
                if pd.isna(value):
                    row_data.append("NaN")
                else:
                    row_data.append(str(value))
            print(f"   第{i+1:2d}行: {row_data}")
        print()
        
        # 分析代码列
        if '代码' in df.columns:
            print("🔢 代码列分析:")
            code_col = df['代码']
            print(f"   非空值数量: {code_col.notna().sum()}")
            print(f"   空值数量: {code_col.isna().sum()}")
            print(f"   数据类型: {code_col.dtype}")
            
            # 显示前20个代码值
            print("   前20个代码值:")
            for i, code in enumerate(code_col.head(20)):
                if pd.isna(code):
                    print(f"     [{i+1:2d}] NaN")
                else:
                    print(f"     [{i+1:2d}] {code} (类型: {type(code).__name__})")
            print()
        
        # 分析持有数量列
        if '持有数量' in df.columns:
            print("📈 持有数量列分析:")
            qty_col = df['持有数量']
            print(f"   非空值数量: {qty_col.notna().sum()}")
            print(f"   空值数量: {qty_col.isna().sum()}")
            print(f"   数据类型: {qty_col.dtype}")
            
            # 显示前20个数量值
            print("   前20个数量值:")
            for i, qty in enumerate(qty_col.head(20)):
                if pd.isna(qty):
                    print(f"     [{i+1:2d}] NaN")
                else:
                    print(f"     [{i+1:2d}] {qty} (类型: {type(qty).__name__})")
            print()
        
        # 尝试过滤股票数据
        print("🎯 尝试过滤股票数据:")
        
        # 过滤掉代码列为空的行
        df_clean = df.dropna(subset=['代码'])
        print(f"   过滤空代码后剩余: {len(df_clean)} 行")
        
        # 转换代码为字符串
        df_clean['代码'] = df_clean['代码'].astype(str)
        
        # 匹配6位数字的股票代码
        stock_mask = df_clean['代码'].str.match(r'^[0-9]{6}$', na=False)
        stock_df = df_clean[stock_mask]
        print(f"   匹配6位数字代码: {len(stock_df)} 行")
        
        if len(stock_df) > 0:
            print("   匹配的股票代码示例:")
            for i, (idx, row) in enumerate(stock_df.head(10).iterrows()):
                code = row['代码']
                qty = row.get('持有数量', 'N/A')
                name = row.get('名称', 'N/A')
                print(f"     {code} - {name} - 数量:{qty}")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    analyze_excel_file()
