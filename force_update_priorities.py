#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制更新所有股票的卖出信号优先级
"""

import sys
import os
import json

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入系统
from 持仓系统_v14 import calculate_sell_signal

def force_update_all_priorities():
    """强制更新所有股票的卖出信号优先级"""
    print("🔄 强制更新所有股票的卖出信号优先级...")
    
    # 读取缓存文件
    cache_file = 'stock_data_cache.json'
    if not os.path.exists(cache_file):
        print(f"❌ 缓存文件不存在: {cache_file}")
        return
    
    with open(cache_file, 'r', encoding='utf-8') as f:
        cache_data = json.load(f)
        stock_data = cache_data.get('stock_data', {})
    
    print(f"📊 找到 {len(stock_data)} 只股票")
    
    updated_count = 0
    priority_changes = []
    
    for code, info in stock_data.items():
        try:
            # 记录原始优先级
            old_priority = info.get('sell_priority', 999)
            old_reason = info.get('sell_reason', '未知')
            
            # 重新计算卖出信号
            new_signal = calculate_sell_signal(info)
            
            # 更新数据
            info['sell_signal'] = new_signal['signal']
            info['sell_reason'] = new_signal['reason']
            info['sell_color'] = new_signal['color']
            info['sell_priority'] = new_signal['priority']
            info['sell_details'] = new_signal.get('details', '')
            
            # 记录变化
            if old_priority != new_signal['priority']:
                priority_changes.append({
                    'code': code,
                    'name': info.get('name', '未知'),
                    'old_priority': old_priority,
                    'new_priority': new_signal['priority'],
                    'old_reason': old_reason,
                    'new_reason': new_signal['reason']
                })
            
            updated_count += 1
            
        except Exception as e:
            print(f"⚠️ 更新 {code} 失败: {e}")
    
    # 保存更新后的数据
    cache_data['stock_data'] = stock_data
    with open(cache_file, 'w', encoding='utf-8') as f:
        json.dump(cache_data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 已更新 {updated_count} 只股票的卖出信号")
    
    # 显示优先级变化
    if priority_changes:
        print(f"\n📊 优先级变化统计 ({len(priority_changes)} 只股票):")
        print("=" * 80)
        
        # 按新优先级排序
        priority_changes.sort(key=lambda x: x['new_priority'])
        
        for change in priority_changes:
            print(f"📈 {change['name']} ({change['code']})")
            print(f"   优先级: {change['old_priority']} → {change['new_priority']}")
            print(f"   原因: {change['old_reason']} → {change['new_reason']}")
            print()
    else:
        print(f"📊 没有优先级变化")
    
    # 统计当前优先级分布
    print(f"\n📊 当前优先级分布:")
    priority_stats = {}
    for code, info in stock_data.items():
        priority = info.get('sell_priority', 999)
        reason = info.get('sell_reason', '未知')
        
        if priority not in priority_stats:
            priority_stats[priority] = []
        priority_stats[priority].append(f"{info.get('name', '未知')} ({reason})")
    
    for priority in sorted(priority_stats.keys()):
        stocks = priority_stats[priority]
        print(f"Priority {priority}: {len(stocks)} 只股票")
        
        # 显示前5只作为示例
        for i, stock in enumerate(stocks[:5]):
            print(f"  {i+1}. {stock}")
        if len(stocks) > 5:
            print(f"  ... 还有 {len(stocks) - 5} 只股票")
        print()

def check_specific_stocks():
    """检查特定的已挂单和已减半股票"""
    print("\n🔍 检查特定状态的股票...")
    
    cache_file = 'stock_data_cache.json'
    with open(cache_file, 'r', encoding='utf-8') as f:
        cache_data = json.load(f)
        stock_data = cache_data.get('stock_data', {})
    
    # 查找已挂单股票
    pending_stocks = []
    reduced_stocks = []
    
    for code, info in stock_data.items():
        if info.get('custom_status_text') == '已挂单':
            pending_stocks.append({
                'code': code,
                'name': info.get('name', '未知'),
                'priority': info.get('sell_priority', 999),
                'reason': info.get('sell_reason', '未知')
            })
        
        if info.get('is_reduced', False):
            reduced_stocks.append({
                'code': code,
                'name': info.get('name', '未知'),
                'priority': info.get('sell_priority', 999),
                'reason': info.get('sell_reason', '未知')
            })
    
    print(f"📋 已挂单股票 ({len(pending_stocks)} 只):")
    for stock in pending_stocks:
        print(f"  {stock['name']} ({stock['code']}) - 优先级: {stock['priority']}, 原因: {stock['reason']}")
    
    print(f"\n✅ 已减半股票 ({len(reduced_stocks)} 只):")
    for stock in reduced_stocks:
        print(f"  {stock['name']} ({stock['code']}) - 优先级: {stock['priority']}, 原因: {stock['reason']}")

def main():
    """主函数"""
    try:
        # 检查更新前的状态
        print("🔍 更新前状态检查:")
        check_specific_stocks()
        
        # 强制更新
        force_update_all_priorities()
        
        # 检查更新后的状态
        print("\n🔍 更新后状态检查:")
        check_specific_stocks()
        
        print("\n✅ 优先级更新完成!")
        print("💡 现在可以刷新网页查看新的排序效果")
        
    except Exception as e:
        print(f"\n❌ 更新失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
