<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .debug-title { font-weight: bold; color: #333; margin-bottom: 10px; }
        .debug-content { background: #f5f5f5; padding: 10px; border-radius: 3px; white-space: pre-wrap; }
        .error { color: red; }
        .success { color: green; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <h1>🧪 前端调试页面</h1>
    
    <div class="debug-section">
        <div class="debug-title">📊 API测试结果</div>
        <div id="apiResult" class="debug-content">正在测试...</div>
    </div>
    
    <div class="debug-section">
        <div class="debug-title">🔍 数据筛选测试</div>
        <div id="filterResult" class="debug-content">等待API数据...</div>
    </div>
    
    <div class="debug-section">
        <div class="debug-title">📋 股票数据样本</div>
        <div id="sampleData" class="debug-content">等待API数据...</div>
    </div>

    <script>
        let stockData = [];
        let filteredData = [];
        let currentSellFilter = 'all';

        // 测试API
        async function testAPI() {
            const apiResult = document.getElementById('apiResult');
            
            try {
                console.log('🔄 开始测试API...');
                const response = await fetch('/api/stocks');
                const result = await response.json();
                
                let output = `✅ API调用成功\n`;
                output += `状态: ${result.success}\n`;
                output += `数据数量: ${result.count}\n`;
                output += `最后更新: ${result.last_update}\n`;
                
                if (result.data && result.data.length > 0) {
                    stockData = result.data;
                    output += `\n📊 数据加载成功: ${stockData.length} 只股票\n`;
                    
                    // 显示第一只股票的详细信息
                    const firstStock = stockData[0];
                    output += `\n第一只股票详情:\n`;
                    output += `代码: ${firstStock.code}\n`;
                    output += `名称: ${firstStock.name}\n`;
                    output += `价格: ${firstStock.price}\n`;
                    output += `卖出信号: ${firstStock.sell_signal}\n`;
                    output += `卖出原因: ${firstStock.sell_reason}\n`;
                    output += `自定义状态: ${firstStock.custom_status}\n`;
                    output += `已清仓: ${firstStock.is_cleared}\n`;
                    output += `已减半: ${firstStock.is_reduced}\n`;
                    
                    testFilter();
                    showSampleData();
                } else {
                    output += `\n❌ 数据为空`;
                }
                
                apiResult.textContent = output;
                apiResult.className = 'debug-content success';
                
            } catch (error) {
                console.error('❌ API测试失败:', error);
                apiResult.textContent = `❌ API调用失败: ${error.message}`;
                apiResult.className = 'debug-content error';
            }
        }

        // 测试筛选逻辑
        function testFilter() {
            const filterResult = document.getElementById('filterResult');
            
            try {
                console.log('🔍 开始测试筛选逻辑...');
                
                // 模拟筛选过程
                const searchTerm = '';
                
                filteredData = stockData.filter(stock => {
                    // 搜索筛选
                    if (searchTerm &&
                        !stock.code.toLowerCase().includes(searchTerm) &&
                        !stock.name.toLowerCase().includes(searchTerm)) {
                        return false;
                    }

                    // 卖出信号筛选
                    if (currentSellFilter !== 'all') {
                        if (stock.sell_signal !== currentSellFilter) {
                            return false;
                        }
                    }

                    return true;
                });
                
                let output = `✅ 筛选测试成功\n`;
                output += `原始数据: ${stockData.length} 只\n`;
                output += `筛选后数据: ${filteredData.length} 只\n`;
                output += `搜索条件: "${searchTerm}"\n`;
                output += `卖出信号筛选: ${currentSellFilter}\n`;
                
                // 统计卖出信号分布
                const signalStats = {};
                stockData.forEach(stock => {
                    const signal = stock.sell_signal || 'unknown';
                    signalStats[signal] = (signalStats[signal] || 0) + 1;
                });
                
                output += `\n📊 卖出信号分布:\n`;
                Object.entries(signalStats).forEach(([signal, count]) => {
                    output += `${signal}: ${count} 只\n`;
                });
                
                filterResult.textContent = output;
                filterResult.className = 'debug-content success';
                
            } catch (error) {
                console.error('❌ 筛选测试失败:', error);
                filterResult.textContent = `❌ 筛选测试失败: ${error.message}`;
                filterResult.className = 'debug-content error';
            }
        }

        // 显示样本数据
        function showSampleData() {
            const sampleData = document.getElementById('sampleData');
            
            try {
                let output = `📋 前5只股票数据样本:\n\n`;
                
                const sampleStocks = stockData.slice(0, 5);
                sampleStocks.forEach((stock, index) => {
                    output += `${index + 1}. ${stock.name} (${stock.code})\n`;
                    output += `   价格: ${stock.price} | 信号: ${stock.sell_signal}\n`;
                    output += `   市值: ${stock.market_value} | 持仓: ${stock.holdings}\n`;
                    output += `   自定义状态: ${stock.custom_status} | 已清仓: ${stock.is_cleared}\n`;
                    output += `\n`;
                });
                
                sampleData.textContent = output;
                sampleData.className = 'debug-content';
                
            } catch (error) {
                console.error('❌ 显示样本数据失败:', error);
                sampleData.textContent = `❌ 显示样本数据失败: ${error.message}`;
                sampleData.className = 'debug-content error';
            }
        }

        // 页面加载后开始测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 调试页面已加载');
            testAPI();
        });
    </script>
</body>
</html>
