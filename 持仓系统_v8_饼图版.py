from flask import Flask, render_template, jsonify, request
import requests
import time
import json
import pandas as pd
import os
from datetime import datetime, timedelta
import threading
from urllib.parse import urlencode
from werkzeug.utils import secure_filename
from wechat_alert import WeChatAlert
from A股交易时间监测_简化版 import AStockTradingTimeMonitor
import akshare as ak
import baostock as bs

app = Flask(__name__)

# 配置文件上传
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
ALLOWED_EXTENSIONS = {'xlsx', 'xls'}

# 确保上传目录存在
if not os.path.exists(app.config['UPLOAD_FOLDER']):
    os.makedirs(app.config['UPLOAD_FOLDER'])

# 全局变量存储股票数据
stock_data = {}
last_update_time = None
imported_stock_list = []  # 存储导入的股票列表
auto_update_enabled = False  # 控制是否启用自动更新

# 配置
CONFIG = {
    'stock_interval': 1.0,      # 每只股票间隔时间（秒）- 增加到1秒避免被限制
    'round_interval': 600,      # 每轮更新间隔时间（秒）
    'request_timeout': 30,      # 请求超时时间（秒）- 增加超时时间
    'alert_threshold': 70.0,    # 企业微信提醒阈值（距离最低点涨幅%）
}

# 企业微信提醒配置
WECHAT_WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7bddd706-ef0f-4d6c-9e1f-3cc8a0a18d9e"

# 初始化企业微信提醒
wechat_alert = WeChatAlert(WECHAT_WEBHOOK_URL)

# 初始化A股交易时间监测器
trading_monitor = AStockTradingTimeMonitor()

def get_security_type(code):
    """根据代码判断证券类型"""
    code_str = str(code)

    if code_str.startswith('60'):  # 上海A股
        return '股票'
    elif code_str.startswith('00') or code_str.startswith('30'):  # 深圳A股/创业板
        return '股票'
    elif code_str.startswith('11') or code_str.startswith('12') or code_str.startswith('13'):  # 可转债
        return '可转债'
    elif code_str.startswith('51') or code_str.startswith('15'):  # ETF/基金
        return 'ETF/基金'
    else:
        return '其他'

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS



def validate_excel_format(file_path):
    """验证Excel文件格式"""
    try:
        df = pd.read_excel(file_path)

        # 检查必要的列是否存在
        required_columns = ['代码', '持有数量']
        missing_columns = []

        for col in required_columns:
            if col not in df.columns:
                missing_columns.append(col)

        if missing_columns:
            return False, f"缺少必要的列: {', '.join(missing_columns)}"

        # 检查是否有有效的股票数据 - 添加空值处理
        df_clean = df.dropna(subset=['代码'])
        df_clean['代码'] = df_clean['代码'].astype(str)
        valid_stocks = df_clean[df_clean['代码'].str.match(r'^[0-9]{6}$', na=False)]
        if len(valid_stocks) == 0:
            return False, "未找到有效的股票代码（6位数字）"

        return True, f"验证通过，找到 {len(valid_stocks)} 只股票"

    except Exception as e:
        return False, f"文件读取失败: {str(e)}"

def gen_secid(rawcode: str) -> str:
    """生成东方财富专用的secid"""
    if rawcode[0] == '6':
        return f'1.{rawcode}'  # 沪市
    return f'0.{rawcode}'      # 深市

def get_yearly_low_price_baostock(code: str) -> dict:
    """使用baostock获取股票一年内的最低价（前复权）"""

    try:
        # 计算一年前的日期
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365)

        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')

        print(f"📊 使用baostock获取 {code} 历史数据 ({start_date_str} 到 {end_date_str})...")

        # baostock需要带交易所前缀的代码格式
        # 例如：sh.600000 或 sz.000001
        bs_code = code
        if not code.startswith(('sh.', 'sz.')):
            if code.startswith('6'):
                bs_code = f'sh.{code}'
            elif code.startswith(('0', '3')):
                bs_code = f'sz.{code}'
            else:
                # 尝试去掉可能的前缀，保留6位数字
                clean_code = code
                if '.' in code:
                    clean_code = code.split('.')[0]
                if len(clean_code) > 6:
                    clean_code = clean_code[-6:]

                if clean_code.startswith('6'):
                    bs_code = f'sh.{clean_code}'
                elif clean_code.startswith(('0', '3')):
                    bs_code = f'sz.{clean_code}'

        print(f"📊 转换股票代码: {code} -> {bs_code}")

        # 登录baostock
        lg = bs.login()
        if lg.error_code != '0':
            print(f"❌ baostock登录失败: {lg.error_msg}")
            return {'yearly_low': None, 'low_date': None, 'data_points': 0}

        # 获取历史数据
        rs = bs.query_history_k_data_plus(
            bs_code,
            "date,code,open,high,low,close,preclose,volume,amount,adjustflag,turn,tradestatus,pctChg,isST",
            start_date=start_date_str,
            end_date=end_date_str,
            frequency="d",
            adjustflag="2"  # 前复权
        )

        if rs.error_code != '0':
            print(f"❌ baostock查询失败: {rs.error_msg}")
            bs.logout()
            return {'yearly_low': None, 'low_date': None, 'data_points': 0}

        # 转换为DataFrame
        data_list = []
        while (rs.error_code == '0') & rs.next():
            data_list.append(rs.get_row_data())

        bs.logout()

        if not data_list:
            print(f"⚠️ {code} baostock返回空数据")
            return {'yearly_low': None, 'low_date': None, 'data_points': 0}

        # 创建DataFrame
        df = pd.DataFrame(data_list, columns=rs.fields)

        # 转换数据类型
        df['low'] = pd.to_numeric(df['low'], errors='coerce')
        df['date'] = pd.to_datetime(df['date'])

        # 过滤有效数据
        df = df[df['low'] > 0]

        if df.empty:
            print(f"⚠️ {code} baostock没有有效的价格数据")
            return {'yearly_low': None, 'low_date': None, 'data_points': 0}

        # 找到最低价
        min_low_idx = df['low'].idxmin()
        yearly_low = df.loc[min_low_idx, 'low']
        low_date = df.loc[min_low_idx, 'date'].strftime('%Y-%m-%d')

        print(f"✅ {code} baostock历史数据获取成功，数据点: {len(df)}, 最低价: {yearly_low}, 日期: {low_date}")
        return {
            'yearly_low': float(yearly_low),
            'low_date': low_date,
            'data_points': len(df)
        }

    except Exception as e:
        print(f"❌ baostock获取股票 {code} 历史数据失败: {e}")
        try:
            bs.logout()
        except:
            pass
        return {'yearly_low': None, 'low_date': None, 'data_points': 0}

def get_yearly_low_price_akshare(code: str) -> dict:
    """使用akshare获取股票一年内的最低价（前复权）- 已弃用"""
    print(f"⚠️ akshare方法已弃用，跳过 {code}")
    return {'yearly_low': None, 'low_date': None, 'data_points': 0}

def get_yearly_low_price_with_retry(code: str) -> dict:
    """使用多种方法和重试机制获取股票一年内最低价"""

    # 方法1：尝试使用不同的akshare配置
    try:
        print(f"📊 方法1: 使用akshare获取 {code} 历史数据...")

        # 计算日期
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365)
        start_date_str = start_date.strftime('%Y%m%d')
        end_date_str = end_date.strftime('%Y%m%d')

        # 设置更长的超时时间
        import socket
        socket.setdefaulttimeout(60)

        df = ak.stock_zh_a_hist(
            symbol=code,
            period="daily",
            start_date=start_date_str,
            end_date=end_date_str,
            adjust="qfq"
        )

        if df is not None and not df.empty:
            min_low_idx = df['最低'].idxmin()
            yearly_low = df.loc[min_low_idx, '最低']
            low_date = df.loc[min_low_idx, '日期'].strftime('%Y-%m-%d')

            print(f"✅ {code} akshare成功，最低价: {yearly_low}, 日期: {low_date}")
            return {
                'yearly_low': yearly_low,
                'low_date': low_date,
                'data_points': len(df)
            }
    except Exception as e:
        print(f"❌ akshare方法失败: {e}")

    # 方法2：使用requests session和连接池
    try:
        print(f"📊 方法2: 使用session获取 {code} 历史数据...")

        session = requests.Session()
        session.mount('http://', requests.adapters.HTTPAdapter(pool_connections=1, pool_maxsize=1))
        session.mount('https://', requests.adapters.HTTPAdapter(pool_connections=1, pool_maxsize=1))

        end_date = datetime.now()
        start_date = end_date - timedelta(days=365)
        start_date_str = start_date.strftime('%Y%m%d')
        end_date_str = end_date.strftime('%Y%m%d')

        secid = gen_secid(code)

        params = {
            'fields1': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13',
            'fields2': 'f51,f52,f53,f54,f55',
            'beg': start_date_str,
            'end': end_date_str,
            'rtntype': '6',
            'secid': secid,
            'klt': '101',
            'fqt': '1',
        }

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
        }

        url = 'https://push2his.eastmoney.com/api/qt/stock/kline/get'

        response = session.get(url, params=params, headers=headers, timeout=60)

        if response.status_code == 200:
            json_data = response.json()

            if 'data' in json_data and json_data['data'] and 'klines' in json_data['data']:
                klines = json_data['data']['klines']

                if klines:
                    min_low = float('inf')
                    low_date = None

                    for kline in klines:
                        kline_data = kline.split(',')
                        if len(kline_data) >= 5:
                            low_price = float(kline_data[4])
                            if low_price < min_low:
                                min_low = low_price
                                low_date = kline_data[0]

                    print(f"✅ {code} session方法成功，最低价: {min_low}, 日期: {low_date}")
                    return {
                        'yearly_low': min_low,
                        'low_date': low_date,
                        'data_points': len(klines)
                    }

        session.close()

    except Exception as e:
        print(f"❌ session方法失败: {e}")

    # 方法3：使用简化的HTTP请求
    try:
        print(f"📊 方法3: 使用简化请求获取 {code} 历史数据...")

        import urllib.request
        import urllib.parse

        end_date = datetime.now()
        start_date = end_date - timedelta(days=365)
        start_date_str = start_date.strftime('%Y%m%d')
        end_date_str = end_date.strftime('%Y%m%d')

        secid = gen_secid(code)

        params = {
            'fields2': 'f51,f52,f53,f54,f55',
            'beg': start_date_str,
            'end': end_date_str,
            'secid': secid,
            'klt': '101',
            'fqt': '1',
        }

        url = 'https://push2his.eastmoney.com/api/qt/stock/kline/get?' + urllib.parse.urlencode(params)

        req = urllib.request.Request(url)
        req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')

        with urllib.request.urlopen(req, timeout=60) as response:
            data = json.loads(response.read().decode())

            if 'data' in data and data['data'] and 'klines' in data['data']:
                klines = data['data']['klines']

                if klines:
                    min_low = float('inf')
                    low_date = None

                    for kline in klines:
                        kline_data = kline.split(',')
                        if len(kline_data) >= 5:
                            low_price = float(kline_data[4])
                            if low_price < min_low:
                                min_low = low_price
                                low_date = kline_data[0]

                    print(f"✅ {code} urllib方法成功，最低价: {min_low}, 日期: {low_date}")
                    return {
                        'yearly_low': min_low,
                        'low_date': low_date,
                        'data_points': len(klines)
                    }

    except Exception as e:
        print(f"❌ urllib方法失败: {e}")

    print(f"💥 {code} 所有方法都失败了")
    return {'yearly_low': None, 'low_date': None, 'data_points': 0}

def load_yearly_low_from_csv(code: str) -> dict:
    """从CSV文件中读取一年内最低价数据"""
    try:
        # 查找最新的持仓数据文件
        csv_files = [f for f in os.listdir('.') if f.startswith('持仓数据_') and f.endswith('.csv')]
        if not csv_files:
            return {'yearly_low': None, 'low_date': None, 'data_points': 0}

        # 使用最新的文件
        latest_csv = sorted(csv_files)[-1]
        print(f"📁 从 {latest_csv} 读取 {code} 的历史数据...")

        df = pd.read_csv(latest_csv)

        # 查找对应股票的数据
        stock_row = df[df['code'] == code]
        if not stock_row.empty:
            row = stock_row.iloc[0]
            yearly_low = row.get('yearly_low')
            low_date = row.get('low_date')

            # 检查数据是否有效
            if pd.notna(yearly_low) and yearly_low != 'inf' and yearly_low > 0:
                print(f"✅ {code} 从CSV读取成功，最低价: {yearly_low}, 日期: {low_date}")
                return {
                    'yearly_low': float(yearly_low),
                    'low_date': str(low_date) if pd.notna(low_date) else None,
                    'data_points': 1,
                    'source': 'csv_cache'
                }

        print(f"⚠️ {code} 在CSV文件中未找到有效数据")
        return {'yearly_low': None, 'low_date': None, 'data_points': 0}

    except Exception as e:
        print(f"❌ 从CSV读取 {code} 数据失败: {e}")
        return {'yearly_low': None, 'low_date': None, 'data_points': 0}

def get_yearly_low_price(code: str) -> dict:
    """获取股票一年内的最低价（前复权）- 优先baostock，失败时使用CSV缓存"""

    # 首先尝试baostock
    result = get_yearly_low_price_baostock(code)
    if result['yearly_low'] is not None:
        return result

    print(f"⚠️ baostock获取失败，尝试其他网络方法获取 {code} 历史数据...")

    # baostock失败时，尝试其他网络方法
    result = get_yearly_low_price_with_retry(code)
    if result['yearly_low'] is not None:
        return result

    print(f"⚠️ 网络获取失败，尝试从CSV缓存读取 {code} 历史数据...")

    # 网络失败时，从CSV文件读取
    csv_result = load_yearly_low_from_csv(code)
    if csv_result['yearly_low'] is not None:
        return csv_result

    print(f"💥 {code} 所有方法都失败了")
    return {'yearly_low': None, 'low_date': None, 'data_points': 0}

def get_eastmoney_stock_data_v2(stock_code):
    """
    获取东方财富股票数据 - 第二版，包含股息率
    """
    # 根据股票代码确定市场
    if stock_code.startswith('6'):
        secid = f'1.{stock_code}'
    else:
        secid = f'0.{stock_code}'

    # 使用批量接口获取更准确的股息率数据
    url = 'http://push2.eastmoney.com/api/qt/ulist.np/get'

    # 请求参数 - 使用正确的股息率字段 f133
    params = {
        'fltt': '2',
        'invt': '2',
        'fields': 'f12,f14,f2,f3,f4,f23,f115,f114,f9,f133,f162,f173,f116,f127,f128,f129,f47,f48',
        'secids': secid,
        '_': str(int(time.time() * 1000))
    }

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'http://quote.eastmoney.com/',
    }

    try:
        response = requests.get(url, params=params, headers=headers, timeout=CONFIG['request_timeout'])

        if response.status_code == 200:
            data = json.loads(response.text)

            if 'data' in data and 'diff' in data['data'] and len(data['data']['diff']) > 0:
                stock_info = data['data']['diff'][0]

                # 处理股息率 - f133字段
                dividend_yield = stock_info.get('f133', 0)
                if dividend_yield == '-' or dividend_yield is None:
                    dividend_yield = 0
                else:
                    try:
                        dividend_yield = float(dividend_yield)
                    except:
                        dividend_yield = 0

                return {
                    'code': stock_info.get('f12', ''),
                    'name': stock_info.get('f14', ''),
                    'price': stock_info.get('f2', 0),
                    'change': stock_info.get('f4', 0),
                    'change_pct': stock_info.get('f3', 0),
                    'dividend_yield': dividend_yield,
                    'pb_ratio': stock_info.get('f23', None) if stock_info.get('f23') != '-' else None,
                    'pe_ttm': stock_info.get('f115', None) if stock_info.get('f115') != '-' else None,
                }

    except Exception as e:
        print(f"获取股票 {stock_code} 数据失败: {e}")
        return None

def get_stock_industry_info(stock_code):
    """
    获取股票行业信息 - 使用单独的接口
    """
    # 根据股票代码确定市场
    if stock_code.startswith('6'):
        secid = f'1.{stock_code}'
    else:
        secid = f'0.{stock_code}'

    # 东方财富API URL - 获取详细信息
    url = 'http://push2.eastmoney.com/api/qt/stock/get'

    params = {
        'fltt': '2',
        'invt': '2',
        'fields': 'f127,f128,f129',  # 行业、板块、概念
        'secid': secid,
        '_': str(int(time.time() * 1000))
    }

    try:
        response = requests.get(url, params=params, timeout=10)

        if response.status_code == 200:
            response_data = json.loads(response.text)
            data = response_data.get('data')

            if data and isinstance(data, dict):
                return {
                    'industry': data.get('f127', '') or '',      # 行业
                    'sector': data.get('f128', '') or '',        # 板块
                    'concept': data.get('f129', '') or '',       # 概念
                }
            else:
                return {
                    'industry': '',
                    'sector': '',
                    'concept': '',
                }

    except Exception as e:
        print(f"获取股票 {stock_code} 行业信息失败: {e}")

    return {'industry': '', 'sector': '', 'concept': ''}

def get_stock_realtime_and_valuation(stock_codes):
    """批量获取股票实时价格和估值数据"""
    
    # 构建股票代码列表
    secids = []
    for code in stock_codes:
        if code.startswith('6'):
            secids.append(f'1.{code}')
        else:
            secids.append(f'0.{code}')
    
    secids_str = ','.join(secids)
    
    url = 'http://push2.eastmoney.com/api/qt/ulist.np/get'
    
    params = {
        'fltt': '2',
        'invt': '2',
        'fields': 'f12,f14,f2,f3,f4,f23,f115,f114,f9,f133,f127',  # 添加f127行业字段
        'secids': secids_str,
        '_': str(int(time.time() * 1000))
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'http://quote.eastmoney.com/',
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=CONFIG['request_timeout'])
        
        if response.status_code == 200:
            data = json.loads(response.text)
            
            if 'data' in data and 'diff' in data['data']:
                results = {}
                
                for item in data['data']['diff']:
                    try:
                        code = item.get('f12', '')
                        
                        # 处理数据
                        pb_ratio = item.get('f23', None)
                        if pb_ratio == '-':
                            pb_ratio = None

                        pe_ttm = item.get('f115', None)
                        if pe_ttm == '-':
                            pe_ttm = None

                        # 处理股息率
                        dividend_yield = item.get('f133', None)
                        if dividend_yield == '-' or dividend_yield == 0:
                            dividend_yield = 0

                        # 处理行业
                        industry = item.get('f127', '')
                        if industry == '-':
                            industry = ''

                        results[code] = {
                            'code': code,
                            'name': item.get('f14', ''),
                            'price': item.get('f2', 0),
                            'change': item.get('f4', 0),
                            'change_pct': item.get('f3', 0),
                            'pb_ratio': pb_ratio,
                            'pe_ttm': pe_ttm,
                            'dividend_yield': dividend_yield,  # 新增股息率
                            'industry': industry,              # 新增行业
                            'update_time': datetime.now().strftime('%H:%M:%S')
                        }
                        
                    except Exception as e:
                        print(f"处理股票数据时出错: {e}")
                        continue
                
                return results
        
        return {}
        
    except Exception as e:
        print(f"批量获取股票数据失败: {e}")
        return {}

def load_stock_list():
    """加载股票列表"""
    try:
        df = pd.read_csv('stocks_list.csv', encoding='utf-8-sig')
        return df.to_dict('records')
    except Exception as e:
        print(f"加载股票列表失败: {e}")
        return []

def update_all_stocks():
    """更新所有股票数据"""
    global stock_data, last_update_time
    
    stock_list = load_stock_list()
    stock_codes = [str(stock['代码']).zfill(6) for stock in stock_list]
    
    print(f"\n🚀 开始更新 {len(stock_codes)} 只股票的综合数据...")
    print(f"⚙️  配置: 股票间隔 {CONFIG['stock_interval']}秒, 轮次间隔 {CONFIG['round_interval']}秒")
    
    # 第一步：批量获取实时价格和估值数据
    print("📊 第一步：获取实时价格和估值数据...")
    realtime_data = get_stock_realtime_and_valuation(stock_codes)
    
    if not realtime_data:
        print("❌ 无法获取实时数据")
        return
    
    print(f"✅ 成功获取 {len(realtime_data)} 只股票的实时数据")
    
    # 第二步：逐个获取一年内最低价
    print("📈 第二步：获取一年内最低价数据...")
    
    stock_data.clear()
    success_count = 0
    
    for i, stock in enumerate(stock_list):
        code = str(stock['代码']).zfill(6)
        
        print(f"[{i+1:3d}/{len(stock_list)}] 正在处理 {code}...", end=' ')
        
        # 获取实时数据（使用第二版方法）
        real_data = get_eastmoney_stock_data_v2(code)
        if real_data:
            # 获取历史最低价
            historical_data = get_yearly_low_price(code)

            # 获取行业信息
            industry_data = get_stock_industry_info(code)

            # 计算距离最低价的涨幅
            yearly_low = historical_data.get('yearly_low')
            current_price = real_data['price']

            # 确保current_price是数值类型
            try:
                current_price = float(current_price) if current_price else 0
            except (ValueError, TypeError):
                current_price = 0

            if yearly_low and yearly_low > 0 and current_price > 0:
                distance_from_low_pct = (current_price - yearly_low) / yearly_low * 100
            else:
                distance_from_low_pct = None
            
            stock_data[code] = {
                'code': code,
                'name': real_data['name'],
                'price': current_price,
                'change': real_data['change'],
                'change_pct': real_data['change_pct'],
                'yearly_low': yearly_low,
                'low_date': historical_data.get('low_date'),
                'distance_from_low_pct': distance_from_low_pct,
                'pb_ratio': real_data['pb_ratio'],
                'pe_ttm': real_data['pe_ttm'],
                'dividend_yield': real_data['dividend_yield'],  # 使用第二版的股息率
                'industry': industry_data['industry'],          # 使用第二版的行业
                'data_points': historical_data.get('data_points', 0),
                'update_time': datetime.now().strftime('%H:%M:%S')
            }
            
            success_count += 1
            
            # 显示结果
            dividend_str = f"{real_data['dividend_yield']:.2f}%" if real_data['dividend_yield'] > 0 else "无"
            industry_str = industry_data['industry'] if industry_data['industry'] else "未知"

            if yearly_low:
                print(f"✅ {real_data['name']} 价格:{current_price} 年内最低:{yearly_low:.2f} 距最低:{distance_from_low_pct:+.1f}% 股息率:{dividend_str} 行业:{industry_str}")
            else:
                print(f"⚠️  {real_data['name']} 价格:{current_price} 年内最低:无数据 股息率:{dividend_str} 行业:{industry_str}")
        else:
            # 如果没有实时数据，使用基本信息
            stock_data[code] = {
                'code': code,
                'name': stock['名称'],
                'price': 0, 'change': 0, 'change_pct': 0,
                'yearly_low': None, 'low_date': None, 'distance_from_low_pct': None,
                'pb_ratio': None, 'pe_ttm': None, 'data_points': 0,
                'update_time': datetime.now().strftime('%H:%M:%S'),
                'error': '暂无数据'
            }
            print(f"❌ 获取失败")
        
        # 每只股票间隔时间
        if i < len(stock_list) - 1:
            time.sleep(CONFIG['stock_interval'])
    
    last_update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    success_rate = success_count / len(stock_list) * 100

    print(f"\n✅ 更新完成！")
    print(f"   📈 成功率: {success_rate:.1f}% ({success_count}/{len(stock_list)})")
    print(f"   🕐 更新时间: {last_update_time}")

    # 检查高涨幅股票并发送企业微信提醒
    try:
        valid_stocks = [stock for stock in stock_data.values() if stock.get('distance_from_low_pct') is not None]
        if valid_stocks:
            high_gain_stocks = wechat_alert.check_and_alert_high_gains(valid_stocks, CONFIG['alert_threshold'])
            if high_gain_stocks:
                print(f"🚀 发现 {len(high_gain_stocks)} 只高涨幅股票，已发送企业微信提醒")
    except Exception as e:
        print(f"❌ 企业微信提醒发送失败: {e}")

def background_update():
    """后台智能更新股票数据 - 只在交易时间更新"""
    global auto_update_enabled
    round_count = 0

    print("🔄 后台更新线程已启动，等待手动触发...")
    print("💡 请访问网页并点击'刷新数据'按钮开始自动更新")

    while True:
        if not auto_update_enabled:
            time.sleep(5)  # 每5秒检查一次是否启用自动更新
            continue

        round_count += 1
        current_time = datetime.now()

        # 获取交易状态
        trading_status = trading_monitor.get_trading_status()
        should_update = trading_monitor.should_update_data(include_auction=True)  # 包含集合竞价
        update_interval = CONFIG['round_interval']  # 使用用户配置的间隔时间

        print(f"\n{'='*60}")
        print(f"🔄 第 {round_count} 轮检查 - {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📊 交易状态: {trading_status['message']}")
        print(f"{'='*60}")

        if should_update:
            print(f"✅ 当前为交易时间，开始更新股票数据...")
            update_all_stocks()
            print(f"✅ 股票数据更新完成")
        else:
            print(f"⏸️  当前为休市时间，跳过数据更新")
            print(f"📅 {trading_status['message']}")

        print(f"\n⏰ 下一轮检查将在 {update_interval} 秒后开始")
        if update_interval >= 60:
            print(f"   (约 {update_interval/60:.1f} 分钟后)")

        time.sleep(update_interval)

@app.route('/')
def index():
    """主页"""
    return render_template('持仓页面_v8.html')

@app.route('/api/stocks')
def get_stocks():
    """获取所有股票数据API"""
    return jsonify({
        'stocks': list(stock_data.values()),
        'last_update': last_update_time,
        'total_count': len(stock_data),
        'config': CONFIG
    })

@app.route('/api/stock/<stock_code>')
def get_stock(stock_code):
    """获取单只股票数据API"""
    if stock_code in stock_data:
        return jsonify(stock_data[stock_code])
    else:
        return jsonify({'error': '股票代码不存在'}), 404

@app.route('/api/trading-status')
def get_trading_status():
    """获取A股交易状态API"""
    try:
        status = trading_monitor.get_trading_status()
        return jsonify({
            'success': True,
            'data': status,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500

@app.route('/api/refresh-holidays')
def refresh_holidays():
    """刷新节假日数据API"""
    try:
        success = trading_monitor.refresh_current_year_holidays()
        if success:
            return jsonify({
                'success': True,
                'message': '节假日数据刷新成功',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
        else:
            return jsonify({
                'success': False,
                'message': '节假日数据刷新失败',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500

@app.route('/api/refresh')
def refresh_data():
    """手动刷新数据并启用自动更新"""
    global auto_update_enabled
    try:
        # 启用自动更新
        auto_update_enabled = True
        print("🚀 自动更新已启用！")

        # 立即执行一次更新
        threading.Thread(target=update_all_stocks, daemon=True).start()
        return jsonify({
            'success': True,
            'message': '数据刷新已启动，自动更新已启用',
            'last_update': last_update_time,
            'total_count': len(stock_data)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'刷新失败: {str(e)}'
        }), 500

@app.route('/api/stop-auto-update')
def stop_auto_update():
    """停止自动更新"""
    global auto_update_enabled
    try:
        auto_update_enabled = False
        print("⏸️ 自动更新已停止！")
        return jsonify({
            'success': True,
            'message': '自动更新已停止'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'停止失败: {str(e)}'
        }), 500

@app.route('/api/test-yearly-low/<code>')
def test_yearly_low_api(code):
    """测试一年内最低价获取API"""
    try:
        print(f"\n🧪 测试股票 {code} 的一年内最低价获取...")
        result = get_yearly_low_price(code)

        return jsonify({
            'success': True,
            'code': code,
            'result': result,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return jsonify({
            'success': False,
            'code': code,
            'error': str(e),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500

@app.route('/api/wechat/test')
def test_wechat():
    """测试企业微信连接"""
    try:
        success = wechat_alert.test_connection()
        return jsonify({
            'success': success,
            'message': '企业微信连接测试成功' if success else '企业微信连接测试失败'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'测试失败: {str(e)}'
        }), 500

@app.route('/api/clear-data', methods=['POST'])
def clear_data():
    """清空数据API"""
    global stock_data, last_update_time

    try:
        stock_count = len(stock_data)
        stock_data.clear()
        last_update_time = None

        return jsonify({
            'success': True,
            'message': f'已清空 {stock_count} 只股票的数据',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500

@app.route('/api/upload-holdings', methods=['POST'])
def upload_holdings():
    """上传持仓表格API"""
    global imported_stock_list

    try:
        # 检查是否有文件上传
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': '未选择文件'
            }), 400

        file = request.files['file']

        # 检查文件名
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': '未选择文件'
            }), 400

        # 检查文件类型
        if not allowed_file(file.filename):
            return jsonify({
                'success': False,
                'error': '只支持 .xlsx 和 .xls 格式的Excel文件'
            }), 400

        # 保存文件
        filename = secure_filename(file.filename)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{timestamp}_{filename}"
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)

        # 验证文件格式
        is_valid, message = validate_excel_format(file_path)
        if not is_valid:
            os.remove(file_path)  # 删除无效文件
            return jsonify({
                'success': False,
                'error': message
            }), 400

        # 读取股票数据
        df = pd.read_excel(file_path)

        # 处理股票数据 - 添加空值处理
        # 首先过滤掉代码列为空的行
        df = df.dropna(subset=['代码'])

        # 确保代码列转换为字符串，并过滤6位数字的股票代码
        df['代码'] = df['代码'].astype(str)
        stock_df = df[df['代码'].str.match(r'^[0-9]{6}$', na=False)].copy()

        # 补齐代码为6位
        stock_df['代码'] = stock_df['代码'].str.zfill(6)

        # 处理持有数量，确保是数值类型
        stock_df['持有数量'] = pd.to_numeric(stock_df['持有数量'], errors='coerce').fillna(0).astype(int)

        # 过滤掉持有数量为0或负数的股票
        stock_df = stock_df[stock_df['持有数量'] > 0]

        # 转换为列表，确保处理空值
        imported_stock_list = []
        for _, row in stock_df.iterrows():
            # 获取股票名称，如果为空或NaN则使用空字符串
            stock_name = row.get('名称', '')
            if pd.isna(stock_name):
                stock_name = ''

            imported_stock_list.append({
                '代码': row['代码'],
                '名称': str(stock_name),
                '持仓数量': int(row['持有数量'])
            })

        # 删除临时文件
        os.remove(file_path)

        # 自动获取股票数据
        print(f"\n🔄 开始自动获取 {len(imported_stock_list)} 只股票的数据...")

        stock_data.clear()  # 清空现有数据
        success_count = 0

        for i, stock in enumerate(imported_stock_list):
            try:
                code = stock['代码']
                holdings = stock['持仓数量']

                print(f"[{i+1:3d}/{len(imported_stock_list)}] 正在处理 {code}...", end=' ')

                # 获取实时数据
                real_data = get_eastmoney_stock_data_v2(code)
                if real_data and real_data.get('name'):
                    # 获取历史最低价
                    historical_data = get_yearly_low_price(code)

                    # 获取行业信息
                    industry_data = get_stock_industry_info(code)

                    # 计算距离最低价的涨幅
                    yearly_low = historical_data.get('yearly_low')
                    current_price = real_data['price']

                    # 确保current_price是数值类型
                    try:
                        current_price = float(current_price) if current_price else 0
                    except (ValueError, TypeError):
                        current_price = 0

                    if yearly_low and yearly_low > 0 and current_price > 0:
                        distance_from_low_pct = (current_price - yearly_low) / yearly_low * 100
                    else:
                        distance_from_low_pct = None

                    # 计算市值
                    market_value = current_price * holdings if current_price > 0 else 0

                    stock_data[code] = {
                        'code': code,
                        'name': real_data['name'],
                        'price': current_price,
                        'change': real_data['change'],
                        'change_pct': real_data['change_pct'],
                        'yearly_low': yearly_low,
                        'low_date': historical_data.get('low_date'),
                        'distance_from_low_pct': distance_from_low_pct,
                        'pb_ratio': real_data['pb_ratio'],
                        'pe_ttm': real_data['pe_ttm'],
                        'dividend_yield': real_data['dividend_yield'],
                        'industry': industry_data['industry'],
                        'holdings': int(holdings),
                        'market_value': round(market_value, 2),
                        'data_points': historical_data.get('data_points', 0),
                        'update_time': datetime.now().strftime('%H:%M:%S'),
                        'type': get_security_type(code)  # 新增：证券类型
                    }

                    success_count += 1
                    print(f"✅ {real_data['name']} 价格:{current_price} 持仓:{holdings}股 市值:{market_value:.2f}元")
                else:
                    # 对于无法获取数据的代码（如可转债、ETF等），创建基本记录
                    stock_name = stock.get('名称', f'代码{code}')
                    stock_data[code] = {
                        'code': code,
                        'name': stock_name,
                        'price': 0,
                        'change': 0,
                        'change_pct': 0,
                        'yearly_low': None,
                        'low_date': None,
                        'distance_from_low_pct': None,
                        'pb_ratio': None,
                        'pe_ttm': None,
                        'dividend_yield': 0,
                        'industry': '其他',
                        'holdings': int(holdings),
                        'market_value': 0,
                        'data_points': 0,
                        'update_time': datetime.now().strftime('%H:%M:%S'),
                        'type': get_security_type(code)  # 新增：证券类型
                    }
                    print(f"⚠️ {stock_name} - 无法获取股票数据（可能是可转债/ETF/基金）")

                # 添加延迟避免请求过快
                time.sleep(0.5)

            except Exception as e:
                print(f"❌ 处理 {code} 时出错: {e}")
                continue

        # 更新最后更新时间
        global last_update_time
        last_update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        return jsonify({
            'success': True,
            'message': f'成功导入 {len(imported_stock_list)} 只股票并获取数据，成功获取 {success_count} 只股票信息',
            'stock_count': len(imported_stock_list),
            'success_count': success_count,
            'timestamp': last_update_time
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500

@app.route('/api/refresh-data', methods=['POST'])
def refresh_all_data():
    """重新获取数据API"""
    global stock_data, last_update_time, imported_stock_list

    try:
        if not imported_stock_list:
            return jsonify({
                'success': False,
                'error': '请先导入持仓表格'
            }), 400

        # 清空现有数据
        stock_data.clear()

        print(f"\n🔄 开始重新获取 {len(imported_stock_list)} 只股票的数据...")

        success_count = 0

        for i, stock in enumerate(imported_stock_list):
            code = stock['代码']
            holdings = stock['持仓数量']

            print(f"[{i+1:3d}/{len(imported_stock_list)}] 正在处理 {code}...", end=' ')

            # 获取实时数据
            real_data = get_eastmoney_stock_data_v2(code)
            if real_data:
                # 获取历史最低价
                historical_data = get_yearly_low_price(code)

                # 获取行业信息
                industry_data = get_stock_industry_info(code)

                # 计算距离最低价的涨幅
                yearly_low = historical_data.get('yearly_low')
                current_price = real_data['price']

                # 确保current_price是数值类型
                try:
                    current_price = float(current_price) if current_price else 0
                except (ValueError, TypeError):
                    current_price = 0

                if yearly_low and yearly_low > 0 and current_price > 0:
                    distance_from_low_pct = (current_price - yearly_low) / yearly_low * 100
                else:
                    distance_from_low_pct = None

                # 计算市值
                market_value = current_price * holdings if current_price > 0 else 0

                stock_data[code] = {
                    'code': code,
                    'name': real_data['name'],
                    'price': current_price,
                    'change': real_data['change'],
                    'change_pct': real_data['change_pct'],
                    'yearly_low': yearly_low,
                    'low_date': historical_data.get('low_date'),
                    'distance_from_low_pct': distance_from_low_pct,
                    'pb_ratio': real_data['pb_ratio'],
                    'pe_ttm': real_data['pe_ttm'],
                    'dividend_yield': real_data['dividend_yield'],
                    'industry': industry_data['industry'],
                    'holdings': int(holdings),
                    'market_value': round(market_value, 2),
                    'data_points': historical_data.get('data_points', 0),
                    'update_time': datetime.now().strftime('%H:%M:%S')
                }

                success_count += 1
                print(f"✅ {real_data['name']} 价格:{current_price} 持仓:{holdings}股 市值:{market_value:.2f}元")
            else:
                print(f"❌ 获取失败")

            # 添加延迟避免请求过快
            time.sleep(0.5)

        last_update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        return jsonify({
            'success': True,
            'message': f'成功获取 {success_count}/{len(imported_stock_list)} 只股票的数据',
            'success_count': success_count,
            'total_count': len(imported_stock_list),
            'timestamp': last_update_time
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500

@app.route('/api/wechat/summary')
def send_wechat_summary():
    """发送每日摘要到企业微信"""
    try:
        valid_stocks = [stock for stock in stock_data.values() if stock.get('distance_from_low_pct') is not None]
        if valid_stocks:
            wechat_alert.send_daily_summary(valid_stocks)
            return jsonify({
                'success': True,
                'message': f'每日摘要已发送，包含 {len(valid_stocks)} 只股票'
            })
        else:
            return jsonify({
                'success': False,
                'message': '暂无有效股票数据'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'发送失败: {str(e)}'
        }), 500

@app.route('/api/wechat/alert/<float:threshold>')
def manual_alert_check(threshold):
    """手动检查高涨幅股票并发送提醒"""
    try:
        valid_stocks = [stock for stock in stock_data.values() if stock.get('distance_from_low_pct') is not None]
        if valid_stocks:
            # 临时清空已发送记录，确保能发送
            wechat_alert.clear_sent_alerts()
            high_gain_stocks = wechat_alert.check_and_alert_high_gains(valid_stocks, threshold)

            if high_gain_stocks:
                return jsonify({
                    'success': True,
                    'message': f'发现 {len(high_gain_stocks)} 只高涨幅股票，提醒已发送',
                    'stocks': [{'code': s['code'], 'name': s['name'], 'gain': s['distance_from_low_pct']} for s in high_gain_stocks]
                })
            else:
                return jsonify({
                    'success': True,
                    'message': f'暂无涨幅超过 {threshold}% 的股票'
                })
        else:
            return jsonify({
                'success': False,
                'message': '暂无有效股票数据'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'检查失败: {str(e)}'
        }), 500

if __name__ == '__main__':
    print("=== 🚀 启动持仓系统 V8 饼图版 ===")
    print("📊 数据包含：最新价、一年内最低价、距最低点涨幅、市净率、TTM市盈率、股息率、行业、持仓数量、市值")
    print("🕐 智能特性：只在A股交易时间更新数据，休市时间自动暂停")
    print("📁 数据管理：支持清空数据、导入持仓表格、重新获取数据")
    print("🏷️ 分类筛选：支持按股票、可转债、ETF/基金分类显示")
    print("📈 统计分析：行业市值统计、资产类别统计、总资产统计")
    print("🎨 界面优化：统计面板置顶、行业仓位显示、布局优化")
    print("🥧 饼图展示：资产配置和行业分布以饼图形式可视化展示")

    # 显示交易状态
    try:
        status = trading_monitor.get_trading_status()
        print(f"📈 当前交易状态: {status['message']}")
        print(f"⏰ 更新间隔: {CONFIG['round_interval']}秒 (约{CONFIG['round_interval']/60:.0f}分钟)")
    except Exception as e:
        print(f"⚠️ 获取交易状态失败: {e}")

    # 启动后台智能更新线程
    print("🔄 启动后台智能更新线程...")
    update_thread = threading.Thread(target=background_update, daemon=True)
    update_thread.start()

    print("🌐 启动Flask Web服务...")
    print("🔗 访问 http://localhost:5000 查看综合股票数据")
    print("🔗 访问 http://localhost:5000/api/trading-status 查看交易状态")
    print("💡 提示：系统已启动但未开始自动更新，请访问网页点击'刷新数据'按钮开始！")

    # 启动Flask应用
    app.run(debug=False, host='0.0.0.0', port=5000)
