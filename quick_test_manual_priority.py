#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试手动优先级功能
用于验证手动状态在单个股票更新时是否被保护
"""

import requests
import time

def quick_test():
    """快速测试手动优先级功能"""
    base_url = 'http://localhost:5000'
    
    print("🚀 快速测试手动优先级功能")
    print("=" * 50)
    
    # 1. 启用手动优先级
    print("\n1️⃣ 启用手动优先级...")
    try:
        response = requests.post(f'{base_url}/api/sell-signal-priority', 
                               json={'manual_priority_over_auto': True})
        if response.status_code == 200 and response.json().get('success'):
            print("✅ 手动优先级已启用")
        else:
            print("❌ 启用失败")
            return False
    except Exception as e:
        print(f"❌ 启用异常: {e}")
        return False
    
    # 2. 获取第一个股票
    print("\n2️⃣ 获取测试股票...")
    try:
        response = requests.get(f'{base_url}/api/stocks')
        if response.status_code == 200:
            result = response.json()
            if result.get('success') and result['data']:
                test_stock = result['data'][0]
                stock_code = test_stock['code']
                stock_name = test_stock['name']
                print(f"✅ 测试股票: {stock_name} ({stock_code})")
            else:
                print("❌ 没有股票数据")
                return False
        else:
            print("❌ 获取股票失败")
            return False
    except Exception as e:
        print(f"❌ 获取异常: {e}")
        return False
    
    # 3. 设置手动状态
    print("\n3️⃣ 设置手动状态为'已清仓'...")
    try:
        response = requests.post(f'{base_url}/api/set-custom-status', json={
            'stock_code': stock_code,
            'status_type': 'cleared',
            'status_text': '已清仓',
            'status_emoji': '🔥'
        })
        if response.status_code == 200 and response.json().get('success'):
            print("✅ 手动状态设置成功")
        else:
            print("❌ 设置失败")
            return False
    except Exception as e:
        print(f"❌ 设置异常: {e}")
        return False
    
    # 4. 等待状态生效
    print("\n4️⃣ 等待状态生效...")
    time.sleep(2)
    
    # 5. 执行单个股票更新
    print("\n5️⃣ 执行单个股票更新（关键测试）...")
    try:
        response = requests.post(f'{base_url}/api/update-single-stock/{stock_code}')
        if response.status_code == 200 and response.json().get('success'):
            print("✅ 单个股票更新完成")
        else:
            print("❌ 更新失败")
            return False
    except Exception as e:
        print(f"❌ 更新异常: {e}")
        return False
    
    # 6. 验证状态是否被保护
    print("\n6️⃣ 验证状态保护...")
    time.sleep(2)
    
    try:
        response = requests.get(f'{base_url}/api/stocks')
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                for stock in result['data']:
                    if stock['code'] == stock_code:
                        current_reason = stock.get('sell_reason', '')
                        is_custom = stock.get('custom_status', False)
                        
                        print(f"   当前状态: {current_reason}")
                        print(f"   自定义状态: {is_custom}")
                        
                        if is_custom and '已清仓' in current_reason:
                            print("🎉 测试成功！手动状态被保护，未被单个更新覆盖")
                            return True
                        else:
                            print("❌ 测试失败！手动状态被覆盖")
                            return False
                
                print("❌ 未找到测试股票")
                return False
            else:
                print("❌ 获取股票失败")
                return False
        else:
            print("❌ 验证请求失败")
            return False
    except Exception as e:
        print(f"❌ 验证异常: {e}")
        return False

if __name__ == '__main__':
    print("请确保持仓系统正在运行 (http://localhost:5000)")
    input("按回车键开始测试...")
    
    success = quick_test()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 测试通过！手动优先级功能正常工作")
        print("💡 现在您可以放心地点击单个股票更新，手动状态不会被覆盖")
    else:
        print("❌ 测试失败！请检查系统配置")
    
    print("\n📝 提示：")
    print("- 如果测试成功，说明修复已生效")
    print("- 您可以在界面中点击'🎯 优先级控制'按钮管理设置")
    print("- 建议保持手动优先级启用状态")
