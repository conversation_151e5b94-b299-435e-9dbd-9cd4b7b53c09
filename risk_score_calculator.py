#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票风险评估分数计算器
基于通达信爆雷宝数据进行风险分数计算
"""

import json
import requests
from typing import Dict, List, Tuple

class RiskScoreCalculator:
    def __init__(self):
        self.risk_weights = {
            "财务类风险": 0.35,      # 财务风险权重35%
            "市场类风险": 0.25,      # 市场风险权重25%
            "交易类风险": 0.25,      # 交易风险权重25%
            "ST风险和退市": 0.15     # ST退市风险权重15%
        }
    
    def fetch_risk_data(self, stock_code: str) -> Dict:
        """
        获取股票风险数据
        """
        url = f"http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/json/{stock_code}.json"
        try:
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"获取数据失败: {e}")
            return {}
    
    def calculate_category_score(self, category_data: Dict) -> Tuple[float, int, int]:
        """
        计算单个风险类别的分数
        返回: (总分数, 触发风险数, 总风险数)
        """
        total_score = 0
        triggered_count = 0
        total_count = 0
        
        for risk_item in category_data.get("rows", []):
            fs = risk_item.get("fs", 0)  # 风险分数
            trig = risk_item.get("trig", 0)  # 是否触发
            level = risk_item.get("level", 1)  # 风险等级
            
            total_count += 1
            
            if trig == 1:  # 如果风险被触发
                triggered_count += 1
                # 触发的风险按原分数计算，可以根据等级调整权重
                level_weight = min(level / 5.0, 2.0)  # 等级权重，最高2倍
                total_score += fs * level_weight
            
            # 检查关联风险
            for common_risk in risk_item.get("commonlxid", []):
                common_fs = common_risk.get("fs", 0)
                common_trig = common_risk.get("trig", 0)
                common_level = common_risk.get("level", 1)
                
                total_count += 1
                if common_trig == 1:
                    triggered_count += 1
                    common_level_weight = min(common_level / 5.0, 2.0)
                    total_score += common_fs * common_level_weight
        
        return total_score, triggered_count, total_count
    
    def calculate_total_score(self, risk_data: Dict) -> Dict:
        """
        计算总风险分数
        """
        if not risk_data or "data" not in risk_data:
            return {"error": "无效的数据格式"}
        
        category_scores = {}
        total_weighted_score = 0
        total_triggered = 0
        total_risks = 0
        
        # 遍历每个风险类别
        for category in risk_data["data"]:
            category_name = category.get("name", "未知类别")
            score, triggered, total = self.calculate_category_score(category)
            
            category_scores[category_name] = {
                "原始分数": score,
                "触发风险数": triggered,
                "总风险数": total,
                "触发率": f"{triggered/total*100:.1f}%" if total > 0 else "0%"
            }
            
            # 应用权重
            weight = self.risk_weights.get(category_name, 0.25)
            weighted_score = score * weight
            total_weighted_score += weighted_score
            total_triggered += triggered
            total_risks += total
            
            category_scores[category_name]["权重"] = f"{weight*100:.0f}%"
            category_scores[category_name]["加权分数"] = round(weighted_score, 2)
        
        # 计算风险等级
        risk_level = self.get_risk_level(total_weighted_score)
        
        return {
            "股票代码": risk_data.get("name", "未知"),
            "总分数": round(total_weighted_score, 2),
            "风险等级": risk_level,
            "总触发风险数": total_triggered,
            "总风险项数": total_risks,
            "整体触发率": f"{total_triggered/total_risks*100:.1f}%" if total_risks > 0 else "0%",
            "各类别详情": category_scores
        }
    
    def get_risk_level(self, score: float) -> str:
        """
        根据分数确定风险等级
        """
        if score >= 100:
            return "极高风险"
        elif score >= 80:
            return "高风险"
        elif score >= 60:
            return "中高风险"
        elif score >= 40:
            return "中等风险"
        elif score >= 20:
            return "中低风险"
        elif score >= 10:
            return "低风险"
        else:
            return "极低风险"
    
    def analyze_stock(self, stock_code: str) -> Dict:
        """
        分析单只股票的风险
        """
        print(f"正在分析股票 {stock_code} 的风险...")
        
        # 获取数据
        risk_data = self.fetch_risk_data(stock_code)
        if not risk_data:
            return {"error": f"无法获取股票 {stock_code} 的数据"}
        
        # 计算分数
        result = self.calculate_total_score(risk_data)
        return result
    
    def print_analysis_result(self, result: Dict):
        """
        打印分析结果
        """
        if "error" in result:
            print(f"错误: {result['error']}")
            return
        
        print("\n" + "="*60)
        print(f"股票: {result['股票代码']}")
        print(f"总风险分数: {result['总分数']}")
        print(f"风险等级: {result['风险等级']}")
        print(f"触发风险: {result['总触发风险数']}/{result['总风险项数']} ({result['整体触发率']})")
        print("="*60)
        
        print("\n各类别风险详情:")
        for category, details in result["各类别详情"].items():
            print(f"\n【{category}】")
            print(f"  原始分数: {details['原始分数']}")
            print(f"  加权分数: {details['加权分数']} (权重: {details['权重']})")
            print(f"  触发情况: {details['触发风险数']}/{details['总风险数']} ({details['触发率']})")

def main():
    """
    主函数
    """
    calculator = RiskScoreCalculator()
    
    # 示例：分析平安银行 (000001)
    stock_code = "000001"
    result = calculator.analyze_stock(stock_code)
    calculator.print_analysis_result(result)
    
    # 可以分析多只股票
    # other_stocks = ["000002", "600000", "600036"]
    # for code in other_stocks:
    #     result = calculator.analyze_stock(code)
    #     calculator.print_analysis_result(result)

if __name__ == "__main__":
    main()
