"""
快速测试Chrome启动
"""

def test_chrome_startup():
    """测试Chrome启动"""
    print("🔍 测试Chrome浏览器启动...")
    
    try:
        # 检查selenium
        import selenium
        print(f"✅ Selenium版本: {selenium.__version__}")
    except ImportError:
        print("❌ Selenium未安装，正在安装...")
        import subprocess
        import sys
        subprocess.check_call([sys.executable, "-m", "pip", "install", "selenium"])
        import selenium
        print(f"✅ Selenium安装完成: {selenium.__version__}")
    
    try:
        # 检查webdriver-manager
        import webdriver_manager
        print("✅ WebDriver Manager已安装")
    except ImportError:
        print("❌ WebDriver Manager未安装，正在安装...")
        import subprocess
        import sys
        subprocess.check_call([sys.executable, "-m", "pip", "install", "webdriver-manager"])
        print("✅ WebDriver Manager安装完成")
    
    # 测试Chrome启动
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        
        print("🔄 配置Chrome选项...")
        options = Options()
        options.add_argument('--headless')  # 无头模式
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1200,800')
        
        print("🔄 下载/配置ChromeDriver...")
        service = Service(ChromeDriverManager().install())
        
        print("🔄 启动Chrome浏览器...")
        driver = webdriver.Chrome(service=service, options=options)
        
        print("✅ Chrome启动成功！")
        
        # 测试访问页面
        print("🔄 测试访问扫雷宝页面...")
        test_url = "http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/bxb.html?code=300479&color=0"
        driver.get(test_url)
        
        print("✅ 页面访问成功！")
        
        # 等待页面加载
        import time
        time.sleep(3)
        
        # 获取页面标题
        title = driver.title
        print(f"📄 页面标题: {title}")
        
        # 截图测试
        screenshot_path = "test_screenshot.png"
        driver.save_screenshot(screenshot_path)
        print(f"📸 截图保存: {screenshot_path}")
        
        # 检查截图文件
        import os
        if os.path.exists(screenshot_path):
            file_size = os.path.getsize(screenshot_path)
            print(f"✅ 截图文件大小: {file_size} 字节")
        else:
            print("❌ 截图文件未生成")
        
        # 关闭浏览器
        driver.quit()
        print("✅ 浏览器正常关闭")
        
        return True
        
    except Exception as e:
        print(f"❌ Chrome启动失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 尝试可见模式
        try:
            print("\n🔄 尝试可见模式...")
            options = Options()
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            # 不使用headless模式
            
            driver = webdriver.Chrome(service=service, options=options)
            print("✅ 可见模式启动成功！")
            
            driver.get("https://www.baidu.com")
            print("✅ 测试页面访问成功")
            
            import time
            time.sleep(2)
            
            driver.quit()
            print("✅ 可见模式测试完成")
            
            return True
            
        except Exception as e2:
            print(f"❌ 可见模式也失败: {e2}")
            return False

def test_ocr_environment():
    """测试OCR环境"""
    print("\n🔍 测试OCR环境...")
    
    try:
        from PIL import Image
        print("✅ Pillow已安装")
    except ImportError:
        print("❌ Pillow未安装，正在安装...")
        import subprocess
        import sys
        subprocess.check_call([sys.executable, "-m", "pip", "install", "Pillow"])
        print("✅ Pillow安装完成")
    
    try:
        import pytesseract
        print("✅ PyTesseract已安装")
        
        # 检查tesseract路径
        import os
        tesseract_path = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
        if os.path.exists(tesseract_path):
            print(f"✅ 找到Tesseract: {tesseract_path}")
            pytesseract.pytesseract.tesseract_cmd = tesseract_path
        else:
            print("⚠️ 未找到Tesseract OCR")
            print("💡 请从以下地址下载安装:")
            print("   https://github.com/UB-Mannheim/tesseract/wiki")
            
    except ImportError:
        print("❌ PyTesseract未安装，正在安装...")
        import subprocess
        import sys
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pytesseract"])
        print("✅ PyTesseract安装完成")

def main():
    """主测试函数"""
    print("🚀 Chrome浏览器快速测试")
    print("=" * 40)
    
    # 测试Chrome启动
    chrome_ok = test_chrome_startup()
    
    # 测试OCR环境
    test_ocr_environment()
    
    print("\n" + "=" * 40)
    if chrome_ok:
        print("🎉 Chrome环境测试通过！")
        print("💡 现在可以运行扫雷宝自动化程序了")
    else:
        print("❌ Chrome环境有问题")
        print("💡 请检查错误信息并解决")

if __name__ == "__main__":
    main()
