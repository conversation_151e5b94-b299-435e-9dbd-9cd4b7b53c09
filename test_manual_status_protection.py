#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动状态保护测试工具
==================

用于测试持仓系统的手动状态保护功能是否正常工作。

测试内容：
1. 设置手动状态
2. 验证状态保护
3. 移除手动状态
4. 验证恢复自动计算

作者: AI Assistant
版本: 1.0
日期: 2025-07-24
"""

import requests
import time
import json

def test_manual_status_protection():
    """测试手动状态保护功能"""
    base_url = 'http://localhost:5000'
    
    print("🧪 开始测试手动状态保护功能")
    print("=" * 50)
    
    # 测试用的股票代码（假设存在）
    test_stock_code = '000822'  # 山东海化
    
    try:
        # 1. 获取当前股票数据
        print("📊 步骤1: 获取当前股票数据")
        response = requests.get(f'{base_url}/api/stocks')
        if response.status_code != 200:
            print("❌ 无法获取股票数据，请确保系统正在运行")
            return False
        
        stocks_data = response.json()
        if not stocks_data.get('success'):
            print("❌ 获取股票数据失败")
            return False
        
        stocks = stocks_data['data']
        test_stock = None
        for stock in stocks:
            if stock['code'] == test_stock_code:
                test_stock = stock
                break
        
        if not test_stock:
            print(f"❌ 未找到测试股票 {test_stock_code}")
            return False
        
        print(f"✅ 找到测试股票: {test_stock['name']} ({test_stock['code']})")
        print(f"   当前信号: {test_stock.get('sell_signal', 'unknown')}")
        print(f"   当前原因: {test_stock.get('sell_reason', 'unknown')}")
        
        # 2. 设置手动状态
        print("\n📝 步骤2: 设置手动状态为'已清仓'")
        set_status_data = {
            'stock_code': test_stock_code,
            'status_type': 'cleared',
            'status_text': '已清仓',
            'status_emoji': '🔥'
        }
        
        response = requests.post(f'{base_url}/api/set-custom-status', json=set_status_data)
        if response.status_code != 200:
            print("❌ 设置手动状态失败")
            return False
        
        result = response.json()
        if not result.get('success'):
            print(f"❌ 设置手动状态失败: {result.get('message')}")
            return False
        
        print(f"✅ 手动状态设置成功: {result['message']}")
        
        # 3. 等待并验证状态保护
        print("\n🔒 步骤3: 验证状态保护（等待5秒后检查）")
        time.sleep(5)
        
        response = requests.get(f'{base_url}/api/stocks')
        if response.status_code == 200:
            stocks_data = response.json()
            if stocks_data.get('success'):
                stocks = stocks_data['data']
                for stock in stocks:
                    if stock['code'] == test_stock_code:
                        current_signal = stock.get('sell_signal', 'unknown')
                        current_reason = stock.get('sell_reason', 'unknown')
                        has_manual_status = stock.get('manual_status')
                        
                        print(f"   当前信号: {current_signal}")
                        print(f"   当前原因: {current_reason}")
                        print(f"   手动状态标记: {has_manual_status}")
                        
                        if current_signal == 'cleared' and current_reason == '已清仓':
                            print("✅ 状态保护正常工作，手动状态未被覆盖")
                        else:
                            print("❌ 状态保护失败，手动状态被覆盖")
                            return False
                        break
        
        # 4. 移除手动状态保护
        print("\n🔄 步骤4: 移除手动状态保护")
        remove_data = {'stock_code': test_stock_code}
        
        response = requests.post(f'{base_url}/api/remove-manual-status', json=remove_data)
        if response.status_code != 200:
            print("❌ 移除手动状态失败")
            return False
        
        result = response.json()
        if not result.get('success'):
            print(f"❌ 移除手动状态失败: {result.get('message')}")
            return False
        
        print(f"✅ 手动状态移除成功: {result['message']}")
        print(f"   新信号: {result['data']['new_signal']}")
        print(f"   新原因: {result['data']['new_reason']}")
        
        # 5. 验证恢复自动计算
        print("\n🔍 步骤5: 验证恢复自动计算")
        time.sleep(2)
        
        response = requests.get(f'{base_url}/api/stocks')
        if response.status_code == 200:
            stocks_data = response.json()
            if stocks_data.get('success'):
                stocks = stocks_data['data']
                for stock in stocks:
                    if stock['code'] == test_stock_code:
                        final_signal = stock.get('sell_signal', 'unknown')
                        final_reason = stock.get('sell_reason', 'unknown')
                        has_manual_status = stock.get('manual_status')
                        
                        print(f"   最终信号: {final_signal}")
                        print(f"   最终原因: {final_reason}")
                        print(f"   手动状态标记: {has_manual_status}")
                        
                        if not has_manual_status:
                            print("✅ 手动状态保护已移除，恢复自动计算")
                        else:
                            print("❌ 手动状态保护未完全移除")
                            return False
                        break
        
        print("\n🎉 所有测试通过！手动状态保护功能正常工作。")
        return True
        
    except Exception as e:
        print(f"\n💥 测试过程出现异常: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 手动状态保护功能测试")
    print("=" * 60)
    
    # 检查系统是否运行
    try:
        response = requests.get('http://localhost:5000/api/stocks', timeout=5)
        if response.status_code != 200:
            print("❌ 持仓系统未运行或无法访问")
            print("💡 请先启动系统: python \"持仓系统_v13 - 副本 - 副本.py\"")
            return
    except Exception as e:
        print(f"❌ 无法连接到持仓系统: {e}")
        print("💡 请先启动系统: python \"持仓系统_v13 - 副本 - 副本.py\"")
        return
    
    # 运行测试
    success = test_manual_status_protection()
    
    if success:
        print("\n✅ 测试结果: 手动状态保护功能正常")
        print("\n📋 功能说明:")
        print("• 手动设置状态后，系统不会自动覆盖")
        print("• 可以通过❌按钮移除手动状态保护")
        print("• 移除后恢复自动卖出信号计算")
    else:
        print("\n❌ 测试结果: 手动状态保护功能异常")
        print("\n🔧 建议操作:")
        print("• 检查代码修改是否正确")
        print("• 重启持仓系统")
        print("• 查看系统日志")

if __name__ == '__main__':
    main()
