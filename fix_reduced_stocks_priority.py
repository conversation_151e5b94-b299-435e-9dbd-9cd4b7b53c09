#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复已减半股票的优先级
"""

import sys
import os
import json

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入系统
from 持仓系统_v14 import calculate_sell_signal

def fix_reduced_stocks_priority():
    """修复已减半股票的优先级"""
    print("🔧 修复已减半股票的优先级...")
    
    # 读取缓存文件
    cache_file = 'stock_data_cache.json'
    if not os.path.exists(cache_file):
        print(f"❌ 缓存文件不存在: {cache_file}")
        return
    
    with open(cache_file, 'r', encoding='utf-8') as f:
        cache_data = json.load(f)
        stock_data = cache_data.get('stock_data', {})
    
    print(f"📊 找到 {len(stock_data)} 只股票")
    
    # 查找已减半股票
    reduced_stocks = []
    for code, info in stock_data.items():
        if info.get('is_reduced', False):
            reduced_stocks.append({
                'code': code,
                'name': info.get('name', '未知'),
                'old_priority': info.get('sell_priority', 999),
                'old_reason': info.get('sell_reason', '未知')
            })
    
    print(f"🔍 找到 {len(reduced_stocks)} 只已减半股票:")
    for stock in reduced_stocks:
        print(f"  {stock['name']} ({stock['code']}) - 当前优先级: {stock['old_priority']}")
    
    if not reduced_stocks:
        print("✅ 没有找到已减半股票")
        return
    
    # 重新计算已减半股票的卖出信号
    print(f"\n🔄 重新计算已减半股票的卖出信号...")
    updated_count = 0
    
    for stock in reduced_stocks:
        code = stock['code']
        info = stock_data[code]
        
        try:
            print(f"\n📊 处理: {stock['name']} ({code})")
            print(f"   旧优先级: {stock['old_priority']}")
            
            # 重新计算卖出信号
            new_signal = calculate_sell_signal(info)
            
            # 更新数据
            info['sell_signal'] = new_signal['signal']
            info['sell_reason'] = new_signal['reason']
            info['sell_color'] = new_signal['color']
            info['sell_priority'] = new_signal['priority']
            info['sell_details'] = new_signal.get('details', '')
            
            print(f"   新优先级: {new_signal['priority']}")
            print(f"   新原因: {new_signal['reason']}")
            
            if new_signal['priority'] != stock['old_priority']:
                print(f"   ✅ 优先级已更新: {stock['old_priority']} → {new_signal['priority']}")
            else:
                print(f"   ⚠️ 优先级未变化: {new_signal['priority']}")
            
            updated_count += 1
            
        except Exception as e:
            print(f"   ❌ 更新失败: {e}")
    
    # 保存更新后的数据
    cache_data['stock_data'] = stock_data
    with open(cache_file, 'w', encoding='utf-8') as f:
        json.dump(cache_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 已更新 {updated_count} 只已减半股票的卖出信号")
    
    # 验证更新结果
    print(f"\n🔍 验证更新结果:")
    for stock in reduced_stocks:
        code = stock['code']
        info = stock_data[code]
        new_priority = info.get('sell_priority', 999)
        new_reason = info.get('sell_reason', '未知')
        
        if new_priority == 4:
            print(f"  ✅ {stock['name']} ({code}) - 优先级: {new_priority}, 原因: {new_reason}")
        else:
            print(f"  ❌ {stock['name']} ({code}) - 优先级: {new_priority}, 原因: {new_reason}")

def main():
    """主函数"""
    try:
        fix_reduced_stocks_priority()
        print("\n🎉 已减半股票优先级修复完成!")
        print("💡 现在可以刷新网页查看新的排序效果")
    except Exception as e:
        print(f"\n❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
