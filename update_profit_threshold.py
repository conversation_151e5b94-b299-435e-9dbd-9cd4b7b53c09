#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新负TTM回本策略配置，使用新的profit_threshold参数
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入策略管理器
from 持仓系统_v14 import SellStrategyManager

def update_profit_threshold():
    """更新profit_threshold参数"""
    print("🔧 更新负TTM回本策略配置...")
    
    # 创建策略管理器实例
    strategy_manager = SellStrategyManager()
    
    # 检查负TTM回本清仓策略
    ttm_profit_strategy = strategy_manager.strategies.get('negative_ttm_profit_clearance')
    if ttm_profit_strategy:
        print(f"\n📊 负TTM回本清仓策略当前参数:")
        for key, value in ttm_profit_strategy['params'].items():
            print(f"   {key}: {value}")
        
        # 更新参数
        if 'warning_offset' in ttm_profit_strategy['params']:
            # 移除旧的warning_offset参数
            del ttm_profit_strategy['params']['warning_offset']
            print(f"🗑️ 移除旧的warning_offset参数")
        
        # 确保有profit_threshold参数
        if 'profit_threshold' not in ttm_profit_strategy['params']:
            ttm_profit_strategy['params']['profit_threshold'] = 10.0
            print(f"✅ 添加profit_threshold参数: 10.0")
        else:
            print(f"✅ profit_threshold参数已存在: {ttm_profit_strategy['params']['profit_threshold']}")
        
        # 更新描述
        ttm_profit_strategy['description'] = 'TTM≤0 且 股息≤3% 且 接近/已回本时清仓'
        
        print(f"\n📊 更新后的参数:")
        for key, value in ttm_profit_strategy['params'].items():
            print(f"   {key}: {value}")
    
    # 保存配置到文件
    print(f"\n💾 保存策略配置到文件...")
    strategy_manager.save_strategy_config()
    
    print(f"✅ 策略配置更新完成!")
    
    # 验证配置文件
    print(f"\n🔍 验证配置文件...")
    try:
        import json
        with open('strategy_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        ttm_profit_config = config.get('negative_ttm_profit_clearance', {})
        if 'params' in ttm_profit_config:
            params = ttm_profit_config['params']
            if 'profit_threshold' in params:
                print(f"✅ 配置文件验证成功: profit_threshold = {params['profit_threshold']}")
            else:
                print(f"❌ 配置文件验证失败: 未找到profit_threshold参数")
            
            if 'warning_offset' in params:
                print(f"⚠️ 仍存在旧的warning_offset参数: {params['warning_offset']}")
            else:
                print(f"✅ 已成功移除warning_offset参数")
        else:
            print(f"❌ 配置文件验证失败: 未找到params")
            
    except Exception as e:
        print(f"❌ 验证配置文件失败: {e}")

def main():
    """主函数"""
    try:
        update_profit_threshold()
        print("\n🎉 负TTM回本策略配置更新完成!")
        print("💡 现在可以刷新网页查看新的回本阈值参数")
    except Exception as e:
        print(f"\n❌ 更新失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
