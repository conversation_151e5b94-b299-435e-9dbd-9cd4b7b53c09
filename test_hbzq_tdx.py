"""
测试华宝证券通达信SAFESCORE指标调用
基于用户提供的路径: C:\zd_hbzq\TdxW.exe
"""
import ctypes
import os
import requests
import json

def explore_tdx_directory():
    """探索通达信目录结构"""
    print("🔍 探索华宝证券通达信目录结构...")
    
    base_path = r"C:\zd_hbzq"
    
    if not os.path.exists(base_path):
        print(f"❌ 通达信目录不存在: {base_path}")
        return False
    
    print(f"✅ 找到通达信目录: {base_path}")
    
    # 探索目录结构
    try:
        items = os.listdir(base_path)
        print(f"📁 根目录内容: {len(items)} 个项目")
        
        # 查找关键目录
        key_dirs = []
        for item in items:
            item_path = os.path.join(base_path, item)
            if os.path.isdir(item_path):
                print(f"   📁 {item}/")
                key_dirs.append(item)
                
                # 如果是T开头的目录，进一步探索
                if item.startswith('T') or 'dll' in item.lower():
                    try:
                        sub_items = os.listdir(item_path)
                        print(f"      子目录: {sub_items[:10]}...")  # 只显示前10个
                        
                        # 查找DLL文件
                        dll_files = [f for f in sub_items if f.endswith('.dll')]
                        if dll_files:
                            print(f"      🎯 DLL文件: {dll_files[:5]}...")
                    except:
                        pass
            else:
                if item.endswith('.exe'):
                    print(f"   📄 {item}")
        
        return True
        
    except Exception as e:
        print(f"❌ 探索目录失败: {e}")
        return False

def test_dll_paths():
    """测试DLL路径"""
    print("\n🧪 测试DLL路径...")
    
    base_path = r"C:\zd_hbzq"
    
    # 可能的DLL路径
    dll_paths = [
        os.path.join(base_path, "T0002", "dlls"),
        os.path.join(base_path, "dlls"),
        os.path.join(base_path, "bin"),
        os.path.join(base_path, "plugin"),
        os.path.join(base_path, "plugins"),
        os.path.join(base_path, "lib"),
        os.path.join(base_path, "system"),
    ]
    
    found_dlls = []
    
    for dll_path in dll_paths:
        if os.path.exists(dll_path):
            print(f"✅ 找到目录: {dll_path}")
            try:
                files = os.listdir(dll_path)
                dll_files = [f for f in files if f.endswith('.dll')]
                if dll_files:
                    print(f"   🎯 DLL文件 ({len(dll_files)}个): {dll_files[:5]}...")
                    found_dlls.extend([(dll_path, dll) for dll in dll_files])
                else:
                    print(f"   📁 无DLL文件")
            except Exception as e:
                print(f"   ❌ 访问失败: {e}")
        else:
            print(f"❌ 目录不存在: {dll_path}")
    
    return found_dlls

def test_dll_loading(dll_info_list):
    """测试DLL加载"""
    print("\n🧪 测试DLL加载...")
    
    if not dll_info_list:
        print("❌ 没有找到DLL文件")
        return False
    
    success_count = 0
    
    for dll_path, dll_name in dll_info_list[:10]:  # 只测试前10个
        full_path = os.path.join(dll_path, dll_name)
        print(f"🔍 测试加载: {dll_name}")
        
        try:
            # 尝试加载DLL
            dll = ctypes.WinDLL(full_path)
            print(f"✅ 成功加载: {dll_name}")
            success_count += 1
            
            # 检查是否有SAFESCORE相关函数
            safescore_functions = [
                'SAFESCORE', 'SafeScore', 'GetSafeScore', 
                'SLBSCORE', 'SlbScore', 'GetSlbScore',
                'BXB', 'GetBxb', 'SLB', 'GetSlb'
            ]
            
            found_functions = []
            for func_name in safescore_functions:
                if hasattr(dll, func_name):
                    found_functions.append(func_name)
            
            if found_functions:
                print(f"   🎯 找到相关函数: {found_functions}")
                
                # 尝试调用函数
                for func_name in found_functions:
                    try:
                        func = getattr(dll, func_name)
                        print(f"   📞 尝试调用 {func_name}...")
                        
                        # 设置函数签名（猜测）
                        func.argtypes = [ctypes.c_char_p]
                        func.restype = ctypes.c_float
                        
                        # 调用函数
                        result = func(b'300479')  # 神思电子
                        print(f"   ✅ {func_name}('300479') = {result}")
                        
                        if 0 <= result <= 100:
                            print(f"   🎉 获得有效评分: {result}分")
                            return True
                            
                    except Exception as e:
                        print(f"   ❌ 调用 {func_name} 失败: {e}")
            else:
                print(f"   📝 未找到SAFESCORE相关函数")
                
        except Exception as e:
            print(f"❌ 加载失败 {dll_name}: {e}")
    
    print(f"\n📊 DLL加载测试结果: {success_count}/{len(dll_info_list[:10])} 个成功")
    return success_count > 0

def test_safescore_with_real_path():
    """使用真实路径测试SAFESCORE"""
    print("\n🎯 使用真实路径测试SAFESCORE...")
    
    # 1. 探索目录
    if not explore_tdx_directory():
        return False
    
    # 2. 测试DLL路径
    dll_info_list = test_dll_paths()
    
    # 3. 测试DLL加载
    if dll_info_list:
        return test_dll_loading(dll_info_list)
    
    return False

def main():
    """主函数"""
    print("🎯 华宝证券通达信SAFESCORE测试")
    print("基于路径: C:\\zd_hbzq\\TdxW.exe")
    print("=" * 60)
    
    success = test_safescore_with_real_path()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试成功！找到了SAFESCORE调用方法")
        print("💡 可以集成到持仓系统中使用")
    else:
        print("❌ 测试失败，未找到可用的SAFESCORE调用方法")
        print("💡 建议检查通达信版本或尝试其他方法")

if __name__ == '__main__':
    main()
