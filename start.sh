#!/bin/bash
# 启动持仓系统

echo "🚀 启动持仓系统..."

# 检查是否已经在运行
if pgrep -f "gunicorn.*持仓系统" > /dev/null; then
    echo "⚠️ 系统已在运行中"
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 启动Gunicorn
echo "📊 启动Web服务..."
gunicorn --config gunicorn_config.py 持仓系统_v12_完整整合版:app --daemon

# 检查启动状态
sleep 2
if pgrep -f "gunicorn.*持仓系统" > /dev/null; then
    echo "✅ 持仓系统启动成功！"
    echo "🌐 访问地址: http://your-server-ip:8000"
    echo "📋 查看日志: tail -f logs/error.log"
else
    echo "❌ 启动失败，请检查日志"
    tail -n 20 logs/error.log
fi
